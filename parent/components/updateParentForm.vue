<template>
  <Popup
    :show="isAddShow"
    @close="clearFormData"
  >
    <view>
      <view class="add-form-title">
        <view>{{ title }}</view>
        <view @click="onComplete">完成</view>
      </view>
      <view style="padding-left: 20rpx">
        <up-form
          ref="formRef"
          labelPosition="left"
          :model="formData"
          labelWidth="80"
          :rules="rules"
        >
          <up-form-item
            :style="addFormItemStyle"
            label="家长姓名"
            prop="name"
            required
          >
            <text v-if="disabled">{{ formData.name }}</text>
            <up-input
              cursorSpacing="100"
              v-model="formData.name"
              placeholder="请输入内容"
              border="none"
              v-else
            />
          </up-form-item>
          <up-form-item
            :style="addFormItemStyle"
            label="关系"
            prop="relationshipText"
            @click="() => childId && openPicker('关系')"
            required
          >
            <up-input
              v-if="childId"
              cursorSpacing="100"
              v-model="formData.relationshipText"
              disabled
              disabledColor="#fff"
              placeholder="请输入内容"
              border="none"
            />
            <template
              v-if="childId"
              #right
            >
              <up-icon name="arrow-down" />
            </template>
            <view v-if="!childId">{{ formData.childRelationshipText }}</view>
          </up-form-item>
          <up-form-item
            :style="addFormItemStyle"
            label="性别"
            prop="genderText"
            @click="openPicker('性别')"
            required
          >
            <up-input
              v-model="genderText"
              disabled
              disabledColor="#fff"
              placeholder="请选择性别"
              border="none"
            />
            <template #right>
              <up-icon name="arrow-down" />
            </template>
          </up-form-item>
          <up-form-item
            :style="addFormItemStyle"
            label="手机号"
            prop="phone"
            required
          >
            <up-input
              cursorSpacing="100"
              v-model="formData.phone"
              placeholder="请输入内容"
              border="none"
            />
          </up-form-item>
          <up-form-item
            :style="addFormItemStyle"
            label="职业"
            prop="occupation"
          >
            <up-input
              cursorSpacing="100"
              v-model="formData.occupation"
              placeholder="请输入内容"
              border="none"
            />
          </up-form-item>
          <up-form-item
            :style="addFormItemStyle"
            label="公司"
            prop="company"
          >
            <up-input
              cursorSpacing="100"
              v-model="formData.company"
              placeholder="请输入内容"
              border="none"
            />
          </up-form-item>

          <up-form-item
            :style="addFormItemStyle"
            label="教育程度"
            @click="openPicker('教育程度')"
            prop="education"
          >
            <up-input
              v-model="formData.educationText"
              disabled
              disabledColor="#fff"
              placeholder="请输入内容"
              border="none"
            />
            <template #right>
              <up-icon name="arrow-down" />
            </template>
          </up-form-item>
        </up-form>
      </view>
      <up-picker
        :show="isPicker"
        :defaultIndex="[[0]]"
        :columns="columns"
        @cancel="closePup"
        @confirm="pickerConfirm"
        keyName="label"
      ></up-picker>
    </view>
  </Popup>
</template>

<script setup>
import { reactive, ref, onMounted, computed, watch, nextTick } from "vue";

import {
  updateParentDetail,
  getParentDetail,
  addParentDetail,
} from "@/api/api.js";
import utils from "../utils/addForm.js";

let isPicker = ref(false);
let formRef = ref(null);
const childId = ref(null);
const formData = reactive({
  company: "",
  education: "",
  educationText: "",
  gender: 1,
  id: "",
  name: "",
  occupation: "",
  phone: "",
});
const isAddShow = ref(false);

const props = defineProps({
  title: String,
  parentItemData: Object,
  disabled: {
    type: Boolean,
    default: true,
  },
});

const columns = ref([
  [
    {
      label: "",
      id: "",
    },
  ],
]);

let istermAge = ref("");

const genderText = computed(() => {
  const t = formData.gender;
  return ["", "男", "女"][t] || "";
});

let addFormItemStyle = {
  height: "115rpx",
  justifyContent: "center",
};

const { rules } = utils();

function closePup() {
  columns.value = [];
  isPicker.value = false;
}

// 清空字段
const clearFormData = () => {
  isAddShow.value = false;
  // 清空formData中所有的字段
  Object.keys(formData).forEach((key) => {
    formData[key] = "";
  });
};

// 确定修改/新增
async function onComplete() {
  if (!childId.value) {
    formData.relationshipText = formData.childRelationshipText;
  }
  formRef.value.validate().then((valid) => {
    if (valid) {
      // 判断男女是否 必填
      const fn = formData.id ? updateParentDetail : addParentDetail;
      const f = { ...formData };
      if (childId.value) {
        f.childId = childId.value;
      }
      fn(f).then((response) => {
        if (response.status === 0) {
          uni.$u.toast("更新成功");
          isAddShow.value = false;
          emit("confirm", formData);
          clearFormData();
        }
      });
    } else {
      uni.$u.toast("请检查是否有漏填");
    }
  });
}

let sexList = [
  {
    label: "男",
    id: 1,
  },
  {
    label: "女",
    id: 2,
  },
]; // 性别
let educationLevels = [
  { label: "未上过学", id: 0 },
  { label: "小学毕业", id: 1 },
  { label: "初中毕业", id: 2 },
  { label: "高中", id: 3 },
  { label: "中专或职高", id: 4 },
  { label: "大专", id: 5 },
  { label: "本科", id: 6 },
  { label: "硕士研究生", id: 7 },
  { label: "博士研究生", id: 8 },
]; // 年级

let relationship = [
  { label: "父亲", id: 1 },
  { label: "母亲", id: 2 },
  { label: "爷爷", id: 3 },
  { label: "奶奶", id: 4 },
  { label: "外公", id: 5 },
  { label: "外婆", id: 6 },
  { label: "伯父", id: 7 },
  { label: "伯母", id: 8 },
  { label: "叔叔", id: 9 },
  { label: "婶婶", id: 10 },
  { label: "姑姑", id: 11 },
  { label: "姑父", id: 12 },
  { label: "舅舅", id: 13 },
  { label: "舅妈", id: 14 },
]; // 年级

function pickerConfirm(e) {
  if (istermAge.value == "性别") {
    formData.gender = Number(e.value[0].id);
    formData.gradename = e.value[0].label;
  }
  if (istermAge.value === "教育程度") {
    formData.education = e.value[0].id;
    formData.educationText = e.value[0].label;
  }
  if (istermAge.value === "关系") {
    formData.relationship = e.value[0].id;
    formData.relationshipText = e.value[0].label;
    // 自动变换男女
    const malePattern = /(父|公|叔|舅|爷)/;
    const femalePattern = /(母|婆|婶|姑|妈|奶)/;
    if (malePattern.test(e.value[0].label)) {
      formData.gender = 1;
      formData.gradename = "男";
      if (e.value[0].label == "舅妈") {
        formData.gender = 2;
        formData.gradename = "女";
      }
    } else if (femalePattern.test(e.value[0].label)) {
      formData.gender = 2;
      formData.gradename = "女";
    }
  }
  isPicker.value = false;
}

function openPicker(e) {
  istermAge.value = e;
  if (e == "性别") columns.value = [sexList];
  if (e == "教育程度") columns.value = [educationLevels];
  if (e == "关系") columns.value = [relationship];
  isPicker.value = true;
}

async function onStartView(item) {
  isAddShow.value = true;
  await nextTick();
  formRef.value.resetFields();
  formRef.value.clearValidate();
  formData.educationText = "";
  formData.relationshipText = "";
  formData.id = "";
  formData.gender = 1;
  childId.value = item && item.childId;
}
// 修改家长详情
function onUpdataView(id, childIds) {
  isAddShow.value = true;
  const params = {
    parentId: id,
    childId: childIds,
  };

  childId.value = childIds;
  if (id) {
    initParentDetail(params);
  }
}

const initParentDetail = (id) => {
  getParentDetail(id).then((response) => {
    Object.keys(response.data).forEach((key) => {
      console.log(key);
      if (key == "relationship") {
        formData.relationshipText = relationship.find((item) => item.id === Number(response.data[key])).label;
      }
      formData[key] = response.data[key];
    });
  });
};

const emit = defineEmits(["confirm"]);
defineExpose({
  onStartView,
  onUpdataView,
});
</script>
<script>
export default {
  options: { styleIsolation: "shared" }, // 解除样式隔离
};
</script>
<style lang="scss" scoped>
::v-deep .u-form-item {
  border-bottom: 1px solid#eee;
}

::v-deep .u-form-item:last-child {
  border-bottom: none;
}

.add-form-title {
  display: flex;
  align-items: center;
  justify-content: space-between;

  view:first-child {
    font-size: 34rpx;
    font-weight: 600;
  }

  view:last-child {
    font-size: 30rpx;
    font-weight: 500;
    color: rgba(63, 121, 255, 1);
  }
}
</style>
