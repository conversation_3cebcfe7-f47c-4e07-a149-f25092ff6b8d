<template>
  <view class="layout">
    <view
      class="list"
      style="margin-top: 20rpx"
    >
      <view v-if="isLoading">
        <up-list
          height="calc(100vh - 240rpx)"
          :enableFlex="true"
          @scrolltolower="scrolltolower"
        >
          <up-list-item
            v-for="(item, index) in listdata.data"
            :key="item.id"
          >
            <view class="col user-item flex">
              <view>
                <image
                  src="https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_boy.png"
                />
              </view>
              <view
                class="user-info"
                @click="gotoClassDetails(item)"
              >
                <view class="flex-ac">
                  <text class="user-name">{{ item.name }}</text>

                  <text
                    class="f-w-500 f-s-22"
                    style="color: #808080"
                    >{{ item.note }}</text
                  >
                </view>
                <view
                  style="color: #808080; margin-right: 12rpx; font-size: 24rpx"
                >
                  {{ item.childRelationshipText }}
                </view>
              </view>
              <!-- <view>
                <up-icon
                  class="card-icon"
                  size="36rpx"
                  name="more-dot-fill"
                  @click="onViewParentDetail(item, index)"
                />
              </view> -->
            </view>
          </up-list-item>
        </up-list>
      </view>
      <up-loading-icon
        style="margin-top: 200rpx"
        v-else
        mode="circle"
      />
      <up-empty
        style="margin-top: 200rpx"
        v-if="!listdata.data && listdata.data.length == 0"
        mode="data"
      />
    </view>

    <add-form
      ref="addFormRef"
      @confirm="onConfirmUpdateParent"
      :title="'家长详情'"
      :parentItemData="listdata.parentItemData"
    ></add-form>
  </view>
</template>

<script setup>
import { reactive, ref } from "vue";
import {
  getCourseList,
  deleteItemList,
  getParentList,
  updateParentDetail,
  getParentDetail,
} from "@/api/api.js";
import { onShow, onShareAppMessage, onLoad } from "@dcloudio/uni-app";
import { checks, sharePageObj } from "@/utils/index.js";
import { dictionaryList } from "@/api/index.js";
import addForm from "./components/updateParentForm.vue";

const listdata = reactive({
  data: [],
  parentItemData: {},
  parentDetail: {},
});
const isLoading = ref(false);
const isPickerShow = ref(false);
const classTypeName = ref("中班");
const addFormRef = ref();
const paging = reactive({
  currentPage: 1,
  pageSize: 10,
});
const isRequest = ref(true);

// 触底增加数据
async function scrolltolower() {
  if (isRequest.value) {
    paging.currentPage++;

    let res = await getParentList({
      ...paging,
    });
    let i = Math.ceil(res.metadata.count / paging.pageSize);
    console.log(i, "123");

    if (paging.currentPage <= i) {
      res.data.forEach((item) => {
        listdata.data.push(item);
      });
    } else {
      isRequest.value = false;
    }
  }
}

function onViewParentDetail(item, i) {
  listdata.parentItemData = item;
  if (addFormRef.value) {
    addFormRef.value.onUpdataView(item.id);
  }
}

function gotoClassDetails(item) {}

const getList = async () => {
  isLoading.value = false;
  paging.currentPage = 1;
  let res = await getParentList({
    ...paging,
  });
  if (res.data) {
    listdata.data = res.data;
  }
  isLoading.value = true;
};

const onConfirmUpdateParent = (formData) => {
  listdata.parentItemData = {};
  getList();
};
onLoad((options) => {
  if (options.classId) {
    paging.classId = options.classId;
    getList();
    return;
  }
  getList();
});
onShow(() => {
  checks();
});
// 小程序分享页面
onShareAppMessage((res) => sharePageObj());
</script>

<style lang="scss" scoped>
.layout {
  box-sizing: border-box;
  overflow: hidden;
  background: #f2f2f2;
  height: 100%;
  padding-left: 32rpx;
  padding-right: 32rpx;

  .search {
    margin-top: 20rpx;
    display: flex;
    background: #fff;
    align-items: center;
    padding-right: 20rpx;
  }

  .card {
    height: 146rpx;
    opacity: 1;
    border-radius: 16rpx;
    margin-bottom: 16rpx;
    background: #ffffff;
    box-sizing: border-box;
    padding: 14rpx;
    display: flex;
    // align-items: center;
  }

  .u-list-item:first-of-type {
    margin-top: 24rpx;
  }

  ::v-deep .u-list-item {
    overflow: hidden;
  }
}

.card-icon {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
}

.user-item {
  position: relative;
  margin-bottom: 24rpx;
  width: 100%;
  border-radius: 28rpx;
  box-sizing: border-box;
  background: #ffffff;
  box-shadow: 4rpx 8rpx 16rpx #eee;
  padding: 28rpx;
  .user-info {
    flex: 1;
  }

  .user-name {
    font-size: 30rpx;
    font-weight: 600;
    margin-right: 12rpx;
  }

  image {
    width: 88rpx;
    height: 88rpx;
    margin-right: 24rpx;
    border-radius: 20rpx;
    overflow: hidden;
  }
}

.user-item:last-child {
  margin-bottom: 28rpx;
}
</style>
