import { reactive } from 'vue';

export default () => {
	const rules = reactive({
		name: [{
			required: true,
			message: '请输入名称',
			trigger: ['blur', 'change'],
		}],
		relationshipText: [{
			required: true,
			message: '请输入关系',
			trigger: ['blur', 'change'],
		}],
		phone: [{
			required: true,
			message: '请输入手机号',
			trigger: ['blur', 'change'],
			validator: (rule, value, callback) => {
				// 上面有说，返回true表示校验通过，返回false表示不通过
				// uni.$u.test.mobile()就是返回true或者false的
				console.log(uni.$u.test.mobile(value));
				let reg = /^1[3456789]\d{9}$/
				return reg.test(value);
			},
		}]
	})
	return {
		rules
	}
}