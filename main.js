import App from "./App";
import uviewPlus from "@/uni_modules/uview-plus";
import * as <PERSON><PERSON> from 'pinia';
import { useCurrentClassId } from "./utils/index.js";

// #ifndef VUE3
import Vue from "vue";
import "./uni.promisify.adaptor";

Vue.config.productionTip = false;
App.mpType = "app";
const app = new Vue({
  ...App,
});
app.$mount();
// #endif

// #ifdef VUE3
import { createSSRApp } from "vue";

export function createApp() {
  const app = createSSRApp(App);
  app.provide('useCurrentClassId', useCurrentClassId);
  app.use(uviewPlus);
  app.use(Pinia.createPinia());
  return {
    app,
    Pinia, // 此处必须将 Pinia 返回
  };
}
// #endif
