<!-- 通用ui -->
<template>
  <view class="work-layout">
    <up-loading-icon
      size="20"
      mode="semicircle"
      style="margin-top: 100rpx"
      :show="loading"
    />

      <view class="work-layout-content">
        <scroll-view
          class="work-layout-content-scrollview"
          :scroll-y="true"
          @scrolltolower="emit('scrolltolower')"
        >
          <slot></slot>
          <view
            v-if="isNoMore"
            class="no-more-tip"
            >没有更多了</view
          >
        </scroll-view>
      </view>
  </view>
</template>

<script setup>
const props = defineProps({
  loading: {
    type: Boolean,
    default: true,
  },
  isNoMore: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["scrolltolower"]);
</script>

<style lang="scss" scoped>
.no-more-tip {
  text-align: center;
  padding: 20rpx;
  color: #999;
  font-size: 26rpx;
}
.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
  color: #999;
  font-size: 28rpx;
}
.work-layout-content {
  // display: flex;
  // flex-direction: column;
  // border: 1px solid red;

  .work-layout-content-scrollview {
    // flex: 1;
    // overflow: scroll;
    height: 76vh;
    overflow: scroll;
  }
  margin-bottom: 28rpx;
}
</style>
