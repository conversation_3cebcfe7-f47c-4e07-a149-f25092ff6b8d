<!-- 儿童主页 -->
<template>
  <view class="hp-layout">
    <base-layout
      navTitle="儿童主页"
      :contentStyle="{
        padding: '0'
      }"
      :footerStyle="{
        display: 'none'
      }"
    >
      <view class="user-info flex-ac">
        <image
          v-if="userInfo?.headers?.length > 0 && userInfo.headers[0]?.uri"
          :src="userInfo.headers[0].uri"
          mode="aspectFill"
        />
        <image v-else :src="userInfo?.sex == 1 ? headList[0] : headList[1]" mode="aspectFill" />

        <view style="flex: 1">
          <view class="flex-ac" style="margin-bottom: 16rpx">
            <view class="name">{{ userInfo.title }}</view>
            <up-icon
              :name="userInfo.sex == 1 ? '/static/icon/male.png' : '/static/icon/female.png'"
              size="36rpx"
            />
          </view>
          <!-- <view class="info" style="margin-top: 0;">若贝尔幼儿园·大一班</view> -->
          <!-- <view class="info" style="margin-top: 0">----</view> -->
          <view class="info">班主任：{{ teacherName || '--' }}</view>
        </view>
      </view>
      <view class="bg">
        <scroll-view class="scroll-view_H" :scroll-x="true">
          <view class="tabs">
            <view
              v-for="(item, index) in tabsList"
              :key="index"
              class="tabs-item"
              :class="{ 'active-tabs-item': activeIndex == item.key }"
              @click="tabSwitcher(item.key)"
            >
              {{ item.name }}
            </view>
          </view>
        </scroll-view>
        <view class="content" :style="{ paddingBottom: getBottonHeight() + 'px' }">
          <dynamics v-if="activeIndex == 'dynamics'" :userInfo="userInfo" />
          <Album v-if="activeIndex == 'album'" :userInfo="userInfo" />
          <profile v-if="activeIndex == 'profile'" :userInfo="userInfo" />
        </view>
      </view>
    </base-layout>
  </view>
</template>

<script setup>
import { ref, onUnmounted } from 'vue'
import { onLoad, onShow, onShareAppMessage } from '@dcloudio/uni-app'
import upIcon from '@/uni_modules/uview-plus/components/u-icon/u-icon.vue'
import dynamics from './components/dynamics/dynamics.vue'
import Album from './components/album/album.vue'
import profile from './components/profile/profile.vue'
import { getBottonHeight, sharePageObj } from '@/utils/index.js'
import { getChildrenInfo } from '@/childrenDetails/api/index.js'
import { getclassList } from '@/api'

let userInfo = ref({})
let tabsList = ref([
  {
    name: '动态',
    key: 'dynamics'
  },
  {
    name: '相册',
    key: 'album'
  },
  {
    name: '档案',
    key: 'profile'
  }
])
let headList = [
  'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_boy.png',
  'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_girl.png'
]
let _id = ''
let teacherName = ref('')
let activeIndex = ref('dynamics')
const childrenInfo = async (id) => {
  const res = await getChildrenInfo(id)
  userInfo.value = res.data
  classList(userInfo.value.schoolId, userInfo.value.classId)
}

const classList = async (schoolId, classId) => {
  const res = await getclassList({
    schoolId
  })
  if (res.status === 0) {
    const curObj = res.data.find((item) => item.id === classId)
    teacherName.value = curObj?.extra?.masterTitle
    userInfo.value.class = curObj?.title
    userInfo.value.classId = classId
  }
}

function tabSwitcher(val) {
  activeIndex.value = val
}

// 移除 scrollLeft 和 tabWidth 定义
onLoad((option) => {
  _id = option.id
  // childrenInfo(option.id);
})
onShow(() => {
  childrenInfo(_id)
})

// 监听儿童信息刷新事件
uni.$on('refreshChildInfo', () => {
  childrenInfo(_id)
})

// 组件销毁时移除事件监听
onUnmounted(() => {
  uni.$off('refreshChildInfo')
})
onShareAppMessage(() =>
  sharePageObj({
    path: '/childrenDetails/homepage/homepage?id=' + _id
  })
)
</script>

<style lang="scss" scoped>
@import '@/common/css/index.scss';

.hp-layout {
  .user-info {
    padding: 20rpx 30rpx;
    .name {
      font-size: 44rpx;
      font-weight: 600;
      line-height: 44rpx;
      margin-right: 12rpx;
    }

    .info {
      font-size: 24rpx;
      font-weight: 400;
      line-height: 28rpx;
      color: rgba(128, 128, 128, 1);
      margin-top: 10rpx;
    }

    image {
      width: 124rpx;
      height: 124rpx;
      border-radius: 50%;
      margin-right: 28rpx;
    }
  }

  .bg {
    box-sizing: border-box;
    // height: 404rpx;
    height: 83vh;
    background: url('https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/homepage_bg1.png')
      no-repeat;
    background-size: 100% auto;
    background-position: top;
    border-radius: 40rpx;
    padding: 28rpx 32rpx 0rpx 32rpx;
    margin-top: 40rpx;
    display: flex;
    flex-direction: column;

    .content {
      margin-top: 28rpx;
      // border: 1px solid red;
      flex: 1;
    }

    .tabs {
      display: inline-flex;
      align-items: center;
      gap: 16rpx;

      .tabs-item {
        flex: 0 0 auto;
        min-width: 76rpx;
        height: 30rpx;
        line-height: 30rpx;
        font-size: 24rpx;
        font-weight: 400;
        color: rgba(82, 82, 82, 1);
        padding: 8rpx 20rpx;
        text-align: center;
        border-radius: 28rpx;
        transition: all 0.3s ease;

        &:last-child {
          margin-right: 10rpx;
        }

        &:first-child {
          margin-left: 10rpx;
        }

        &.active-tabs-item {
          color: rgba(255, 255, 255, 1);
          background: rgba(63, 121, 255, 1);
        }
      }
    }
  }

  image {
    width: 124rpx;
    height: 124rpx;
    border-radius: 50%;
    margin-right: 28rpx;
  }
}

.bg {
  box-sizing: border-box;
  height: 404rpx;
  background: url('https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/homepage_bg1.png')
    no-repeat;
  background-size: 100% auto;
  background-position: top;
  border-radius: 40rpx;
  padding: 28rpx 32rpx 0rpx 32rpx;
  margin-top: 40rpx;

  .content {
    margin-top: 28rpx;
  }
}

.scroll-view_H {
  width: 100%;
  white-space: nowrap;
}
</style>
