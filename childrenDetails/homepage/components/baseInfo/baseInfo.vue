<!-- 基本信息 -->
<template>
  <view class="base-info-layout">
    <view class="col">
      <view class="flex-jsb-ac top">
        <text class="title">基本信息</text>
        <!-- <image src="@/static/common/editor.png" style="width: 32rpx;height: 32rpx; margin-top: 10rpx;" /> -->
      </view>
      <view class="item" v-for="(item, index) in itemList" :key="index">
        <text class="name">{{ item.label }}</text>
        <text class="content">{{ item.value || '-' }}</text>
      </view>
      <!-- 童言童语 照片 观察评价 -->
      <!-- <view
        class="item item-last"
        v-for="(item, index) in list"
        :key="index"
      >
        <text class="name">{{ item.label }}</text>
        <text class="content">{{ item.value || "-" }}</text>
        <up-icon name="arrow-right" />
      </view> -->
      <!-- 修改儿童人脸 -->
      <view class="UpdataItem" style="justify-content: space-between" @tap="changeAvatar('avatar')">
        <text class="name">上传头像</text>
        <up-icon name="arrow-right" size="28rpx" />
      </view>
      <view class="UpdataItem" style="justify-content: space-between" @tap="changeAvatar('face')">
        <text class="name">上传人脸（用于人脸识别）</text>
        <up-icon name="arrow-right" size="28rpx" />
      </view>
      <!-- 儿童过敏源 -->
      <view class="item">
        <text class="name">儿童过敏源</text>
        <view class="allergen-content">
          <view v-if="allergenList.length === 0" class="content">-</view>
          <view v-else class="allergen-list">
            <view v-for="(allergen, index) in allergenList" :key="allergen.allergenItemCode" class="allergen-item">
              <text class="allergen-name">{{ allergen.allergenItemName }}</text>
              <text v-if="allergen.desc" class="allergen-desc">（{{ allergen.desc }}）</text>
              <text v-if="index < allergenList.length - 1" class="separator">、</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { watch, ref } from 'vue'
import { timeFormat } from '@/uni_modules/uview-plus'
import utils from './utils'
import { getChildAllergen } from '@/childrenDetails/api/index.js'
import { onLoad } from '@dcloudio/uni-app'

// 过敏源数据
const allergenList = ref([])

onLoad(async (options) => {
  const res = await getChildAllergen({
    childId: options.id
  })
  if (res && res.data) {
    allergenList.value = res.data
  }
})

let props = defineProps({
  userInfo: {
    type: Object,
    default: () => {}
  }
})

const { itemList } = utils()

watch(
  () => props.userInfo,
  (newVal) => {
    console.log('%c, itemList.value', 'color: #008080', itemList.value)
    if (newVal) {
      itemList.value = itemList.value.map((item) => {
        let value = newVal[item.key]
        if (item.key === 'sex') {
          value = value === 1 ? '男' : '女'
        } else if (item.key === 'isSingle') {
          value = value === 1 ? '是' : '否'
        } else if (item.key === 'symptoms') {
          value = value === 1 ? '正常儿童' : value === 2 ? '疑似特殊' : '诊断特殊'
        } else if (item.key === 'entryTime' || item.key === 'birthday') {
          value = timeFormat(new Date(value), 'yyyy-mm-dd')
        }
        return {
          ...item,
          value
        }
      })
    }
  },
  { deep: true, immediate: true }
)

const changeAvatar = (type) => {
  if (props.userInfo.id) {
    const url =
      type == 'face'
        ? '/childrenDetails/updateAvatar/updateFace'
        : '/childrenDetails/updateAvatar/updateAvatar'
    uni.navigateTo({
      url: url + `?cId=${props.userInfo.id}&classId=${props.userInfo.classId}`
    })
  }
}
</script>

<style lang="scss" scoped>
@import '@/common/css/index.scss';

.base-info-layout {
  padding-bottom: 32rpx;
  box-sizing: border-box;
}

.top {
  height: 92rpx;
  border-bottom: 1rpx solid #eeeeee;
}

.item,
.UpdataItem {
  display: flex;
  align-items: stretch;
  border-bottom: 1rpx solid #eeeeee;
  padding: 32rpx 0;

  &:last-child {
    border-bottom: none;
  }

  .name {
    width: fit-content;
    font-size: 28rpx;
    font-weight: 600;
    color: rgba(51, 51, 51, 1);
    text-align: left;
    margin-right: 32rpx;
    display: inline-block;
  }

  .content {
    font-size: 28rpx;
    font-weight: 400;
    color: #333333;
    flex: 1;
  }
}

.UpdataItem:active {
  background: #f5f5f5;
}

.item-last {
  .name {
    width: 112rpx;
  }

  .content {
    flex: 1;
  }
}

.item:last-child {
  border-bottom: none;
}

.allergen-content {
  flex: 1;

  .allergen-list {
    display: flex;
    flex-wrap: wrap;
    align-items: center;

    .allergen-item {
      display: flex;
      align-items: center;

      .allergen-name {
        font-size: 28rpx;
        font-weight: 400;
        color: #333333;
      }

      .allergen-desc {
        font-size: 24rpx;
        color: #999999;
        margin-left: 4rpx;
      }

      .separator {
        font-size: 28rpx;
        color: #333333;
        margin: 0 8rpx;
      }
    }
  }
}

.title {
  font-size: 30rpx;
  font-weight: 600;
  line-height: 36rpx;
  color: #333333;
}

.col {
  border-radius: 28rpx;
  background: rgba(255, 255, 255, 1);
  box-shadow: 4rpx 8rpx 16rpx rgba(0, 0, 0, 0.02);
  padding: 32rpx;
  box-sizing: border-box;
}
</style>
