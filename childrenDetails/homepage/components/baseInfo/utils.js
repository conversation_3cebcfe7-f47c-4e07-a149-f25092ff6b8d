import { ref, watch } from "vue";
export default () => {
    let itemList = ref([
        {
            label: '姓名',
            key: 'title',
            value: ''
        },
        {
            label: '性别',
            key: 'sex', // 1 男 2 女
            value: ''
        },
        {
            label: '出生日期',
            key: 'birthday',
            value: 0
        },
        {
            label: '独生子女',
            key: 'isSingle', // 1 是 0 否
            value: ''
        },
        {
            label: '家庭住址',
            key: 'note',
            value: ''
        },
        {
            label: '入园时间',
            key: 'entryTime',
            value: 0
        },
        {
            label: '当前班级',
            key: 'class',
            value: ''
        },
        {
            label: '发展症状',
            key: 'symptoms', // 1 正常儿童 2 疑似特殊 3 诊断特殊
            value: ''
        },
    ])
    let list = ref([
        {
            label: '童言童语',
            key: 'tyty', 
            value: 5,
            url: ''
        },
        {
            label: '照片',
            key: 'zp',
            value: 6,
            url: ''
        },
        {
            label: '观察评价',
            key: 'gcpj', 
            value: 12,
            url: ''
        },
    ])
    return {
        itemList,
        list
    }
}