import { ref } from "vue";
export const scenes = [
  {
    label: "学习",
    id: "Learning",
  },
  {
    label: "生活",
    id: "Life",
  },
  {
    label: "区域游戏",
    id: "AreaGame",
  },
  {
    label: "体育活动",
    id: "Sports",
  },
  {
    label: "户外自主游戏",
    id: "OutdoorGame",
  },
];

export const getInitFormData = (args = {}) => ({
  observationType: "",
  schoolClassId: '',
  observationTime: "",
  observationLocation: "",
  activityName: "",
  productDesc: "",
  observationBackground: "",
  observationPurpose: "",
  observationContent: "",
  analysis: "",
  support: "",
  reflection: "",
  childNames: [],
  children: [],
  picList: [], // 附件id
  ...args
});

export const rules = ref({
  "activityName": { type: 'string', required: true, message: '请输入活动名称', trigger: ['blur'] },
  observationLocation: { required: true, message: '请输入观察地点', trigger: ['blur'] },
  children: { type: 'array', min: 1, required: true, message: '请选择观察对象, 如以选择忽略', trigger: ['blur'] }
});

/**
 *  observationRecordScene: "Learning",
  schoolClassId: 1487,
  observationTime: "2025-02-27",
  observationLocation: "1",
  activityName: "2",
  productDesc: "3",
  backgroundAndPurpose: "4",
  content: "6",
  analysis: "f",
  support: "s",
  reflection: "a",
  aiComment: "",
  childIds: [3801, 3800],
  attachmentResourceIds: [],
  matrices: [
    { matrix1Id: 1244, matrix2Id: 1252, matrix3Id: 1260, targetId: 2529 },
  ],
 */

// 去除对象中所有为空的属性
export function removeEmpty(oj) {
  let obj = JSON.parse(JSON.stringify(oj));
  Object.keys(obj).forEach((key) => {
    if (obj[key] === null || obj[key] === undefined || obj[key] === "" || (Array.isArray(obj[key]) && obj[key].length === 0)) {
      delete obj[key];
    }
    if (key === 'childNames') {
      delete obj[key];
    }
  });
  return obj;
}

// 整理孩子的信息
export function childrenFilter(arr) {
  // 判断 是对象还是数组
  if (typeof arr === 'object' && !Array.isArray(arr)) {
    return {
      classId: arr.classId,
      childId: arr.id,
      childName: arr.name,
    }
  }
  let array = JSON.parse(JSON.stringify(arr));
  let result = [];
  array.forEach((item) => {
    result.push({
      classId: item.classId,
      childId: item.id,
      childName: item.name,
    });
  });
  return result;
}

// 去重
export function removeDuplicates(array1 = [], array2 = [], type = 'childId') {
  const combinedArray = array1.concat(array2);
  const uniqueArray = combinedArray.reduce((accumulator, current) => {
    const exists = accumulator.some(obj => obj[type] === current[type]);
    if (!exists) {
      accumulator.push(current);
    }
    return accumulator;
  }, []);

  return uniqueArray;
}

