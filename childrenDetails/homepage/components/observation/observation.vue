<template>
  <view
    class="observationRecord"
    v-if="observationList.length != 0"
  >
    <up-list
      :enableFlex="true"
      height="100%"
    >
      <up-list-item
        v-for="item in observationList"
        :key="item.id"
      >
        <view class="observationRecord-title">
          <text class="observationRecord-title-left"
            >{{ item.date }}&nbsp;{{ item.dayOfWeek }}</text
          >
          <text class="observationRecord-title-right">{{ item.year }}年</text>
        </view>
        <view
          @click="onViewDetail(itm)"
          class="observationRecord-item"
          v-for="(itm, index) in item.items"
          :key="itm.id + new Date().getTime()"
        >
          <view
            v-if="itm.state == 2"
            class="Mask"
            @tap.capture.stop="onMask"
          />
          <view class="observationRecord-item-header">
            <image
              class="item-header-left"
              :src="createdIcon(itm)"
              alt=""
            />
            <view class="item-header-right">
              <view class="item-header-right-name">{{
                itm.children.map((v) => v.childName).join("，")
              }}</view>
              <view class="item-header-right-time">{{
                createdAtS(itm.observationTime)
              }}</view>
            </view>
            <!-- <view
              class="item-header-center"
              @click.capture.stop="onOpenPop(itm)"
            >
              <up-icon
                class="card-icon"
                size="42rpx"
                name="more-dot-fill"
              />
            </view> -->
          </view>
          <view class="observationRecord-item-content">
            <view
              class="content-item"
              v-for="column in columns"
              :key="column.prop"
            >
              <view class="item-label">{{ column.label }}:</view>
              <view class="item-value">{{
                column.formatter
                  ? column.formatter(itm, optionsList)
                  : itm[column.prop]
              }}</view>
            </view>
          </view>
        </view>
      </up-list-item>
    </up-list>
  </view>
  <view
    v-else
    class="empty-tip"
    >暂无数据</view
  >
    <!-- <Popup
        :show="isPop"
        @close="isPop = false"
    >
        <view class="iconAction">
        <view @click="editItem">
            <image src="@/static/common/editor.png"></image>
            <view>编辑</view>
            </view>
        <view @click="onDelete">
            <image src="@/static/common/delete.png"></image>
            <view>删除</view>
        </view>
        </view>
    </Popup> -->
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { onShareAppMessage, onShow } from "@dcloudio/uni-app";
import { sharePageObj } from "@/utils";
import { getclassList } from "@/api";
import { getDICT } from "@/utils";
import dayjs from "dayjs";
import {
  getObservationList,
} from "./api.js";
import { removeDuplicates } from "./data";
let isPop = ref(false); // 弹框控制
let activeItem = ref(null); // 当前点击的item
const props = defineProps({
  userInfo: {
    type: Object,
    default: () => {},
  },
});
const optionsList = reactive({
  schoolClassId: [],
  observationRecordScene: [],
});

const createdAtS = (v) => dayjs(v).format("YYYY-MM-DD HH:mm:ss");
const columns = [
  {
    label: "活动名称",
    prop: "activityName",
  },
  {
    label: "观察地点",
    prop: "observationLocation",
  },
  {
    label: "观察老师",
    prop: "createdByName",
  },
  {
    label: "观察班级",
    prop: "schoolClassId",
    formatter(row, optionsList) {
      // 查找交集
      const o = removeDuplicates(row.children, [], "classId");
      let arr = [];
      o.forEach((v) => {
        arr.push(
          optionsList.schoolClassId.find((f) => f.id === v.classId)?.title
        );
      });
      return arr.join("，") || "-";
    },
  },
];
const observationList = ref([]);

const onMask = () => {
  uni.$u.toast("观察正在⽣成中!");
};

function initObservationList() {
  observationList.value = [];
  getObservationList({
    pageSize: 999,
    currentPage: 1,
    pageModel: {
      classId: props.userInfo.classId,
      childId: props.userInfo.id,
    },
    // createdBy: userInfo.value.id,
  }).then((r) => {
    observationList.value = groupDataByDay(r.data);
    //
    observationList.value.sort((a, b) => b.key - a.key);
    console.log(observationList.value, "observationList.value");
  });
}

// getUserInfo().then((r) => {
//   userInfo.value = r.data;

// });
getDICT("all").then((dics) => {
  const ObservationRecordSceneEnumDesc = dics["ObservationRecordSceneEnumDesc"];

  if (ObservationRecordSceneEnumDesc) {
    optionsList.observationRecordScene = Object.keys(
      dics["ObservationRecordSceneEnumDesc"]
    ).map((key) => {
      return {
        label: ObservationRecordSceneEnumDesc[key],
        id: key,
      };
    });
  }
});

// 删除一项
// function onDelete() {
//   console.log("删除");
//   console.log(activeItem.value);
//   deleteObservation(activeItem.value.id).then((r) => {
//     if (r.status === 0) {
//       uni.showToast({
//         title: "删除成功",
//         icon: "none",
//       });
//       isPop.value = false;
//       initObservationList();
//     }
//   });
// }

// 按天进行分组
function groupDataByDay(data) {
  if (JSON.stringify(data) == "[]") {
    return [];
  }
  const groupedData = {};
  const dayOfWeekMap = {
    0: "周日",
    1: "周一",
    2: "周二",
    3: "周三",
    4: "周四",
    5: "周五",
    6: "周六",
  };
  data.forEach((item) => {
    const date = dayjs(item.observationTime);
    const dayKey = date.format("YYYY-MM-DD");
    const year = date.year();
    const dayOfWeek = date.day();
    const month = date.month() + 1; // 月份从0开始，所以需要+1
    const day = date.date();

    if (!groupedData[dayKey]) {
      groupedData[dayKey] = {
        year,
        key: dayjs().year(year).month(month).date(day).valueOf(),
        date: `${month}月${day}日`,
        dayOfWeek: dayOfWeekMap[dayOfWeek],
        items: [],
      };
    }

    groupedData[dayKey].items.push(item);
  });

  return Object.values(groupedData);
}
// 详情
function onViewDetail(item) {
  uni.navigateTo({
    url: `/observation/updateObservation?id=${item.id}&observationRecordScene=${item.observationType}&isChild=${true}`,
  });
}

// function onOpenPop(item) {
//   isPop.value = true;
//   activeItem.value = "";
//   activeItem.value = item;
// }

onShow(() => {
  console.log(props.userInfo, "props.userInfo");
});
onMounted(() => {
  initObservationList();
});

onShareAppMessage(() => sharePageObj());

const defaultUrl =
  "https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_boy.png";
const createdIcon = (item) => {
  if (item.createdByUser) {
    return item.createdByUser.url || defaultUrl;
  }
  return defaultUrl;
};

getclassList()
  .then((r) => {
    optionsList.schoolClassId = r.data.map((d) => ({
      ...d,
      label: d.title,
    }));
  })
  .catch((err) => {});
</script>
<style lang="scss" scoped>
.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
  color: #999;
  font-size: 28rpx;
}

.iconAction {
  & > view {
    height: 88rpx;
    display: flex;
    align-items: center;
    font-size: 30rpx;
    font-weight: 400;
    letter-spacing: 0rpx;
    line-height: 48rpx;
    color: rgba(51, 51, 51, 1);
    text-align: left;
    vertical-align: middle;

    image {
      width: 40rpx;
      height: 40rpx;
      margin-right: 28rpx;
    }
  }
}

.observationRecord {
  padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
  &-title {
    height: 42rpx;
    line-height: 42rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 28rpx;
    font-weight: 400;
    margin-bottom: 22rpx;

    &-left {
      color: #333333;
    }

    &-right {
      color: #808080;
    }
  }

  .observationRecord-item {
    background: #fff;
    margin-bottom: 20rpx;
    border-radius: 12rpx;
    box-shadow: 4rpx 8rpx 16rpx #eee;
    padding: 32rpx;
    border-radius: 28rpx;
    position: relative;

    .Mask {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.5);
      z-index: 20;
    }

    .content-item {
      display: flex;
      // align-items: center;
      min-height: 48rpx;
      font-size: 28rpx;
      font-weight: 400;

      .item-label {
        width: 140rpx;
        color: #808080;
      }

      .item-value {
        color: #333333;
        flex: 1;
      }
    }
  }

  .observationRecord-item-header {
    border-bottom: 1px solid #eeeeee;
    padding-bottom: 24rpx;
    margin-bottom: 24rpx;
    display: flex;
    color: #333333;

    .item-header-center {
      // margin-left: auto;
      position: relative;
      z-index: 2;
    }

    .item-header-left {
      width: 72rpx;
      height: 72rpx;
      border-radius: 50%;
      overflow: hidden;
      margin-right: 20rpx;
    }
    .item-header-right {
      flex: 1;
    }

    .item-header-right-name {
      font-size: 30rpx;
      font-weight: 600;
      padding-bottom: 8rpx;
    }

    .item-header-right-time {
      font-size: 24rpx;
      font-weight: 400;
      color: #808080;
    }
  }
}
</style>
