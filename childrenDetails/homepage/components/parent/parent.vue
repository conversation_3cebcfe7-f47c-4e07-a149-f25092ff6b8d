<!-- 家长 -->
<template>
	<view class="parent-layout">
		<view class="col flex" style="align-items: flex-start" v-for="(item, index) in parentInfo" :key="index">
			<view>
				<image class="img" :src="isSex(item.childRelationshipText) == 1 ? imgList[0] : imgList[1]" />
			</view>
			<view class="userInfo">
				<view class="top">
					<text class="name">{{ item.name || '-' }}</text>
					<text>{{ item.childRelationshipText }}</text>
				</view>
				<view class="conter" v-if="item.educationText || item.phone">
					<text>{{ item.educationText }}</text>
					<view v-if="item.educationText && item.phone" class="conter-line" />
					<text>{{ item.phone }}</text>
				</view>
				<view class="botton" v-if="item.occupation || item.company">
					<text>{{ item.occupation }}</text>
					<view v-if="item.occupation && item.company" class="conter-line" />
					<text>{{ item.company }}</text>
				</view>
			</view>
			<view>
				<up-icon class="card-icon" size="36rpx" name="more-dot-fill" @click="actionfn(item, index)" />
			</view>
		</view>
		<view v-if="parentInfo.length == 0" class="noData">暂无家长信息</view>
		<view class="action-btn" :style="{ height: heightBottom + 52 + 'px' }">
			<up-button type="primary" text="添加家长" color="#367CFF" round shape="circle" @click="send"></up-button>
		</view>
		<updateParentForm ref="addFormRef" @confirm="onConfirmUpdateParent" :title="'家长详情'" :parentItemData="parentItemData" :disabled="false" />
		<Popup :show="isShow" @close="isShow = false">
			<view class="iconAction">
				<view class="editorIcon" @click="onEditItem">
					<image src="@/static/common/editor.png" />
					<view class="">编辑</view>
				</view>
				<view class="deleteIcon" :asyncClose="true" @click="onDeleteItem">
					<image src="@/static/common/delete.png" />
					<view>删除</view>
				</view>
			</view>
		</Popup>
		<up-modal width="622rpx" showCancelButton @cancel="isModal = false" :show="isModal" @confirm="modalConfirm" :content="modalContent" contentTextAlign="center" />
	</view>
</template>

<script setup>
import { ref, defineProps, watch } from 'vue';
import updateParentForm from './components/updateParentForm.vue';
import { getParentDetail, deleteParentDetail } from '../../../api/index';
let heightBottom = ref(0);
const { safeAreaInsets } = uni.getSystemInfoSync();
heightBottom.value = safeAreaInsets.bottom;
const addFormRef = ref(null);
let isShow = ref(false);
let isModal = ref(false);
let modalContent = ref('');
// TODO  家长数据
const parentItemData = ref({});

const props = defineProps({
	childInfo: Object
});

const imgList = ['https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_boy.png', 'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_girl.png'];

// 当前选中的一项数据
let fromItem = ref({});

// 父母信息
let parentInfo = ref([]);
// 判断男女
const isSex = (text) => {
	let name = text.slice(-2);
	let sex;
	const malePattern = /(父|公|叔|舅|爷)/;
	const femalePattern = /(母|婆|婶|姑|妈|奶)/;
	if (malePattern.test(name)) {
		sex = 1;
		if (name == '舅妈') sex = 2;
	} else if (femalePattern.test(name)) {
		sex = 2;
	}
	return sex;
};

const onEditItem = () => {
	isShow.value = false;
	parentItemData.value = fromItem.value;
	if (addFormRef.value) {
		addFormRef.value.onUpdataView(parentItemData.value.id, props.childInfo.id);
	}
};
const onDeleteItem = () => {
	isModal.value = true;
	isShow.value = false;
	modalContent.value = `确定删除家长${fromItem.value.name}吗？`;
};
const modalConfirm = async () => {
	const res = await deleteParentDetail(fromItem.value.id);
	if (res.status == 0) {
		uni.showToast({
			title: '删除成功',
			icon: 'none',
			duration: 2000
		});
		isModal.value = false;
		parentInfo.value.splice(fromItem.value.bindex, 1);
	}
};
const actionfn = (item, i) => {
	isShow.value = true;
	fromItem.value = null;
	fromItem.value = item;
	fromItem.value.bindex = i;
};

// TODO  新增/编辑成功回调
const onConfirmUpdateParent = () => {
	ParentDetailList();
};
// TODO 添加家长处理
const send = () => {
	parentItemData.value = {};
	if (addFormRef.value) {
		addFormRef.value.onStartView({ childId: props.childInfo.id });
	}
};

const ParentDetailList = async () => {
	const res = await getParentDetail(props.childInfo.id);
	if (res.status == 0) {
		parentInfo.value = res.data;
	}
};
ParentDetailList();
</script>

<style lang="scss" scoped>
@import '@/common/css/index.scss';

.parent-layout {
	padding-bottom: 80rpx;
	box-sizing: border-box;

	.noData {
		text-align: center;
		color: #808080;
		font-size: 26rpx;
		margin-top: 100rpx;
		font-weight: 400;
	}
}

.iconAction {
	& > view {
		height: 88rpx;
		display: flex;
		align-items: center;
		font-size: 30rpx;
		font-weight: 400;
		letter-spacing: 0rpx;
		line-height: 48rpx;
		color: rgba(51, 51, 51, 1);
		text-align: left;
		vertical-align: middle;

		image {
			width: 40rpx;
			height: 40rpx;
			margin-right: 28rpx;
		}
	}
}

.action-btn {
	position: fixed;
	bottom: 0;
	left: 0;
	z-index: 50;
	width: 100%;
	box-sizing: border-box;
	padding: 16rpx 32rpx;
	background-color: #fff;
}

.userInfo {
	flex: 1;
	color: #333333;

	.top {
		font-size: 22rpx;
		font-weight: 500;
		margin-bottom: 14rpx;

		.name {
			margin-right: 16rpx;
			font-size: 30rpx;
			font-weight: 600;
		}
	}

	.conter,
	.botton {
		display: flex;
		align-items: center;
		font-size: 24rpx;
		font-weight: 400;
		color: #808080;
	}

	.conter {
		margin-bottom: 14rpx;

		&-line {
			display: inline-block;
			width: 1px;
			height: 24rpx;
			background: #808080;
			margin: 0 8rpx;
		}
	}
}

.img {
	width: 88rpx;
	height: 88rpx;
	border-radius: 50%;
	margin-right: 24rpx;
}

.col {
	border-radius: 28rpx;
	background: rgba(255, 255, 255, 1);
	box-shadow: 4rpx 8rpx 16rpx rgba(0, 0, 0, 0.02);
	padding: 28rpx;
	box-sizing: border-box;
	margin-bottom: 24rpx;
}
</style>
