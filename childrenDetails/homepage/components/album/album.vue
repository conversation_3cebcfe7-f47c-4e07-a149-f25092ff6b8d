<template>
  <view class="album">
    <usePublicLayout
      :loading="status.loading"
      :isNoMore="status.isNoMore"
      @scrolltolower="scrolltolower"
    >
      <view
        class="baby-talk"
        v-for="(item, index) in albumList"
        :key="index"
      >
        <view class="talk-item">
          <view class="talk-header">
            <text>活动：{{ item.name }}</text>
          </view>
          <view class="talk-header">
            <text class="talk-date">课程：{{ item?.subject?.title }}</text>
          </view>
          <view class="baby-talk-content">
            <up-album
              v-if="item?.imgResources"
              :urls="item.imgResources"
              keyName="uri"
            />
          </view>
        </view>
        <text class="baby-talk-time">{{
          formatDate(item.implementedAt, "YYYY-MM-DD")
        }}</text>
      </view>
      <view
        v-if="albumList.length === 0 && !status.loading"
        class="empty-tip"
      >
        暂无数据
      </view>
    </usePublicLayout>
  </view>
</template>

<script setup>
import { reactive, onMounted, ref } from "vue";
import { getAlbumList } from "@/childrenDetails/api";
import usePublicLayout from "@/childrenDetails/components/usePublicLayout/usePublicLayout.vue";
import { formatDate } from "@/utils";
const albumList = ref([]);

const initParams = reactive({
  current: 1, // 第几页 number
  pageSize: 10, // 每页多少条 number
  childId: -1, // 孩子id number
});
const status = reactive({
  loading: true,
  isNoMore: false,
});

const props = defineProps({
  userInfo: {
    type: Object,
    default: () => {},
  },
});

// 滚动到底
const scrolltolower = (e) => {
  initParams.current++;
  if ((status.isNoMore = true)) {
    return;
  }
  getWorkList();
};

const queryGetAlbumList = async () => {
  const res = await getAlbumList(initParams);
  if (res.status == 0) {
    const data = res.data;
    data.forEach((item) => {
      item.imgResources = item.resources.filter((item) => item.category == 1);
    });
    console.log(data);

    albumList.value = albumList.value.concat(res.data);
  }
  if (
    initParams.current >= res.metadata.totalPages &&
    albumList.value.length > 0
  ) {
    status.isNoMore = true;
  }

  status.loading = false;
  console.log(res);
};

onMounted(() => {
  if (props.userInfo.id) initParams.childId = Number(props.userInfo.id);
  queryGetAlbumList();
});
</script>

<style lang="scss" scoped>
.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
  color: #999;
  font-size: 28rpx;
}
.baby-talk {
  padding: 20rpx;
  border-radius: 28rpx;
  background: white;
  margin-bottom: 28rpx;
  .talk-item {
    margin-bottom: 20rpx;
    padding: 20rpx;
    border-radius: 10rpx;
    font-size: 30rpx;
    font-weight: 600;
    color: #000;
    .talk-date {
      font-size: 26rpx;
      font-weight: 400;
      color: #999;
      text-align: right;
    }
  }
  &-time {
    display: block;
    text-align: right;
    font-size: 24rpx;
    font-weight: 400;
    color: #999999;
  }
  &-content {
    font-size: 26rpx;
    font-weight: 400;
    white-space: pre-wrap;
    margin: 10rpx 0;
    &-active {
      color: #367cff;
    }
  }
}
</style>
