<!-- 档案 -->
<template>
  <view class="profile-layout">
    <!-- 基本信息部分 -->
    <view class="col">
      <view class="flex-jsb-ac top">
        <text class="title">基本信息</text>
        <image src="@/static/common/editor.png" class="edit-icon" @click="showChildEditPopup" />
      </view>
      <view class="item" v-for="(item, index) in itemList" :key="index">
        <text class="name">{{ item.label }}</text>
        <text class="content">{{ item.value || '-' }}</text>
      </view>
      <!-- 修改儿童人脸 -->
      <view class="UpdataItem" style="justify-content: space-between" @tap="changeAvatar('avatar')">
        <text class="name">上传头像</text>
        <up-icon name="arrow-right" size="28rpx" />
      </view>
      <view class="UpdataItem" style="justify-content: space-between" @tap="changeAvatar('face')">
        <text class="name">上传人脸（用于人脸识别）</text>
        <up-icon name="arrow-right" size="28rpx" />
      </view>
      <!-- 儿童过敏源 -->
      <view class="item">
        <text class="name">儿童过敏源</text>
        <view class="allergen-content">
          <view v-if="allergenList.length === 0" class="content">-</view>
          <view v-else class="allergen-list">
            <view v-for="(allergen, index) in allergenList" :key="allergen.allergenItemCode" class="allergen-item">
              <text class="allergen-name">{{ allergen.allergenItemName }}</text>
              <text v-if="allergen.desc" class="allergen-desc">（{{ allergen.desc }}）</text>
              <text v-if="index < allergenList.length - 1" class="separator">、</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 家长信息部分 -->
    <view class="parent-section">
      <view class="section-header">
        <view class="section-title">家长信息</view>
        <view class="add-parent-btn" @click="send">+ 添加家长</view>
      </view>
      <view class="col flex" style="align-items: flex-start" v-for="(item, index) in parentInfo" :key="index">
        <view>
          <image class="img" :src="isSex(item.childRelationshipText) == 1 ? imgList[0] : imgList[1]" />
        </view>
        <view class="userInfo">
          <view class="top">
            <text class="name">{{ item.name || '-' }}</text>
            <text>{{ item.childRelationshipText }}</text>
          </view>
          <view class="conter" v-if="item.educationText || item.phone">
            <text>{{ item.educationText }}</text>
            <view v-if="item.educationText && item.phone" class="conter-line" />
            <text>{{ item.phone }}</text>
          </view>
          <view class="botton" v-if="item.occupation || item.company">
            <text>{{ item.occupation }}</text>
            <view v-if="item.occupation && item.company" class="conter-line" />
            <text>{{ item.company }}</text>
          </view>
        </view>
        <view>
          <up-icon class="card-icon" size="36rpx" name="more-dot-fill" @click="actionfn(item, index)" />
        </view>
      </view>
      <view v-if="parentInfo.length == 0" class="noData">暂无家长信息</view>
    </view>

    <updateParentForm ref="addFormRef" @confirm="onConfirmUpdateParent" :title="'家长详情'" :parentItemData="parentItemData" :disabled="false" />
    <Popup :show="isShow" @close="isShow = false">
      <view class="iconAction">
        <view class="editorIcon" @click="onEditItem">
          <image src="@/static/common/editor.png" />
          <view class="">编辑</view>
        </view>
        <view class="deleteIcon" :asyncClose="true" @click="onDeleteItem">
          <image src="@/static/common/delete.png" />
          <view>删除</view>
        </view>
      </view>
    </Popup>
    <up-modal width="622rpx" showCancelButton @cancel="isModal = false" :show="isModal" @confirm="modalConfirm" :content="modalContent" contentTextAlign="center" />

    <!-- 儿童编辑弹窗 -->
    <Popup :show="showChildEdit" @close="showChildEdit = false">
      <view class="iconAction" v-if="isChildAction">
        <view v-if="userInfo.state == 0" @click="selectChildFn('返园')">
          <image src="@/static/common/leaving.png" />
          <view>返园</view>
        </view>
        <view @click="editChildInfo">
          <image src="@/static/common/editor.png" />
          <view>编辑</view>
        </view>
        <view @click="selectChildFn('删除')">
          <image src="@/static/common/delete.png" />
          <view>删除</view>
        </view>
        <view v-if="userInfo.state == 1" @click="selectChildFn('离园')">
          <image src="@/static/common/leaving.png" />
          <view>离园</view>
        </view>
      </view>

      <ChildrenAddStudent
        v-else
        @successSend="successChildSend"
        :editData="editChildData"
      />
    </Popup>

    <!-- 儿童操作确认弹窗 -->
    <up-modal
      :show="isChildModal"
      contentTextAlign="center"
      @cancel="cancelChild"
      showCancelButton
      @confirm="selectChildModalFn"
      :asyncClose="true"
    >
      <view style="font-size: 28rpx">
        {{ isActionDelete ? "删除" : "请您确认是否将" }}
        <text class="delstyle">{{ userInfo.title }}</text>
        {{
          isActionDelete
            ? "时，所有相关资料（包括照片、视频、语言等）将被一并删除，请确认无误后再进行！"
            : isActionLeave
            ? "离园？"
            : isActionReturn
            ? "返园？"
            : ""
        }}
      </view>
    </up-modal>
  </view>
</template>

<script setup>
import { watch, ref } from 'vue'
import { timeFormat } from '@/uni_modules/uview-plus'
import utils from '../baseInfo/utils'
import { getChildAllergen, getParentDetail, deleteParentDetail } from '@/childrenDetails/api/index.js'
import { deleteChildrenItem, putChildrenItem } from '@/api/children.js'
import updateParentForm from '../parent/components/updateParentForm.vue'
import ChildrenAddStudent from '../../childrenAddStudent.vue'
import Popup from '@/components/Popup/Popup.vue'

// 过敏源数据
const allergenList = ref([])

// 家长相关数据
const addFormRef = ref(null)
let isShow = ref(false)
let isModal = ref(false)
let modalContent = ref('')
const parentItemData = ref({})
let fromItem = ref({})
let parentInfo = ref([])

// 儿童编辑相关
let showChildEdit = ref(false)
let isChildAction = ref(true) // 是否显示操作
let editChildData = ref({})
let isChildModal = ref(false)
let isActionDelete = ref(false)
let isActionLeave = ref(false)
let isActionReturn = ref(false)

const imgList = ['https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_boy.png', 'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_girl.png']

let props = defineProps({
  userInfo: {
    type: Object,
    default: () => {}
  }
})

const { itemList } = utils()

// 加载过敏源信息的函数
const loadAllergenInfo = async (childId) => {
  const res = await getChildAllergen({
    childId: childId
  })
  if (res && res.data) {
    allergenList.value = res.data
  }
}

// 加载家长信息的函数
const ParentDetailList = async () => {
  const res = await getParentDetail(props.userInfo.id)
  if (res.status == 0) {
    parentInfo.value = res.data
  }
}

watch(
  () => props.userInfo,
  (newVal) => {
    if (newVal && newVal.id) {
      itemList.value = itemList.value.map((item) => {
        let value = newVal[item.key]
        if (item.key === 'sex') {
          value = value === 1 ? '男' : '女'
        } else if (item.key === 'isSingle') {
          value = value === 1 ? '是' : '否'
        } else if (item.key === 'symptoms') {
          value = value === 1 ? '正常儿童' : value === 2 ? '疑似特殊' : '诊断特殊'
        } else if (item.key === 'entryTime' || item.key === 'birthday') {
          value = timeFormat(new Date(value), 'yyyy-mm-dd')
        }
        return {
          ...item,
          value
        }
      })

      // 加载家长信息和过敏源信息
      ParentDetailList()
      loadAllergenInfo(newVal.id)
    }
  },
  { deep: true, immediate: true }
)

const changeAvatar = (type) => {
  if (props.userInfo.id) {
    const url =
      type == 'face'
        ? '/childrenDetails/updateAvatar/updateFace'
        : '/childrenDetails/updateAvatar/updateAvatar'
    uni.navigateTo({
      url: url + `?cId=${props.userInfo.id}&classId=${props.userInfo.classId}`
    })
  }
}

// 判断男女
const isSex = (text) => {
  let name = text.slice(-2)
  let sex
  const malePattern = /(父|公|叔|舅|爷)/
  const femalePattern = /(母|婆|婶|姑|妈|奶)/
  if (malePattern.test(name)) {
    sex = 1
    if (name == '舅妈') sex = 2
  } else if (femalePattern.test(name)) {
    sex = 2
  }
  return sex
}

const onEditItem = () => {
  isShow.value = false
  parentItemData.value = fromItem.value
  if (addFormRef.value) {
    addFormRef.value.onUpdataView(parentItemData.value.id, props.userInfo.id)
  }
}

const onDeleteItem = () => {
  isModal.value = true
  isShow.value = false
  modalContent.value = `确定删除家长${fromItem.value.name}吗？`
}

const modalConfirm = async () => {
  const res = await deleteParentDetail(fromItem.value.id)
  if (res.status == 0) {
    uni.showToast({
      title: '删除成功',
      icon: 'none',
      duration: 2000
    })
    isModal.value = false
    parentInfo.value.splice(fromItem.value.bindex, 1)
  }
}

const actionfn = (item, i) => {
  isShow.value = true
  fromItem.value = null
  fromItem.value = item
  fromItem.value.bindex = i
}

const onConfirmUpdateParent = () => {
  ParentDetailList()
}

const send = () => {
  parentItemData.value = {}
  if (addFormRef.value) {
    addFormRef.value.onStartView({ childId: props.userInfo.id })
  }
}

// 显示儿童编辑弹窗
const showChildEditPopup = () => {
  console.log('显示儿童编辑弹窗')
  showChildEdit.value = true
  isChildAction.value = true
  console.log('showChildEdit:', showChildEdit.value, 'isChildAction:', isChildAction.value)
}

// 编辑儿童信息
const editChildInfo = () => {
  showChildEdit.value = false
  editChildData.value = props.userInfo
  setTimeout(() => {
    showChildEdit.value = true
    isChildAction.value = false
  }, 310)
}

// 儿童操作选择
const selectChildFn = (type) => {
  isChildModal.value = true
  if (type == "删除") isActionDelete.value = true
  if (type == "离园") isActionLeave.value = true
  if (type == "返园") isActionReturn.value = true
}

// 儿童操作确认
const selectChildModalFn = () => {
  if (isActionDelete.value) delChildItem()
  if (isActionLeave.value) leaveChildItem()
  if (isActionReturn.value) leaveChildItem(1)
}

// 取消儿童操作
const cancelChild = () => {
  isChildModal.value = false
  setTimeout(() => {
    isActionDelete.value = false
    isActionLeave.value = false
    isActionReturn.value = false
  }, 400)
}

// 删除儿童
const delChildItem = async () => {
  const res = await deleteChildrenItem(props.userInfo.id)
  if (res.status == 0) {
    uni.showToast({
      title: '删除成功',
      icon: 'none',
      duration: 2000
    })
    showChildEdit.value = false
    isChildModal.value = false
    isActionDelete.value = false
    // 返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 1000)
  }
}

// 离园/返园操作
const leaveChildItem = async (state = 0) => {
  const res = await putChildrenItem({
    id: props.userInfo.id,
    state
  })
  if (res.status == 0) {
    const message = state == 1 ? "返园成功" : "离园成功"
    uni.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    })
    showChildEdit.value = false
    isChildModal.value = false
    isActionLeave.value = false
    isActionReturn.value = false
    // 返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 1000)
  }
}

// 儿童编辑成功回调
const successChildSend = () => {
  showChildEdit.value = false
  // 重新获取儿童信息
  uni.showToast({
    title: '编辑成功',
    icon: 'none',
    duration: 2000
  })

  // 触发父组件重新加载数据
  setTimeout(() => {
    // 通过页面事件通知父组件刷新数据
    uni.$emit('refreshChildInfo')
  }, 500)
}
</script>

<style lang="scss" scoped>
@import '@/common/css/index.scss';

.profile-layout {
  padding-bottom: 120rpx;
  box-sizing: border-box;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 40rpx 0 20rpx 0;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  line-height: 36rpx;
  color: #333333;
}

.add-parent-btn {
  font-size: 28rpx;
  color: #367CFF;
  font-weight: 400;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;

  &:active {
    background-color: rgba(54, 124, 255, 0.1);
  }
}

.parent-section {
  margin-top: 40rpx;
}

.top {
  height: 92rpx;
  border-bottom: 1rpx solid #eeeeee;
}

.item,
.UpdataItem {
  display: flex;
  align-items: stretch;
  border-bottom: 1rpx solid #eeeeee;
  padding: 32rpx 0;

  &:last-child {
    border-bottom: none;
  }

  .name {
    width: fit-content;
    font-size: 28rpx;
    font-weight: 600;
    color: rgba(51, 51, 51, 1);
    text-align: left;
    margin-right: 32rpx;
    display: inline-block;
  }

  .content {
    font-size: 28rpx;
    font-weight: 400;
    color: #333333;
    flex: 1;
  }
}

.UpdataItem:active {
  background: #f5f5f5;
}

.allergen-content {
  flex: 1;

  .allergen-list {
    display: flex;
    flex-wrap: wrap;
    align-items: center;

    .allergen-item {
      display: flex;
      align-items: center;

      .allergen-name {
        font-size: 28rpx;
        font-weight: 400;
        color: #333333;
      }

      .allergen-desc {
        font-size: 24rpx;
        color: #999999;
        margin-left: 4rpx;
      }

      .separator {
        font-size: 28rpx;
        color: #333333;
        margin: 0 8rpx;
      }
    }
  }
}

.title {
  font-size: 30rpx;
  font-weight: 600;
  line-height: 36rpx;
  color: #333333;
}

.edit-icon {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.6;

  &:active {
    opacity: 1;
  }
}

.col {
  border-radius: 28rpx;
  background: rgba(255, 255, 255, 1);
  box-shadow: 4rpx 8rpx 16rpx rgba(0, 0, 0, 0.02);
  padding: 32rpx;
  box-sizing: border-box;
  margin-bottom: 24rpx;
}

.noData {
  text-align: center;
  color: #808080;
  font-size: 26rpx;
  margin-top: 100rpx;
  font-weight: 400;
}

.iconAction {
  & > view {
    height: 88rpx;
    display: flex;
    align-items: center;
    font-size: 30rpx;
    font-weight: 400;
    letter-spacing: 0rpx;
    line-height: 48rpx;
    color: rgba(51, 51, 51, 1);
    text-align: left;
    vertical-align: middle;

    image {
      width: 40rpx;
      height: 40rpx;
      margin-right: 28rpx;
    }
  }
}



.userInfo {
  flex: 1;
  color: #333333;

  .top {
    font-size: 22rpx;
    font-weight: 500;
    margin-bottom: 14rpx;

    .name {
      margin-right: 16rpx;
      font-size: 30rpx;
      font-weight: 600;
    }
  }

  .conter,
  .botton {
    display: flex;
    align-items: center;
    font-size: 24rpx;
    font-weight: 400;
    color: #808080;
  }

  .conter {
    margin-bottom: 14rpx;

    &-line {
      display: inline-block;
      width: 1px;
      height: 24rpx;
      background: #808080;
      margin: 0 8rpx;
    }
  }
}

.img {
  width: 88rpx;
  height: 88rpx;
  border-radius: 50%;
  margin-right: 24rpx;
}

.delstyle {
  color: #ff6b6b;
  font-weight: 600;
}
</style>
