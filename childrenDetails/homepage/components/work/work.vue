<!-- 家长 -->
<template>
  <view class="work-layout">
    <!-- <up-loading-icon
      size="20"
      mode="semicircle"
      style="margin-top: 100rpx"
      :show="loading"
    />
    <view
      v-if="workList.length === 0 && !loading"
      class="empty-tip"
    >
      <text>暂无数据</text>
    </view> -->
    <usePublicLayout
      :loading="status.loading"
      :isNoMore="status.isNoMore"
      @scrolltolower="scrolltolower"
    >
      <view
        class="baby-talk"
        v-for="(item, index) in workList"
        :key="index"
      >
        <view class="talk-item">
          <view class="talk-header">
            <text>活动：{{ item.name }}</text>
          </view>
          <view class="talk-header">
            <text class="talk-date">课程：{{ item?.subject?.title }}</text>
          </view>
          <view class="baby-talk-content">
            <up-album
              :urls="item.childArtworkResources"
              keyName="uri"
            />
          </view>
        </view>
        <text class="baby-talk-time">{{
          formatDate(item.implementedAt, "YYYY-MM-DD")
        }}</text>
      </view>
      <view v-if ="workList.length === 0 && !status.loading" class="empty-tip">
       暂无数据 
      </view>
    </usePublicLayout>
  </view>
</template>

<script setup>
import { ref, onMounted, reactive } from "vue";
import {
  updateChildrenWorks,
  getChildrenWorksList,
} from "@/childrenDetails/api";
import usePublicLayout from "@/childrenDetails/components/usePublicLayout/usePublicLayout.vue";
import { formatDate } from "@/utils";
const props = defineProps({
  userInfo: {
    type: Object,
    default: () => {},
  },
});
const initParams = reactive({
  current: 1,
  pageSize: 10,
});
const status = reactive({
  loading: true,
  isNoMore: false,
});
const workList = ref([]); // 作品列表
const getWorkList = async () => {
  status.loading = true;
  const parms = {
    ...initParams,
    childId: props.userInfo.id,
  };
  const res = await getChildrenWorksList(parms);
  if (res.status == 0) {
    // 合并两个数组
    workList.value = workList.value.concat(res.data);
  }
  if (initParams.current >= res.metadata.totalPages && workList.value.length > 0) {
    status.isNoMore = true;
  }

  status.loading = false;
};
// 滚动到底
const scrolltolower = (e) => {
  initParams.current++;
  if ((status.isNoMore = true)) {
    return;
  }
  getWorkList();
};

onMounted(() => {
  getWorkList();
});
</script>

<style lang="scss" scoped>
.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
  color: #999;
  font-size: 28rpx;
}
.baby-talk {
  padding: 20rpx;
  border-radius: 28rpx;
  background: white;
  margin-bottom: 28rpx;
  .talk-item {
    margin-bottom: 20rpx;
    padding: 20rpx;
    border-radius: 10rpx;
    font-size: 30rpx;
    font-weight: 600;
    color: #000;
    .talk-date {
      font-size: 26rpx;
      font-weight: 400;
      color: #999;
      text-align: right;
    }
  }
  &-time {
    display: block;
    text-align: right;
    font-size: 24rpx;
    font-weight: 400;
    color: #999999;
  }
  &-content {
    font-size: 26rpx;
    font-weight: 400;
    white-space: pre-wrap;
    margin: 10rpx 0;
    &-active {
      color: #367cff;
    }
  }
}
</style>
