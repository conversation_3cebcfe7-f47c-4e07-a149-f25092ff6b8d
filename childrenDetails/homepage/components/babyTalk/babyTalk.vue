<template>
  <view>
    <!-- <up-loading-page :loading="loading" iconSize="38" /> -->
    <up-loading-icon
      size="20"
      mode="semicircle"
      style="margin-top: 100rpx"
      :show="loading"
    />
    <view v-if="talkList.length > 0">
      <scroll-view
        style="height: 76.5vh"
        scroll-y="true"
        @scrolltolower="scrolltolower"
      >
        <view
          class="baby-talk"
          v-for="(item, index) in talkList"
          :key="index"
        >
          <view class="talk-item">
            <view class="talk-header">
              <text>活动：{{ item.name }}</text>
            </view>
            <view class="talk-header">
              <text class="talk-date">课程：{{ item?.subject?.title }}</text>
            </view>
            <view
              class="baby-talk-content"
              v-for="(subItem, subIndex) in item.childTeacherInteracList"
              :key="subIndex"
            >
              <text
                :class="{
                  'baby-talk-content-active': containsTarget(subItem.name),
                }"
                >{{ subItem.name }}</text
              >：
              <text>{{ subItem.content }}</text>
            </view>
          </view>
          <text class="baby-talk-time">{{
            formatDate(item.implementedAt)
          }}</text>
        </view>
        <view
          v-if="isNoMore"
          class="no-more-tip"
          >没有更多了</view
        >
      </scroll-view>
    </view>
    <view
      v-if="talkList.length === 0 && !loading"
      class="empty-tip"
    >
      <text>暂无数据</text>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { getBabyTalk } from "@/childrenDetails/api/babyTalk.js";
import dayjs from "dayjs";

const props = defineProps({
  userInfo: {
    type: Object,
    default: () => {},
  },
});
const loading = ref(true);
const talkList = ref([]);
const totalPages = ref(0); // 总页数
const isNoMore = ref(false); // 是否没有更多数据
// 初始化参数
const initParams = {
  current: 1, // 当前页码
  pageSize: 10, // 每页显示条数
  state: 1, // 状态 1: 已完成 0 离园
};

const scrolltolower = (e) => {
  initParams.current++;
  console.log(initParams.current, totalPages.value);

  console.log(initParams.current > totalPages.value);

  if (initParams.current > totalPages.value) {
    return;
  }
  getTalkList(props.userInfo.id);
};

// 格式化日期
const formatDate = (date) => (date ? dayjs(date).format("YYYY-MM-DD") : "");
// 是否包含姓名
const containsTarget = (text) => {
  const targetText = props.userInfo.title; // 替换为您要检测的目标文字
  return text.includes(targetText);
};

// 获取童言童语列表
const getTalkList = async (childId) => {
  loading.value = true;
  const params = { ...initParams, childId };
  const res = await getBabyTalk(params);
  if (res.status === 0) {
    totalPages.value = res.metadata.totalPages;
    res.data.forEach((item) => {
      talkList.value.push(item);
    });
    // 把talkList中的childTeacherInteraction按照\n分割成数组
    talkList.value.forEach((item) => {
      item.childTeacherInteracList = item.childTeacherInteraction.split("\n");
      item.childTeacherInteracList.forEach((subItem, subIndex) => {
        let subItemArr = subItem.split("：");
        item.childTeacherInteracList[subIndex] = {
          name: subItemArr[0],
          content: subItemArr[1],
        };
      });
    });
  }
  if (initParams.current >= res.metadata.totalPages) {
    isNoMore.value = true;
  }
  loading.value = false;
};

onMounted(() => {
  getTalkList(props.userInfo.id);
});
</script>

<style lang="scss" scoped>
.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
  color: #999;
  font-size: 28rpx;
}
.baby-talk {
  padding: 20rpx;
  border-radius: 28rpx;
  background: white;
  margin-bottom: 28rpx;
  .talk-item {
    margin-bottom: 20rpx;
    padding: 20rpx;
    border-radius: 10rpx;
    font-size: 30rpx;
    font-weight: 600;
    color: #000;
    .talk-date {
      font-size: 26rpx;
      font-weight: 400;
      color: #999;
      text-align: right;
    }
  }
  &-time {
    display: block;
    text-align: right;
    font-size: 24rpx;
    font-weight: 400;
    color: #999999;
  }
  &-content {
    font-size: 26rpx;
    font-weight: 400;
    white-space: pre-wrap;
    margin: 10rpx 0;
    &-active {
      color: #367cff;
    }
  }
}
.talk-header {
  margin-bottom: 10rpx;
}

.talker {
  font-size: 28rpx;
}
.talk-activity {
  color: #367cff;
}
.no-more-tip {
  text-align: center;
  padding: 20rpx;
  color: #999;
  font-size: 26rpx;
}
</style>
