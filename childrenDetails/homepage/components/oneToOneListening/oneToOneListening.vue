<!-- 表征/倾听 -->
<template>
  <view class="container">
    <view
      class="observationRecord"
      v-if="observationList.length != 0"
    >
      <view
        v-for="item in observationList"
        :key="item.id"
        class="date-group"
      >
        <view class="observationRecord-title">
          <text class="observationRecord-title-left"
            >{{ item.date }}&nbsp;{{ item.dayOfWeek }}</text
          >
          <text class="observationRecord-title-right">{{ item.year }}年</text>
        </view>
        <view
          @click="onViewDetail(itm)"
          class="observationRecord-item"
          v-for="(itm, index) in item.items"
          :key="itm.id + new Date().getTime()"
        >
          <view
            v-if="itm.state == 2"
            class="Mask"
            @tap.capture.stop="onMask"
          />
          <view class="observationRecord-item-header">
            <image
              class="item-header-left"
              :src="createdIcon(itm)"
              alt=""
            />
            <view class="item-header-right">
              <view class="item-header-right-name">{{
                itm.child ? itm.child.title : ""
              }}</view>
              <view class="item-header-right-time">{{
                createdAtS(itm.observationTime)
              }}</view>
            </view>
            <!-- <view
              class="item-header-center"
              @click.capture.stop="onOpenPop(itm)"
            >
              <up-icon
                class="card-icon"
                size="42rpx"
                name="more-dot-fill"
              />
            </view> -->
          </view>
          <view class="observationRecord-item-content">
            <view class="content-item">
              <view class="item-label">标题:</view>
              <view class="item-value">{{ itm.title }}</view>
            </view>
            <view class="content-item">
              <view class="item-label">倾听老师:</view>
              <view class="item-value">{{
                itm.teacher ? itm.teacher.name : ""
              }}</view>
            </view>
            <view class="content-item">
              <view class="item-label">倾听地点:</view>
              <view class="item-value">{{ itm.activityLocation }}</view>
            </view>
            <view class="content-item">
              <view class="item-label">所属班级:</view>
              <view class="item-value">{{
                itm.schoolClass
                  ? itm.schoolClass.nickname || itm.schoolClass.schoolTitle
                  : ""
              }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view
      v-else
      class="text-Empty"
      >暂无倾听记录</view
    >
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { onShow, onLoad } from "@dcloudio/uni-app";
import dayjs from "dayjs";
import { getUserInfo } from "@/api/login";
import { listOneToOneListeningRecord } from "@/api/game";
// props
const props = defineProps({ userInfo: Object });
// 当前点击的item
let activeItem = ref(null);
// 用户信息
const userInfo = ref({});
// 是否已初始化
const isInitialized = ref(false);

// 格式化时间
const createdAtS = (v) => dayjs(v).format("YYYY-MM-DD HH:mm");

// 列表数据
const observationList = ref([]);

// 分页相关
const pagination = reactive({
  currentPage: 1,
  pageSize: 999,
  hasMore: true,
  loading: false,
  totalCount: 0,
});

// 正在生成中提示
const onMask = () => {
  uni.$u.toast("记录正在⽣成中!");
};

// 初始化列表数据
function initObservationList() {
  console.log(props.userInfo);
  const childId = props.userInfo.id;
  // 防止重复加载
  if (!userInfo.value || !userInfo.value.currentClassId) {
    console.log("用户信息未加载，无法初始化列表");
    return;
  }

  // 重置分页状态
  pagination.currentPage = 1;
  pagination.hasMore = true;
  pagination.loading = false;
  observationList.value = [];

  uni.showLoading({
    title: "加载中...",
    mask: true,
  });

  listOneToOneListeningRecord({
    pageSize: pagination.pageSize,
    current: pagination.currentPage,
    schoolClassId: userInfo.value.currentClassId,
  })
    .then((r) => {
      if (r.status === 0 && r.data) {
        // 过滤 id为childId 的记录
        const filteredData = r.data.filter(
          (item) => item?.child?.id == childId
        );
        console.log("🚀 ~ .then ~ filteredData:", filteredData);

        observationList.value = groupDataByDay(filteredData);
        isInitialized.value = true;

        // 更新总数和分页状态
        if (r.metadata && r.metadata.count !== undefined) {
          pagination.totalCount = r.metadata.count;
          const totalPages = Math.ceil(
            pagination.totalCount / pagination.pageSize
          );
          pagination.hasMore = pagination.currentPage < totalPages;
        } else {
          // 如果没有metadata，根据返回数据量判断
          pagination.hasMore = r.data.length >= pagination.pageSize;
        }
      } else {
        uni.showToast({
          title: r.message || "获取数据失败",
          icon: "none",
        });
      }
    })
    .catch((err) => {
      console.error("获取倾听记录失败:", err);
      uni.showToast({
        title: "获取数据失败",
        icon: "none",
      });
    })
    .finally(() => {
      uni.hideLoading();
    });
}

// 页面加载时执行，只执行一次
// onLoad(() => {
//   console.log("页面加载(onLoad)，开始初始化");

//   // 获取用户信息并初始化列表
//   getUserInfo()
//     .then((r) => {
//       userInfo.value = r.data;
//       console.log("用户信息获取成功，准备初始化列表");
//       initObservationList();
//     })
//     .catch((err) => {
//       console.error("获取用户信息失败:", err);
//     });
// });

onMounted(() => {
  console.log("页面加载(onLoad)，开始初始化");

  // 获取用户信息并初始化列表
  getUserInfo()
    .then((r) => {
      userInfo.value = r.data;
      console.log("用户信息获取成功，准备初始化列表");
      initObservationList();
    })
    .catch((err) => {
      console.error("获取用户信息失败:", err);
    });
});

// 页面显示时检查是否需要刷新
onShow(() => {
  console.log("页面显示(onShow)，检查是否需要刷新列表");
  const isRef = uni.getStorageSync("isRef");
  console.log("isRef 标志:", isRef);

  // 如果有刷新标记，强制刷新列表
  if (isRef) {
    console.log("发现刷新标记，执行刷新操作");
    // 确保用户信息已加载
    if (userInfo.value && userInfo.value.currentClassId) {
      initObservationList();
    } else {
      // 如果用户信息未加载，先加载用户信息
      getUserInfo()
        .then((r) => {
          userInfo.value = r.data;
          initObservationList();
        })
        .catch((err) => {
          console.error("获取用户信息失败:", err);
        });
    }
    // 清除刷新标记
    uni.removeStorageSync("isRef");
  }
});

// 按天进行分组
function groupDataByDay(data) {
  if (!data || data.length === 0) {
    return [];
  }

  const groupedData = {};
  const dayOfWeekMap = {
    0: "周日",
    1: "周一",
    2: "周二",
    3: "周三",
    4: "周四",
    5: "周五",
    6: "周六",
  };

  data.forEach((item) => {
    const date = dayjs(item.observationTime);
    const dayKey = date.format("YYYY-MM-DD");
    const year = date.year();
    const dayOfWeek = date.day();
    const month = date.month() + 1; // 月份从0开始，所以需要+1
    const day = date.date();

    if (!groupedData[dayKey]) {
      groupedData[dayKey] = {
        year,
        date: `${month}月${day}日`,
        dayOfWeek: dayOfWeekMap[dayOfWeek],
        items: [],
      };
    }

    groupedData[dayKey].items.push(item);
  });

  return Object.values(groupedData);
}

// 查看详情
function onViewDetail(item) {
  uni.navigateTo({
    url: `/oneToOneListening/detail?id=${item.id}`,
  });
}

// 获取头像
const defaultUrl =
  "https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_boy.png";
const createdIcon = (item) => {
  if (item.child && item.child.headerUrl) {
    return item.child.headerUrl;
  } else if (item.teacher && item.teacher.headerUrl) {
    return item.teacher.headerUrl;
  }
  return defaultUrl;
};
</script>

<style lang="scss" scoped>
.container {
  .observationRecord {
    .date-group {
      margin-bottom: 20rpx;
    }

    &-title {
      height: 42rpx;
      line-height: 42rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 28rpx;
      font-weight: 400;
      margin-bottom: 22rpx;

      &-left {
        color: #333333;
      }

      &-right {
        color: #808080;
      }
    }

    .observationRecord-item {
      background: #fff;
      margin-bottom: 20rpx;
      border-radius: 12rpx;
      box-shadow: 4rpx 8rpx 16rpx #eee;
      padding: 32rpx;
      border-radius: 28rpx;
      position: relative;

      .Mask {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.5);
        z-index: 20;
      }

      .content-item {
        display: flex;
        min-height: 48rpx;
        font-size: 28rpx;
        font-weight: 400;
        margin-bottom: 8rpx;

        .item-label {
          width: 140rpx;
          color: #808080;
        }

        .item-value {
          color: #333333;
          flex: 1;
          word-break: break-all;
        }
      }
    }

    .observationRecord-item-header {
      border-bottom: 1px solid #eeeeee;
      padding-bottom: 24rpx;
      margin-bottom: 24rpx;
      display: flex;
      color: #333333;

      .item-header-center {
        position: relative;
        z-index: 2;
      }

      .item-header-left {
        width: 72rpx;
        height: 72rpx;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 20rpx;
      }
      .item-header-right {
        flex: 1;
      }

      .item-header-right-name {
        font-size: 30rpx;
        font-weight: 600;
        padding-bottom: 8rpx;
      }

      .item-header-right-time {
        font-size: 24rpx;
        font-weight: 400;
        color: #808080;
      }
    }
  }
}
</style>
