<!-- 头像 -->
<template>
  <BaseLayout2
    navTitle="上传头像"
    :footerStyle="{
      paddingBottom: `calc(20rpx + env(safe-area-inset-bottom))`,
      backgroundColor: '#fff',
    }"
    :scrollEnabled="true"
  >
    <view class="avatar-content">
      <view class="avatar">
        <up-avatar
          class="avatar-img"
          :src="
            userInfo?.headers?.length > 0
              ? userInfo.headers[0]?.uri
              : userInfo.sex == 1
              ? headList[0]
              : headList[1]
          "
          shape="circle"
          size="124rpx"
          mode="aspectFill"
        />
        <text>{{ userInfo.title || "-" }}</text>
      </view>
      <view class="upload">
        <Upload
          type="image"
          :value="imgList"
          :count="1"
          :showDel="isShow"
          :maxCount="1"
          @callback="callback"
          @emitDelFile="DelFile"
          :fileCategory="501"
        />
      </view>
    </view>

    <template #footer>
      <up-button
        type="primary"
        text="提交"
        shape="circle"
        color="#367CFF"
        @tap="onSave"
      />
    </template>
  </BaseLayout2>
</template>

<script setup>
import { reactive, ref } from "vue";
import { onLoad } from "@dcloudio/uni-app";

import Upload from "@/components/Upload/Upload.vue";
import BaseLayout2 from "@/components/base-layout/base-layout2.vue";
import { getChildrenInfo, uploadAvatar } from "@/childrenDetails/api/index.js";

let userInfo = ref({});
let headList = [
  "https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_boy.png",
  "https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_girl.png",
];
let imgList = ref([]);
const childInfo = reactive({
  childId: 0,
  classId: 0,
  faceUrl: "",
});
let isShow = ref(true);
let isExistAvatar = ref(true); // 是否已经存在头像 true:存在 false:不存在
//保存
const onSave = async () => {
  if (imgList.value.length == 0) {
    return uni.showToast({
      title: "请上传头像",
      icon: "none",
    });
  }

  let res = await addAvatar(imgList.value[0].id);
  if (res.status == 0) {
    imgList.value = [];
    uni.navigateBack();
    // childrenInfo(childInfo.childId);
    return uni.showToast({
      title: "保存成功！",
      icon: "none",
    });
  }
  uni.$u.toast(res.message || "保存失败，请重试！");
};

// 判断后缀是否为图片的后缀
function isImageFile(fileName) {
  const imageExtensions = [".jpg", ".jpeg", ".png"];
  const fileExtension = fileName.toLowerCase().slice(fileName.lastIndexOf("."));
  return imageExtensions.includes(fileExtension);
}

// 添加头像
const addAvatar = async (id) => {
  const res = await uploadAvatar({ ...userInfo.value, headerIds: [id] });
  return res;
};
// 儿童信息
const childrenInfo = async (id) => {
  const res = await getChildrenInfo(id);
  console.log(res);

  if (res.status === 0) {
    userInfo.value = res.data;
    if (
      Array.isArray(userInfo.value?.headers) &&
      userInfo.value?.headers?.length == 0
    )
      isExistAvatar.value = false;
  }
};
// 上传回调
const callback = (res) => {
  imgList.value = imgList.value.concat(res);
};

const DelFile = async (res, index) => {
  uni.showLoading({
    title: "删除中，请勿操作！",
  });

  uni.hideLoading();
};

onLoad((option) => {
  childInfo.childId = Number(option.cId);
  childInfo.classId = Number(option.classId);
  childrenInfo(option.cId);
});
</script>

<style lang="scss" scoped>
.avatar-content {
  box-sizing: border-box;
  padding: 32rpx;
  height: 100%;

  .tip {
    margin-bottom: 50rpx;
    font-size: 26rpx;
    color: #999;
    text-align: center;
  }
  .tip-first {
    margin: 50rpx 0 20rpx 0;
  }

  .upload {
    margin-top: 50rpx;
  }

  & .avatar {
    display: flex;
    align-items: center;

    &-img {
      margin-right: 28rpx;
    }

    & text {
      font-size: 44rpx;
      font-weight: 600;
    }
  }
}
</style>
