<template>
  <BaseLayout2
    navTitle="上传人脸"
    :footerStyle="{
      paddingBottom: `calc(20rpx + env(safe-area-inset-bottom))`,
      backgroundColor: '#fff',
    }"
    :scrollEnabled="false"
  >
    <view class="avatar-content">
      <view class="avatar">
        <up-avatar
          class="avatar-img"
          :src="
            userInfo?.headers?.length > 0
              ? userInfo.headers[0]?.uri
              : userInfo.sex == 1
              ? headList[0]
              : headList[1]
          "
          shape="circle"
          size="124rpx"
          mode="aspectFill"
        />
        <text>{{ userInfo.title || "-" }}</text>
      </view>
      <!-- <view class="tip tip-first"
        >照片将用于人脸匹配，请拍摄高清白色背景照片。</view
      > -->
      <up-alert
        class="tip tip-first"
        title="照片将用于人脸匹配，请拍摄高清纯色背景的幼儿大头照。"
        type="error"
        fontSize="26rpx"
      />
      <up-alert
        title="未上传头像时，将使用第一张人脸识别图片作为头像。"
        type="error"
        fontSize="26rpx"
      />
      <!-- <view class="tip">未上传头像时，将使用第一张人脸识别图片作为头像。</view> -->
      <view class="upload">
        <Upload
          type="image"
          :value="imgList"
          :count="1"
          :showDel="isShow"
          @callback="callback"
          @emitDelFile="DelFile"
          :fileCategory="503"
        />
      </view>
    </view>

    <template #footer>
      <up-button
        type="primary"
        text="提交"
        shape="circle"
        color="#367CFF"
        @tap="onSave"
      />
    </template>
  </BaseLayout2>
</template>

<script setup>
import { reactive, ref } from "vue";
import { onLoad } from "@dcloudio/uni-app";

import Upload from "@/components/Upload/Upload.vue";
import BaseLayout2 from "@/components/base-layout/base-layout2.vue";
import upAlert from "@/uni_modules/uview-plus/components/u-alert/u-alert.vue";
import {
  getChildrenInfo,
  getFaceList,
  addFace,
  deleteFace,
  uploadAvatar,
} from "@/childrenDetails/api/index.js";

let userInfo = ref({});
let headList = [
  "https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_boy.png",
  "https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_girl.png",
];
let imgList = ref([]);
const childInfo = reactive({
  childId: 0,
  classId: 0,
  faceUrl: "",
});
let isShow = ref(true);
let isExistAvatar = ref(true); // 是否已经存在头像 true:存在 false:不存在
//保存
const onSave = async () => {
  if (imgList.value.length == 0) {
    return uni.$u.toast("请上传人脸！");
  }
  console.log(imgList.value);

  const id = imgList.value[0].isOne
    ? imgList.value[0].resourceId
    : imgList.value[0].id;
  console.log(imgList.value[0]);

  uni.showLoading({
    title: "正在保存，请勿操作！",
    mask: true,
  });
  console.log(!isExistAvatar.value, "是否有头像");

  if (!isExistAvatar.value) {
    const res = await addAvatar(id);
    console.log(res);

    if (res.status == 0) {
      await childrenInfo(childInfo.childId);
      uni.hideLoading();
    }
  }
  fetchData();
};

// 循环imglist发起请求
async function fetchData() {
  let params = imgList.value.map((item) => {
    if (!item.isOne) {
      return {
        childId: childInfo.childId,
        classId: childInfo.classId,
        resourceId: item.id,
      };
    }
  });
  console.log(params, "参数列表");
  const requests = params.map((item) => {
    if (item) {
      return addFaceList(item);
    }
  }); // 将所有请求生成数组

  try {
    let errAr = ""; // 错误信息
    const responses = await Promise.all(requests); // 并行请求
    console.log(responses, "responses");
    responses.forEach((item, index) => {
      if (item && item.status == 0) {
        uni.$u.toast("上传成功");
      } else {
        errAr = errAr + " " + (index + 1);
        uni.$u.toast(`第${errAr}张失败`);
      }
    });
    let falg = responses.every((item) => item);
    console.log(falg);
    if (!falg) {
      uni.$u.toast("保存成功！");
      errAr = ""; // 清空错误信息
    }
    uni.hideLoading();
  } catch (error) {
    console.error("Error fetching data:", error);
    uni.hideLoading();
  }
}

// 人脸列表
const faceList = async (id) => {
  try {
    const res = await getFaceList(id);
    if (res.status === 0) {
      // 把res.data中的faceUrl属性放到imgList中, 并且重命名为uri
      const arr = JSON.parse(JSON.stringify(res.data));
      arr.forEach((item) => {
        item.uri = item.faceUrl;
        item.isOne = true;
        if (isImageFile(item.faceUrl)) {
          item.category = 1;
        }
      });
      imgList.value = arr;
      // imgList.value = res.data.map((item) => item.faceUrl);
    }
  } catch (err) {
    console.log(err);
    uni.$u.toast("请求失败");
  }
};
// 判断后缀是否为图片的后缀
function isImageFile(fileName) {
  const imageExtensions = [".jpg", ".jpeg", ".png"];
  const fileExtension = fileName.toLowerCase().slice(fileName.lastIndexOf("."));
  return imageExtensions.includes(fileExtension);
}
// 添加人脸
const addFaceList = async (data) => {
  const res = await addFace(data);
  return res;
};

// 添加头像
const addAvatar = async (id) => {
  const res = await uploadAvatar({ ...userInfo.value, headerIds: [id] });
  return res;
};

const childrenInfo = async (id) => {
  const res = await getChildrenInfo(id);
  if (res.status === 0) {
    userInfo.value = res.data;
    if (
      Array.isArray(userInfo.value?.headers) &&
      userInfo.value?.headers?.length == 0
    )
      isExistAvatar.value = false;
  }
};
// 上传回调
const callback = (res) => {
  imgList.value = imgList.value.concat(res);
  console.log(imgList.value, "回调函数");
};

const DelFile = async (res, index) => {
  uni.showLoading({
    title: "删除中，请勿操作！",
  });
  if (res[0].id && res[0]?.resourceId) {
    const respense = await deleteFace(res[0].id);
    if (respense.status == 0) {
      uni.$u.toast("删除成功");
      imgList.value.splice(index, 1);
      uni.hideLoading();
      return;
    }
    uni.$u.toast(respense?.error?.stack || "删除失败");
    uni.hideLoading();
    return;
  }
  imgList.value.splice(index, 1);
  uni.hideLoading();
};

onLoad((option) => {
  childInfo.childId = Number(option.cId);
  childInfo.classId = Number(option.classId);
  childrenInfo(option.cId);
  faceList(option.cId);
});
</script>

<style lang="scss" scoped>
.avatar-content {
  box-sizing: border-box;
  padding: 32rpx;
  height: 100%;

  .tip {
    margin-bottom: 50rpx;
    font-size: 26rpx;
    color: #999;
    text-align: center;
  }
  .tip-first {
    margin: 50rpx 0 20rpx 0;
  }

  .upload {
    margin-top: 50rpx;
  }

  & .avatar {
    display: flex;
    align-items: center;

    &-img {
      margin-right: 28rpx;
    }

    & text {
      font-size: 44rpx;
      font-weight: 600;
    }
  }
}
</style>
