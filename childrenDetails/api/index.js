// 儿童详情
import { request } from '@/common/request.js'

export * from './baseInfo.js'
export * from './album.js'
export * from './babyTalk.js'
export * from './work.js'

// 获取儿童详情
export function getChildrenInfo(id) {
  return request({
    url: `/business/child/detail/${id}`,
    method: 'GET'
  })
}

// // 获取儿童父母详情
export function getParentDetail(id) {
  return request({
    url: `/jsapi/business/parent/listByChildId/${id}`,
    method: 'GET'
  })
}

// 删除家长
export function deleteParentDetail(id) {
  return request({
    url: `/jsapi/business/parent/delete/${id}`,
    method: 'POST'
  })
}

// 儿童过敏源  /business/child/childAllergen
export function getChildAllergen(data) {
  return request({
    url: `/jsapi/business/child/childAllergen`,
    method: 'POST',
    data
  })
}
