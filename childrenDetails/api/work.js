import { request } from "@/common/request.js"

// 更新孩子作品 
/**
 * @param {number|string} id 孩子的id
 * **/
export function updateChildrenWorks(data) {
    return request({
        url: `/business/resource/${data.id}/child_ids_from_artwork`,
        method: 'PUT',
        data: {
            childIdsFromArtwork: [...data.childIdsFromArtwork]
        }
    })
}

// 获取孩子作品列表
/**
 * @param {number|string} current 当前页
 * @param {number|string} pageSize 每页条数
 * @param {number|string} childId  孩子的id 必填
 * **/
export function getChildrenWorksList(data) {
    return request({
        url: `/business/subject-activity/list-by-child-artwork`,
        method: 'GET',
        data
    })
}
