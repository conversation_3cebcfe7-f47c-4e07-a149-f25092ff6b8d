import { request } from "@/common/request.js"

// 人脸列表
export function getFaceList(params) {
    return request({
        url: `/jsapi/business/childFace/list?childId=${params}`,
        method: 'POST'
    })
}

/**
 * 新增人脸
 * @param {integer} childId 孩子id
 * @param {integer} classId 班级id
 * @param {string} faceUrl 人脸图片地址
 * */
export function addFace(data) {
    return request({
        url: `/jsapi/business/childFace/add`,
        method: 'POST',
        data
    })
}

/**
 * 删除人脸
 * @param {integer} childId 孩子id
 * @param {integer} classId 班级id
 * @param {string} faceUrl 人脸图片地址
 * */
export function deleteFace(params) {
    return request({
        url: `/jsapi/business/childFace/delete/${params}`,
        method: 'POST'
    })
}

/* 上传头像*/
export function uploadAvatar(data) {
    return request({
        url: `/business/child/u`,
        method: 'PUT',
        data
    })
}