<template>
    <view>
        <view class="sort-list-title">
            <view>活动排序</view>
            <view @click="btn">完成</view>
        </view>
        <up-checkbox-group v-model="checkboxValue" placement="column">
            <up-checkbox v-for="(item, index) in List" :key="index" :label="item[label]" :name="item[name]"
                shape="circle">
            </up-checkbox>
        </up-checkbox-group>
    </view>
</template>

<script setup>
import { ref, watch, onMounted } from "vue"

const props = defineProps({
    ParentList: Array, // 父组件选中的数据
    List: Array, // 固定的列数据
    label: String,
    name: String,
})
const emit = defineEmits(['bylChange'])
let once = ref(true)

const checkboxValue = ref([]);
const checkboxList = ref([])

onMounted(() => {
    checkboxList.value = props.ParentList
    checkboxList.value.forEach(item => checkboxValue.value.push(item.key))
})

watch(() => props.ParentList, (newVal) => {
    if (newVal && once.value) {
        once.value = false
        // checkboxValue.value = newVal
        console.log('%c [ checkboxValue.value ]-545', ' color:#bf2c9f;', newVal);
    }
}, { deep: true })


function btn() {
    emit('bylChange', checkboxValue.value)
    
    once.value = true
}
</script>

<style lang="scss" scoped>
.sort-list-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 32rpx;

    view:first-child {
        font-size: 34rpx;
        font-weight: 600;
    }

    view:last-child {
        font-size: 30rpx;
        font-weight: 500;
        color: rgba(63, 121, 255, 1);

    }

}
</style>
