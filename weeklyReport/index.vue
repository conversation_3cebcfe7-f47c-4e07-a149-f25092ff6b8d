<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <up-navbar
      title="周报"
      :safeAreaInsetTop="true"
      :fixed="true"
      :placeholder="true"
      bgColor="transparent"
      @leftClick="handleBack"
    >
    </up-navbar>

    <view class="container">
      <!-- 顶部信息栏 -->
      <view class="header-bar">
        <view class="class-info">
          <text class="class-title">{{ currentSchoolName || '-' }}</text>
          <!-- <text class="class-title">深圳市星星幼儿园</text> -->
        </view>
        <view class="date-filter" @click="isDateShow = true">
          <text class="date-text">{{ formatDateRange }}</text>
          <image class="change" src="/static/game/change.svg" />
        </view>
      </view>
      <!-- 加载状态 -->
      <view class="loading-container" v-if="isLoading">
        <view class="loading-content">
          <view class="loading-spinner"></view>
          <text class="loading-text">正在加载数据...</text>
        </view>
      </view>

      <!-- 统计表格 -->
      <view class="table-container" v-else-if="tableData.length > 0">
        <view class="table-content">
          <!-- 表头 -->
          <view class="table-header">
            <view class="header-cell name-cell">班级</view>
            <view class="header-cell data-cell">班级圈</view>
            <view class="header-cell data-cell">观察记录</view>
            <view class="header-cell data-cell">活动教案</view>
            <view class="header-cell data-cell">活动评价</view>
            <view class="header-cell data-cell">幼儿进区</view>
          </view>
          <!-- 表体 -->
          <view class="table-body">
            <view class="table-row" v-for="classItem in tableData" :key="classItem.id">
              <view class="body-cell name-cell">{{ classItem.className }}</view>
              <view class="body-cell data-cell clickable" @click="handleCircleClick(classItem)">
                {{ classItem.circleCount }}
              </view>
              <view
                class="body-cell data-cell clickable"
                @click="handleObservationClick(classItem)"
              >
                {{ classItem.observationCount }}
              </view>
              <view class="body-cell data-cell">{{ classItem.lessonPlanCount }}</view>
              <view class="body-cell data-cell">{{ classItem.evaluationCount }}</view>
              <view class="body-cell data-cell">{{ classItem.childAreaEntryCount }}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-else>
        <text>暂无数据</text>
      </view>
      <!-- 统计时间选择弹窗 -->
      <Popup :show="isDateShow" @close="isDateShow = false">
        <view class="date-popup">
          <view class="popup-header">
            <text class="popup-title">统计时间</text>
            <text
              class="confirm-btn"
              :class="{ disabled: isLoading }"
              @click="confirmDateSelection"
            >
              {{ isLoading ? '加载中...' : '确定' }}
            </text>
          </view>
          <view class="popup-content">
            <text class="popup-subtitle">请选择查询时间范围</text>
            <view class="date-options">
              <view class="date-row">
                <view
                  class="date-option"
                  :class="{ active: selectedDateType === 'thisWeek', disabled: isLoading }"
                  @click="selectDateType('thisWeek')"
                >
                  本周
                </view>
                <view
                  class="date-option"
                  :class="{ active: selectedDateType === 'lastWeek', disabled: isLoading }"
                  @click="selectDateType('lastWeek')"
                >
                  上周
                </view>
              </view>
              <view class="custom-week-row" :class="{ disabled: isLoading }">
                <text class="custom-week-label">指定周数</text>
                <view
                  class="custom-week-display"
                  @click="showWeekPicker = true"
                  :class="{ disabled: isLoading }"
                >
                  {{ selectedWeekRange || '请选择周数' }}
                </view>
              </view>
            </view>
          </view>
        </view>
      </Popup>

      <!-- 周数选择器 -->
      <up-picker
        :show="showWeekPicker"
        :columns="weekColumns"
        @confirm="onWeekConfirm"
        @cancel="showWeekPicker = false"
        @close="showWeekPicker = false"
      ></up-picker>
    </view>
  </view>
</template>

<script setup>
import Popup from '@/components/Popup/Popup.vue'
import { listWeek, listWeeklyReports } from './api.js'
import { ref, computed } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { checks } from '@/utils/index.js'
import { schoolDetail } from '@/api/classApi.js'

// 基础数据
let currentTitle = ref('') // 当前班级名称
let currentSchoolName = ref('') // 当前学校名称
let classId = ref('') // 班级ID
let isLoading = ref(false)
let pathParam = ref('') // URL参数中的path值

// 日期筛选相关
let isDateShow = ref(false)
let selectedDateType = ref('lastWeek') // 默认上周
let selectedWeekRange = ref('') // 选中的周数范围
let selectedWeekIndex = ref(0) // 选中的周数索引
let showWeekPicker = ref(false) // 显示周数选择器

// 表格数据
let tableData = ref([]) // 班级统计数据
let availableWeeks = ref([]) // 可用的周数列表

// up-picker的列数据
const weekColumns = computed(() => {
  return [availableWeeks.value]
})

// 日期类型映射
const dateTypeMap = {
  thisWeek: '本周',
  lastWeek: '上周',
  custom: '指定周数'
}

// 计算格式化的日期范围显示
const formatDateRange = computed(() => {
  if (selectedDateType.value === 'custom') {
    if (selectedWeekRange.value) {
      return selectedWeekRange.value
    }
    return '指定周数'
  }
  return dateTypeMap[selectedDateType.value] || '上周'
})

// 选择日期类型
const selectDateType = (type) => {
  if (isLoading.value) return // 防止加载时切换
  selectedDateType.value = type
}

// 处理up-picker确认选择
const onWeekConfirm = (e) => {
  if (isLoading.value) return // 防止加载时操作

  const index = e.indexs[0] // up-picker返回的是indexs数组
  selectedWeekIndex.value = index
  selectedWeekRange.value = availableWeeks.value[index]
  selectedDateType.value = 'custom'
  showWeekPicker.value = false
}

// 确认自定义周数选择
const confirmCustomWeekRange = (weekRange) => {
  if (isLoading.value) return // 防止加载时操作

  if (weekRange) {
    selectedWeekRange.value = weekRange
    selectedDateType.value = 'custom'
  }
}

// 确认日期选择
const confirmDateSelection = async () => {
  if (isLoading.value) return // 防止重复点击

  isDateShow.value = false
  // 重新获取数据
  await getTableData()
}

// 处理班级圈点击事件
const handleCircleClick = (classItem) => {
  // 获取学校ID
  const userInfo = uni.getStorageSync('USER_INFO')
  const schoolId = userInfo.currentSchoolId

  uni.navigateTo({
    url: `/subPages/classes/circleV1?id=${classItem.classId}&schoolId=${schoolId}&type=班级圈`
  })
}

// 处理观察记录点击事件
const handleObservationClick = (classItem) => {
  uni.navigateTo({
    url: `/observation/observationRecord`
  })
}

// 处理返回按钮点击
const handleBack = () => {
  if (pathParam.value === 'home') {
    // 如果参数中有 path=home，跳转到首页
    uni.switchTab({
      url: '/pages/index/index'
    })
  } else {
    // 否则返回上一页
    uni.navigateBack()
  }
}

// 获取可用的周数列表
const getAvailableWeeks = async () => {
  try {
    const response = await listWeek()
    if (response && response.status === 0 && response.data) {
      availableWeeks.value = response.data
    }
  } catch (error) {
    console.error('获取周数列表失败', error)
  }
}

// 获取当前周和上周的日期范围
const getCurrentWeekRange = () => {
  const now = new Date()
  const dayOfWeek = now.getDay() // 0=周日, 1=周一, ..., 6=周六
  const monday = new Date(now)
  monday.setDate(now.getDate() - (dayOfWeek === 0 ? 6 : dayOfWeek - 1)) // 获取本周一

  const sunday = new Date(monday)
  sunday.setDate(monday.getDate() + 6) // 获取本周日

  const formatDate = (date) => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }

  return `${formatDate(monday)} - ${formatDate(sunday)}`
}

const getLastWeekRange = () => {
  const now = new Date()
  const dayOfWeek = now.getDay()
  const lastMonday = new Date(now)
  lastMonday.setDate(now.getDate() - (dayOfWeek === 0 ? 6 : dayOfWeek - 1) - 7) // 上周一

  const lastSunday = new Date(lastMonday)
  lastSunday.setDate(lastMonday.getDate() + 6) // 上周日

  const formatDate = (date) => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }

  return `${formatDate(lastMonday)} - ${formatDate(lastSunday)}`
}

// 获取统计数据
const getTableData = async () => {
  isLoading.value = true

  try {
    // 构建请求参数 - 统一使用日期范围格式
    let weekParam = ''

    if (selectedDateType.value === 'thisWeek') {
      weekParam = getCurrentWeekRange()
    } else if (selectedDateType.value === 'lastWeek') {
      weekParam = getLastWeekRange()
    } else if (selectedDateType.value === 'custom' && selectedWeekRange.value) {
      weekParam = selectedWeekRange.value
    } else {
      weekParam = getLastWeekRange() // 默认上周
    }

    // 调用API获取数据
    const response = await listWeeklyReports({
      week: weekParam,
      schoolId: uni.getStorageSync('USER_INFO').currentSchoolId
    })

    if (response && response.status === 0 && response.data) {
      // 映射API返回的数据到表格数据
      const mappedData = response.data.map((item) => ({
        classId: item.classId,
        className: item.className,
        circleCount: item.postCount || 0, // 班级圈数量
        observationCount: item.orCount || 0, // 观察记录数量
        lessonPlanCount: item.seCount || 0, // 活动教案数量
        evaluationCount: item.saCount || 0, // 活动评价数量
        childAreaEntryCount: item.caeCount || 0 // 幼儿进区计划数
      }))

      tableData.value = mappedData
    } else {
      console.error('API返回数据格式错误:', response)
      tableData.value = []
      uni.showToast({
        title: response?.message || '获取数据失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('获取数据失败', error)
    tableData.value = []
    uni.showToast({
      title: '获取数据失败',
      icon: 'none'
    })
  } finally {
    isLoading.value = false
  }
}

onLoad(async (options) => {
  // 从URL参数获取className和classId
  if (options.className) {
    currentTitle.value = options.className
  }

  if (options.classId) {
    classId.value = options.classId
  } else {
    // 如果没有传classId，从用户信息中获取
    const userInfo = uni.getStorageSync('USER_INFO')
    classId.value = userInfo.currentClassId
  }

  // 获取path参数，用于判断返回逻辑
  if (options.path) {
    pathParam.value = options.path
  }

  // 获取学校名称
  const savedSchoolTitle = uni.getStorageSync('CURRENT_SCHOOL_TITLE')
  if (savedSchoolTitle) {
    currentSchoolName.value = savedSchoolTitle
  } else {
    // 从用户信息中获取学校ID，然后通过API获取学校名称
    const userInfo = uni.getStorageSync('USER_INFO')
    if (userInfo && userInfo.currentSchoolId) {
      try {
        const res = await schoolDetail(userInfo.currentSchoolId)
        if (res.status === 0 && res.data && res.data.title) {
          currentSchoolName.value = res.data.title
          // 缓存学校名称
          uni.setStorageSync('CURRENT_SCHOOL_TITLE', res.data.title)
        } else {
          currentSchoolName.value = '未知学校'
        }
      } catch (error) {
        console.error('获取学校名称失败:', error)
        currentSchoolName.value = '未知学校'
      }
    }
  }

  // 获取可用周数列表
  await getAvailableWeeks()

  // 获取初始数据
  await getTableData()
})

onShow(async () => {
  checks()
})
</script>

<script>
export default {
  options: { styleIsolation: 'shared', multipleSlots: true }
}
</script>

<style scoped lang="scss">
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f5f5 url('https://c.mypacelab.com/vxmp/img/history_background_3x.png') no-repeat
    center top;
  background-size: contain;
}

.container {
  flex: 1;
  min-height: 0;
  margin: 20rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

// 顶部信息栏
.header-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  // margin-bottom: 24rpx;
  padding: 20rpx 0;

  .class-info {
    .class-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
    }
  }

  .date-filter {
    display: flex;
    align-items: center;
    padding: 12rpx 20rpx;
    .date-text {
      font-size: 24rpx;
      color: rgba(128, 128, 128, 1);
      margin-right: 8rpx;
    }

    .change {
      width: 24rpx;
      height: 24rpx;
    }
  }
}

// 统计表格
.table-container {
  flex: 1;
  min-height: 0;
  border-radius: 28rpx;
  overflow: hidden;
  background: linear-gradient(0deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)),
    linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
  box-shadow: 4rpx 8rpx 16rpx rgba(0, 0, 0, 0.02);
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .table-content {
    flex: 1;
    min-height: 0;
    overflow-y: auto;
    box-sizing: border-box;
  }

  .table-header {
    display: flex;
    background: #fff;
    border-bottom: 1rpx solid rgba(247, 247, 247, 1);
    position: sticky;
    top: 0;
    z-index: 10;

    .header-cell {
      padding: 24rpx 8rpx;
      font-size: 24rpx;
      font-weight: 400;
      color: rgba(128, 128, 128, 1);
      text-align: center;
      background: #fff;
      flex: 1.2;

      &:last-child {
        border-right: none;
      }

      &.name-cell {
        flex: 1.2;
        padding-left: 20rpx;
      }

      &.data-cell {
        flex: 1.2;

        &:first-of-type {
          flex: 1;
        }

        &:last-child {
          padding-right: 20rpx;
        }
      }
    }
  }

  .table-body {
    .table-row {
      display: flex;
      border-bottom: 1rpx solid rgba(247, 247, 247, 1);

      &:last-child {
        border-bottom: none;
      }

      .body-cell {
        padding: 20rpx 8rpx;
        font-size: 24rpx;
        color: rgba(51, 51, 51, 1);
        text-align: center;
        flex: 1.2;

        &:last-child {
          border-right: none;
        }

        &.name-cell {
          flex: 1.2;
          background: #fff;
          color: rgba(51, 51, 51, 1);
          font-weight: 500;
          padding-left: 20rpx;
        }

        &.data-cell {
          flex: 1.2;

          &:first-of-type {
            flex: 1;
          }

          &:last-child {
            padding-right: 20rpx;
          }

          &.clickable {
            color: rgba(63, 121, 255, 1);
            cursor: pointer;

            &:hover {
              background-color: rgba(63, 121, 255, 0.1);
            }
          }
        }
      }
    }
  }
}

// 加载状态
.loading-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400rpx;

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;

    .loading-spinner {
      width: 60rpx;
      height: 60rpx;
      border: 4rpx solid #f3f3f3;
      border-top: 4rpx solid #3f79ff;
      border-radius: 50%;
      animation: loading-spin 1s linear infinite;
      margin-bottom: 20rpx;
    }

    .loading-text {
      font-size: 28rpx;
      color: #666;
    }
  }
}

@keyframes loading-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 空状态
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  color: #999;
  font-size: 28rpx;
}

// 日期选择弹窗
.date-popup {
  background: #fff;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #eee;

    .popup-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }

    .confirm-btn {
      font-size: 28rpx;
      color: #3f79ff;
      font-weight: 500;

      &.disabled {
        color: #ccc;
        pointer-events: none;
      }
    }
  }

  .popup-content {
    padding: 30rpx;

    .popup-subtitle {
      font-size: 24rpx;
      color: #666;
      margin-bottom: 30rpx;
    }

    .date-options {
      .date-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20rpx;

        .date-option {
          flex: 1;
          margin: 0 10rpx;
          padding: 20rpx;
          background: #f5f5f5;
          border-radius: 12rpx;
          text-align: center;
          font-size: 26rpx;
          color: #333;
          transition: all 0.3s ease;

          &.active {
            background: #3f79ff;
            color: #fff;
          }

          &.disabled {
            opacity: 0.5;
            pointer-events: none;
          }

          &:first-child {
            margin-left: 0;
          }

          &:last-child {
            margin-right: 0;
          }
        }
      }

      .custom-week-row {
        display: flex;
        align-items: center;
        margin-top: 30rpx;
        padding-top: 20rpx;
        border-top: 1rpx solid #eee;

        &.disabled {
          opacity: 0.5;
          pointer-events: none;
        }

        .custom-week-label {
          font-size: 26rpx;
          color: #333;
          margin-right: 20rpx;
        }

        .custom-week-display {
          flex: 1;
          padding: 20rpx;
          background: #f5f5f5;
          border-radius: 12rpx;
          text-align: center;
          font-size: 26rpx;
          color: #333;
          border: none;
          outline: none;
          cursor: pointer;
          transition: background-color 0.3s ease;

          &:active {
            background-color: #e0e0e0;
          }

          &.disabled {
            opacity: 0.5;
            pointer-events: none;
          }
        }
      }
    }
  }
}

// 雷达图弹窗
.radar-popup {
  max-height: 80vh;
  .radar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    .radar-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }

    .close-btn {
      font-size: 48rpx;
      color: #999;
      font-weight: 300;
      line-height: 1;
      padding: 10rpx;

      &:active {
        color: #666;
      }
    }
  }
  .radar-content {
    .chart-description {
      text-align: center;
      margin-bottom: 30rpx;

      .desc-title {
        display: block;
        font-size: 28rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 10rpx;
      }

      .desc-text {
        display: block;
        font-size: 24rpx;
        color: #666;
      }
    }

    .charts-box {
      width: 100%;
      height: 600rpx;

      .chart-container {
        width: 100%;
        height: 100%;
        position: relative;
      }

      .chart-empty {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 100%;
        color: #999;
        font-size: 28rpx;

        .debug-info {
          font-size: 24rpx;
          color: #ccc;
          margin-top: 20rpx;
        }
      }
    }
  }
}
</style>
