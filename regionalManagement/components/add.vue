<template>
  <BaseLayout :nav-title="isEdit ? '编辑区域' : '添加区域'" :content-style="{ padding: '0' }">
    <view class="form-wrap">
      <up-form
        ref="formRef"
        :labelStyle="{ color: 'rgba(51, 51, 51, 1)', fontSize: '30rpx', fontWeight: '600' }"
        :rules="rules"
        labelPosition="top"
        :model="formData"
        labelWidth="120"
      >
        <view class="wrap">
          <up-form-item
            :labelWidth="400"
            label="区域名称(如无对应区域请联系客服添加)"
            prop="area"
            required
          >
            <!-- 编辑模式下默认显示文本，点击后切换到选择组件 -->
            <view v-if="isEdit && !showAreaSelector" class="area-text">
              <view class="selected-area">{{ formData.area || '请选择区域名称' }}</view>
              <!-- 替换为可点击的清除按钮 -->
              <view v-if="formData.area" class="area-clear-btn" @click.stop="clearArea">
                <up-icon name="close" size="28rpx" color="#bbb" />
              </view>
            </view>
            <!-- 新增模式或已切换到选择模式时显示选择组件 -->
            <cus-selects-fan
              v-else
              v-model="formData.area"
              :data="regionOptions"
              :valueType="{ label: 'label', value: 'value' }"
              placeholder="请选择区域名称"
              :clearable="true"
              :filterable="true"
              :size="650"
              @change="handleRegionChange"
              class="custom-select"
              @blur="onCusSelectBlur"
            ></cus-selects-fan>
          </up-form-item>
        </view>
        <view class="wrap">
          <up-form-item :labelWidth="300" label="区域昵称(在幼儿选区页面展示)">
            <view class="input-wrap">
              <up-input v-model="formData.areaAlias" placeholder="请输入区域昵称" border="none" />
            </view>
          </up-form-item>
        </view>
        <view class="wrap">
          <up-form-item :labelWidth="300" label="区域图片(在幼儿选区页面展示)">
            <view class="upload-container">
              <Upload
                type="image"
                :value="formData.attachmentResourceIds"
                @callback="uploadCallback"
                @emitDelFile="delFile"
                :showDel="true"
              >
                <view class="upload-img-wrap">
                  <image src="@/static/icon/u-icon.png" class="upload-img" mode="aspectFill" />
                  <view class="upload-img-text">上传图片</view>
                </view>
              </Upload>
            </view>
          </up-form-item>
        </view>
        <view class="wrap">
          <up-form-item label="区域限定人数" prop="limitNum" required>
            <view class="select-input" @click="showLimitPicker = true">
              <text :class="{ placeholder: !formData.limitNum }">{{
                formData.limitNum || '请选择限定人数'
              }}</text>
              <up-icon name="arrow-down" size="28rpx" color="#bbb" />
            </view>
          </up-form-item>
        </view>
      </up-form>
    </view>
    <view class="submit-btn-wrap">
      <button class="submit-btn" @click="submit">提交</button>
    </view>
    <!-- 限定人数选择弹窗 -->
    <up-picker
      :show="showLimitPicker"
      @cancel="showLimitPicker = false"
      @confirm="confirmLimitPicker"
      :columns="limitColumns"
      title="选择限定人数"
    ></up-picker>
  </BaseLayout>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import BaseLayout from '@/components/base-layout/base-layout.vue'
import Popup from '@/components/Popup/Popup.vue'
import Upload from '@/components/Upload/Upload.vue'
import CusSelectsFan from '@/components/cus-selects-fan/cus-selects-fan.vue'
import { addArea, getAreaList, editArea, getOutSideAreaList } from '@/api/game.js'

// 更改formData字段名称，与接口参数保持一致
const formRef = ref(null)
const formData = reactive({
  area: '', // 区域名称
  areaAlias: '', // 区域昵称
  areaImg: '', // 区域图片
  limitNum: '', // 限定人数
  attachmentResourceIds: [],
  id: '' // 添加id字段，用于编辑时
})

// 校验规则也需要更新对应的字段名
const rules = {
  area: [{ required: true, message: '请选择区域名称' }], // 更新字段名
  areaAlias: [{ required: true, message: '请输入区域昵称' }], // 更新字段名
  areaImg: [{ required: true, message: '请上传区域图片' }], // 更新字段名
  limitNum: [{ required: true, message: '请选择限定人数' }] // 更新字段名
}

const isEdit = ref(false)
const showLimitPicker = ref(false)
const showAreaSelector = ref(false)
// 将区域名称列表转换为组件需要的格式
const regionNameList = ref(['区域A', '区域B', '区域C', '区域D', '区域E'])
const regionOptions = ref(
  regionNameList.value.map((item) => ({
    label: item,
    value: item
  }))
)
const limitList = ref(Array.from({ length: 20 }, (_, i) => i + 1))
// 限定人数转换为picker需要的格式
const limitColumns = computed(() => {
  return [limitList.value.map((num) => String(num))]
})

const tempLimit = ref('')
const classId = ref('') // 班级ID
const schoolId = ref('') // 学校ID
const teacherId = ref('') // 教师ID
const className = ref('') // 班级名称
const isOutside = ref(0) // 是否户外

// 处理区域选择变化
const handleRegionChange = (value) => {
  console.log('区域选择变化：', value)
  // 确保选择的值被正确赋值给formData.area
  formData.area = value
}

// 清除区域名称并显示下拉选择
const clearArea = () => {
  formData.area = ''
  showAreaSelector.value = true
}

// 选择器失焦时，编辑模式下如果没选值则回到文本模式
const onCusSelectBlur = () => {
  if (isEdit.value && !formData.area) {
    showAreaSelector.value = false
  }
}

onLoad(async (options) => {
  let userInfo = uni.getStorageSync('USER_INFO')
  // 获取班级ID
  classId.value = userInfo.currentClassId
  // 学校id
  schoolId.value = userInfo.currentSchoolId
  // 教师id
  teacherId.value = userInfo.id
  // 班级名称
  className.value = options.className
  // 是否户外
  isOutside.value = options.isOutside
  // 获取区域列表
  let res = null
  if (isOutside.value == 0) {
    res = await getAreaList({
      classId: classId.value
    })
  } else {
    res = await getOutSideAreaList({
      schoolId: schoolId.value
    })
  }
  if (res.status === 0) {
    regionNameList.value = res.data
    // 强制转换为字符串
    regionOptions.value = regionNameList.value.map((item) => ({
      label: item,
      value: String(item)
    }))
  } else {
    regionOptions.value = []
  }

  // 如果传入了ID，说明是编辑模式
  if (options && options.id) {
    isEdit.value = true
    // 从Storage中获取编辑的区域信息，而不是通过API获取
    const areaInfo = uni.getStorageSync('EDIT_AREA_INFO')

    if (areaInfo) {
      // 使用Storage中的数据填充表单
      formData.id = areaInfo.id
      formData.areaAlias = areaInfo.areaAlias
      formData.areaImg = areaInfo.areaImg
      formData.limitNum = areaInfo.limitNum

      // 简化区域名称回填 - 直接赋值
      if (areaInfo.area) {
        formData.area = String(areaInfo.area)

        // 确保 regionOptions 中包含该选项，为后续可能的修改做准备
        const areaStr = String(areaInfo.area)
        const areaExists = regionOptions.value.some((item) => item.value === areaStr)
        if (!areaExists) {
          // 如果区域名称不在现有选项中，添加到选项中
          regionOptions.value.unshift({
            label: areaStr,
            value: areaStr
          })
        }
      }

      // 处理图片数据 - 将areaImg转换为Upload组件可用的格式
      if (areaInfo.areaImg) {
        // 构建Upload组件需要的数据结构
        const imageObject = {
          category: 1, // 图片类型
          uri: areaInfo.areaImg,
          serviceUri: areaInfo.areaImg,
          filename: '区域图片', // 为图片提供一个默认文件名
          path: areaInfo.areaImg
        }

        // 将图片对象添加到attachmentResourceIds数组
        formData.attachmentResourceIds = [imageObject]
      } else {
        formData.attachmentResourceIds = []
      }

      // 初始化完成后，清除Storage中的数据，避免影响下次操作
      uni.removeStorageSync('EDIT_AREA_INFO')
    } else {
      // 如果没有获取到编辑数据，给出提示
      uni.$u.toast('获取区域信息失败')
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  }

  // 初始化限定人数临时变量
  tempLimit.value = formData.limitNum
})

// 限定人数选择
const confirmLimitPicker = (e) => {
  formData.limitNum = e.value[0]
  showLimitPicker.value = false
}

// 处理上传回调
const uploadCallback = (list) => {
  console.log('上传回调---------：', list)

  formData.attachmentResourceIds.push(...list)
  if (list.length > 0) {
    formData.areaImg = list[0].serviceUri || list[0].uri
  }
}

// 处理删除文件
const delFile = (item, index) => {
  formData.attachmentResourceIds.splice(index, 1)
  if (formData.attachmentResourceIds.length === 0) {
    formData.areaImg = '' // 更新字段名
  } else {
    formData.areaImg =
      formData.attachmentResourceIds[0].serviceUri ||
      formData.attachmentResourceIds[0].uri ||
      formData.attachmentResourceIds[0].url ||
      formData.attachmentResourceIds[0].path
  }
}

const submit = () => {
  formRef.value.validate().then(async (valid) => {
    if (valid) {
      // 构建提交到接口的数据
      const submitData = {
        classId: classId.value,
        area: formData.area,
        areaAlias: formData.areaAlias,
        teacherId: teacherId.value,
        schoolId: schoolId.value,
        limitNum: formData.limitNum,
        areaImg: formData.areaImg
      }
      if (isOutside.value == 1) {
        submitData.isOutside = 1
        delete submitData.classId
      }

      try {
        uni.showLoading({
          title: '提交中...'
        })

        let res

        // 根据是否是编辑模式决定调用哪个接口
        if (isEdit.value) {
          // 编辑模式，需要传递ID
          submitData.id = formData.id
          res = await editArea(submitData)
        } else {
          // 新增模式
          res = await addArea(submitData)
        }

        uni.hideLoading()

        if (res.status === 0) {
          uni.$u.toast(isEdit.value ? '编辑成功' : '添加成功')
          setTimeout(() => {
            uni.navigateBack()
          }, 1000)
        } else {
          uni.$u.toast(res.message || '提交失败')
        }
      } catch (error) {
        uni.hideLoading()
        console.error('提交出错', error)
        uni.$u.toast('网络异常，请稍后再试')
      }
    } else {
      uni.$u.toast('请检查必填项')
    }
  })
}
</script>

<style lang="scss" scoped>
.form-wrap {
  padding: 0 20rpx;
  .wrap {
    margin-bottom: 30rpx;
    padding: 5rpx 30rpx;
    border-radius: 28rpx;
    background: linear-gradient(0deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)),
      linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
    box-shadow: 4rpx 8rpx 16rpx rgba(0, 0, 0, 0.02);
  }
}

.up-form-item {
  margin-bottom: 0;
  border-bottom: 1px solid #f2f2f2;
  padding: 0 32rpx 0 32rpx;
  background: #fff;
  border-radius: 0;
}
.up-form-item:last-child {
  border-bottom: none;
}
.select-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 8rpx;
  font-size: 30rpx;
  min-height: 60rpx;
  // color: rgba(177, 179, 181, 1);
}
.input-wrap {
  padding: 0 8rpx;
  min-height: 60rpx;
  ::v-deep .u-input {
    min-height: 60rpx;
  }
}
.placeholder {
  color: #bbb;
}
.upload-container {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
}

/* 
  使用/deep/或::v-deep来深度选择组件内部元素
  不同版本的Vue可能需要不同的深度选择器语法
*/
:deep(.content) {
  width: 100%;
}

:deep(.grid-container) {
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: wrap !important;
  width: 100% !important;
  gap: 16rpx !important;
}

:deep(.file-item) {
  width: 148rpx !important;
  height: 148rpx !important;
  margin-right: 0 !important;
  margin-bottom: 0 !important;
}

:deep(.file-item-image) {
  width: 148rpx !important;
  height: 148rpx !important;
  object-fit: cover !important;
}

:deep(.image-border) {
  margin-bottom: 0 !important;
}

.upload-img-wrap {
  width: 160rpx;
  height: 160rpx;
  // background: #f2f2f2;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  cursor: pointer;
}
.upload-img {
  width: 36rpx;
  height: 30rpx;
  margin-bottom: 20rpx;
}
.upload-img-text {
  font-size: 24rpx;
  color: rgba(128, 128, 128, 1);
}

.submit-btn-wrap {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100vw;
  background: #fff;
  box-shadow: 0 -2rpx 16rpx rgba(0, 0, 0, 0.04);
  padding: 30rpx 0 50rpx 0;
  display: flex;
  justify-content: center;
  .submit-btn {
    width: 90vw;
    height: 80rpx;
    line-height: 80rpx;
    background: #3f79ff;
    color: #fff;
    border-radius: 44rpx;
    font-size: 30rpx;
    font-weight: 600;
    border: none;
  }
}

.picker-popup {
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 32rpx 0 0 0;
  min-height: 400rpx;
}
.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
  margin-bottom: 32rpx;
  font-size: 30rpx;
}
.picker-header-title {
  font-weight: 600;
}
.picker-submit {
  font-weight: 500;
  color: rgba(63, 121, 255, 1);
}
.picker-list {
  padding: 0 32rpx;
  max-height: 300rpx; /* 限制高度以支持滚动 */
  overflow-y: auto;
}
.picker-item {
  display: flex;
  align-items: center;
  font-size: 30rpx;
  margin-bottom: 32rpx;
}
.picker-item:last-child {
  margin-bottom: 0;
}
.picker-footer {
  display: flex;
  justify-content: flex-end;
  padding: 24rpx 32rpx 32rpx 32rpx;
}
.picker-confirm {
  background: #3f79ff;
  color: #fff;
  border-radius: 12rpx;
  font-size: 30rpx;
  padding: 12rpx 48rpx;
  border: none;
}

/* 区域选择组件样式调整 */
:deep(.select_wrap) {
  width: 100% !important;
}

:deep(.select_input) {
  border: none !important;
  background-color: transparent !important;
  padding: 0 8rpx !important;
  min-height: 60rpx !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  font-size: 30rpx !important;
}

:deep(.icon_arrow) {
  right: 0 !important;
}

/* 调整输入文本样式以匹配原先的样式 */
:deep(.text_tips) {
  font-size: 30rpx !important;
}

/* 修改选中状态的样式 */
:deep(.select_input_select) {
  border: none !important;
}

/* 下拉面板的样式 */
:deep(.select_modal_con) {
  border-radius: 12rpx !important;
}

/* 区域文本显示样式 */
.area-text {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 8rpx;
  font-size: 30rpx;
  min-height: 60rpx;
  cursor: pointer;
}

.selected-area {
  color: #333;
  /* 如果值为空，显示为占位符颜色 */
  &:empty {
    color: #bbb;
  }
}

/* 新增：清除按钮样式 */
.area-clear-btn {
  display: flex;
  align-items: center;
  margin-left: 12rpx;
  cursor: pointer;
}
</style>
