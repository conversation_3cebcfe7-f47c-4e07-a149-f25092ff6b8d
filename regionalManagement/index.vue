<template>
  <BaseLayout :nav-title="navTitle" :content-style="{ padding: '0' }">
    <view class="container">
      <view class="title">{{ className }}</view>
      <view class="region-list" v-if="regionList.length">
        <view v-for="(item, index) in regionList" :key="item.id" class="region-item">
          <image
            class="region-img"
            :src="item.areaImg || '/static/game/placeholder.png'"
            mode="aspectFill"
          />
          <view class="region-info">
            <view class="region-title">
              <text>{{ item.area }}</text>
              <text class="sub-title">{{ item.areaAlias }}</text>
            </view>
            <view class="region-desc"
              >区域材料 {{ item.materialNum || 0 }} 个｜限定人数 {{ item.limitNum }} 人</view
            >
          </view>
          <up-icon
            name="more-dot-fill"
            size="36rpx"
            class="more-icon"
            @click.stop="openAction(item, index)"
          />
        </view>
      </view>
      <view class="no-data">暂无数据</view>
      <view class="add-btn-wrap">
        <button class="add-btn" @click="gotoAdd">添加区域</button>
      </view>
      <Popup :show="showAction" @close="showAction = false">
        <view class="iconAction">
          <view @click="editRegion">
            <image
              src="/static/common/editor.png"
              style="width: 40rpx; height: 40rpx; margin-right: 28rpx"
            />
            <view>编辑</view>
          </view>
          <view class="deleteIcon" @click="deleteRegion">
            <image
              src="/static/common/delete.png"
              style="width: 40rpx; height: 40rpx; margin-right: 28rpx"
            />
            <view>删除</view>
          </view>
        </view>
      </Popup>
      <up-modal
        :show="showDeleteModal"
        content="确定要删除该区域吗？"
        showCancelButton
        asyncClose
        @cancel="showDeleteModal = false"
        @confirm="confirmDelete"
      />
    </view>
  </BaseLayout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import BaseLayout from '@/components/base-layout/base-layout.vue'
import Popup from '@/components/Popup/Popup.vue'
import { onShow, onLoad } from '@dcloudio/uni-app'
import { listByClass, deleteArea, listOutSideArea } from '@/api/game.js'

// 当前班级名称
const className = ref('-')
const classId = ref('')
const navTitle = ref('区域管理')
const isOutside = ref(0)
// 初始化空区域列表
const regionList = ref([])
const pageNo = ref(1)
const pageSize = ref(999) // 设置一个较大的值，一次性获取所有区域
const total = ref(0)
const schoolId = ref('')
async function getListByClass() {
  try {
    uni.showLoading({ title: '加载中...' })
    const res = await listByClass({
      pageNo: pageNo.value,
      pageSize: pageSize.value,
      classId: classId.value
    })
    uni.hideLoading()

    if (res.status === 0) {
      regionList.value = res.data.records || []
      total.value = res.data.total || 0
    } else {
      uni.$u.toast(res.message || '获取区域列表失败')
    }
  } catch (error) {
    uni.hideLoading()
    console.error('获取区域列表失败', error)
    uni.$u.toast('网络异常，请稍后再试')
  }
}
// 获取户外区域列表
async function getListOutSideArea() {
  try {
    uni.showLoading({ title: '加载中...' })
    const res = await listOutSideArea({
      pageNo: pageNo.value,
      pageSize: pageSize.value,
      schoolId: schoolId.value
    })
    uni.hideLoading()

    if (res.status === 0) {
      regionList.value = res.data.records || []
      total.value = res.data.total || 0
    } else {
      uni.$u.toast(res.message || '获取区域列表失败')
    }
  } catch (error) {
    uni.hideLoading()
    console.error('获取区域列表失败', error)
    uni.$u.toast('网络异常，请稍后再试')
  }
}
// 在页面显示时刷新列表，确保从编辑/添加页面返回时能看到最新数据
onShow(() => {
  if (classId.value && isOutside.value == 0) {
    getListByClass()
  } else if (classId.value && isOutside.value == 1) {
    getListOutSideArea()
  }
})

const showAction = ref(false)
const showDeleteModal = ref(false)
const currentIndex = ref(-1)
const currentItem = ref(null)

onLoad((options) => {
  const userInfo = uni.getStorageSync('USER_INFO')
  className.value = decodeURIComponent(options.className || '')
  classId.value = options.classId
  isOutside.value = options.isOutside || 0
  schoolId.value = userInfo.currentSchoolId
  navTitle.value = isOutside.value ? '户外区域' : '班级区域'
  if (isOutside.value == 0) {
    getListByClass()
  } else if (isOutside.value == 1) {
    getListOutSideArea()
  }
})

const gotoAdd = () => {
  uni.navigateTo({
    url: `/regionalManagement/components/add?className=${className.value}&isOutside=${isOutside.value}`
  })
}

const openAction = (item, index) => {
  currentIndex.value = index
  currentItem.value = item
  showAction.value = true
}

const editRegion = () => {
  showAction.value = false

  // 确保当前选中项有完整的数据
  const editItem = { ...currentItem.value }

  // 存储当前选中的区域信息，用于编辑页面获取
  uni.setStorageSync('EDIT_AREA_INFO', editItem)

  setTimeout(() => {
    uni.navigateTo({
      url: `/regionalManagement/components/add?id=${editItem.id}`
    })
  }, 200)
}

const deleteRegion = () => {
  showAction.value = false

  // 检查当前区域是否有材料
  if (currentItem.value && currentItem.value.materialNum > 0) {
    uni.$u.toast(`该区域还有${currentItem.value.materialNum}个材料，请先清理材料后再删除`)
    return
  }

  showDeleteModal.value = true
}

const confirmDelete = async () => {
  if (currentItem.value && currentItem.value.id) {
    try {
      uni.showLoading({ title: '删除中...' })
      const res = await deleteArea(currentItem.value.id)
      uni.hideLoading()

      if (res.status === 0) {
        uni.$u.toast('删除成功')
        // 从列表中移除
        if (currentIndex.value > -1) {
          regionList.value.splice(currentIndex.value, 1)
        }
      } else {
        uni.$u.toast(res.message || '删除失败')
      }
    } catch (error) {
      uni.hideLoading()
      console.error('删除区域失败', error)
      uni.$u.toast('网络异常，请稍后再试')
    }
  }
  showDeleteModal.value = false
}
</script>

<style lang="scss" scoped>
.container {
  padding-bottom: 120rpx;
}
.title {
  font-size: 34rpx;
  color: #333;
  font-weight: 600;
  padding: 32rpx 32rpx 0 32rpx;
}
.region-list {
  padding: 24rpx 32rpx 0 32rpx;
}
.no-data {
  width: 100%;
  font-size: 28rpx;
  color: rgba(128, 128, 128, 1);
  text-align: center;
  margin-top: 20rpx;
}
.region-item {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 28rpx;
  margin-bottom: 24rpx;
  padding: 24rpx;
  position: relative;
}
.region-img {
  width: 88rpx;
  height: 88rpx;
  border-radius: 16rpx;
  background: #eee;
  margin-right: 24rpx;
}
.region-info {
  flex: 1;
}
.region-title {
  font-size: 30rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 8rpx;
  .sub-title {
    font-size: 24rpx;
    color: #808080;
    margin-left: 16rpx;
  }
}
.region-desc {
  font-size: 24rpx;
  color: #808080;
}
.more-icon {
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
}
.add-btn-wrap {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100vw;
  background: #fff;
  box-shadow: 0 -2rpx 16rpx rgba(0, 0, 0, 0.04);
  padding: 30rpx 0 50rpx 0;
  display: flex;
  justify-content: center;
  .add-btn {
    width: 90vw;
    height: 80rpx;
    line-height: 80rpx;
    background: #3f79ff;
    color: #fff;
    border-radius: 44rpx;
    font-size: 30rpx;
    font-weight: 600;
    border: none;
  }
}

.iconAction > view {
  height: 88rpx;
  display: flex;
  align-items: center;
  font-size: 30rpx;
  font-weight: 400;
  letter-spacing: 0rpx;
  line-height: 48rpx;
  color: #333;
  text-align: left;
  vertical-align: middle;
}
.deleteIcon {
  color: #f56c6c;
}
</style>
