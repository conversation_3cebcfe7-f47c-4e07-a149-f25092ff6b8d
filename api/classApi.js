import { request } from "@/common/request.js";
const _prefix = '/jsapi'

// 保存草稿
export const draft = (data) => request({
  url: `${_prefix}/business/class/moments/draft`,
  method: "POST",
  data
});

// 发布
export const publish = (data) => request({
  url: `${_prefix}/business/class/moments/publish`,
  method: "POST",
  data
});

// 根据ID获取详情
export const getInfo = (data) => request({
  url: `${_prefix}/business/class/moments/getInfo`,
  method: "GET",
  data
});

// 删除帖子
export const deleteMoment = (data) => request({
  url: `${_prefix}/business/class/moments/delete?id=${data.id}`,
  method: "POST",
  data
});

// 帖子置顶
export const pin = (data) => request({
  url: `${_prefix}/business/class/moments/pin?id=${data.id}`,
  method: "POST",
  data
});

// 取消置顶
export const unpin = (data) => request({
  url: `${_prefix}/business/class/moments/unpin?id=${data.id}`,
  method: "Post",
  data
});

// 查看置顶帖子
export const pinned = (data) => request({
  url: `${_prefix}/business/class/moments/list/pinned`,
  method: "GET",
  data
});

// 教师查看帖子列表
export const teacherList = (data) => request({
  url: `${_prefix}/business/class/moments/teacher/list`,
  method: "GET",
  data
});

// 获取可选的幼儿园
export const schoolList = (data) => request({
  url: `/business/school/my_list`,
  method: "GET",
  data
});

// 根据id获取幼儿园详情
export const schoolDetail = (id) => request({
  url: `/admin/school/detail/${id}`,
  method: "GET",
});

// 根据id获取班级列表
export const getclassList = (data) => request({
  url: `${_prefix}/business/class/moments/listClassWithClassMoments`,
  method: "GET",
  data
});
// 根据班级id获取班级详情
export const getclassDetail = (classId) => request({
  url: `${_prefix}/business/class/moments/getClassWithClassMoments`,
  method: "GET",
  data: { classId }
});

// 查询班级教师列表
export const getTeachList = (data) => request({
  url: `/jsapi/global/user/list/teacher`,
  method: "POST",
  data,
  isGLobal: true
});

// 查询班级教师详情
export const getTeachInfo = (data) => request({
  url: `${_prefix}/business/class/moments/teacher/getInfo`,
  method: "GET",
  data
});

// 根据班级选取主题
export const getsubJectList = (data) => request({
  url: `/business/subject/list_v2`,
  method: "GET",
  data
});

// 根据班级选取主题
export const getActList = (data) => request({
  url: `/business/subject-activity/list`,
  method: "GET",
  data
});

// AI生成帖子内容
export const generatePostContentByAi = (data) => request({
  url: `${_prefix}/business/class/moments/generatePostContentByAi`,
  method: "POST",
  data
});

// AI润色
export const polishiPostByAi = (data) => request({
  url: `${_prefix}/business/class/moments/polishiPostByAi`,
  method: "POST",
  data
});

// new 获取当前班级统计信息
export const getNewclassStati = (data) => request({
  url: `/jsapi/business/class/getClassCountInfo`,
  method: "GET",
  data
})