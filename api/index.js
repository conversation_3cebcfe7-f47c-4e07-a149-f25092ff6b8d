import { request } from "@/common/request.js"

// 通用
export function enumList() {
	return request({
		url: `/enum`,
		method: 'GET'
	})
}

//Business - 字典/配置项
export function dictionaryList(data) {
	return request({
		url: `/business/dictionary/list`,
		method: 'GET',
		data
	})
}

//Business - 矩阵管理  矩阵 - 查询列表
export function getMatrixList(data) {
	return request({
		url: `/business/matrix/list`,
		method: 'GET',
		data
	})
}

//Business - 指标管理
export function getTargetList(data) {
	return request({
		url: `/business/target/list`,
		method: 'GET',
		data
	})
}

//查询对应班级
export function getclassList(data) {
	return request({
		url: `/business/class/my_list_v3`,
		method: 'GET',
		data
	})
}
//查询所有学校
export function getSchoolList(data) {
	return request({
		url: `/business/school/my_list`,
		method: 'GET',
		data
	})
}
// 更换当前班级ID
export function changeClassId(data) {
	return request({
		url: `/business/user/change`,
		method: 'PUT',
		data
	})
}

// ai生成课程
export function getAiclassList(data) {
	return request({
		url: `/jsapi/business/subject/pageList`,
		method: 'POST',
		data
	})
}

// 临时用户登录
export function tempUserLogin(data) {
	return request({
		url: `/business/temp_user`,
		method: 'POST',
		data
	})
}


// 通用 更改头像或昵称
export function updateAvatarOrNickName(data) {
	return request({
		url: `/jsapi/global/user/updateAvatarOrNickName`,
		method: 'POST',
		data
	})
}

// 通用 请求学校模板
export function getTemplateList(data) {
	return request({
		url: `/business/document-template/list`,
		method: "GET",
		data
	})
}

// 导出word
export function exportWord(data) {
	return request({
		url: `/business/document-template/export-document`,
		method: "POST",
		data
	})
}

// 页面日志
export function setLogger(data) {
	return request({
		url: `/business/page_log/logger`,
		method: 'POST',
        data
	})
}


// 获取班级儿童考勤列表 儿童+基本数据
export function getChildrenAttendanceList(data) {
  return request({
    url: '/jsapi/business/attendance/class',
    method: 'POST',
    data
  })
}
