import { request } from "@/common/request.js"

// 短信验证码
export function getsms(data) {
	return request({
		url: '/sms',
		method: 'post',
		data
	})
}

// 登录
export function login(data) {
	return request({
		url: '/business/user/phone',
		method: 'post',
		data
	})
}

// 使用手机密码登录
export function loginPwd(data) {
	return request({
		url: '/business/user/pwd',
		method: 'post',
		data
	})
}

// 修改密码
export function changePwd(data) {
	return request({
		url: '/business/user/pwd',
		method: 'PUT',
		data
	})
}

// 获取用户信息
export function getUserInfo() {
	return request({
		url: '/business/user/me',
		method: 'GET'
	})
}

/**
 * 新登录接口
 * **/


/**
 * 1、新登录接口
 * @param {number} appId  //应用id
 * @param {string} code // 微信code
 * **/
export function wxLogin(data) {
	return request({
		url: '/jsapi/uaa/wa/wxLogin',
		method: 'GET',
		data
	})
}

/**
 * 2、 获取手机号 (未绑定，获取手机号授权)
 * @param {number} appId  //应用id
 * @param {string} openId
 * @param {string} unionId
 * @param {string} code // code (手机号授权)
 * **/
export function getPhoneNumber(data) {
	return request({
		url: '/jsapi/uaa/wa/getPhoneNumber',
		method: 'POST',
		data
	})
}

/** 小程序登录 **/
// 查询二维码是否使用
export function openQrCode(data) {
	return request({
		url: '/jsapi/uaa/wa/openQrCode',
		method: 'POST',
		data
	})
}

// 确认授权
export function confirmQrCode(data) {
    	return request({
		url: '/jsapi/uaa/wa/confirmQrCode',
		method: 'POST',
		data
	})
}
