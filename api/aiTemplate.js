import { request } from '@/common/request.js'

// 获取ai模版列表
export const getAiTemplateList = (params) => {
  return request({
    url: '/business/ai-assistant-template/list',
    method: 'GET',
    data: params
  })
}

// 创建对话
export const createByAiTemplate = ({ data, onSuccess, onFail }) => {
  return request({
    url: '/business/ai-assistant-record/create',
    method: 'POST',
    data: data,
    responseType: 'arraybuffer',
    enableChunked: true,
    onSuccess,
    onFail
  })
}

// 创建流式对话
export const initialChat = () => {
  return request({
    url: '/business/assistant_conversation/start',
    method: 'POST'
  })
}

// 开始流式对话
export const startChat = ({ data, onSuccess, onFail }) => {
  return request({
    url: '/business/assistant_conversation/run',
    method: 'POST',
    data: data,
    responseType: 'arraybuffer',
    enableChunked: true,
    onSuccess,
    onFail
  })
}

// 获取聊天记录列表
export const getChatRecordList = () => {
  return request({
    url: '/business/assistant_conversation/history',
    method: 'GET'
  })
}

// 获取当前聊天的聊天记录
export const getCurrentChatRecord = (conversationId) => {
  return request({
    url: `/business/assistant_conversation/detail/${conversationId}`,
    method: 'GET'
  })
}

// 获取当前模版的使用记录
export const getAiTemplateRecordList = (data) => {
  return request({
    url: '/business/ai-assistant-record/list',
    method: 'POST',
    data: {
      templateId: data
    }
  })
}

// 获取当前聊天的聊天记录 （新）
export const getCurrentChatRecordNew = (data) => {
  return request({
    url: `/jsapi/template/chat/assistantQuestionPageList`,
    method: 'POST',
    data: data
  })
}

// 获取当前模版的使用记录（新）
export const getAiTemplateRecordListNew = (data) => {
  return request({
    url: `/jsapi/template/chat/templateRecordPageList`,
    method: 'POST',
    data: data
  })
}

// 提交反馈
export const submitFeedback = ({ questionId, content }) => {
  return request({
    url: `/business/assistant_conversation/comment/${questionId}`,
    method: 'POST',
    data: {
      comment: content
    }
  })
}

// 生成word
export const generateWord = (questionId) => {
  return request({
    url: `/business/assistant_conversation/createWord/${questionId}`,
    method: 'GET'
  })
}

// 根据模版生成word
export const exportWordByTemplate = ({ templateRecordId, questionId }) => {
  return request({
    url: `/business/ai-assistant-record/creatWord`,
    method: 'GET',
    data: {
      templateRecordId: templateRecordId,
      questionId: questionId
    }
  })
}

// new

// 获取ai模版列表
export const getAiTemplateListNew = (params) => {
  return request({
    url: '/jsapi/template/chat/listTemplate',
    method: 'POST',
    data: params
  })
}

// 删除聊天记录
export const delConversation = (conversationId) => {
  return request({
    url: `/jsapi/template/chat/delConversation?conversationId=${conversationId}`,
    method: 'GET'
  })
}

// 修改聊天标题
export const updateConversation = ({ conversationId, title }) => {
  return request({
    url: '/jsapi/template/chat/updateConversation',
    method: 'POST',
    data: {
      conversationId: conversationId,
      title: title
    }
  })
}

// 获取当前模板对话记录详情  /template/chat/getTemplateRecord
export const getTemplateRecord = (recordId) => {
  return request({
    url: `/jsapi/template/chat/getTemplateRecord?recordId=${recordId}`,
    method: 'GET'
  })
}

// 根据模板创建聊天  /template/chat/createTemplateRecord
export const createTemplateRecord = (data) => {
  return request({
    url: '/jsapi/template/chat/createTemplateRecord',
    method: 'POST',
    data: data
  })
}

// 创建对话 /template/chat/createConversation
export const createConversation = (data) => {
  return request({
    url: '/jsapi/template/chat/createConversation',
    method: 'POST',
    data: data
  })
}

// 生成word /template/chat/generateWord
export const generateWordNew = (questionId) => {
  return request({
    url: `/jsapi/template/chat/generateWord?questionId=${questionId}`,
    method: 'GET'
  })
}

// 生成全量word /template/chat/generateWholeWord
export const generateWholeWordNew = (conversationId) => {
  return request({
    url: `/jsapi/template/chat/generateWholeWord?conversationId=${conversationId}`,
    method: 'GET'
  })
}

// 重新生成回复  /template/chat/regenerateAnswer
export const regenerateAnswerNew = (questionId) => {
  return request({
    url: `/jsapi/template/chat/regenerateAnswer?questionId=${questionId}`,
    method: 'GET'
  })
}


//  获取当前用户历史聊天记录 /template/chat/conversationPageList
export const getConversationPageList = (data) => {
  return request({
    url: '/jsapi/template/chat/conversationPageList',
    method: 'POST',
    data: data
  })
}
