import { request } from '@/common/request.js'

//根据班级查询区域列表 /business/areaManagement/listByClass
export function listByClass(data) {
  return request({
    url: `/jsapi/business/areaManagement/listByClass`,
    method: 'GET',
    data
  })
}

// 新增区域 /business/areaManagement/addArea
export function addArea(data) {
  return request({
    url: `/jsapi/business/areaManagement/addArea`,
    method: 'POST',
    data
  })
}

// 获取区域类型列表 /business/areaManagement/getAreaList
export function getAreaList(data) {
  return request({
    url: `/jsapi/business/areaManagement/getAreaList`,
    method: 'GET',
    data
  })
}

// 编辑区域 /business/areaManagement/editArea
export function editArea(data) {
  return request({
    url: `/jsapi/business/areaManagement/editArea`,
    method: 'POST',
    data
  })
}

//通过id删除区域 /business/areaManagement/deleteArea
export function deleteArea(id) {
  return request({
    url: `/jsapi/business/areaManagement/deleteArea?id=${id}`,
    method: 'GET'
  })
}

// 班级材料区域列表  /business/material/getClassMaterialArea
export function getClassMaterialArea(data) {
  return request({
    url: `/jsapi/business/material/getClassMaterialArea`,
    method: 'GET',
    data
  })
}

// 某班级某区域组合材料表 /business/material/getClassCombinedMaterial
export function getClassCombinedMaterial(data) {
  return request({
    url: `/jsapi/business/material/getClassCombinedMaterial`,
    method: 'GET',
    data
  })
}

//  某班级区域组合材料查询 /business/material/queryClassCombinedMaterial
export function queryClassCombinedMaterial(data) {
  return request({
    url: `/jsapi/business/material/queryClassCombinedMaterial`,
    method: 'GET',
    data
  })
}

// 添加材料-淘宝链接-获取商品数据 /business/material/getMaterialByTaobaoUrl
export function getMaterialByTaobaoUrl(data) {
  return request({
    url: `/jsapi/business/material/getMaterialByTaobaoUrl`,
    method: 'GET',
    data
  })
}
// 添加材料 -保存-淘宝链接and自行编辑 /business/material/saveMaterialInfo
export function saveMaterialInfo(data) {
  return request({
    url: `/jsapi/business/material/saveMaterialInfo`,
    method: 'POST',
    data
  })
}

// 生成组合材料玩法 /business/material/generatePlayList
export function generatePlayList(data) {
  return request({
    url: `/jsapi/business/material/generatePlayList`,
    method: 'POST',
    data
  })
}

// 获取组合材料分析 /business/material/getMaterialAnalysis
export function getMaterialAnalysis(data) {
  return request({
    url: `/jsapi/business/material/getMaterialAnalysis
`,
    method: 'GET',
    data
  })
}
// 获取组合材料玩法 /business/material/getMaterialPlayList
export function getMaterialPlayList(data) {
  return request({
    url: `/jsapi/business/material/getMaterialPlayList`,
    method: 'GET',
    data
  })
}

// 根据编号获取玩法详情 /business/material/getPlayDetail
export function getPlayDetail(data) {
  return request({
    url: `/jsapi/business/material/getPlayDetail`,
    method: 'GET',
    data
  })
}
// 玩法采纳/不采纳 /business/material/adoptPlay
export function adoptPlay(data) {
  return request({
    url: `/jsapi/business/material/adoptPlay`,
    method: 'GET',
    data
  })
}

// 玩法删除 /business/material/deletePlay
export function deletePlay(data) {
  return request({
    url: `/jsapi/business/material/deletePlay`,
    method: 'GET',
    data
  })
}

// 根据编号获取基础信息 /business/material/getCombinedDetail
export function getCombinedDetail(data) {
  return request({
    url: `/jsapi/business/material/getCombinedDetail`,
    method: 'GET',
    data
  })
}

// 编辑材料基础信息 /business/material/editMaterBaseInfo
export function editMaterBaseInfo(data) {
  return request({
    url: `/jsapi/business/material/editMaterBaseInfo`,
    method: 'POST',
    data
  })
}

// getDictByCode  /business/material/getDictByCode
export function getDictByCode(data) {
  return request({
    url: `/jsapi/business/material/getDictByCode`,
    method: 'GET',
    data
  })
}

// 更新玩法儿童兴趣度 /business/material/updatePlayChildInterest
export function updatePlayChildInterest(data) {
  return request({
    url: `/jsapi/business/material/updatePlayChildInterest`,
    method: 'GET',
    data
  })
}

// 根据班级编号查询总材料数 /business/material/getMaterialNumByClassId
export function getMaterialNumByClassId(data) {
  return request({
    url: `/jsapi/business/material/getMaterialNumByClassId`,
    method: 'GET',
    data
  })
}

// /business/material/deleteMaterialById  根据编号删除区域材料
export function deleteMaterialById(id, classId, sourceType = 'toy') {
  return request({
    url: `/jsapi/business/material/deleteMaterialById?id=${id}&classId=${classId}&sourceType=${sourceType}`,
    method: 'POST'
  })
}

// 编辑玩法
export function updatePlayDetail(data) {
  return request({
    url: `/jsapi/business/material/editPlayDetail`,
    method: 'POST',
    data
  })
}

// 获取当前班级/区域儿童列表 https://jsapi.mypacelab.com/api//business/childAreaEntry/listChild
export function listChild(data) {
  return request({
    url: `/jsapi/business/childAreaEntry/listChild`,
    method: 'GET',
    data
  })
}

// 获取当前班级区域列表（带实时人数） https://jsapi.mypacelab.com/api//business/childAreaEntry/listArea
export function listArea(data) {
  return request({
    url: `/jsapi/business/childAreaEntry/listArea`,
    method: 'GET',
    data
  })
}

// 幼儿进区 https://jsapi.mypacelab.com/api//business/childAreaEntry/entry
export function entry(data) {
  return request({
    url: `/jsapi/business/childAreaEntry/entry`,
    method: 'POST',
    data
  })
}
// 幼儿出区 https://jsapi.mypacelab.com/api//business/childAreaEntry/exitAllAreas
export function exitAllAreas(data) {
  return request({
    url: `/jsapi/business/childAreaEntry/exitAllAreas`,
    method: 'POST',
    data
  })
}
// /business/childAreaEntry/deleteChildEntry 删除进区记录
export function deleteChildEntry(data) {
  return request({
    url: '/jsapi/business/childAreaEntry/deleteChildEntry',
    method: 'POST',
    headers: {
      user_id: uni.getStorageSync('USER_INFO').userId
    },
    data
  })
}
// 幼儿进区-历史操作 https://jsapi.mypacelab.com/api//business/childAreaEntry/history/entry
export function historyEntry(data) {
  return request({
    url: `/jsapi/business/childAreaEntry/history/entry`,
    method: 'POST',
    data
  })
}
// 获取班级/区域儿童列表-历史操作  https://jsapi.mypacelab.com/api//business/childAreaEntry/history/listChild
export function listChildHistory(data) {
  return request({
    url: `/jsapi/business/childAreaEntry/history/listChild`,
    method: 'GET',
    data
  })
}

// 获取班级区域列表（带实时人数-历史操作
export function listAreaHistory(data) {
  return request({
    url: `/jsapi/business/childAreaEntry/history/listArea`,
    method: 'GET',
    data
  })
}

// 一对一倾听列表 /pts/business/one-to-one-listening-record/list
export function listOneToOneListeningRecord(data) {
  return request({
    url: `/business/one-to-one-listening-record/list`,
    method: 'GET',
    data
  })
}

// 获取核心经验数据 /business/material/getCoreExperience
export function getCoreExperience(parentId, depth) {
  return request({
    url: `/jsapi/business/material/getCoreExperience?parentId=${parentId}&depth=${depth}`,
    method: 'GET'
  })
}

// 获取观察记录数量
export function getObservationList(data) {
  return request({
    url: '/jsapi/business/observation/pageList',
    method: 'POST',
    data
  })
}

// /business/material/exportMaterialInfo?combinedIds=61&fields=productionMethod&combinedIds=62
export function exportMaterialInfo(data) {
  return request({
    url: '/jsapi/business/material/exportMaterialInfo',
    method: 'POST',
    data
    // responseType: 'arraybuffer'
  })
}
//根据班级查询区域列表
export function listOutSideArea(data) {
  return request({
    url: `/jsapi/business/areaManagement/listOutSideArea`,
    method: 'GET',
    data
  })
}

//获取户外区域类型列表 /business/areaManagement/getOutSideAreaList
export function getOutSideAreaList(data) {
  return request({
    url: `/jsapi/business/areaManagement/getOutSideAreaList`,
    method: 'GET',
    data
  })
}

// /business/material/getMaterialNumBySchool 根据学校编号查询户外总材料数
export function getMaterialNumBySchool(data) {
  return request({
    url: `/jsapi/business/material/getMaterialNumBySchool`,
    method: 'GET',
    data
  })
}

// /business/material/getOutSideMaterialArea  户外材料区域列表
export function getOutSideMaterialArea(data) {
  return request({
    url: `/jsapi/business/material/getOutSideMaterialArea`,
    method: 'GET',
    data
  })
}
// 户外区域组合材料表 /business/material/getOutSideCombinedMaterial
export function getOutSideCombinedMaterial(data) {
  return request({
    url: `/jsapi/business/material/getOutSideCombinedMaterial`,
    method: 'GET',
    data
  })
}

// 户外区域组合材料查询 /business/material/queryOutSideCombinedMaterial
export function queryOutSideCombinedMaterial(data) {
  return request({
    url: `/jsapi/business/material/queryOutSideCombinedMaterial`,
    method: 'GET',
    data
  })
}

// https://jsapi.mypacelab.com/api//business/childAreaEntry/countClassEntryDay
export function countClassEntryDay(data) {
  return request({
    url: `/jsapi/business/childAreaEntry/countClassEntryDay`,
    method: 'GET',
    data
  })
}

// AI分析报告列表 /business/material/queryMaterialReportByClass
export function queryMaterialReportByClass(data) {
  return request({
    url: `/jsapi/business/material/queryMaterialReportByClass`,
    method: 'GET',
    data
  })
}
// /business/material/createMaterialReport 生成AI分析报告接口

export function createMaterialReport(data) {
  return request({
    url: `/jsapi/business/material/createMaterialReport`,
    method: 'POST',
    data
  })
}
// /business/material/deleteMaterialReport 删除AI分析报告接口
export function deleteMaterialReport(id) {
  return request({
    url: `/jsapi/business/material/deleteMaterialReport?id=${id}`,
    method: 'POST'
  })
}

// 根据班级编号查询AI分析报告数  /business/material/getMaterialReportNumByClass
export function getMaterialReportNumByClass(data) {
  return request({
    url: `/jsapi/business/material/getMaterialReportNumByClass`,
    method: 'GET',
    data
  })
}

// /business/material/retryMaterialReport 重试某个材料分析报告

export function retryMaterialReport(id) {
  return request({
    url: `/jsapi/business/material/retryMaterialReport?id=${id}`,
    method: 'GET'
  })
}

//  /business/material/exportMaterialDetail 材料详情导出（玩法+分析）

export function exportMaterialDetail(data) {
  return request({
    url: `/jsapi/business/material/exportMaterialDetail`,
    method: 'POST',
    data
  })
}


// /business/material/editMaterialAnalysis 修改组合材料分析
export function editMaterialAnalysis(data) {
  return request({
    url: `/jsapi/business/material/editMaterialAnalysis`,
    method: 'POST',
    data
  })
}
