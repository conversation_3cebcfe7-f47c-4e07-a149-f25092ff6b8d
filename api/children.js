// 儿童管理
import { request } from "@/common/request.js"
const basicUrl = '/business'
// 获取儿童列表
export function getChildrenList(data) {
    return request({
        url: `/business/child/list`,
        method: 'GET',
        data
    })
}

// 新增 - 儿童
export function addChildrenItem(data) {
    return request({
        url: `/business/child`,
        method: 'POST',
        data
    })
}

// 删除 - 儿童
export function deleteChildrenItem(id) {
    return request({
        url: `/business/child/${id}`,
        method: 'DELETE'
    })
}

// 编辑 - 儿童
export function putChildrenItem(data) {
    return request({
        url: `/business/child/u`,
        method: 'PUT',
        data
    })
}
