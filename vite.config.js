// vite.config.*
import uni from '@dcloudio/vite-plugin-uni'
import Optimization from '@uni-ku/bundle-optimizer'
import {
	defineConfig
} from 'vite'

export default defineConfig({
	plugins: [
		uni(),
		// 可以无需传递任何参数，默认开启所有插件功能，并在项目根目录生成类型定义文件
		Optimization({
			// 插件功能开关，默认为true，即开启所有功能
			enable: {
				'optimization': true,
				'async-import': true,
				'async-component': true,
			},
			// dts文件输出配置，默认为true，即在项目根目录生成类型定义文件
			dts: {
				'enable': false,
				'base': './',
				// 上面是对类型生成的比较全局的一个配置
				// 下面是对每个类型生成的配置，以下各配置均为可选参数
				'async-import': {
					enable: true,
					base: './',
					name: 'async-import.d.ts',
					path: './async-import.d.ts',
				},
				'async-component': {
					enable: true,
					base: './',
					name: 'async-component.d.ts',
					path: './async-component.d.ts',
				},
			},
			// 也可以传递具体的子插件的字符串列表，如 ['optimization', 'async-import', 'async-component']，开启部分插件的log功能
			logger: true,
		}),
	]
})