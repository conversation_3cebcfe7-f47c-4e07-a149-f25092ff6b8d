<template>
  <base-layout
    nav-title="表征作品/倾听详情"
    containerClass="template-container"
    navBgColor="transparent"
    :footerStyle="{
      display: 'none'
    }"
    :contentStyle="{
      padding: '0'
    }"
  >
    <view class="observation-detail">
      <!-- 标题卡片 -->
      <view class="form-item-card">
        <view class="form-label">标题</view>
        <view class="form-content">{{ detailData.title || '无标题' }}</view>
      </view>

      <!-- 记录班级卡片 -->
      <view class="form-item-card">
        <view class="form-label">记录班级</view>
        <view class="form-content">{{
          detailData.schoolClass?.nickname || detailData.schoolClass?.schoolTitle || '未知班级'
        }}</view>
      </view>

      <!-- 倾听对象卡片 -->
      <view class="form-item-card">
        <view class="form-label">倾听对象</view>
        <view class="form-content">{{ detailData.child?.title || '未知' }}</view>
      </view>

      <!-- 倾听时间卡片 -->
      <view class="form-item-card">
        <view class="form-label">倾听时间</view>
        <view class="form-content">{{ formatTime(detailData.observationTime) }}</view>
      </view>

      <!-- 活动地点卡片 -->
      <view class="form-item-card">
        <view class="form-label">活动地点</view>
        <view class="form-content">{{ detailData.activityLocation || '未知地点' }}</view>
      </view>

      <!-- 倾听内容卡片 -->
      <view class="form-item-card" v-if="detailData.audioResource || detailData.audioText">
        <view class="form-label">倾听内容</view>
        <view class="form-content">
          <view class="audio-player" v-if="detailData.audioResource">
            <!-- 音频文件信息 -->
            <view class="audio-info">
              <view class="file-details">
                <view class="file-name text-ellipsis">{{ detailData.audioResource.filename }}</view>
                <view class="file-meta">
                  <!-- <text class="file-size">{{ formatFileSize(detailData.audioResource.size) }}</text> -->
                  <text class="file-duration" v-if="audioDuration">{{
                    formatDuration(audioDuration)
                  }}</text>
                </view>
              </view>
            </view>

            <!-- 播放进度条 -->
            <view class="progress-container">
              <text class="time-label">{{ currentTime }}</text>
              <progress
                :percent="playPercent"
                class="progress-bar"
                stroke-width="4"
                activeColor="#3e82f4"
                backgroundColor="#E8E9EB"
                border-radius="2"
              />
              <text class="time-label">{{ totalTime }}</text>
            </view>

            <!-- 播放控制按钮 -->
            <view class="playback-controls">
              <view
                class="control-btn play-btn"
                @click="togglePlayAudio"
                :class="{ playing: isPlaying }"
              >
                <up-icon v-if="!isPlaying" name="play-right" color="#ffffff" size="23"></up-icon>
                <up-icon v-else name="pause" color="#ffffff" size="23"></up-icon>
              </view>
              <view class="control-btn qr-code-btn" @click="generateQRCode">
                <text>生成二维码</text>
              </view>
            </view>

            <!-- <view class="control-tip">
            {{ isPlaying ? '点击暂停播放' : '点击播放录音' }}
          </view> -->
          </view>
          <view class="audio-text" v-if="detailData.audioText">{{ detailData.audioText }}</view>
        </view>
      </view>

      <!-- AI润色后的倾听内容卡片 -->
      <view class="form-item-card" v-if="detailData.refinedAudioText">
        <view class="form-label">倾听内容润色版</view>
        <view class="form-content">
          <view class="refined-audio-text">{{ detailData.refinedAudioText }}</view>
        </view>
      </view>

      <!-- QR Code Modal -->
      <view class="qr-code-modal" v-if="showQRCode" @click="closeQRCodeModal">
        <view class="qr-code-content" @click.stop>
          <view class="qr-code-close" @click="closeQRCodeModal">
            <up-icon name="close" size="24"></up-icon>
          </view>
          <view class="qr-code-title">录音文件： {{ detailData.audioResource?.filename }}</view>
          <image
            v-if="qrCodeUrl"
            :src="qrCodeUrl"
            mode="aspectFit"
            class="qr-code-image"
            @longpress="saveQRCodeImage"
          />
          <view class="qr-code-tip">扫描二维码查看，长按图片可保存</view>
        </view>
      </view>

      <!-- 表征作品卡片 -->
      <view
        class="form-item-card"
        v-if="detailData.representationResources && detailData.representationResources.length > 0"
      >
        <view class="form-label">表征作品</view>
        <view class="form-content">
          <view class="resources-list">
            <view
              class="resource-item"
              v-for="(item, index) in detailData.representationResources"
              :key="index"
            >
              <image
                v-if="isImageFile(item.uri)"
                :src="item.uri"
                mode="aspectFill"
                class="resource-image"
                @click="previewImage(item.uri)"
              />
              <view v-else class="resource-file" @click="openFile(item.uri)">
                <up-icon name="file" size="40"></up-icon>
                <text class="file-name">{{ item.filename }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 分析评价卡片 -->
      <view class="form-item-card" v-if="detailData.analysisEvaluation">
        <view class="form-label">分析评价</view>
        <view class="form-content">
          <view class="analysis-content">{{ detailData.analysisEvaluation }}</view>
        </view>
      </view>

      <!-- 核心经验匹配 -->
      <view class="core-experience-section" v-if="detailId && matric.length > 0">
        <view class="matrixSelect-title">核心经验匹配</view>
        <view v-for="(item, index) in matric" :key="new Date().getTime() + index">
          <view class="matrixSelect-display">
            <view class="matrixSelect-header">
              <view class="matricText-top">核心经验{{ index + 1 }}</view>
            </view>
            <view class="matrixSelect-content">
              <text class="matrixSelect-content-text">{{ item.matrix1Name }}-{{ item.matrix2Name }}-{{ item.matrix3Name }}-{{ item.targetName }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </base-layout>
</template>

<script setup>
import BaseLayout from '@/components/base-layout/base-layout.vue'
import { onLoad } from '@dcloudio/uni-app'
import { ref, onBeforeUnmount } from 'vue'
import { useQueryParams } from '@/common/hooks/useQueryParams.js'
import dayjs from 'dayjs'
import { getOneToOneListeningDetail, generateCode } from './api'
import coreExp from './coreExperience.js'

// URL query
const { params } = useQueryParams('id')
const { matric, initCoreExp } = coreExp() // 核心经验相关功能
const detailData = ref({})
const innerAudioContext = ref(null)
const isPlaying = ref(false)
const showQRCode = ref(false)
const qrCodeUrl = ref('')
const detailId = ref('')

// 播放进度相关
const currentTime = ref('00:00')
const totalTime = ref('00:00')
const playPercent = ref(0)
const audioDuration = ref(0)
// 加载数据
onLoad((options) => {
  if (options.id) {
    detailId.value = options.id
    fetchDetailData(options.id)
  } else {
    uni.showToast({
      title: '缺少必要参数',
      icon: 'none'
    })
  }
})

// 获取详情数据
const fetchDetailData = (id) => {
  uni.showLoading({
    title: '加载中...'
  })

  getOneToOneListeningDetail(id)
    .then((res) => {
      uni.hideLoading()
      if (res.status === 0 && res.data) {
        detailData.value = res.data
        console.log('倾听详情数据:', detailData.value)

        // 设置音频时长和总时间
        if (res.data.audioResource && res.data.audioResource.duration) {
          audioDuration.value = Math.ceil(res.data.audioResource.duration / 1000)
          totalTime.value = formatPlayTime(audioDuration.value)
        }

        // 初始化核心经验
        initCoreExp(id)
      } else {
        uni.showToast({
          title: res.message || '获取详情失败',
          icon: 'none'
        })
      }
    })
    .catch((err) => {
      uni.hideLoading()
      console.error('获取倾听详情失败:', err)
      uni.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      })
    })
}

// 生成二维码
const generateQRCode = async () => {
  uni.showLoading({
    title: '生成二维码...'
  })
  const res = await generateCode(detailId.value)
  if (res.status === 0 && res.data && res.data.uri) {
    qrCodeUrl.value = res.data.uri
    showQRCode.value = true
    uni.hideLoading()
  } else {
    uni.showToast({
      title: res.message || '生成二维码失败',
      icon: 'none'
    })
    uni.hideLoading()
  }
}

// 关闭二维码弹窗
const closeQRCodeModal = () => {
  showQRCode.value = false
}

// 保存二维码图片
const saveQRCodeImage = () => {
  if (!qrCodeUrl.value) {
    uni.showToast({
      title: '二维码不存在',
      icon: 'none'
    })
    return
  }

  uni.showLoading({
    title: '保存中...'
  })

  // 先下载图片到本地
  uni.downloadFile({
    url: qrCodeUrl.value,
    success: (res) => {
      if (res.statusCode === 200) {
        // 保存图片到相册
        uni.saveImageToPhotosAlbum({
          filePath: res.tempFilePath,
          success: () => {
            uni.hideLoading()
            uni.showToast({
              title: '保存成功',
              icon: 'success'
            })
          },
          fail: (err) => {
            uni.hideLoading()
            console.error('保存图片失败:', err)

            // 检查是否是权限问题
            if (err.errMsg.includes('auth deny')) {
              uni.showModal({
                title: '需要相册权限',
                content: '请在设置中允许访问相册权限',
                confirmText: '去设置',
                success: (modalRes) => {
                  if (modalRes.confirm) {
                    uni.openSetting()
                  }
                }
              })
            } else {
              uni.showToast({
                title: '保存失败',
                icon: 'none'
              })
            }
          }
        })
      } else {
        uni.hideLoading()
        uni.showToast({
          title: '下载图片失败',
          icon: 'none'
        })
      }
    },
    fail: () => {
      uni.hideLoading()
      uni.showToast({
        title: '下载图片失败',
        icon: 'none'
      })
    }
  })
}

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return '未知时间'
  return dayjs(timeStr).format('YYYY-MM-DD HH:mm')
}

// 格式化文件大小
const formatFileSize = (size) => {
  if (!size) return '未知大小'
  return `${Math.round(size / 1024)}KB`
}

// 格式化时长显示
const formatDuration = (seconds) => {
  if (!seconds) return '00:00'
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 格式化播放时间
const formatPlayTime = (seconds) => {
  if (!seconds) return '00:00'
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 判断是否为图片文件
const isImageFile = (url) => {
  if (!url) return false
  return /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(url)
}

// 预览图片
const previewImage = (url) => {
  if (!url) return
  uni.previewImage({
    urls: [url],
    current: url
  })
}

// 打开文件
const openFile = (url) => {
  if (!url) return
  uni.showLoading({
    title: '打开中...'
  })

  // 根据平台处理文件打开
  // #ifdef H5
  window.open(url)
  uni.hideLoading()
  // #endif

  // #ifdef APP-PLUS || MP
  uni.downloadFile({
    url: url,
    success: (res) => {
      uni.hideLoading()
      if (res.statusCode === 200) {
        uni.openDocument({
          filePath: res.tempFilePath,
          showMenu: true,
          success: () => {
            console.log('打开文档成功')
          },
          fail: (err) => {
            console.error('打开文档失败', err)
            uni.showToast({
              title: '无法打开此类型文件',
              icon: 'none'
            })
          }
        })
      }
    },
    fail: () => {
      uni.hideLoading()
      uni.showToast({
        title: '文件下载失败',
        icon: 'none'
      })
    }
  })
  // #endif
}

// 播放音频
const togglePlayAudio = () => {
  if (!detailData.value.audioResource || !detailData.value.audioResource.uri) {
    uni.showToast({
      title: '录音文件不存在',
      icon: 'none'
    })
    return
  }

  // 如果正在播放，则停止
  if (isPlaying.value && innerAudioContext.value) {
    innerAudioContext.value.stop()
    isPlaying.value = false
    return
  }

  // 如果已存在音频上下文，先销毁
  if (innerAudioContext.value) {
    innerAudioContext.value.destroy()
    innerAudioContext.value = null
  }

  // 创建新的音频上下文
  innerAudioContext.value = uni.createInnerAudioContext()
  innerAudioContext.value.src = detailData.value.audioResource.uri
  innerAudioContext.value.obeyMuteSwitch = false // 忽略手机静音开关

  // 监听事件
  innerAudioContext.value.onPlay(() => {
    console.log('开始播放录音')
    isPlaying.value = true
  })

  innerAudioContext.value.onEnded(() => {
    console.log('录音播放结束')
    isPlaying.value = false
    currentTime.value = '00:00'
    playPercent.value = 0
  })

  innerAudioContext.value.onStop(() => {
    console.log('录音播放停止')
    isPlaying.value = false
    currentTime.value = '00:00'
    playPercent.value = 0
  })

  innerAudioContext.value.onError((res) => {
    console.log('播放录音失败:', res.errMsg)
    isPlaying.value = false
    uni.showToast({
      title: '播放录音失败',
      icon: 'none'
    })
  })

  // 监听播放进度
  innerAudioContext.value.onTimeUpdate(() => {
    const current = innerAudioContext.value.currentTime
    const duration = innerAudioContext.value.duration || audioDuration.value

    if (duration > 0) {
      const percent = Math.ceil((current / duration) * 100)
      playPercent.value = percent > 100 ? 100 : percent
      currentTime.value = formatPlayTime(current)
    }
  })

  // 监听音频准备就绪
  innerAudioContext.value.onCanplay(() => {
    console.log('音频准备好播放')
    const duration = innerAudioContext.value.duration || audioDuration.value
    if (duration > 0) {
      totalTime.value = formatPlayTime(duration)
    }
  })

  // 开始播放
  innerAudioContext.value.play()
}

// 组件卸载前清理资源
onBeforeUnmount(() => {
  // 销毁音频实例
  if (innerAudioContext.value) {
    if (isPlaying.value) {
      innerAudioContext.value.stop()
    }
    innerAudioContext.value.destroy()
    innerAudioContext.value = null
  }
})


</script>

<style lang="scss" scoped>
.observation-detail {
  padding: 32rpx;

  // 表单卡片样式，与updateOneToOneListening页面保持一致
  .form-item-card {
    border-radius: 28rpx;
    padding: 28rpx;
    background: #fff;
    margin-bottom: 24rpx;
    display: flex;
    flex-direction: column;

    .form-label {
      font-size: 30rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 16rpx;
      min-width: 160rpx;
    }

    .form-content {
      font-size: 28rpx;
      color: #666;
      line-height: 1.6;
      word-break: break-all;

      // 音频播放器样式
      .audio-player {
        padding: 24rpx;
        background-color: #f8f9fa;
        border-radius: 16rpx;
        margin-bottom: 20rpx;

        .audio-info {
          margin-bottom: 24rpx;

          .file-details {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8rpx;

            .file-name {
              font-size: 30rpx;
              font-weight: 600;
              color: #333;
              text-align: center;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              max-width: 100%;
            }

            .file-meta {
              display: flex;
              align-items: center;
              gap: 16rpx;

              .file-size,
              .file-duration {
                font-size: 24rpx;
                color: #666;
              }

              .file-duration {
                &::before {
                  content: '•';
                  margin-right: 8rpx;
                  color: #ccc;
                }
              }
            }
          }
        }

        .progress-container {
          display: flex;
          align-items: center;
          margin-bottom: 32rpx;
          gap: 16rpx;

          .time-label {
            font-size: 24rpx;
            color: #666;
            min-width: 80rpx;
            text-align: center;
          }

          .progress-bar {
            flex: 1;
          }
        }

        .playback-controls {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 40rpx;
          margin-bottom: 16rpx;

          .control-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;

            &:active {
              transform: scale(0.95);
            }

            &.play-btn {
              width: 80rpx;
              height: 80rpx;
              border-radius: 50%;
              background-color: #3e82f4;

              &.playing {
                background-color: #ff6b6b;
                animation: pulse 2s infinite;
              }
            }

            &.qr-code-btn {
              padding: 12rpx 24rpx;
              background-color: #3e82f4;
              border-radius: 24rpx;

              text {
                font-size: 26rpx;
                color: #ffffff;
                font-weight: 500;
              }
            }
          }
        }
      }

      // 音频文本样式
      .audio-text {
        padding: 20rpx;
        background-color: #f8f8f8;
        border-radius: 10rpx;
        font-size: 28rpx;
        color: #333;
        line-height: 1.6;
        margin-top: 16rpx;
      }

      // AI润色后内容样式
      .refined-audio-text {
        font-size: 28rpx;
        color: #333;
        line-height: 1.6;
        padding: 20rpx;
        background-color: #f0f5ff;
        border-radius: 10rpx;
        border-left: 4rpx solid #3e82f4;
        white-space: pre-wrap;
      }

      // 分析内容样式
      .analysis-content {
        font-size: 28rpx;
        color: #333;
        line-height: 1.6;
        white-space: pre-wrap;
      }

      // 资源列表样式
      .resources-list {
        display: flex;
        flex-wrap: wrap;
        gap: 20rpx;

        .resource-item {
          width: calc(33.33% - 14rpx);

          .resource-image {
            width: 100%;
            height: 200rpx;
            border-radius: 8rpx;
            object-fit: cover;
          }

          .resource-file {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20rpx;
            background-color: #f8f8f8;
            border-radius: 8rpx;
            height: 200rpx;

            .file-name {
              font-size: 24rpx;
              color: #666;
              margin-top: 10rpx;
              text-align: center;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              line-clamp: 2;
              -webkit-box-orient: vertical;
            }
          }
        }
      }
    }
  }

  // 动画效果
  @keyframes pulse {
    0% {
      transform: scale(1);
      box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7);
    }
    70% {
      transform: scale(1.05);
      box-shadow: 0 0 0 20rpx rgba(255, 107, 107, 0);
    }
    100% {
      transform: scale(1);
      box-shadow: 0 0 0 0 rgba(255, 107, 107, 0);
    }
  }
}

/* QR Code Modal Styles */
.qr-code-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;

  .qr-code-content {
    width: 80%;
    background-color: #fff;
    border-radius: 16rpx;
    padding: 40rpx;
    position: relative;

    .qr-code-close {
      position: absolute;
      top: 20rpx;
      right: 20rpx;
      width: 40rpx;
      height: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .qr-code-title {
      font-size: 28rpx;
      color: #333;
      margin-bottom: 30rpx;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .qr-code-image {
      width: 400rpx;
      height: 400rpx;
      margin: 0 auto;
      display: block;
    }

    .qr-code-tip {
      font-size: 24rpx;
      color: #666;
      text-align: center;
      margin-top: 30rpx;
    }
  }

}

// 核心经验匹配样式
.core-experience-section {
  margin-top: 32rpx;

  .matrixSelect-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
    margin-bottom: 24rpx;
  }

  .matrixSelect-display {
    font-size: 30rpx;
    color: #333333;
    border-radius: 28rpx;
    background: rgba(255, 255, 255, 1);
    padding: 28rpx;
    box-sizing: border-box;
    margin-bottom: 24rpx;

    .matrixSelect-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      margin-bottom: 28rpx;
    }

    .matrixSelect-content {
      .matrixSelect-content-text {
        font-size: 28rpx;
        font-weight: 400;
        color: #666;
      }
    }
  }
}
</style>
