<template>
  <base-layout
    nav-title="表征作品/倾听记录"
    containerClass="template-container"
    navBgColor="transparent"
    :contentStyle="{
      padding: '0',
      paddingBottom: '32rpx'
    }"
    :footerStyle="{
      display: 'none'
    }"
    :refresherEnabled="true"
    :refresherTriggered="refreshing"
    @scrolltolower="handleScrollToLower"
    @refresherrefresh="handleRefresh"
  >
    <template #extra>
      <!-- 悬浮添加按钮 -->
      <view class="addClass" @click="onAdd" />
    </template>

    <view class="observationRecord" v-if="observationList.length != 0">
      <view v-for="item in observationList" :key="item.id" class="date-group">
        <view class="observationRecord-title">
          <text class="observationRecord-title-left"
            >{{ item.date }}&nbsp;{{ item.dayOfWeek }}</text
          >
          <text class="observationRecord-title-right">{{ item.year }}年</text>
        </view>
        <view
          @click="onViewDetail(itm)"
          class="observationRecord-item"
          v-for="(itm, index) in item.items"
          :key="itm.id + new Date().getTime()"
        >
          <view v-if="itm.state == 2" class="Mask" @tap.capture.stop="onMask" />
          <view class="observationRecord-item-header">
            <image class="item-header-left" :src="createdIcon(itm)" alt="" />
            <view class="item-header-right">
              <view class="item-header-right-name">{{ itm.child ? itm.child.title : '' }}</view>
              <view class="item-header-right-time">{{ createdAtS(itm.observationTime) }}</view>
            </view>
            <view class="item-header-center" @click.capture.stop="onOpenPop(itm)">
              <up-icon class="card-icon" size="42rpx" name="more-dot-fill" />
            </view>
          </view>
          <view class="observationRecord-item-content">
            <view class="content-item">
              <view class="item-label">标题:</view>
              <view class="item-value">{{ itm.title }}</view>
            </view>
            <view class="content-item">
              <view class="item-label">倾听老师:</view>
              <view class="item-value">{{ itm.teacher ? itm.teacher.name : '' }}</view>
            </view>
            <view class="content-item">
              <view class="item-label">倾听地点:</view>
              <view class="item-value">{{ itm.activityLocation }}</view>
            </view>
            <view class="content-item">
              <view class="item-label">所属班级:</view>
              <view class="item-value">{{
                itm.schoolClass ? itm.schoolClass.nickname || itm.schoolClass.schoolTitle : ''
              }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view v-else class="empty">暂无倾听记录</view>
    <Popup :show="isPop" @close="isPop = false">
      <view class="iconAction">
        <view @click="onEdit">
          <image src="@/static/common/editor.png"></image>
          <view>编辑</view>
        </view>
        <view @click="onDelete">
          <image src="@/static/common/delete.png"></image>
          <view>删除</view>
        </view>
      </view>
    </Popup>
  </base-layout>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { onShareAppMessage, onShow, onLoad } from '@dcloudio/uni-app'
import { sharePageObj } from '@/utils'
import BaseLayout from '@/components/base-layout/base-layout.vue'
import Popup from '@/components/Popup/Popup.vue'
import { getclassList } from '@/api'
import { getDICT } from '@/utils'
import dayjs from 'dayjs'
import { getUserInfo } from '@/api/login'
import { deleteObservation, deleteOneToOneListeningRecord } from './api'
import { listOneToOneListeningRecord } from '@/api/game'

// 弹框控制
let isPop = ref(false)
// 当前点击的item
let activeItem = ref(null)
// 用户信息
const userInfo = ref({})
// 是否已初始化
const isInitialized = ref(false)
// 选项列表
const optionsList = reactive({
  schoolClassId: []
})

// 格式化时间
const createdAtS = (v) => dayjs(v).format('YYYY-MM-DD HH:mm')

// 列表数据
const observationList = ref([])

// 分页相关
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  hasMore: true,
  loading: false,
  totalCount: 0
})

// 下拉刷新状态
const refreshing = ref(false)

// 新增一对一倾听
const onAdd = () => {
  uni.navigateTo({
    url: '/oneToOneListening/updateOneToOneListening'
  })
}

// 触底加载更多
const handleScrollToLower = () => {
  if (pagination.hasMore && !pagination.loading) {
    loadMoreData()
  }
}

// 下拉刷新
const handleRefresh = () => {
  if (refreshing.value) return

  refreshing.value = true
  console.log('开始下拉刷新')

  // 重置分页状态
  pagination.currentPage = 1
  pagination.hasMore = true
  pagination.loading = false

  listOneToOneListeningRecord({
    pageSize: pagination.pageSize,
    current: pagination.currentPage,
    schoolClassId: userInfo.value.currentClassId
  })
    .then((r) => {
      if (r.status === 0 && r.data) {
        // 刷新时重新设置数据
        observationList.value = groupDataByDay(r.data)

        // 更新总数和分页状态
        if (r.metadata && r.metadata.count !== undefined) {
          pagination.totalCount = r.metadata.count
          const totalPages = Math.ceil(pagination.totalCount / pagination.pageSize)
          pagination.hasMore = pagination.currentPage < totalPages
        } else {
          // 如果没有metadata，根据返回数据量判断
          pagination.hasMore = r.data.length >= pagination.pageSize
        }

        console.log('下拉刷新成功，条数:', r.data.length, '总数:', pagination.totalCount)
        uni.showToast({
          title: '刷新成功',
          icon: 'success',
          duration: 1000
        })
      } else {
        uni.showToast({
          title: r.message || '刷新失败',
          icon: 'none'
        })
      }
    })
    .catch((err) => {
      console.error('下拉刷新失败:', err)
      uni.showToast({
        title: '刷新失败',
        icon: 'none'
      })
    })
    .finally(() => {
      refreshing.value = false
    })
}

// 加载更多数据
const loadMoreData = () => {
  if (pagination.loading) return

  pagination.loading = true
  pagination.currentPage++

  console.log('加载更多数据，页码:', pagination.currentPage)

  listOneToOneListeningRecord({
    pageSize: pagination.pageSize,
    current: pagination.currentPage,
    schoolClassId: userInfo.value.currentClassId
  })
    .then((r) => {
      if (r.status === 0 && r.data) {
        if (r.data.length === 0) {
          pagination.hasMore = false
          uni.showToast({
            title: '没有更多了',
            icon: 'none'
          })
          return
        }

        // 合并新数据到现有列表
        const newGroupedData = groupDataByDay(r.data)
        mergeGroupedData(newGroupedData)

        // 更新分页状态
        if (r.metadata && r.metadata.count !== undefined) {
          pagination.totalCount = r.metadata.count
          const totalPages = Math.ceil(pagination.totalCount / pagination.pageSize)
          pagination.hasMore = pagination.currentPage < totalPages
        } else {
          // 如果没有metadata，根据返回数据量判断
          pagination.hasMore = r.data.length >= pagination.pageSize
        }

        console.log('加载更多数据成功，新增条数:', r.data.length)
      } else {
        uni.showToast({
          title: r.message || '获取数据失败',
          icon: 'none'
        })
      }
    })
    .catch((err) => {
      console.error('获取倾听记录失败:', err)
      uni.showToast({
        title: '获取数据失败',
        icon: 'none'
      })
    })
    .finally(() => {
      pagination.loading = false
    })
}

// 合并分组数据
const mergeGroupedData = (newGroupedData) => {
  newGroupedData.forEach((newGroup) => {
    const existingGroupIndex = observationList.value.findIndex(
      (group) => group.date === newGroup.date && group.year === newGroup.year
    )

    if (existingGroupIndex !== -1) {
      // 如果日期组已存在，合并items
      observationList.value[existingGroupIndex].items.push(...newGroup.items)
    } else {
      // 如果日期组不存在，添加新组
      observationList.value.push(newGroup)
    }
  })
}

// 正在生成中提示
const onMask = () => {
  uni.$u.toast('记录正在⽣成中!')
}

// 初始化列表数据
function initObservationList() {
  // 防止重复加载
  if (!userInfo.value || !userInfo.value.currentClassId) {
    console.log('用户信息未加载，无法初始化列表')
    return
  }

  // 重置分页状态
  pagination.currentPage = 1
  pagination.hasMore = true
  pagination.loading = false
  observationList.value = []

  uni.showLoading({
    title: '加载中...',
    mask: true
  })

  console.log('加载列表数据，班级ID:', userInfo.value.currentClassId)
  listOneToOneListeningRecord({
    pageSize: pagination.pageSize,
    current: pagination.currentPage,
    schoolClassId: userInfo.value.currentClassId
  })
    .then((r) => {
      if (r.status === 0 && r.data) {
        observationList.value = groupDataByDay(r.data)
        isInitialized.value = true

        // 更新总数和分页状态
        if (r.metadata && r.metadata.count !== undefined) {
          pagination.totalCount = r.metadata.count
          const totalPages = Math.ceil(pagination.totalCount / pagination.pageSize)
          pagination.hasMore = pagination.currentPage < totalPages
        } else {
          // 如果没有metadata，根据返回数据量判断
          pagination.hasMore = r.data.length >= pagination.pageSize
        }

        console.log('列表数据加载成功，条数:', r.data.length, '总数:', pagination.totalCount)
      } else {
        uni.showToast({
          title: r.message || '获取数据失败',
          icon: 'none'
        })
      }
    })
    .catch((err) => {
      console.error('获取倾听记录失败:', err)
      uni.showToast({
        title: '获取数据失败',
        icon: 'none'
      })
    })
    .finally(() => {
      uni.hideLoading()
    })
}

// 页面加载时执行，只执行一次
onLoad(() => {
  console.log('页面加载(onLoad)，开始初始化')

  // 获取用户信息并初始化列表
  getUserInfo()
    .then((r) => {
      userInfo.value = r.data
      console.log('用户信息获取成功，准备初始化列表')
      initObservationList()
    })
    .catch((err) => {
      console.error('获取用户信息失败:', err)
    })

  // 获取字典数据
  getDICT('all')
    .then((dics) => {
      // 字典处理如果需要的话
    })
    .catch((err) => {
      console.error('获取字典数据失败:', err)
    })

  // 获取班级列表
  getclassList()
    .then((r) => {
      optionsList.schoolClassId = r.data.map((d) => ({
        ...d,
        label: d.title
      }))
    })
    .catch((err) => {
      console.error('获取班级列表失败:', err)
    })
})

// 页面显示时检查是否需要刷新
onShow(() => {
  console.log('页面显示(onShow)，检查是否需要刷新列表')
  const isRef = uni.getStorageSync('isRef')
  console.log('isRef 标志:', isRef)

  // 如果有刷新标记，强制刷新列表
  if (isRef) {
    console.log('发现刷新标记，执行刷新操作')
    // 确保用户信息已加载
    if (userInfo.value && userInfo.value.currentClassId) {
      initObservationList()
    } else {
      // 如果用户信息未加载，先加载用户信息
      getUserInfo()
        .then((r) => {
          userInfo.value = r.data
          initObservationList()
        })
        .catch((err) => {
          console.error('获取用户信息失败:', err)
        })
    }
    // 清除刷新标记
    uni.removeStorageSync('isRef')
  }
})

// 删除一项
function onDelete() {
  if (!activeItem.value || !activeItem.value.id) {
    uni.showToast({
      title: '无效的记录ID',
      icon: 'none'
    })
    return
  }

  uni.showModal({
    title: '确认删除',
    content: '确定要删除这条倾听记录吗？',
    success: function (res) {
      if (res.confirm) {
        uni.showLoading({
          title: '删除中...',
          mask: true
        })

        deleteOneToOneListeningRecord(activeItem.value.id)
          .then((r) => {
            if (r.status === 0) {
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              })
              isPop.value = false
              initObservationList()
            } else {
              uni.showToast({
                title: r.message || '删除失败',
                icon: 'none'
              })
            }
          })
          .catch((err) => {
            console.error('删除记录失败:', err)
            uni.showToast({
              title: '删除失败',
              icon: 'none'
            })
          })
          .finally(() => {
            uni.hideLoading()
          })
      }
    }
  })
}

// 按天进行分组
function groupDataByDay(data) {
  if (!data || data.length === 0) {
    return []
  }

  const groupedData = {}
  const dayOfWeekMap = {
    0: '周日',
    1: '周一',
    2: '周二',
    3: '周三',
    4: '周四',
    5: '周五',
    6: '周六'
  }

  data.forEach((item) => {
    const date = dayjs(item.observationTime)
    const dayKey = date.format('YYYY-MM-DD')
    const year = date.year()
    const dayOfWeek = date.day()
    const month = date.month() + 1 // 月份从0开始，所以需要+1
    const day = date.date()

    if (!groupedData[dayKey]) {
      groupedData[dayKey] = {
        year,
        date: `${month}月${day}日`,
        dayOfWeek: dayOfWeekMap[dayOfWeek],
        items: []
      }
    }

    groupedData[dayKey].items.push(item)
  })

  return Object.values(groupedData)
}

// 查看详情
function onViewDetail(item) {
  uni.navigateTo({
    url: `/oneToOneListening/detail?id=${item.id}`
  })
}
//编辑
function onEdit() {
  uni.navigateTo({
    url: `/oneToOneListening/updateOneToOneListening?id=${activeItem.value.id}`
  })
}
// 打开操作弹窗
function onOpenPop(item) {
  isPop.value = true
  activeItem.value = item
}

// 分享功能
onShareAppMessage(() => sharePageObj())

// 获取头像
const defaultUrl = 'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_boy.png'
const createdIcon = (item) => {
  if (item.child && item.child.headerUrl) {
    return item.child.headerUrl
  } else if (item.teacher && item.teacher.headerUrl) {
    return item.teacher.headerUrl
  }
  return defaultUrl
}
</script>
<style lang="scss" scoped>
.addClass {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  background: url('https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/course_addClass.png')
    no-repeat 100%;
  background-size: 140% 140%;
  background-position: -13rpx -7rpx;
  position: fixed;
  right: 32rpx;
  bottom: 120rpx;
  z-index: 9999;
}

.empty {
  color: #ccc;
  font-weight: 400;
  font-size: 28rpx;
  text-align: center;
  margin-top: 200rpx;
}

.iconAction {
  & > view {
    height: 88rpx;
    display: flex;
    align-items: center;
    font-size: 30rpx;
    font-weight: 400;
    letter-spacing: 0rpx;
    line-height: 48rpx;
    color: rgba(51, 51, 51, 1);
    text-align: left;
    vertical-align: middle;

    image {
      width: 40rpx;
      height: 40rpx;
      margin-right: 28rpx;
    }
  }
}

.observationRecord {
  padding: 8rpx 32rpx 0 32rpx;

  .date-group {
    margin-bottom: 20rpx;
  }

  &-title {
    height: 42rpx;
    line-height: 42rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 28rpx;
    font-weight: 400;
    margin-bottom: 22rpx;

    &-left {
      color: #333333;
    }

    &-right {
      color: #808080;
    }
  }

  .observationRecord-item {
    background: #fff;
    margin-bottom: 20rpx;
    border-radius: 12rpx;
    box-shadow: 4rpx 8rpx 16rpx #eee;
    padding: 32rpx;
    border-radius: 28rpx;
    position: relative;

    .Mask {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.5);
      z-index: 20;
    }

    .content-item {
      display: flex;
      min-height: 48rpx;
      font-size: 28rpx;
      font-weight: 400;
      margin-bottom: 8rpx;

      .item-label {
        width: 140rpx;
        color: #808080;
      }

      .item-value {
        color: #333333;
        flex: 1;
        word-break: break-all;
      }
    }
  }

  .observationRecord-item-header {
    border-bottom: 1px solid #eeeeee;
    padding-bottom: 24rpx;
    margin-bottom: 24rpx;
    display: flex;
    color: #333333;

    .item-header-center {
      position: relative;
      z-index: 2;
    }

    .item-header-left {
      width: 72rpx;
      height: 72rpx;
      border-radius: 50%;
      overflow: hidden;
      margin-right: 20rpx;
    }
    .item-header-right {
      flex: 1;
    }

    .item-header-right-name {
      font-size: 30rpx;
      font-weight: 600;
      padding-bottom: 8rpx;
    }

    .item-header-right-time {
      font-size: 24rpx;
      font-weight: 400;
      color: #808080;
    }
  }
}
</style>
