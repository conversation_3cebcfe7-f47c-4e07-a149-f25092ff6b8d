// 核心经验 - 一对一倾听
import { getCoreExp } from './api'
import { ref } from 'vue'
import { getMatrixList } from "@/api";

export default () => {
    // 根据id获取 领域 的核心经验
    let matric = ref([])
    let matrix1List = ref([])

    const getMatrixDetails = async (mId) => {
        if (!mId) return;
        let res = await getCoreExp({
            relId: mId,
            matchType: 'onetoone'
        })
        if (res.status == 0) {
            matric.value = res.data || [];
            // 每一项添加一个字段，用于判断是否需要编辑
            matric.value.forEach(item => {
                item.isEdit = false;
            })
            return
        }
        console.log("获取核心经验详情：", res.data);
        uni.$u.toast(res.message || '获取核心经验失败')
    }

    // 请求领域的下拉数据
    const matrixList = async () => {
        let res = await getMatrixList({
            pid: 0,
            pageSize: 1000,
        })
        if (res.status == 0) {
            matrix1List.value = res.data;
        }
    }

    // 初始化方法
    const initCoreExp = (recordId) => {
        console.log("初始化核心经验", recordId)
        if (recordId) {
            getMatrixDetails(recordId)
        }
        matrixList()
    }

    return { matric, matrix1List, initCoreExp }
}
