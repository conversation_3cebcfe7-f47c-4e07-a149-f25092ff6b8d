// 儿童管理
import { request } from '@/common/request.js'

export function addObservation(data) {
  return request({
    url: `/jsapi/business/observation/manualRecord`,
    method: 'post',
    data
  })
}

export function addAIObservation(data) {
  return request({
    url: `/jsapi/business/observation/submitAiObservationRecord`,
    method: 'POST',
    data
  })
}

export function updateObservation(data) {
  return request({
    url: '/jsapi/business/observation/updateSubject',
    method: 'POST',
    data
  })
}

export function getObservationList(data) {
  return request({
    url: '/jsapi/business/observation/pageList',
    method: 'POST',
    data
  })
}

export function getObservationDetailById(id) {
  return request({
    url: `/jsapi/business/observation/detail/${id}`,
    method: 'POST'
  })
}

export function deleteObservation(id) {
  return request({
    url: `/jsapi/business/observation/delete/${id}`,
    method: 'POST'
  })
}

// 添加表征作品/倾听
export function addOneToOneListening(data) {
  return request({
    url: '/business/one-to-one-listening-record',
    method: 'POST',
    data
  })
}

// 获取表征作品/倾听详情
export function getOneToOneListeningById(id) {
  return request({
    url: `/business/one-to-one-listening-record/${id}`,
    method: 'GET'
  })
}

// 更新表征作品/倾听
export function updateOneToOneListening(data) {
  return request({
    url: `/business/one-to-one-listening-record/${data.id}`,
    method: 'PUT',
    data
  })
}

// 上传录音文件
export function uploadAudioFile(file, type = 351) {
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: '/file/upload',
      filePath: file,
      name: 'file',
      formData: {
        fileCategory: type // 351为表征作品/倾听录音文件类型
      },
      success: (res) => {
        try {
          const data = JSON.parse(res.data)
          resolve(data)
        } catch (e) {
          reject(e)
        }
      },
      fail: (err) => {
        reject(err)
      }
    })
  })
}

// 获取表征作品/倾听记录详情 /pts/business/one-to-one-listening-record/detail/{id}
export function getOneToOneListeningDetail(id) {
  return request({
    url: `/business/one-to-one-listening-record/detail/${id}`,
    method: 'GET'
  })
}

// 更新表征作品/倾听记录  /pts/business/one-to-one-listening-record
export function updateOneToOneListeningRecord(data) {
  return request({
    url: `/business/one-to-one-listening-record`,
    method: 'PUT',
    data
  })
}

//  生成小程序码 /pts/business/one-to-one-listening-record/generate-code/{id}
export function generateCode(id) {
  return request({
    url: `/business/one-to-one-listening-record/generate-code/${id}`,
    method: 'GET'
  })
}

// 删除表征作品/倾听记录
export function deleteOneToOneListeningRecord(id) {
  return request({
    url: `/business/one-to-one-listening-record/${id}`,
    method: 'DELETE'
  })
}

// /pts/business/one-to-one-listening-record/refine-audio-text   倾听文本润色
export function refineAudioText(data) {
  return request({
    url: `/business/one-to-one-listening-record/refine-audio-text`,
    method: 'POST',
    data
  })
}
// /pts/business/one-to-one-listening-record/analysis-by-ai  智能分析表征作品/倾听记录
export function analysisByAi(data) {
  return request({
    url: `/business/one-to-one-listening-record/analysis-by-ai`,
    method: 'POST',
    data
  })
}

// 核心经验获取  relId 数据编号 matchType 数据类型，observation观察记录 subject教案 onetoone 一对一

export function getCoreExp(data) {
  return request({
    url: `/jsapi/business/coreExp/getCoreExpById`,
    method: 'GET',
    data
  })
}

// 核心经验批量更新
export function updateCoreExpList(data) {
  return request({
    url: `/jsapi/business/coreExp/updateCoreExpBatch/${data.matchType}/${data.relId}`,
    method: 'POST',
    data: data.list
  })
}
