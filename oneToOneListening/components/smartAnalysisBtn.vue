<template>
  <view class="smart-analysis" @tap.stop.prevent="emit('click')">
    <image src="@/static/icon/ai.png" />
    <text>智能分析</text>
  </view>
</template>

<script setup>
const emit = defineEmits(["click"]);
</script>

<style lang="scss" scoped>
.smart-analysis {
  width: fit-content;
  height: 60rpx;
  color: #3f79ff;
  font-size: 24rpx;
  font-weight: 500;
  padding: 0 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 34rpx;
  background: rgba(54, 124, 255, 0.06);
  image {
    width: 40rpx;
    height: 30rpx;
    margin-right: 6rpx;
  }
}
</style>
