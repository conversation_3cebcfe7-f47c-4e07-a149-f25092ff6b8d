<template>
  <up-popup
    :show="isShow"
    :animation="false"
    :isMaskClick="1 === 0"
    :maskClick="1 === 0"
  >
    <view class="popup-content">
      <view class="popup-content-main">
        <view class="picker__toolbar justify-between">
          <view class="picker__cancel" @click="cancel">取消</view>
          <!-- <view class="van-ellipsis picker__title">{{label}}</view> -->
          <view class="picker__confirm" @click="onConfirm">确认</view>
        </view>
        <scroll-view class="picker__content" scroll-y="true">
          <input
            class="uni-input bottom-line keyword-input"
            v-if="isSearch"
            v-model="searchKey"
            placeholder="输入关键字搜索"
            @input="search"
          />
          <view class="uni-list" v-if="multiple && !isSearching">
            <custom-checkbox
              v-model="checkedAll"
              label="全选"
              @toggle="toggleAll"
              fontSize="36rpx"
              circleSize=""
              :color="color"
              :circleColor="circleColor"
            />
          </view>
          <view class="uni-list" v-if="multiple">
            <template v-if="columnsData.length > 0">
              <custom-checkbox
                v-model="item.checked"
                :label="item.name"
                :item="item"
                @toggle="toggleIt($event, item, index)"
                v-for="(item, index) in columnsData"
                :key="index"
                :fontSize="labelFontSize"
                :circleSize="circleSize"
                :color="color"
                :circleColor="circleColor"
              />
            </template>
            <template v-else>
              <view class="no__data">{{ noData }}</view>
            </template>
          </view>
          <view class="uni-list" v-else>
            <template v-if="columnsData.length > 0">
              <custom-checkbox
                v-model="item.checked"
                :label="item.name"
                @toggle="toggleIt($event, item, index)"
                v-for="(item, index) in columnsData"
                :key="index"
                :item="item"
                :fontSize="labelFontSize"
                :circleSize="circleSize"
                :color="color"
                :circleColor="circleColor"
              />
            </template>
            <template v-else>
              <view class="no__data">{{ noData }}</view>
            </template>
          </view>
        </scroll-view>
      </view>
    </view>
  </up-popup>
</template>

<script setup>
import { ref, defineProps, defineEmits } from "vue";
import CustomCheckbox from "./CustomCheckbox.vue";

// 定义 props
const props = defineProps({
  value: {
    type: Array,
    default: () => [],
  },
  columns: {
    type: Array,
    default: () => [],
  },
  selectValue: {
    type: Array,
    default: () => [],
  },
  option: {
    type: Object,
    default: () => ({ label: "label", value: "value" }),
  },
  isSearch: {
    type: Boolean,
    default: true,
  },
  multiple: {
    type: Boolean,
    default: true,
  },
  labelFontSize: {
    type: String,
    default: "32rpx",
  },
  color: {
    type: String,
    default: "#333",
  },
  circleSize: {
    type: String,
    default: "scale(1)",
  },
  circleColor: {
    type: String,
    default: "#004aff",
  },
});

// 定义 emits
const emit = defineEmits(["confirm"]);

// 定义响应式数据
const isSearching = ref(false);
const searchKey = ref("");
const columnsData = ref([]);
const checkedValue = ref([]);
const checkedAll = ref(false);
const tempValue = ref([]);
const noData = ref("没有更多内容了");
// const value = ref(false);
const popup = ref(null);
const isShow = ref(false);

// 方法
const getData = (val) => {
  return columnsData.value?.filter((item) =>
    val.includes(item[props.option.value])
  );
};

const onConfirm = () => {
  tempValue.value = [];
  searchKey.value = "";
  isSearching.value = false;
  const ck = columnsData.value.filter((d) => d.checked);
  const ks = ck.map((d) => d[props.option.value]);
  emit("confirm", ({ value: ks, fullValue: getData(ks) }));
};

const cancel = () => {
  tempValue.value = [];
  searchKey.value = "";
  isSearching.value = false;
  closePopup();
};

const showPopup = () => {
  columnsData.value = JSON.parse(JSON.stringify(props.columns));
  columnsData.value.forEach((d) => (d.checked = false));

  checkedValue.value = JSON.parse(JSON.stringify(props.selectValue));

  if (checkedValue.value && checkedValue.value.length > 0) {
    columnsData.value.forEach((d) => {
      if (checkedValue.value.includes(d[props.option.value])) {
        d.checked = true;
      }
      checkedValue.value.forEach((cv) => {
        if (cv === d.id) {
          d.checked = true;
        }
      })
    });
    checkedAll.value = checkedValue.value.length === columnsData.value.length;
  } else {
    checkedAll.value = false;
  }

console.log("checkedValue.value:", columnsData.value);
  // 假设 popup 是一个 ref
  isShow.value = true;
};

const closePopup = () => {
  // 假设 popup 是一个 ref
  isShow.value = false;
};

const toggleIt = (v, item, index) => {
  if (props.multiple) {
    item.checked = v;
    columnsData.value[index] = item;
    if (!v) {
      checkedAll.value = false;
    } else {
      const ck = columnsData.value.filter((d) => !d.checked);
      if (ck.length === 0) {
        checkedAll.value = true;
      }
    }
  } else {
    columnsData.value.forEach((d) => (d.checked = false));
    item.checked = true;
    columnsData.value[index] = item;
  }
  if (!v) {
    tempValue.value = tempValue.value.filter(
      (tv) => tv !== item[props.option.value]
    );
  } else {
    if (!tempValue.value.includes(item[props.option.value])) {
      tempValue.value.push(item[props.option.value]);
    }
  }
};

const toggleAll = (v) => {
  checkedAll.value = v;
  columnsData.value.forEach((d) => (d.checked = v));
};

const refreshTemp = () => {
  const ck = columnsData.value.filter((d) => d.checked);
  const ks = ck.map((d) => d[props.option.value]);
  ks.forEach((k) => {
    if (!tempValue.value.includes(k)) {
      tempValue.value.push(k);
    }
  });
};

const search = (val) => {
  console.log("search-------val:", val);
  // #ifdef MP-WEIXIN
  // #endif
  val = val.detail.value; // 小程序取值

  if (val) {
    isSearching.value = true;
    refreshTemp();
    columnsData.value = columnsData.value.filter((item) =>
      item[props.option.label].includes(val)
    );
  } else {
    isSearching.value = false;
    columnsData.value = JSON.parse(JSON.stringify(props.columns));
    if (tempValue.value && tempValue.value.length > 0) {
      columnsData.value.forEach((d) => {
        if (tempValue.value.includes(d[props.option.value])) {
          d.checked = true;
        }
      });
      checkedAll.value = tempValue.value.length === columnsData.value.length;
    }
  }
};

defineExpose({
  showPopup,
  closePopup,
});
</script>

<style lang="scss">
//结果弹窗
.close-view {
  height: 130rpx;
  image {
    width: 100rpx;
    height: 100rpx;
  }
}
.popup-content {
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  background-color: #fff;
  padding: 1rpx;
  width: 100vw;
  // height: 70vh;
  // overflow-y: scroll;
  .popup-content-main {
    margin: 50rpx auto 30rpx;

    .picker__toolbar {
      box-sizing: border-box;
      margin: 20rpx 32rpx;
      font-size: $uni-font-size-lg;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .picker__cancel {
        color: #969799;
      }
      .picker__confirm {
        color: #576b95;
      }
      .picker__title {
        font-size: 38rpx;
        color: #6f6f6f;
      }
    }

    .picker__content {
      max-height: 500rpx;
      overflow-y: auto;

      .keyword-input {
        font-size: 32rpx;
      }
      .check__all {
        box-sizing: border-box;
        margin: 20rpx 23rpx 20rpx 32rpx;
        padding: 20rpx 0rpx;
        border-bottom: solid 1rpx #f7f7f7;
        display: flex;
        justify-content: space-between;
        .check__all_left {
          color: #666;
          font-size: 32rpx;
        }
        .check__all_right {
        }
      }

      .uni-list {
        box-sizing: border-box;
        margin: 20rpx 32rpx;
        label {
          padding: 20rpx 0rpx;
          border-bottom: solid 1rpx #f7f7f7;
        }
        .uni-list-cell {
          .cell-label {
            font-size: 35rpx;
          }
          checkbox {
            //transform:scale(0.8,0.8)
          }
        }
        .no__data {
          color: #999;
          font-size: 30rpx;
          text-align: center;
          margin-top: 50rpx;
        }
      }
    }
  }
}

.bottom-line {
  border-bottom: solid 3rpx #eee;
  margin: 20rpx 32rpx;
  height: 72rpx;
}
</style>
