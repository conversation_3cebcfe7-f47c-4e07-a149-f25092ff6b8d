<template>
  <base-layout
    nav-title="表征作品/倾听"
    containerClass="template-container"
    navBgColor="transparent"
    :footerStyle="{
      display: 'none'
    }"
    :contentStyle="{
      padding: '0'
    }"
  >
    <view class="addObservation">
      <view class="addObservation-form">
        <up-form
          ref="formRef"
          labelPosition="top"
          :model="formData"
          :rules="rules"
          labelWidth="80"
          labelStyle="{fontSize: 30rpx; fontWeight: 600;"
          labelAlign="left"
        >
          <up-form-item
            :style="formItemStyle"
            label="记录班级"
            prop="schoolClassId"
            @click="onShowPicker('schoolClassId')"
          >
            <view>
              {{ getOptionLabelByKeyAndId('schoolClassId', formData.schoolClassId) }}
              <text
                v-if="!getOptionLabelByKeyAndId('schoolClassId', formData.schoolClassId)"
                class="placeholder"
                >请输入记录班级</text
              >
            </view>

            <template #right>
              <up-icon name="arrow-down" />
            </template>
          </up-form-item>

          <up-form-item
            :style="formItemStyle"
            label="倾听对象"
            prop="children"
            required
            :disabled="!formData.schoolClassId"
            @click="onShowPicker('childIds')"
          >
            <view>
              {{ formData.childNames.join(',') }}
              <text v-if="!formData.childNames.join(',')" class="placeholder">请选择倾听对象</text>
            </view>
            <template #right>
              <up-icon name="arrow-down" />
            </template>
          </up-form-item>

          <up-form-item :style="formItemStyle" label="倾听时间" prop="observationTime" required>
            <uni-datetime-picker
              class="datetime-picker"
              :border="false"
              leftIcon="none"
              type="datetime"
              placeholder="请选择倾听时间"
              v-model="formData.observationTime"
              @change="calendarConfirm"
            />
            <template #right v-if="!formData.observationTime">
              <up-icon name="arrow-down" />
            </template>
          </up-form-item>

          <up-form-item :style="formItemStyle" label="活动地点" prop="activityLocation">
            <up-input
              :cursorSpacing="100"
              v-model="formData.activityLocation"
              placeholder="请输入活动地点"
              border="none"
            />
          </up-form-item>

          <up-form-item :style="formItemStyle" label="标题" prop="title">
            <up-input
              :cursorSpacing="100"
              v-model="formData.title"
              placeholder="请输入标题"
              border="none"
            />
          </up-form-item>

          <up-form-item :style="formItemStyle" label="表征作品" prop="representationResourceIds">
            <Upload
              type="all"
              :value="formData.representationResources"
              @callback="handleRepresentationUpload"
              @emitDelFile="handleRepresentationDelete"
              :showDel="!disabled"
              :fileCategory="352"
            >
              <view class="add-works">
                <text>+添加</text>
              </view>
            </Upload>
          </up-form-item>

          <up-form-item :style="formItemStyle" label="倾听内容" prop="audioText">
            <view class="content-section">
              <view v-if="audioRecordStatus === 'idle'" class="audio-prep">
                <view class="record-btn" @click="startRecording">
                  <image src="/static/icon/evaluate_speech.png" />
                  <text>开始录音</text>
                </view>
                <view class="text-input-area">
                  <up-textarea
                    :cursorSpacing="100"
                    v-model="formData.audioText"
                    placeholder="或直接输入倾听内容文本"
                    border="none"
                    autoHeight
                    maxlength="-1"
                    confirmType="done"
                  />
                  <view class="textarea-actions">
                    <ai-polish-btn v-if="shouldShowAudioPolishBtn" @click="onAudioTextAiPolish" />
                  </view>
                </view>
              </view>

              <view v-else-if="audioRecordStatus === 'recording'" class="recording-section">
                <view class="recording-time">{{ recordingTime }}s</view>
                <view class="recording-status">
                  <text>录音中</text>
                  <view class="recording-dots">
                    <text class="dot">.</text>
                    <text class="dot">.</text>
                    <text class="dot">.</text>
                  </view>
                </view>
                <view class="recording-actions">
                  <view class="cancel-btn" @click="cancelRecording">取消</view>
                  <view class="stop-btn" @click="stopRecording">结束录音</view>
                </view>
                <view class="recording-tip">最长录制10分钟，将自动结束</view>

                <!-- 录音过程中显示实时转文字内容 -->
                <view class="text-input-area">
                  <up-textarea
                    :cursorSpacing="100"
                    v-model="formData.audioText"
                    placeholder="实时语音转文字内容将显示在这里..."
                    border="none"
                    autoHeight
                    maxlength="-1"
                    confirmType="done"
                    :disabled="false"
                  />
                </view>
              </view>

              <view v-else-if="audioRecordStatus === 'completed'" class="audio-result">
                <view class="audio-player">
                  <!-- 音频文件信息 -->
                  <view class="audio-info">
                    <view class="file-details">
                      <view class="file-name text-ellipsis">{{ audioFileName }}</view>
                      <view class="file-meta">
                        <text class="file-duration" v-if="audioDuration">{{
                          formatDuration(audioDuration)
                        }}</text>
                      </view>
                    </view>
                  </view>

                  <!-- 播放进度条 -->
                  <view class="progress-container">
                    <text class="time-label">{{ currentTime }}</text>
                    <progress
                      :percent="playPercent"
                      class="progress-bar"
                      stroke-width="4"
                      activeColor="#3e82f4"
                      backgroundColor="#E8E9EB"
                      border-radius="2"
                    />
                    <text class="time-label">{{ totalTime }}</text>
                  </view>

                  <!-- 播放控制按钮 -->
                  <view class="playback-controls">
                    <view
                      class="control-btn play-btn"
                      @click="togglePlayAudio"
                      :class="{ playing: isPlaying }"
                    >
                      <up-icon
                        v-if="!isPlaying"
                        name="play-right"
                        color="#ffffff"
                        size="23"
                      ></up-icon>
                      <up-icon v-else name="pause" color="#ffffff" size="23"></up-icon>
                    </view>
                    <view class="control-btn delete-btn" @click="deleteAudioFile">
                      <text>删除录音</text>
                    </view>
                  </view>
                </view>
                <view class="text-input-area">
                  <up-textarea
                    :cursorSpacing="100"
                    v-model="formData.audioText"
                    placeholder="补充文本内容"
                    autoHeight
                    maxlength="-1"
                    confirmType="done"
                  />
                  <view class="textarea-actions">
                    <ai-polish-btn v-if="shouldShowAudioPolishBtn" @click="onAudioTextAiPolish" />
                  </view>
                </view>

                <!-- AI润色后的倾听内容 -->
                <view v-if="formData.refinedAudioText" class="refined-text-area">
                  <view class="refined-label">AI润色后内容：</view>
                  <up-textarea
                    :cursorSpacing="100"
                    v-model="formData.refinedAudioText"
                    placeholder="AI润色后的内容将显示在这里"
                    autoHeight
                    maxlength="-1"
                    confirmType="done"
                    :disabled="false"
                  />
                </view>
              </view>
            </view>
          </up-form-item>

          <up-form-item
            v-if="visualMaps.analysis || formData.analysisEvaluation"
            label-position="top"
            :style="formItemStyle"
            label="分析评价"
            prop="analysisEvaluation"
          >
            <view class="analysis-section">
              <up-textarea
                :cursorSpacing="100"
                v-model="formData.analysisEvaluation"
                placeholder="请输入分析内容"
                border="none"
                autoHeight
                maxlength="-1"
                confirmType="none"
              />
              <view class="textarea-actions">
                <ai-polish-btn @click="onAnalysisAiPolish" />
                <smart-analysis-btn v-if="shouldShowSmartAnalysisBtn" @click="onSmartAnalysis" />
              </view>
            </view>
          </up-form-item>
          <up-form-item v-else :style="formItemStyle" label="" prop="analysisEvaluation">
            <view @click="visualMaps.analysis = true" class="selectable-content"> + 分析评价 </view>
          </up-form-item>
        </up-form>

        <!-- 核心经验匹配 -->
        <template v-if="params.id">
          <view class="matrixSelect-title">核心经验匹配</view>
          <view v-for="(item, index) in matric" :key="new Date().getTime() + index">
            <MatrixSelect
              v-if="item && matrix1List.length > 0"
              :matric="item"
              :matrixList="matrix1List"
              :index="index"
              @onDeleteMatrix="deleteMatric"
              @change="userChange"
            />
          </view>
          <view class="matrixSelect-add" @tap="addMatric">+ 新增核心经验</view>
        </template>
      </view>
    </view>
    <view class="addObservation-buttons">
      <view @click="onSaveOb" class="addObservation-button-save">保存</view>
    </view>
    <up-picker
      :show="isPickerShow"
      :defaultIndex="[0]"
      :columns="activeOption"
      @confirm="pickerConfirm"
      @cancel="isPickerShow = false"
      keyName="label"
    ></up-picker>

    <niceui-popup-select
      ref="popupSelectRef"
      :value="children"
      :columns="activeOption[0]"
      :selectValue="optionsList.ClassnameSelectValue"
      :option="{ label: 'label', value: 'id' }"
      @confirm="pickerConfirm"
      :multiple="true"
    ></niceui-popup-select>
  </base-layout>
</template>

<script setup>
import BaseLayout from '@/components/base-layout/base-layout.vue'
import NiceuiPopupSelect from './niceui-popup-select/niceui-popup-select.vue'
import MatrixSelect from './components/MatrixSelect.vue'
import { scenes, getInitFormData, childrenFilter, rules } from './data'
import Upload from '@/components/Upload/Upload.vue'
import { computed, ref, reactive, nextTick, onMounted, onBeforeUnmount } from 'vue'
import { useQueryParams } from '@/common/hooks/useQueryParams.js'
import { getclassList } from '@/api'
import { getDICT } from '@/utils'
import { getChildrenList } from '@/api/children'
import {
  addOneToOneListening,
  getOneToOneListeningDetail,
  updateOneToOneListeningRecord,
  refineAudioText,
  analysisByAi,
  updateCoreExpList
} from './api'
import { getAiPolishText } from './api/aiPolish'
import AiPolishBtn from './components/aiPolishBtn.vue'
import SmartAnalysisBtn from './components/smartAnalysisBtn.vue'
import dayjs from 'dayjs'
import { listByClass } from '@/api/game.js'
import { useRecordingWithASR } from './hook/useRecordingWithASR.js'
import coreExp from './coreExperience.js'

const disabled = ref(false)
// 单选选择控制器
const isPickerShow = ref(false)
// 多选选择控制器
const popupSelectRef = ref()
// 分析 支持 反思显示控制
const visualMaps = reactive({
  analysis: true,
  support: false,
  reflection: false
})

const togglePopUpSelect = (show) => {
  if (show) {
    // 确保选择器能够正确显示已经选中的值
    if (activeOptionKey.value === 'childIds' && formData.value.children.length > 0) {
      // 从children中提取childId作为预设值
      optionsList.ClassnameSelectValue = formData.value.children.map((child) => child.childId)
    }
    popupSelectRef.value.showPopup()
  } else {
    popupSelectRef.value.closePopup()
  }
}

/** URL query */
const { params } = useQueryParams('observationRecordScene', 'id')
/** 核心经验相关功能 */
const { matric, matrix1List, initCoreExp } = coreExp()
const isUserChange = ref(false) // 核心经验是否有变更
/** 表单 */
const formRef = ref()
/** 表单数据 */
const formData = ref(
  getInitFormData({
    observationType: params.value.observationRecordScene,
    audioText: '', // 录音转文字内容
    refinedAudioText: '', // AI润色后的倾听内容
    analysisEvaluation: '', // 分析评价
    representationResources: [], // 表征作品
    activityLocation: '', // 初始化为空字符串，表示未选择
    children: [], // 初始化为空数组
    childNames: [] // 初始化为空数组
  })
)

// 使用录音和语音转文字hook
const {
  // 状态
  audioRecordStatus,
  recordingTime,
  audioFileName,
  audioFileSize,
  audioResourceId,
  audioUrl,
  isPlaying,
  currentTime,
  totalTime,
  playPercent,
  audioDuration,

  // 方法
  initRecorderManager,
  startRecording,
  stopRecording,
  cancelRecording,
  deleteAudioFile,
  formatPlayTime,
  formatDuration
} = useRecordingWithASR(formData)

// 计算是否显示AI倾听内容润色按钮
const shouldShowAudioPolishBtn = computed(() => {
  return formData.value.audioText && formData.value.audioText.trim().length > 0
})

// 计算是否显示智能分析按钮
const shouldShowSmartAnalysisBtn = computed(() => {
  return formData.value.audioText && formData.value.audioText.trim().length > 0
})

/** 表征作品上传相关 */
const handleRepresentationUpload = (list) => {
  formData.value.representationResources.push(...list)
}

const handleRepresentationDelete = (_, index) => {
  formData.value.representationResources.splice(index, 1)
}

/** 选项 */
const optionsList = reactive({
  // 场景选项
  observationType: scenes,
  // 用户班级列表
  schoolClassId: [],
  // 当前班级学生列表
  childIds: [],
  // 班级区域列表
  activityLocation: [],
  // 观察对象选中的值
  ClassnameSelectValue: [],
  matrixList: []
})
/** 当前选项 */
const activeOptionKey = ref('')
/** 表单样式 */
const formItemStyle = `
  border-radius: 28rpx;
  padding: 0 28rpx;
  background: #fff;
  margin-bottom: 24rpx;
`

// 初始化RecorderManager
onMounted(() => {
  // 使用hook初始化录音管理器
  initRecorderManager()
})

// 组件卸载前清理资源
onBeforeUnmount(() => {
  // 如果正在播放音频，停止播放并销毁实例
  if (innerAudioContext.value) {
    if (isPlaying.value) {
      innerAudioContext.value.stop()
    }
    innerAudioContext.value.destroy()
    innerAudioContext.value = null
  }
})

// 播放录音 - 使用InnerAudioContext优化
const innerAudioContext = ref(null)

const togglePlayAudio = () => {
  if (!audioUrl.value) {
    uni.showToast({
      title: '录音文件不存在',
      icon: 'none'
    })
    return
  }

  // 如果正在播放，则停止
  if (isPlaying.value && innerAudioContext.value) {
    innerAudioContext.value.stop()
    isPlaying.value = false
    return
  }

  // 如果已存在音频上下文，先销毁
  if (innerAudioContext.value) {
    innerAudioContext.value.destroy()
    innerAudioContext.value = null
  }

  // 创建新的音频上下文
  innerAudioContext.value = uni.createInnerAudioContext()

  // 处理音频URL，兼容iOS中文路径问题
  let processedAudioUrl = audioUrl.value
  try {
    // 检查URL中是否包含中文字符
    if (/[\u4e00-\u9fa5]/.test(audioUrl.value)) {
      // 找到域名部分和文件路径部分
      const urlParts = audioUrl.value.split('/')
      const domain = urlParts.slice(0, 3).join('/') // https://domain.com
      const pathParts = urlParts.slice(3) // 剩余路径部分

      // 对路径部分进行编码
      const encodedPath = pathParts.map((part) => encodeURIComponent(part)).join('/')
      processedAudioUrl = `${domain}/${encodedPath}`
    }
  } catch (error) {
    console.error('音频URL编码失败:', error)
    processedAudioUrl = audioUrl.value
  }

  // 设置属性
  innerAudioContext.value.autoplay = false
  innerAudioContext.value.loop = false
  innerAudioContext.value.obeyMuteSwitch = false // 忽略手机静音开关
  innerAudioContext.value.src = processedAudioUrl

  // 监听事件
  innerAudioContext.value.onCanplay(() => {
    const duration = innerAudioContext.value.duration || audioDuration.value
    if (duration > 0) {
      totalTime.value = formatPlayTime(duration)
    }
  })

  innerAudioContext.value.onPlay(() => {
    isPlaying.value = true
  })

  innerAudioContext.value.onEnded(() => {
    isPlaying.value = false
    currentTime.value = '00:00'
    playPercent.value = 0
  })

  innerAudioContext.value.onStop(() => {
    isPlaying.value = false
    currentTime.value = '00:00'
    playPercent.value = 0
  })

  innerAudioContext.value.onError((res) => {
    console.error('播放录音失败:', res)
    isPlaying.value = false
    uni.showToast({
      title: '播放录音失败: ' + (res.errMsg || res.errCode),
      icon: 'none'
    })
  })

  // 监听播放进度
  innerAudioContext.value.onTimeUpdate(() => {
    const current = innerAudioContext.value.currentTime
    const duration = innerAudioContext.value.duration || audioDuration.value

    if (duration > 0) {
      const percent = Math.ceil((current / duration) * 100)
      playPercent.value = percent > 100 ? 100 : percent
      currentTime.value = formatPlayTime(current)
    }
  })

  // 开始播放
  innerAudioContext.value.play()
}

// AI文字润色分析评价
const onAnalysisAiPolish = async () => {
  if (!formData.value.analysisEvaluation) {
    uni.showToast({
      title: '请先输入内容',
      icon: 'none'
    })
    return
  }

  uni.showLoading({
    title: 'AI润色中...',
    mask: true
  })

  try {
    const data = {
      content: formData.value.analysisEvaluation
    }

    const res = await getAiPolishText(data)

    if (res.status === 0 && res.data) {
      formData.value.analysisEvaluation = res.data
      uni.showToast({
        title: 'AI润色完成',
        icon: 'success'
      })
    } else {
      uni.showToast({
        title: res.message || 'AI润色失败',
        icon: 'none'
      })
    }
  } catch (error) {
    uni.showToast({
      title: '网络错误，请重试',
      icon: 'none'
    })
  } finally {
    uni.hideLoading()
  }
}

// AI倾听内容润色
const onAudioTextAiPolish = async () => {
  if (!formData.value.audioText) {
    uni.showToast({
      title: '请先输入倾听内容',
      icon: 'none'
    })
    return
  }

  uni.showLoading({
    title: 'AI润色中...',
    mask: true
  })

  try {
    const data = {
      audioText: formData.value.audioText
    }

    const res = await refineAudioText(data)

    if (res.status === 0 && res.data.result) {
      formData.value.refinedAudioText = res.data.result
      uni.showToast({
        title: 'AI润色完成',
        icon: 'success'
      })
    } else {
      uni.showToast({
        title: res.message || 'AI润色失败',
        icon: 'none'
      })
    }
  } catch (error) {
    uni.showToast({
      title: '网络错误，请重试',
      icon: 'none'
    })
  } finally {
    uni.hideLoading()
  }
}

// 智能分析
const onSmartAnalysis = async () => {
  if (!formData.value.audioText) {
    uni.showToast({
      title: '请先录音或输入倾听内容',
      icon: 'none'
    })
    return
  }

  uni.showLoading({
    title: '智能分析中...',
    mask: true
  })

  try {
    const data = {
      activityLocation: formData.value.activityLocation,
      title: formData.value.title,
      audioText: formData.value.audioText
    }

    const res = await analysisByAi(data)

    if (res.status === 0 && res.data && res.data.result) {
      formData.value.analysisEvaluation = res.data.result
      uni.showToast({
        title: '智能分析完成',
        icon: 'success'
      })
    } else {
      uni.showToast({
        title: res.message || '智能分析失败',
        icon: 'none'
      })
    }
  } catch (error) {
    uni.showToast({
      title: '网络错误，请重试',
      icon: 'none'
    })
  } finally {
    uni.hideLoading()
  }
}

// 选择器确认选择
const pickerConfirm = ({ value, fullValue }) => {
  switch (activeOptionKey.value) {
    case 'observationRecordScene':
      formData.value.observationRecordScene = value[0].id
      break
    case 'schoolClassId':
      formData.value.schoolClassId = value[0].id
      updateChildList()
      updateClassAreaList()
      break
    case 'childIds':
      // 确保我们正确处理选择的倾听对象
      const selectedChildren = childrenFilter(fullValue)

      // 将处理后的数据设置到formData中
      formData.value.children = selectedChildren
      formData.value.childNames = selectedChildren.map((item) => item.childName)

      // 确保至少选择了一个倾听对象
      if (selectedChildren.length > 0) {
        formData.value.childId = selectedChildren[0].childId
      }

      togglePopUpSelect(false)
      break
    case 'activityLocation':
      // 直接使用数字型ID，避免字符串比较问题
      formData.value.activityLocation = Number(value[0].id)
      break
    default:
      break
  }

  isPickerShow.value = false
}

function getOptionLabelByKeyAndId(key, id) {
  try {
    if (id === undefined || id === null) {
      return ''
    }

    if (typeof id === 'string' || typeof id === 'number') {
      if (!optionsList[key]) {
        return id
      }
      const target = optionsList[key].find((item) => item.id == id)

      return target ? target.label : id
    }

    if (Array.isArray(id)) {
      if (!optionsList[key]) {
        return id.join(',')
      }
      const ts = id.map((m) => optionsList[key].find((item) => item.id == m)).filter(Boolean)
      if (ts.length) {
        return ts.map((v) => v.label).join(',')
      }
      return ''
    }

    return id
  } catch (error) {
    console.error('getOptionLabelByKeyAndId出错:', error, 'key:', key, 'id:', id)
    return ''
  }
}

const activeOption = computed(() => {
  return [optionsList[activeOptionKey.value] || []]
})

const multipleKeys = ['childIds']

function onShowPicker(key) {
  if (key === 'childIds' && !formData.value.schoolClassId) {
    uni.$u.toast('请先选择班级')
    return
  }

  if (key === 'activityLocation' && !formData.value.schoolClassId) {
    uni.$u.toast('请先选择班级')
    return
  }

  activeOptionKey.value = key
  if (multipleKeys.includes(key)) {
    nextTick(() => {
      togglePopUpSelect(true)
    })
  } else {
    isPickerShow.value = true
  }

  return
}

function updateChildList() {
  getChildrenList({
    current: 1,
    pageSize: 500,
    classId: formData.value.schoolClassId
  })
    .then((response) => {
      optionsList.childIds = response.data.map((d) => ({
        ...d,
        label: d.title,
        name: d.title,
        checked: false
      }))
    })
    .catch((err) => {
      console.error('获取班级学生列表失败:', err)
    })
}

function updateClassAreaList() {
  if (!formData.value.schoolClassId) {
    return Promise.resolve()
  }

  // 使用listByClass接口获取区域列表
  return listByClass({
    pageNo: 1,
    pageSize: 999, // 一次获取所有区域
    classId: formData.value.schoolClassId
  })
    .then((res) => {
      if (res.status === 0 && res.data.records) {
        // 处理区域列表数据
        optionsList.activityLocation = res.data.records.map((item) => ({
          id: item.id,
          label: item.area || item.areaAlias || `区域${item.id}`
        }))
      } else {
        console.error('获取区域列表失败:', res.message)
        optionsList.activityLocation = []
      }
    })
    .catch((err) => {
      console.error('调用区域列表接口失败:', err)
      optionsList.activityLocation = []
    })
}

// 保存
const onSaveOb = () => {
  // 手动验证活动地点
  if (
    !formData.value.activityLocation ||
    formData.value.activityLocation === '' ||
    formData.value.activityLocation === 0
  ) {
    uni.$u.toast('请输入活动地点')
    return
  }

  // 验证倾听内容 - 录音和文本至少需要一个
  if (!audioResourceId.value && !formData.value.audioText) {
    uni.$u.toast('请输入倾听内容或上传录音')
    return
  }

  formRef.value
    .validate()
    .then((valid) => {
      if (valid) {
        uni.showLoading({
          title: '保存中...',
          mask: true
        })

        // 确保 observationTime 包含时间
        if (formData.value.observationTime && !formData.value.observationTime.includes(':')) {
          // 只有日期没有时间，添加当前时间
          const now = dayjs()
          const currentTime = now.format('HH:mm:ss')
          formData.value.observationTime = `${formData.value.observationTime} ${currentTime}`
        }

        // 准备提交数据
        const submitData = {
          schoolClassId: formData.value.schoolClassId,
          childId: formData.value.childId,
          teacherId: uni.getStorageSync('USER_INFO').id, // 当前登录的教师ID
          activityLocation: formData.value.activityLocation, // 直接使用文本
          observationTime: formData.value.observationTime,
          title: formData.value.title,
          audioText: formData.value.audioText || '',
          refinedAudioText: formData.value.refinedAudioText || '',
          analysisEvaluation: formData.value.analysisEvaluation || '',
          representationResourceIds: formData.value.representationResources.map((item) => item.id),
          audioResourceId: audioResourceId.value || null
        }

        // 修改为使用updateOneToOneListeningRecord
        const fn = params.value.id ? updateOneToOneListeningRecord : addOneToOneListening

        if (params.value.id) {
          submitData.id = params.value.id
        }

        fn(submitData)
          .then(async (r) => {
            if (r.status === 0) {
              // 如果是编辑模式，保存核心经验
              if (params.value.id) {
                const coreExpSaved = await onSaveCore()
                if (!coreExpSaved) {
                  uni.hideLoading()
                  return
                }
              }

              uni.showToast({
                title: '保存成功',
                icon: 'success'
              })

              // 先设置刷新标记，确保返回时能刷新列表
              uni.setStorageSync('isRef', true)

              setTimeout(() => {
                uni.navigateBack({
                  delta: 1 // 返回上一页
                })
              }, 300)
            } else {
              uni.showToast({
                title: r.message || '保存失败',
                icon: 'none'
              })
            }
            uni.hideLoading()
          })
          .catch((err) => {
            uni.hideLoading()
            uni.showToast({
              title: err.message || '保存失败',
              icon: 'none'
            })
          })
      } else {
        uni.$u.toast('有未填项，请检查！')
      }
    })
    .catch((e) => {
      // 处理验证错误
      uni.$u.toast('校验失败: ' + (e.message || '请检查表单'))
    })
}

/**
 * 初始化当前页面数据
 */
function initPageData() {
  try {
    /** 初始化字典 */
    getDICT('all')
      .then((dics) => {
        try {
          const ObservationRecordSceneEnumDesc = dics['ObservationRecordSceneEnumDesc']
          if (ObservationRecordSceneEnumDesc) {
            optionsList.observationRecordScene = Object.keys(
              dics['ObservationRecordSceneEnumDesc']
            ).map((key) => {
              return {
                label: ObservationRecordSceneEnumDesc[key],
                id: key
              }
            })
          }

          getclassList()
            .then((r) => {
              try {
                const curClassid = uni.getStorageSync('USER_INFO').currentClassId
                optionsList.schoolClassId = r.data.map((d) => ({
                  ...d,
                  label: d.title
                }))
                formData.value.schoolClassId = optionsList.schoolClassId.find(
                  (item) => item.id == curClassid
                )?.id

                if (formData.value.schoolClassId) {
                  updateChildList()
                  updateClassAreaList()
                }
              } catch (error) {
                console.error('班级初始化错误:', error)
              }
            })
            .catch((err) => {
              console.error('获取班级列表错误:', err)
            })

          if (params.value.id) {
            // 获取详情，使用新接口 getOneToOneListeningDetail
            getOneToOneListeningDetail(params.value.id)
              .then((r) => {
                try {
                  if (r.status === 0 && r.data) {
                    const data = r.data

                    // 处理基础数据
                    formData.value = {
                      ...formData.value,
                      schoolClassId: data.schoolClassId,
                      childId: data.childId,
                      childNames: data.child ? [data.child.title] : [],
                      activityLocation: data.activityLocation || '', // 直接使用文本字段
                      observationTime: dayjs(data.observationTime).format('YYYY-MM-DD'),
                      title: data.title,
                      audioText: data.audioText || '',
                      refinedAudioText: data.refinedAudioText || '',
                      analysisEvaluation: data.analysisEvaluation || '',
                      representationResources: data.representationResources || []
                    }

                    // 处理录音信息
                    if (data.audioResource) {
                      audioResourceId.value = data.audioResource.id
                      audioUrl.value = data.audioResource.uri
                      audioFileName.value = data.audioResource.filename
                      audioFileSize.value = `${Math.round(data.audioResource.size / 1024)}KB`
                      audioRecordStatus.value = 'completed'

                      // 设置音频时长
                      if (data.audioResource.duration) {
                        audioDuration.value = Math.ceil(data.audioResource.duration / 1000)
                        totalTime.value = formatPlayTime(audioDuration.value)
                      }
                    }

                    // 更新子列表和区域列表
                    updateChildList()
                    updateClassAreaList()

                    // 初始化核心经验
                    initCoreExp(params.value.id)

                    // 更新选中的儿童
                    if (data.child) {
                      formData.value.children = [
                        {
                          childId: data.childId,
                          childName: data.child.title
                        }
                      ]
                      formData.value.childNames = [data.child.title]
                      optionsList.ClassnameSelectValue = [data.childId]
                    }
                  }
                } catch (error) {
                  console.error('获取倾听详情错误:', error)
                  uni.showToast({
                    title: '获取详情失败',
                    icon: 'none'
                  })
                }
              })
              .catch((err) => {
                console.error('获取倾听详情接口错误:', err)
                uni.showToast({
                  title: '网络错误，请重试',
                  icon: 'none'
                })
              })
          }
        } catch (innerError) {
          console.error('字典处理错误:', innerError)
        }
      })
      .catch((err) => {
        console.error('获取字典错误:', err)
      })
  } catch (outerError) {
    console.error('初始化页面数据错误:', outerError)
  }
}

const calendarConfirm = () => {
  // uni-datetime-picker 直接返回完整的日期时间字符串
  // 无需额外处理，直接使用即可
}

initPageData()

// 核心经验相关方法
// 用户变更核心经验
const userChange = () => {
  isUserChange.value = true
}

// 删除核心经验
const deleteMatric = (index) => {
  isUserChange.value = true
  matric.value.splice(index, 1)
}

// 添加核心经验
const addMatric = () => {
  isUserChange.value = true
  matric.value.push({
    relId: params.value.id,
    matchType: 'onetoone',
    matrix1Id: null,
    matrix1Name: null,
    matrix2Id: null,
    matrix2Name: null,
    matrix3Id: null,
    matrix3Name: null,
    targetId: null,
    targetName: null,
    isEdit: true
  })
}

// 保存核心经验
const onSaveCore = async () => {
  if (isUserChange.value) {
    let data = {
      relId: Number(params.value.id),
      matchType: 'onetoone',
      list: []
    }
    data.list = matric.value.map((item) => {
      item.schoolId = uni.getStorageSync('USER_INFO').currentSchoolId
      return {
        relId: Number(params.value.id),
        matchType: 'onetoone',
        matrix1Id: item.matrix1Id,
        matrix1Name: item.matrix1Name,
        matrix2Id: item.matrix2Id,
        matrix2Name: item.matrix2Name,
        matrix3Id: item.matrix3Id,
        matrix3Name: item.matrix3Name,
        targetId: item.targetId,
        targetName: item.targetName
      }
    })
    try {
      let res = await updateCoreExpList(data)
      if (res.status != 0) {
        uni.$u.toast(res?.message || '核心经验保存失败')
        return false
      }
      return true
    } catch (err) {
      console.log(err)
      uni.$u.toast('核心经验保存失败')
      return false
    }
  }
  return true
}
</script>

<style lang="scss" scoped>
.placeholder {
  color: rgb(192, 196, 204);
  font-size: 30rpx;
  font-weight: 400;
}
.addObservation-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  width: calc(100vw - 48rpx);
  padding: 12rpx 24rpx;
  padding-bottom: calc(env(safe-area-inset-bottom) + 12rpx);
  height: 80rpx;
  line-height: 80rpx;
  background: #fff;

  .addObservation-button-save {
    background: #3e82f4;
    color: #fff;
    width: 100%;
    height: 100%;
    text-align: center;
    border-radius: 44rpx;
  }
}

.addObservation {
  padding: 0 32rpx 202rpx 32rpx;
  // #ifdef H5
  padding-bottom: 136rpx;
  // #endif
  &-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 88rpx;
    font-size: 24rpx;

    .addObservation-title-right-box {
      display: flex;
      align-items: center;
      font-size: 26rpx;
      font-weight: 400;
      color: rgba(128, 128, 128, 1);
      image {
        margin-right: 10rpx;
        margin-top: 5rpx;
      }
    }

    .addObservation-title-right {
      width: 32rpx;
      height: 32rpx;
      margin-left: 12rpx;
    }
  }
}

.matric-title {
  height: 80rpx;
  line-height: 80rpx;
}

.selectable-content {
  width: 100%;
  height: 110rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  color: #3f79ff;
  font-size: 30rpx;
  font-weight: 500;
  border-radius: 28rpx;
}

.recorder-container {
  width: 100%;
}

.content-section,
.analysis-section {
  width: 100%;
}

.textarea-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-top: 10rpx;
  flex-wrap: wrap;
}

.add-works {
  color: #3f79ff;
  font-size: 28rpx;
  padding: 10rpx 0;
}

/* 录音相关样式 */
.record-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  width: 100%;
  height: 90rpx;
  background-color: #f0f5ff;
  border-radius: 8rpx;
  color: #3e82f4;
  font-size: 28rpx;

  image {
    width: 36rpx;
    height: 36rpx;
  }
}

.recording-section {
  background-color: #f8f8f8;
  padding: 20rpx;
  border-radius: 8rpx;
  margin-bottom: 20rpx;

  .recording-time {
    font-size: 48rpx;
    color: #3e82f4;
    text-align: center;
    font-weight: bold;
  }

  .recording-status {
    font-size: 24rpx;
    color: #666;
    text-align: center;
    margin: 10rpx 0 20rpx;
    display: flex;
    justify-content: center;
    align-items: center;

    .recording-dots {
      display: flex;

      .dot {
        animation: dotFlashing 1.5s infinite;
        animation-fill-mode: both;
        margin-left: 2rpx;

        &:nth-child(2) {
          animation-delay: 0.5s;
        }

        &:nth-child(3) {
          animation-delay: 1s;
        }
      }
    }
  }

  .recording-actions {
    display: flex;
    justify-content: space-between;
    margin: 0 40rpx;

    .stop-btn {
      background-color: #3e82f4;
      color: #fff;
      padding: 12rpx 30rpx;
      border-radius: 30rpx;
      font-size: 28rpx;
    }

    .cancel-btn {
      background-color: #f5f5f5;
      color: #666;
      padding: 12rpx 30rpx;
      border-radius: 30rpx;
      font-size: 28rpx;
      border: 1px solid #ddd;
    }
  }

  .recording-tip {
    font-size: 22rpx;
    color: #999;
    text-align: center;
    margin-top: 16rpx;
  }
}

.audio-result {
  background-color: #fff;
  border-radius: 8rpx;
  margin-bottom: 20rpx;

  .audio-player {
    padding: 24rpx;
    background-color: #f8f9fa;
    border-radius: 16rpx;
    margin-bottom: 20rpx;

    .audio-info {
      margin-bottom: 24rpx;

      .file-details {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8rpx;

        .file-name {
          font-size: 30rpx;
          font-weight: 600;
          color: #333;
          text-align: center;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 100%;
        }

        .file-meta {
          display: flex;
          align-items: center;
          gap: 16rpx;

          .file-size,
          .file-duration {
            font-size: 24rpx;
            color: #666;
          }

          .file-duration {
            &::before {
              content: '•';
              margin-right: 8rpx;
              color: #ccc;
            }
          }
        }
      }
    }

    .progress-container {
      display: flex;
      align-items: center;
      margin-bottom: 32rpx;
      gap: 16rpx;

      .time-label {
        font-size: 24rpx;
        color: #666;
        min-width: 80rpx;
        text-align: center;
      }

      .progress-bar {
        flex: 1;
      }
    }

    .playback-controls {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 40rpx;
      margin-bottom: 16rpx;

      .control-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;

        &:active {
          transform: scale(0.95);
        }

        &.play-btn {
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
          background-color: #3e82f4;

          &.playing {
            background-color: #ff6b6b;
            animation: pulse 2s infinite;
          }
        }

        &.delete-btn {
          padding: 12rpx 24rpx;
          background-color: #ff6b6b;
          border-radius: 24rpx;
          gap: 8rpx;

          text {
            font-size: 26rpx;
            color: #ffffff;
            font-weight: 500;
          }
        }
      }
    }

    .control-tip {
      font-size: 24rpx;
      color: #999;
      text-align: center;
    }
  }
}

@keyframes dotFlashing {
  0% {
    opacity: 0.2;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.2;
  }
}

.audio-prep {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  width: 100%;

  .filename-input {
    width: 100%;
    margin-bottom: 16rpx;

    .filename-hint {
      font-size: 24rpx;
      color: #999;
      display: block;
      margin-top: 6rpx;
    }
  }

  .record-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10rpx;
    width: 100%;
    height: 90rpx;
    background-color: #f0f5ff;
    border-radius: 8rpx;
    color: #3e82f4;
    font-size: 28rpx;

    image {
      width: 36rpx;
      height: 36rpx;
    }
  }
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.play-btn {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #3e82f4;
  transition: all 0.3s;
}

.play-btn.playing {
  background-color: #ff6b6b;
  animation: pulse 2s infinite;
}

/* 新增文本输入区域样式 */
.text-input-area {
  margin-top: 20rpx;
}

/* AI润色后内容区域样式 */
.refined-text-area {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border-left: 4rpx solid #3e82f4;

  .refined-label {
    font-size: 26rpx;
    color: #3e82f4;
    font-weight: 600;
    margin-bottom: 12rpx;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

// 核心经验匹配样式
.matrixSelect-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 24rpx;
  margin-top: 32rpx;
}

.matrixSelect-add {
  width: 100%;
  height: 88rpx;
  border: 2rpx dashed #e6e6e6;
  border-radius: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #999999;
  background-color: #fafafa;
  margin-top: 24rpx;
}
</style>
