<template>
  <view class="class-item">
    <view class="info" @click="goCircle">
      <view class="tit">
        <view>
          <text class="name">{{ classData.title }}</text>
          <text>{{ classData.extra.masterTitle }}</text>
        </view>
        <up-icon name="more-dot-fill" color="rgba(128, 128, 128, 1)"></up-icon>
      </view>
      <view class="info-itm">
        {{ classData.childCount || 0 }}位 学生
        <!-- <text class="line">｜</text> 4条 观察记录-->
        <text class="line">｜</text> {{ classData.postCount || 0 }}条 班级圈
      </view>
      <!-- <view class="info-itm">
				<up-tag text="薄弱领域" plain plainFill borderColor="#fff" size="mini"> </up-tag>
				数学、社会情感、语言
			</view>
			<view class="info-itm"> 活动： 2/10 <text class="line">｜</text> 评价：2/10 </view> -->
    </view>
  </view>
</template>

<script setup>
  import { reactive, ref } from 'vue'
  import { onShow } from '@dcloudio/uni-app'

  const props = defineProps({
    // 班级
    classData: {
      type: Object,
      required: true,
    },
  })

  //跳转圈子
  const goCircle = () => {
    const id = props.classData.id
    const schoolId = props.classData.schoolId
    uni.navigateTo({
      url: `/subPages/classes/circle?id=${id}&schoolId=${schoolId}`,
    })
  }
</script>

<style lang="scss" scoped>
  .class-item {
    @include selfshaow;
    font-family: PingFangSC-regular;
    box-sizing: border-box;
    margin-top: 24upx;
    width: 100%;
    background-color: #fff;
    border-radius: 28upx;
    padding: 28upx;
    font-size: 24upx;
    line-height: 28upx;
    color: rgba(128, 128, 128, 1);

    .line {
      color: rgba(204, 204, 204, 1);
    }

    .info {
      .tit {
        @include selflex(x, between, center);
        line-height: 36upx;
        width: 100%;

        .name {
          font-size: 30upx;
          font-weight: 600;
          letter-spacing: 0upx;
          color: rgba(51, 51, 51, 1);
          margin-right: 8upx;
        }
      }

      .info-itm {
        padding-top: 14upx;
      }
    }
  }
</style>