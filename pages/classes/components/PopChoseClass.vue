<template>
	<Popup :show="show" @close="show = false">
		<view class="wrap">
			<view class="tit">
				<text>请选择幼儿园 </text>
				<text class="sure" @click="sure">确定</text>
			</view>
			<view class="cell">
				<up-radio-group iconPlacement="right" v-model="_id">
					<view class="class-item" v-for="(item, idx) in schoolArr" :key="idx" @click="_id = item.id">
						<up-image
							width="88upx"
							height="88upx"
							class="img"
							mode="scaleToFill"
							radius="8"
							src="https://cdn.uviewui.com/uview/album/1.jpg"
							:lazy-load="true"
						></up-image>
						<view class="info">
							<text class="tit">{{ item.title }}</text>
							<text class="up-line-2 cont"> {{item.classCount}}个班级 </text>
						</view>
						<view class="radio">
							<up-radio shape="circle" :name="item.id"></up-radio>
						</view>
					</view>
				</up-radio-group>
			</view>
		</view>
	</Popup>
</template>

<script setup>
import { reactive, ref, toRaw } from 'vue'
import { onShow, onShareAppMessage } from '@dcloudio/uni-app'
import { checks } from '@/utils/index.js'
import { schoolList } from '@/api/classApi.js'

const emit = defineEmits(['chose'])
const show = ref(false)
const schoolArr = ref([])
const _id = ref(null)

const open = id => {
	show.value = true
	// 使用传入的ID或从本地存储获取当前选中的幼儿园ID
	_id.value = id || uni.getStorageSync('CURRENT_SCHOOL_ID')
	initList()
}
const sure = () => {
	show.value = false
	const findSchool = schoolArr.value.find(item => item.id === _id.value)
	if (findSchool) {
		emit('chose', _id.value, toRaw(findSchool))
	}
}
const initList = async () => {
	const { data } = await schoolList()
	schoolArr.value = data
}

defineExpose({
	open,
})
</script>

<style lang="scss" scoped>
.wrap {
	& > .tit {
		@include selflex(x, between, center);
		font-weight: 600;
		letter-spacing: 0px;
		line-height: 47upxpx;
		color: rgba(51, 51, 51, 1);
		font-size: 32upx;
		text-align: center;
		font-family: PingFangSC-regular;
		padding-bottom: 37upx;

		.sure {
			font-size: 30upx;
			color: rgba(63, 121, 255, 1);
		}
	}

	.cell {
		min-height: 550upx;
		overflow: auto;
	}

	.class-item {
		@include selfshaow;
		@include selflex(x, start, center);
		font-family: PingFangSC-regular;
		box-sizing: border-box;
		margin-top: 20upx;
		width: 100%;
		height: 136upx;
		background-color: #fff;
		border-radius: 28upx;
		padding: 28upx;

		.img {
			width: 44upx;
			height: 44upx;
			flex-shrink: 0;
		}

		.info {
			margin-left: 20upx;
			flex-grow: 1;

			.tit {
				line-height: 46upx;
				color: rgba(0, 0, 0, 1);
				font-size: 30upx;
				text-align: left;
			}

			.cont {
				margin-top: 14upx;
				color: rgba(128, 128, 128, 1);
				font-size: 24upx;
				text-align: left;
			}
		}

		.radio {
			width: 50upx;

			::v-deep .u-radio-group--row {
				width: 50upx;
			}
		}
	}
}
</style>
