<template>
  <view class="layout">
    <u-gap :height="StatusBarHeight"></u-gap>
    <!-- 幼儿园选择器 -->
    <view class="title" @click="openSchoolSelector" :style="{ paddingRight: rightSafeArea }">
      <text class="titinfo">{{ title }}</text>
      <image src="/static/icon/arrow.png" class="img" mode="scaleToFill"></image>
    </view>
    <!-- 班级列表 -->
    <view class="class-wrap">
      <ClassesItem v-for="(item, index) in classArr" :key="index" :classData="item" />
    </view>
    <up-loadmore :status="loading" loadmore-text="上拉或点击加载更多" @loadmore="loadMore" />
  </view>
  <PopChoseClass ref="popRef" @chose="onSchoolSelected" />
</template>

<script setup>
  import { ref, onMounted, watch } from 'vue'
  import { onShow, onShareAppMessage, onReachBottom, onLoad} from '@dcloudio/uni-app'
  import { checks, sharePageObj } from '@/utils/index.js'
  import ClassesItem from './components/ClassesItem.vue'
  import PopChoseClass from './components/PopChoseClass.vue'
  import useSystemBar from '@/utils/useSystemBar'
  import { useSchool } from '@/composables/useSchool'
  import { useClassList } from '@/composables/useClassList'

  // 系统状态栏高度
  const { StatusBarHeight } = useSystemBar()

  // 幼儿园选择器引用
  const popRef = ref(null)

  // 幼儿园相关状态和方法
  const { currentSchoolId, title, setSchool, getSchoolTitle } = useSchool()

  // 班级列表相关状态和方法
  const { classArr, loading, paramsPage, getClassArr, loadMore } = useClassList(currentSchoolId)

  // 微信小程序胶囊按钮右侧安全区域
  const rightSafeArea = ref('0')

  // 生命周期钩子
  onMounted(() => {
    // #ifdef MP-WEIXIN
    const { width } = uni.getMenuButtonBoundingClientRect()
    rightSafeArea.value = width + 20 + 'px'
    // #endif
  })

  onLoad(async() => {
    await getSchoolTitle()
    await getClassArr()
    if (classArr.value && classArr.value.length === 1) {
      const id = classArr.value[0].id
      const schoolId = classArr.value[0].schoolId
      uni.navigateTo({
        url: `/subPages/classes/circle?id=${id}&schoolId=${schoolId}`,
      })
    }
  })
  
  // 页面显示时初始化数据
  onShow(() => {
    checks()
  })

  // 触底加载更多
  onReachBottom(loadMore)

  // 打开幼儿园选择器
  const openSchoolSelector = () => popRef.value?.open(currentSchoolId.value)

  // 选择幼儿园后的回调
  const onSchoolSelected = (id, school) => {
    if (id) {
      setSchool(id, school.title)
      paramsPage.currentPage = 1
      getClassArr()
    }
  }

  // 小程序分享
  onShareAppMessage(() => sharePageObj())
</script>

<style lang="scss" scoped>
  .layout {
    box-sizing: border-box;
    min-height: 100vh;
    overflow: auto;
    background: #f5f5f5 url('https://c.mypacelab.com/vxmp/img/history_background_3x.png') no-repeat center top;
    background-size: contain;
    font-family: PingFangSC-regular;
  }

  .title {
    box-sizing: border-box;
    width: 100%;
    @include selflex(x, start, center);
    margin-top: 30upx;
    font-size: 34upx;
    color: #333;
    padding: 0 30upx;
  }

  .titinfo {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .img {
    width: 30upx;
    height: 30upx;
    margin-left: 18upx;
  }

  .class-wrap {
    padding: 0 30upx 50upx;
  }

  button {
    padding: 10px 20px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
  }
</style>