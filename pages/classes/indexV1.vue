<template>
  <BaseLayout2
    nav-title="班级"
    :scrollEnabled="true"
    :content-style="{ padding: '0 0 50rpx 0' }"
    :footerStyle="{ display: 'none' }"
    :autoBack="false"
  >
    <view style="min-height: 100vh">
      <view class="class-bar" @click="isClassShow = true">
        <view class="class-bar-img">
          <image src="/static/tabbar/activeClass.png"></image>
        </view>
        <view class="class-bar-right">
          <view class="class-bar-right-text">
            <view class="class-bar-right-text-top">
              <text>{{ curInfo?.title || '-' }}</text>
              <text>{{ curInfo?.schoolTitle || '-' }}</text>
            </view>
            <view class="class-bar-right-text-bottom"
              >班主任: {{ curInfo?.extra?.masterTitle || '-' }}</view
            >
          </view>
          <view class="class-bar-right-icon">
            <image class="change" src="/static/game/change.svg" />
            <!-- <image class="change" src="/static/icon/edit.png" /> -->
          </view>
        </view>
      </view>
      <view class="list">
        <view class="row" v-for="(it, i) in list" :key="i">
          <view class="row-item" v-for="(item, index) in it" :key="index" @tap="gotoPage(item)">
            <view class="row-left">
              <image :src="item.icon" />
              <text>{{ item.title }}</text>
            </view>
            <view class="row-right">
              <text class="row-right-count">{{ item.count }}</text>
              <up-icon name="arrow-right" size="30rpx" />
            </view>
          </view>
        </view>

        <view class="bg" />
      </view>

      <!-- 幼儿出勤区域 -->
      <view class="attendance-section">
        <view class="attendance-item" @tap="gotoPage(attendanceData)">
          <view class="row-left">
            <image :src="attendanceData.icon" />
            <text>{{ attendanceData.title }}</text>
          </view>
          <view class="row-right">
            <text class="row-right-count">{{ attendanceData.count }}</text>
            <up-icon name="arrow-right" size="30rpx" />
          </view>
        </view>

        <!-- 今日出勤统计 -->
        <view class="today-stats">
          <text class="stats-title">今日</text>
          <view class="stats-items">
            <view class="stats-item">
              <text class="stats-label">已入园</text>
              <text class="stats-value attended">{{ todayAttendanceStats.presentCount }}</text>
            </view>
            <view class="stats-item">
              <text class="stats-label">事假</text>
              <text class="stats-value leave">{{ todayAttendanceStats.personalLeaveCount }}</text>
            </view>
            <view class="stats-item">
              <text class="stats-label">病假</text>
              <text class="stats-value sick">{{ todayAttendanceStats.sickLeaveCount }}</text>
            </view>
            <view class="stats-item">
              <text class="stats-label">停课</text>
              <text class="stats-value suspend">{{ todayAttendanceStats.suspensionCount }}</text>
            </view>
          </view>
        </view>

        <!-- 出勤打卡按钮 -->
        <view class="attendance-btn" @tap="gotoPageNew"> 出勤打卡 </view>
      </view>
    </view>
  </BaseLayout2>
  <up-picker
    ref="uPicker"
    :show="isClassShow"
    :columns="columns"
    :defaultIndex="[0, 0]"
    keyName="title"
    :loading="pickerLoading"
    :closeOnClickOverlay="true"
    @confirm="changeClass"
    @change="classChange"
    @cancel="isClassShow = false"
    @close="isClassShow = false"
  />
</template>

<script setup>
import { reactive, ref, onMounted } from 'vue'
import { onShow, onShareAppMessage, onReachBottom, onLoad } from '@dcloudio/uni-app'
import { checks, sharePageObj, useCurrentClassId, headleLogger } from '@/utils/index.js'
import BaseLayout2 from '@/components/base-layout/base-layout2.vue'
import config from '@/common/config.js'
import { getNewclassStati } from '@/api/classApi.js'
import { getSchoolList, getclassList, getChildrenAttendanceList } from '@/api'
import { getClassDynamic } from '@/api/api.js'
import dayjs from 'dayjs'
const domainName = config[config.DEFINE_ENV].WEB_DOMAIN_NAME // 域名替换
let schoolId = ref(uni.getStorageSync('USER_INFO').currentSchoolId)
let id = ref(uni.getStorageSync('USER_INFO').currentClassId)
const isClassShow = ref(false) // 班级选择
let columns = reactive([[]]) // 班级列表
let pickerLoading = ref(false)
const list = reactive({
  // 幼儿动态
  row0: [
    {
      title: '幼儿动态',
      key: 'childrenAttendance',
      count: '-',
      icon: `/static/icon/video1.png`,
      router: '/childrenDynamic/class'
    }
  ],
  row1: [
    {
      title: '班级圈',
      key: 'postCount',
      count: '-',
      icon: `${domainName}/vxmp/img/class/class_pyq.svg`,
      router: `/subPages/classes/circleV1`,
      type: '班级圈'
    }
  ],
  row2: [
    {
      title: '儿童',
      key: 'childCount',
      count: '-',
      icon: `${domainName}/vxmp/img/class/class_student.svg`,
      router: `/subPages/classes/circleV1`,
      type: '儿童'
    },
    {
      title: '家长',
      key: 'parentCount',
      count: '-',
      icon: `${domainName}/vxmp/img/class/class_parent.svg`,
      router: `/parent/index`
    },
    {
      title: '教师',
      key: 'teacherCount',
      count: '-',
      icon: `${domainName}/vxmp/img/class/class_teacher.svg`,
      router: `/subPages/classes/circleV1`,
      type: '教师'
    }
  ],
  // TODO: 9月份出来
  // row3: [
  //   {
  //     title: "周计划",
  //     key: "weekPlanCount",
  //     count: "0",
  //     icon: `${domainName}/vxmp/img/class/class_week.svg`,
  //     router: "",
  //   },
  // ],
  row4: [
    {
      title: '区域',
      key: 'areaCount',
      count: '-',
      icon: `${domainName}/vxmp/img/class/class_region.svg`,
      router: '/regionalManagement/index'
    },
    {
      title: '材料',
      key: 'materialCount',
      count: '-',
      icon: `${domainName}/vxmp/img/class/class_material.svg`,
      router: '/classMaterials/index'
    }
  ],
  row5: []
})

// 幼儿出勤数据
const attendanceData = reactive({
  title: '幼儿出勤',
  key: 'childrenAttendance',
  count: '出勤记录',
  icon: `/static/icon/kq.svg`,
  router: '/childrenAttendance/index'
})

// 今日出勤统计数据
const todayAttendanceStats = reactive({
  presentCount: 0,
  personalLeaveCount: 0,
  sickLeaveCount: 0,
  suspensionCount: 0
})

const curInfo = ref({})
// 跳转
const gotoPage = (item) => {
  console.log(curInfo.value)
  if (!item.router) {
    uni.$u.toast('敬请期待！')
    return
  }
  switch (item.title) {
    case '家长':
      uni.navigateTo({
        url: `${item.router}?classId=${curInfo.value?.id}`
      })
      break
    case '区域':
      uni.navigateTo({
        url: `${item.router}?className=${curInfo.value?.title}&classId=${curInfo.value?.id}`
      })
      break
    default:
      uni.navigateTo({
        url: `${item.router}?id=${id.value}&schoolId=${schoolId.value}&type=${item.title}`
      })
      break
  }
}
const gotoPageNew = () => {
  uni.navigateTo({
    url: '/childrenAttendance/addAttendance'
  })
}
// 页面更新
// const UpdataPage = (data) => {
//   curInfo.value = data;

//   const row = Object.values(list);
//   console.log(data, "111111111111111111111111111111");
//   // row.forEach((key) => {
//   //   key.forEach((item) => {
//   //     item.count = data[item.key] || 0;
//   //   });
//   // });
// };

// 班级改变
const changeClass = async (e) => {
  const { columnIndex, value } = e
  id.value = value[1].id
  await useCurrentClassId(value[1].id)
  curInfo.value = value[1]
  await getClassInfo(value[1]?.id)
  // 确保学校信息同步更新
  if (value[0]) {
    // 同步更新学校名称到本地存储
    uni.setStorageSync('CURRENT_SCHOOL_TITLE', value[0].title)
  }
  isClassShow.value = false
}

// 班级选择
const classChange = async (e) => {
  const { columnIndex, value } = e
  console.log(e)

  if (columnIndex === 0) {
    pickerLoading.value = true
    await useCurrentClassId(null, value[columnIndex].id)
    // 同步更新学校名称到本地存储，确保数据一致性
    uni.setStorageSync('CURRENT_SCHOOL_TITLE', value[columnIndex].title)
    await classList()
    await getClassInfo(uni.getStorageSync('USER_INFO').currentClassId)
    pickerLoading.value = false
  }
}

// 初始化页面
const initPage = async () => {
  try {
    columns.length = 0
    // 学校列表
    const res = await getSchoolList()
    if (res.status == 0) {
      columns[0] = res.data
      changeColumnsPosi(columns[0], uni.getStorageSync('USER_INFO').currentSchoolId)
      // 班级数据
      await classList()
      // 页面数据
      await getClassInfo(uni.getStorageSync('USER_INFO').currentClassId)
    }
    // const
  } catch (error) {
    console.error('获取班级失败', error)
    uni.showToast({ title: '获取班级失败', icon: 'none' })
  }
}

// 请求班级数据
const classList = async (sid) => {
  const classRes = await getclassList()
  if (classRes.status == 0) {
    curInfo.value = classRes.data.find((item) => item.id == id.value)

    if (columns.length > 1) {
      columns.splice(1) // 删除第二列及之后的所有列
    }
    columns[1] = classRes.data
    changeColumnsPosi(columns[1], uni.getStorageSync('USER_INFO').currentClassId)
  }
}

// 页面数据
const getClassInfo = async (cid) => {
  const params = {
    classId: cid
  }
  const res = await getNewclassStati(params)
  if (res.status == 0) {
    // 确保只有两列：学校列和班级列
    console.log(res)
    const data = Object.values(list)
    data.forEach((key) => {
      key.forEach((item) => {
        item.count = res.data[item.key] || 0
      })
    })

    // 幼儿出勤数据保持显示"出勤记录"
    attendanceData.count = '出勤记录'
  }

  // 获取幼儿动态数量
  await getChildrenDynamicCount(cid)

  // 获取今日出勤统计
  await getTodayAttendanceStats(cid)
}

// 获取幼儿动态数量
const getChildrenDynamicCount = async (cid) => {
  try {
    // 获取当前月的第一天和最后一天
    const today = dayjs()
    const firstDay = today.startOf('month').format('YYYY-MM-DD')
    const lastDay = today.endOf('month').format('YYYY-MM-DD')

    const params = {
      classId: parseInt(cid),
      beginDate: firstDay,
      endDate: lastDay
    }

    const res = await getClassDynamic(params)

    if (res.status === 0 && res.data) {
      // 统计总记录数
      let totalCount = 0
      res.data.forEach(childData => {
        totalCount += childData.recordList.length
      })

      // 更新幼儿动态的数量显示
      const childrenDynamicItem = list.row0.find(item => item.key === 'childrenAttendance')
      if (childrenDynamicItem) {
        childrenDynamicItem.count = totalCount
      }
    }
  } catch (error) {
    console.error('获取幼儿动态数量失败:', error)
    // 出错时显示默认值
    const childrenDynamicItem = list.row0.find(item => item.key === 'childrenAttendance')
    if (childrenDynamicItem) {
      childrenDynamicItem.count = 0
    }
  }
}

// 获取今日出勤统计数据
const getTodayAttendanceStats = async (cid) => {
  try {
    const today = dayjs().format('YYYY-MM-DD')
    const res = await getChildrenAttendanceList({
      attendanceDate: today,
      classId: cid
    })

    if (res && res.data) {
      todayAttendanceStats.presentCount = res.data.presentCount || 0
      todayAttendanceStats.personalLeaveCount = res.data.personalLeaveCount || 0
      todayAttendanceStats.sickLeaveCount = res.data.sickLeaveCount || 0
      todayAttendanceStats.suspensionCount = res.data.suspensionCount || 0
    }
  } catch (error) {
    console.error('获取今日出勤统计失败', error)
    // 出错时重置为0
    todayAttendanceStats.presentCount = 0
    todayAttendanceStats.personalLeaveCount = 0
    todayAttendanceStats.sickLeaveCount = 0
    todayAttendanceStats.suspensionCount = 0
  }
}

// 改变选择器的位置，把当前的id替换成第一个
const changeColumnsPosi = (arr, id) => {
  const index = arr.findIndex((item) => item.id === id)
  arr.unshift(arr[index])
  arr.splice(index + 1, 1)
  return arr
}

onMounted(() => {
  initPage()
})

// 页面显示时初始化数据
onShow(() => {
  // 每次页面显示时都重新获取数据
  initPage()
  // 更新数据
  if (uni.getStorageSync('post') || id.value != uni.getStorageSync('USER_INFO').currentClassId) {
    id.value = uni.getStorageSync('USER_INFO').currentClassId
    uni.removeStorageSync('post')
    initPage()
  }
  checks()
  // #ifdef MP-WEIXIN
  headleLogger('班级')
  // #endif
})

// 触底加载更多
onReachBottom(() => {})
// 小程序分享
onShareAppMessage(() => sharePageObj())
</script>

<style lang="scss" scoped>
.class-bar {
  padding: 0 32rpx;
  padding-top: 24rpx;
  height: 88rpx;
  display: flex;
  color: #333333;

  &-right {
    flex: 1;
    display: flex;
    justify-content: space-between;

    &-text {
      display: flex;
      flex: 1;
      flex-direction: column;
      justify-content: space-between;

      &-top text:first-of-type {
        margin-right: 16rpx;
        font-size: 30rpx;
        font-weight: 600;
      }

      &-top text:last-of-type {
        font-size: 22rpx;
        font-weight: 500;
      }

      &-bottom {
        font-size: 24rpx;
        font-weight: 400;
      }
    }

    &-icon {
      image {
        width: 30rpx;
        height: 30rpx;
        margin-left: 32rpx;
      }

      image:first-of-type {
        margin-right: 32rpx;
      }
    }
  }

  &-img {
    width: 88rpx;
    height: 88rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 16rpx;
    background: rgba(54, 124, 255, 0.1);
    margin-right: 24rpx;

    image {
      width: 58rpx;
      height: 58rpx;
    }
  }
}

.list {
  position: relative;
  margin-top: 44rpx;
  padding: 28rpx 32rpx 0 32rpx;

  .row {
    font-size: 30rpx;
    font-weight: 400;
    box-sizing: border-box;
    border-radius: 28rpx;
    background: #ffffff;
    // padding: 0 32rpx;
    margin-bottom: 24rpx;

    &-item {
      padding: 0 32rpx;
      display: flex;
      height: 110rpx;
      align-items: center;
      justify-content: space-between;
      position: relative;
    }

    &-item::before {
      position: absolute;
      content: '';
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: calc(100% - 64rpx);
      /* 自定义边框的长度 */
      height: 1px;
      background-color: rgba(245, 245, 245, 1);
    }

    &-item:last-child::before {
      display: none;
    }

    &-item:active {
      background: #f5f5f5;
    }

    &-right {
      display: flex;
      align-items: center;

      &-count {
        margin-right: 16rpx;
        color: #b1b3b5;
      }
    }

    &-left {
      display: flex;
      align-items: center;

      image {
        width: 42rpx;
        height: 42rpx;
        margin-right: 14rpx;
      }
    }
  }

  .bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    opacity: 1;
    border-radius: 40rpx 40rpx 0rpx 0rpx;
    height: 404rpx;
    border: 2rpx solid #ffffff;
    box-shadow: inset 0rpx 12rpx 24rpx #ffffff;
    backdrop-filter: blur(40rpx);
    z-index: -1;
  }
}

// 幼儿出勤区域样式
.attendance-section {
  margin: 32rpx;
  background: linear-gradient(135deg, #ffffff, #f8f9ff);
  border-radius: 28rpx;
  box-shadow: 4rpx 8rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 2rpx solid rgba(82, 196, 26, 0.1);
  overflow: hidden;

  .attendance-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx 40rpx;

    .row-left {
      display: flex;
      align-items: center;

      image {
        width: 60rpx;
        height: 60rpx;
        margin-right: 24rpx;
      }

      text {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
      }
    }

    .row-right {
      display: flex;
      align-items: center;

      .row-right-count {
        font-size: 28rpx;
        color: #666;
        margin-right: 16rpx;
      }
    }
  }

  .today-stats {
    display: flex;
    align-items: center;
    padding: 20rpx 40rpx;
    border-top: 1rpx solid rgba(0, 0, 0, 0.05);

    .stats-title {
      font-size: 28rpx;
      color: #666;
      margin-right: 32rpx;
      font-weight: 500;
    }

    .stats-items {
      display: flex;
      flex: 1;
      justify-content: space-around;

      .stats-item {
        display: flex;
        align-items: center;
        gap: 8rpx;

        .stats-label {
          font-size: 24rpx;
          color: #666;
        }

        .stats-value {
          font-size: 28rpx;
          font-weight: 600;
          color: #333;

          &.attended {
            color: #52c41a;
          }

          &.leave {
            color: #faad14;
          }

          &.sick {
            color: #ff4d4f;
          }

          &.suspend {
            color: #722ed1;
          }
        }
      }
    }
  }
}

.attendance-btn {
  margin: 0 40rpx 32rpx 40rpx;
  padding: 16rpx 40rpx;
  background: linear-gradient(135deg, #52c41a, #73d13d);
  color: #fff;
  font-size: 30rpx;
  font-weight: 600;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.3);
  transition: all 0.3s ease;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.4);
  }
}

::v-deep .u-navbar__content__left {
  display: none !important;
}
</style>
