<!-- 身份验证 -->
<template>
	<view class="layout">
		<base-layout navTitle="登录" :footerStyle="{
			display: 'none'
		}" :contentStyle="{
			padding: 0
		}">
			<view style="padding: 0 32rpx">
				<up-form ref="formRef" labelPosition="top" labelWidth="auto" :model="formData" :rules="rules">
					<view class="col">
						<up-form-item label="您所在的幼儿园" prop="school" required>
							<up-input v-model="formData.school" placeholder="请输入幼儿园名称" border="none" />
						</up-form-item>
					</view>
					<view class="col">
						<up-form-item label="职位" prop="post" required>
							<up-input v-model="formData.post" placeholder="请输入职位" border="none" />
						</up-form-item>
					</view>
					<view class="col">
						<up-form-item label="姓名" prop="name" required>
							<up-input v-model="formData.name" placeholder="请输入姓名" border="none" />
						</up-form-item>
					</view>
					<view class="col">
						<up-form-item label="手机号" prop="name" required>
							<up-input v-model="formData.mobile" placeholder="请输入手机号" border="none" />
						</up-form-item>
					</view>
				</up-form>
			</view>
			<view class="footer">
				<up-button color="#367CFF" text="提交" shape="circle" formType="submit" @click="submit" />
			</view>
		</base-layout>
	</view>
</template>

<script setup>
import { reactive, ref } from 'vue';
import { onReady, onLoad } from '@dcloudio/uni-app';
import { tempUserLogin } from '@/api';
import { getUserInfo } from '@/api/login.js';
import { setUserInfo, setToken, clearToken, getToken } from '@/utils/token.js';
let formData = reactive({
	school: '', // 幼儿园
	post: '', // 职位
	name: '', // 姓名
	uid: '', // 用户id
	ts: '', // 时间戳
	s: '', // 密钥
	cid: '', // 客户端id
	mobile: '' // 手机号
});
let formRef = ref(null);
let rules = {
	school: [
		{
			required: true,
			message: '请输入幼儿园名称',
			trigger: ['blur', 'change']
		}
	],
	post: [{ required: true, message: '请输入职位', trigger: ['blur', 'change'] }],
	name: [{ required: true, message: '请输入姓名', trigger: ['blur', 'change'] }]
};
onLoad((options) => {
	console.log(options);
	formData.uid = options.uid;
	formData.ts = options.ts;
	formData.cid = options.cid;
	formData.s = options.s;
});

const submit = async () => {
	formRef.value.validate().then(async (valid) => {
		if (valid) {
			// 判断有无token，有直接跳登录，没有登录正常
			if (getToken()) {
				let rul = await getUserInfo();
				if (rul.status == 0) {
					setUserInfo(rul.data);
					uni.switchTab({
						url: '/pages/index/index'
					});
				} else {
					uni.showToast({
						title: rul.message,
						icon: 'error'
					})
				}
				return;
			}
			// 提交
			let res = await tempUserLogin(formData);
			if (res.status === 0) {
				// 设置token，设置用户 跳到首页
				console.log(res.data.token);
				setToken(res.data.token, 3);
				let rul = await getUserInfo();
				setUserInfo(rul.data);
				uni.switchTab({
					url: '/pages/index/index'
				});
			} else {
				uni.showToast({
					title: res.message,
					icon: 'error'
				});
			}
		} else {
			console.log('校验失败');
		}
	});
};
</script>
<script>
export default {
	options: { styleIsolation: 'shared' } //解除样式隔离
};
</script>

<style lang="scss" scoped>
:deep(.u-form-item__body__left__content) {
	flex-direction: row-reverse;
	flex: initial !important;

	.u-form-item__body__left__content__required {
		right: -16rpx;
		left: initial;
	}
}

.footer {
	width: 100%;
	position: fixed;
	bottom: 0;
	left: 0;
	padding: 28rpx;
	box-sizing: border-box;
	background: #ffffff;
	padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}

.col {
	width: 100%;
	border-radius: 28rpx;
	background: #ffffff;
	box-shadow: 4rpx 8rpx 16rpx #eee;
	box-sizing: border-box;
	padding: 28rpx;
	margin: 28rpx 0;
}
</style>
