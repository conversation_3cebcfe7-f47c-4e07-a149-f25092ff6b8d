<template>
  <BaseLayout2
    @leftClick="isClassShow = true"
    :autoBack="false"
    navTitle="课程"
    :contentStyle="{
      padding: '0 32rpx',
    }"
    :footerStyle="{
      display: 'none',
    }"
    :scrollEnabled="false"
    :enableLoadMore="true"
    :refresherEnabled="true"
    :refresherTriggered="refreshing"
    @scrolltolower="scrolltolower"
    @refresherrefresh="handleRefresherRefresh"
  >
    <!-- 导航栏左侧内容 -->
    <template #navleft>
      <view class="nav">
        <text class="nav-left">{{ currentClassName || "-" }}</text>
        <image
          class="change"
          src="/static/game/change.svg"
        />
      </view>
    </template>

    <view style="height: 100%">
      <!-- 导航栏 -->
      <view class="course-container">
        <view
          v-for="(item, index) in couresList.slice(0, 3)"
          :key="index"
          class="course-box"
          :hover-class="'course-box-hover'"
          @tap="gotoPage(item)"
        >
          <!-- @tap="gotoPage(item)" -->
          <view class="course-box-img">
            <image :src="item.src" />
          </view>
          <text class="course-value">{{ item.title }}</text>
        </view>
      </view>
      <!-- 库 -->
      <view class="course-container">
        <view
          v-for="(item, index) in couresList.slice(3)"
          :key="index"
          class="course-box1"
          :hover-class="'course-box-hover'"
          @tap="gotoPage(item)"
        >
          <view class="course-title">{{ item.title }}</view>
          <view class="course-value">共 {{ item.value }} 个</view>
          <view class="course-box1-img">
            <image :src="item.src" />
          </view>
        </view>
      </view>

      <!-- 班级课程 -->
      <view class="class-title">班级课程</view>
      <up-list
        v-if="pageData.courseList.length > 0"
        :height="listHeight || '50vh'"
        :enableFlex="true"
        pagingEnabled
        @scrolltolower="scrolltolower"
      >
        <up-list-item
          v-for="(item, index) in pageData.courseList"
          :key="item.id"
        >
          <view
            class="card"
            @click="gotoPage(item, 'card')"
          >
            <view class="img-bg">
              <text
                class="img-bg-text"
                v-if="item.aiFlag == 1"
                >青禾AI</text
              >
              <image
                style="width: 110rpx; height: 110rpx"
                src="https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/index_icon-1.png"
              />
            </view>
            <view class="content">
              <view class="card-title">
                <text
                  class="title"
                  :style="{
                    width: !utils.getCategoryMap(item.categoryId)
                      ? '400rpx'
                      : '290rpx',
                  }"
                  >{{ item.title }}</text
                >
                <view
                  class="tag"
                  v-if="utils.getCategoryMap(item.categoryId)"
                  :style="utils.gradeStyleMap(item.categoryId)"
                  >{{ utils.getCategoryMap(item.categoryId) }}</view
                >
              </view>
              <view
                >{{ item?.schoolClass?.title }} | {{ termMap(item.term) }} |
                {{ item.creator }}
              </view>
              <view class="card-schoolTitle">{{
                item?.schoolClass?.schoolTitle
              }}</view>
              <view class="card-date"
                >实施周期：{{ splitTime(item.startAt) }} -
                {{ splitTime(item.endAt) }}</view
              >
              <view class="card-date"
                >最新实施日期：{{ splitTime(item.latestImplementedAt) }}</view
              >
            </view>
            <!-- #ifdef MP-WEIXIN -->
            <up-icon
              class="card-icon"
              @tap.navtive.stop="onListClick(item, index)"
              stop
              size="42rpx"
              name="more-dot-fill"
            />
            <!-- #endif -->
            <!-- #ifdef H5 -->
            <up-icon
              class="card-icon"
              @tap="onListClick(item, index)"
              stop
              size="42rpx"
              name="more-dot-fill"
            />
            <!-- #endif -->
          </view>
        </up-list-item>
        
        <up-loading-icon
          v-if="isShowListLoading"
          mode="circle"
        />
        <view
          class="text-Empty"
          v-if="!isScrolltolower"
          >没有更多啦^v^</view
        >
      </up-list>
      <view
        v-if="pageData.courseList.length == 0 && !isShowListLoading"
        class="navBtn"
        style="margin-top: 200rpx"
        @tap="
          gotoPage({ key: 'draft', route: '/courseDetails/list/draftList' })
        "
        >点击到草稿箱添加课程</view
      >
      <up-loading-icon
        style="margin-top: 200rpx"
        v-if="isShowListLoading"
        mode="circle"
      />
    </view>
  </BaseLayout2>

  <up-picker
    ref="uPicker"
    :show="isClassShow"
    :columns="columns"
    :defaultIndex="[0, 0]"
    keyName="title"
    closeOnClickOverlay
    :loading="pickerLoading"
    @confirm="confirmClass"
    @change="classChange"
    @cancel="isClassShow = false"
    @close="isClassShow = false"
  />
  <Popup
    :show="isShowPopup"
    @close="isShowPopup = false"
  >
    <add-form
      v-if="isShowAddForm"
      @confirm="confirmEdit"
      title="编辑课程"
      :data="curData"
    />
    <view v-else>
      <popup-item
        icon="/static/common/editor.png"
        text="编辑"
        @tap="headleAction(1)"
      />
      <popup-item
        icon="/static/common/delete.png"
        text="删除"
        @tap="headleAction(2)"
      />
    </view>
  </Popup>
  <up-modal
    :show="deletePar.isShowDelete"
    showCancelButton
    asyncClose
    @cancel="deletePar.isShowDelete = false"
    @confirm="confirmDelete"
    width="622rpx"
  >
    <view>
      本课程的所有阶段、活动、评价信息等都将被删除，删除后将不能找回，确定要删除
      <text style="color: red">{{ curData?.title }}</text> 吗？
    </view>
  </up-modal>
</template>

<script setup>
import { onMounted, reactive, ref, nextTick, getCurrentInstance } from "vue";
import { onShow, onShareAppMessage } from "@dcloudio/uni-app";
import BaseLayout2 from "@/components/base-layout/base-layout2.vue";
import { checks, sharePageObj, useCurrentClassId } from "@/utils/index.js";
import { getclassList, getSchoolList, getAiclassList } from "@/api";
import { getCategoryList, deleteItemList } from "@/api/api.js";
import { getStatisticsInfo } from "./api";
import utils from "./utils";
import Popup from "@/components/Popup/Popup.vue";
import PopupItem from "@/components/Popup-item/Popup-item.vue";
import addForm from "./components/addForm.vue";
const { splitTime } = utils;
const instance = getCurrentInstance();
// 课程列表相关
const paging = reactive({
  currentPage: 1,
  pageSize: 10,
});
let pageData = reactive({
  statistics: {},
  courseList: [],
});
let listHeight = ref(0); // 列表高度
let isScrolltolower = ref(true); // 是否触底加载
let isShowListLoading = ref(false); // 是否显示列表加载中
let isShowPopup = ref(false); // 是否显示弹窗
let isShowAddForm = ref(false); // 是否显示添加表单
let curData = ref(null); // 当前选中数据
let deletePar = reactive({
  content: "",
  isShowDelete: false, // 是否显示删除弹窗
}); // 删除参数

const termMap = (id) => {
  const map = uni.getStorageSync("ENUM").SubjectTermEnum;
  return map[id] || `-`;
};

// 下拉刷新状态
const refreshing = ref(false);
// 页面导航数据相关
const couresList = reactive([
  {
    title: "AI教案",
    type: "AI教案",
    key: "ai",
    route: "/courseDetails/fnlist/fnlist",
    src: "https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/course/course_AI.svg",
  },
  {
    title: "活动评价",
    type: "活动评价",
    key: "hdpj",
    route: "/courseDetails/fnlist/fnlist",
    src: "https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/course/course_eva.svg",
  },
  {
    title: "导出Word",
    type: "导出Word",
    key: "doc",
    route: "/courseDetails/fnlist/fnlist",
    src: "https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/course/course_word.svg",
  },
  {
    title: "班级课程库",
    value: 0,
    type: "currentClassSubject",
    key: "currentClassSubjectCount",
    route: "/courseDetails/list/list",
    src: "https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/course/course_tabs1.svg",
  },
  {
    title: "园本课程库",
    value: 0,
    type: "currentSchoolSubject",
    key: "currentSchoolSubjectCount",
    route: "/courseDetails/list/list",
    src: "https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/course/course_tabs2.svg",
  },
  // {
  //   title: "公共课程库",
  //   value: 0,
  //   type: "platformPublicSubject",
  //   key: "platformPublicSubjectCount",
  //   route: "/courseDetails/list/list",
  //   src: "https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/course/course_tabs3.svg",
  // }, // TODO: 公共课程库暂时隐藏
]);

// 确认删除
const confirmDelete = async (e) => {
  const { id, index } = curData.value;
  let res = await deleteItemList({ id, forceFlag: true });
  if (res.status == 0) {
    pageData.courseList.splice(index, 1);
    uni.$u.toast("删除成功");
  } else {
    uni.$u.toast(res?.message || "删除失败");
  }
  deletePar.isShowDelete = false;
  isShowPopup.value = false;
};

// 确认编辑按钮,成功后会触发
const confirmEdit = async (e) => {
  isShowPopup.value = false;
  setTimeout(() => {
    isShowAddForm.value = false;
    handleRefresherRefresh(); // 重新获取列表
  }, 300);
};

// 操作区
const headleAction = (e) => {
  const { id, title } = curData.value;
  // 1 编辑 2 删除
  if (e === 1) {
    isShowPopup.value = false;
    setTimeout(() => {
      isShowAddForm.value = true;
      isShowPopup.value = true;
    }, 350);
  } else {
    deletePar.content = `${title}  吗？`;
    deletePar.isShowDelete = true;
  }
  isShowPopup.value = false;
};

// 点击列表一项
const onListClick = (e, i) => {
  curData.value = e;
  curData.value.index = i;
  isShowPopup.value = true;
};

// 切换班级相关
const isClassShow = ref(false);
let columns = reactive([[]]);
let pickerLoading = ref(false); // 切换班级时加载中状态
let currentClassName = ref("-"); // 当前班级名称
let currentClassId = ref(null); // 当前班级id
currentClassId.value = uni.getStorageSync("USER_INFO").currentClassId;
// 跳转页面
const gotoPage = (item, type) => {
  const classId = uni.getStorageSync("USER_INFO").currentClassId;
  if (type === "card") {
    uni.navigateTo({
      url:
        "/courseDetails/classHome/classHomeV1" +
        `?id=${item.id}&gradeId=${item.gradeId}`,
    });
    return;
  }

  switch (item.key) {
    case "currentClassSubjectCount":
      uni.navigateTo({
        url:
          item.route +
          `?type=${item.type}&classId=${classId}&classTitle=${currentClassName.value}`,
      });
      break;
    case "draft":
      uni.navigateTo({
        url: item.route,
      });
      break;
    default:
      uni.navigateTo({
        url: item.route + `?type=${item.type}`,
      });
      break;
  }
};

// 重置分页
const resetPaging = () => {
  paging.currentPage = 1;
  pageData.courseList = [];
  isScrolltolower.value = true;
};
// 触底加载
const scrolltolower = async () => {
  if (!isScrolltolower.value) return;
  isShowListLoading.value = true;
  paging.currentPage++;
  await getCurClassList();
  isShowListLoading.value = false;
};
// 下拉刷新
const handleRefresherRefresh = async () => {
  if (refreshing.value) return;
  refreshing.value = true;
  resetPaging();
  await getCurClassList();
  refreshing.value = false;
};

// 当前用户的统计信息
const getStatistics = async () => {
  try {
    let res = await getStatisticsInfo();
    if (res.status == 0) {
      pageData.statistics = res.data;
      couresList.forEach((item) => {
        item.value = res.data[item.key] || 0;
      });
    }
  } catch (error) {
    uni.$u.toast("获取统计信息失败");
  }
};
// 当前班级的课程列表
const getCurClassList = async () => {
  isShowListLoading.value = true;
  const userInfo = uni.getStorageSync("USER_INFO");
  const params = {
    currentPage: paging.currentPage,
    pageSize: paging.pageSize,
    pageModel: {
      classId: userInfo.currentClassId,
      statusList: [1],
    },
  };
  try {
    let res = await getAiclassList(params);
    isShowListLoading.value = false;
    if (res.status == 0) {
      pageData.courseList = pageData.courseList.concat(res.data);
      uni.stopPullDownRefresh();
      if (paging.currentPage >= Math.ceil(res.metadata.count / paging.pageSize))
        isScrolltolower.value = false;
    }
  } catch (error) {
    uni.$u.toast("获取班级课程列表失败");
  }
};

// 获取课程类别
const getCategory = async () => {
  try {
    let res = await getCategoryList({
      currentPage: 1,
      pageSize: 10,
      category: 31,
    });
    if (res.status == 0) uni.setStorageSync("CATEGORY_LIST", res.data);
  } catch (e) {}
};

// 确认所选班级
const confirmClass = async (e) => {
  const { value } = e;
  await useCurrentClassId(value[1].id);
  // 清空课程列表 重置分页
  resetPaging();
  // 重新请求课程列表
  await getCurClassList();
  await getStatistics();
  uni.setStorageSync("classId", value[1].id);
  currentClassName.value = value[1].title;
  isClassShow.value = false;
  uni.stopPullDownRefresh();
};

// 切换班级
const classChange = async (e) => {
  const { columnIndex, value } = e;
  if (columnIndex === 0) {
    pickerLoading.value = true;
    await useCurrentClassId(null, value[columnIndex].id);
    columns.pop();
    await classList(value[columnIndex].id);
    pickerLoading.value = false;
  }
};

// 请求当前班级
const classList = async (schoolId) => {
  try {
    const userInfo = uni.getStorageSync("USER_INFO");
    uni.setStorageSync("classId", userInfo.currentClassId);
    const data = { schoolId };
    const fn = schoolId ? getclassList(data) : getclassList();
    const res = await fn;
    const list = res.data;
    if (res.status == 0) {
      let curData = list.find((item) => item.id == userInfo.currentClassId);
      currentClassName.value = curData?.title;
      currentClassId.value = curData?.id;

      //  currentClassName.value = lis
      // 确保只有两列：学校列和班级列
      if (columns.length > 1) {
        columns.splice(1); // 删除第二列及之后的所有列
      }
      columns.push(list);
    }
  } catch (error) {
    console.error("获取班级列表失败", error);
    uni.showToast({ title: "获取班级列表失败", icon: "none" });
  }
};

// 请求当前学校
const schoolList = async () => {
  try {
    const userInfo = uni.getStorageSync("USER_INFO");
    columns.length = 0;
    const res = await getSchoolList();
    if (res.status == 0) {
      columns[0] = res.data;
      let val = res.data.find((item) => item.id == userInfo.currentSchoolId);
      let index = res.data.findIndex(
        (item) => item.id == userInfo.currentSchoolId
      );
      columns[0].splice(index, 1);
      columns[0].unshift(val);
      await classList();
    }
  } catch (error) {
    console.error("获取学校列表失败", error);
    uni.showToast({ title: "获取学校列表失败", icon: "none" });
  }
};

// 获取当前类名到屏幕底部的距离
const getDistanceToBottom = () => {
  nextTick(() => {
    const query = uni.createSelectorQuery().in(instance);
    query.select(".class-title").boundingClientRect();
    query.selectViewport().scrollOffset();
    query.exec((res) => {
      const titleRect = res[0];
      const windowHeight = uni.getSystemInfoSync().windowHeight;
      listHeight.value = Math.ceil(
        windowHeight - titleRect.top - titleRect.height - 10
      );
    });
  });
};

// 更新页面数据
const updatePageData = () => {
  getCurClassList();
  getStatistics();
};

onMounted(() => {
  schoolList();
  updatePageData();
  getCategory();
  getDistanceToBottom();
  uni.setStorageSync("classId", currentClassId.value);
});

onShow(() => {
  checks();
  let us = uni.getStorageSync("USER_INFO").currentClassId;
  // 检查是否切换班级，切换就重新请求班级接口
  if (currentClassId.value != us) {
    pageData.courseList = [];
    classList();
    updatePageData();
  }
});
onShareAppMessage(() => sharePageObj());
</script>
<script>
export default {
  options: { styleIsolation: "shared" }, // 解除样式隔离
};
</script>

<style lang="scss" scoped>
.navBtn {
  width: fit-content;
  margin: 0 auto;
  border-radius: 28rpx;
  background: #367cff;
  padding: 10rpx 20rpx;
  color: #fff;
  font-size: 28rpx;

  &:active {
    background: #ccc;
  }
}

:deep(.u-list) {
  box-sizing: border-box;
}

:deep(.u-list .u-list-item:first-child .card) {
  margin-top: 0;
}

.class-title {
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 32rpx;
}

.card {
  min-height: 216rpx;
  opacity: 1;
  border-radius: 28rpx;
  margin-top: 24rpx;
  background: #ffffff;
  box-sizing: border-box;
  padding: 28rpx;
  display: flex;
  align-items: flex-start;
  position: relative;

  .mask {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 28rpx;
    z-index: 10;
  }

  .img-bg {
    width: 130rpx;
    height: 130rpx;
    background: rgba(215, 242, 179, 1);
    border-radius: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 24rpx;
    position: relative;

    .img-bg-text {
      display: inline-block;
      position: absolute;
      top: 0;
      left: 0;
      font-size: 18rpx;
      font-weight: 500;
      width: 74rpx;
      height: 30rpx;
      line-height: 30rpx;
      border-radius: 16rpx 0rpx 16rpx 0rpx;
      background: rgba(51, 51, 51, 0.6);
      z-index: 5;
      text-align: center;
      color: #fff;
    }
  }

  .content {
    flex: 1;
    font-size: 24rpx;
    font-weight: 500;
    letter-spacing: 0rpx;
    line-height: 33.6rpx;
    color: rgba(128, 128, 128, 1);

    .card-title {
      font-size: 30rpx;
      font-weight: 700;
      line-height: 42rpx;
      margin-right: 32rpx;
      color: rgba(51, 51, 51, 1);
      margin-bottom: 4rpx;

      display: flex;
      justify-content: space-between;
      align-items: baseline;

      .title {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .tag {
        font-size: 20rpx;
        line-height: 24rpx;
        font-weight: 500;
        display: inline-block;
        min-width: 92rpx;
        height: 32rpx;
        border-radius: 5.84rpx;
        padding: 6rpx 12rpx;
        box-sizing: border-box;
      }
    }

    .card-schoolTitle {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

.nav {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 175rpx;
  height: 52rpx;
  border-radius: 32rpx;
  border: 1rpx solid rgba(227, 230, 235, 1);
  box-sizing: border-box;
  margin-top: 10rpx;
  .nav-left {
    margin-right: 10rpx;
    /** 文本1 */
    font-size: 28rpx;
    font-weight: 500;
    color: rgba(51, 51, 51, 1);
  }

  image {
    width: 27rpx;
    height: 27rpx;
  }
}

.course-container {
  margin: 32rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 116rpx;
}

.course-box {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  width: 120rpx;
  box-sizing: border-box;
  border-radius: 16rpx;
  transition: all 0.3s ease;

  &-img {
    width: 120rpx;
    height: 120rpx;
    box-shadow: 4rpx 8rpx 12rpx rgba(0, 0, 0, 0.06);
    background: #fff;
    border-radius: 28rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  image {
    width: 72rpx;
    height: 72rpx;
  }

  &:not(:first-child) {
    image {
      width: 96rpx;
      height: 96rpx;
    }
  }
}

.course-box1 {
  width: 212rpx;
  height: 148rpx;
  padding: 24rpx;
  box-sizing: border-box;
  opacity: 1;
  border-radius: 28rpx;
  background: #fff;
  position: relative;
  box-shadow: 4rpx 8rpx 12rpx rgba(0, 0, 0, 0.06);

  &-img {
    position: absolute;
    bottom: 5rpx;
    right: 10rpx;

    image {
      width: 62rpx;
      height: 62rpx;
    }
  }

  .course-value {
    text-align: left;
    font-size: 24rpx;
    font-weight: 400;
    color: #808080;
    margin-top: 14rpx;
  }

  .course-title {
    font-size: 28rpx;
    font-weight: 500;
    color: #333;
  }
}

.course-box-hover {
  transform: scale(0.98);
}

.course-value {
  width: 100%;
  text-align: center;
  margin-top: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #333333;
}
</style>
