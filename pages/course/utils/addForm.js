import { reactive } from 'vue';

export default () => {
	const rules = reactive({
		title: [{
			required: true,
			message: '请输入主题名称',
			trigger: ['blur'],
		}],
		termname: [{
			required: true,
			message: '请输入学期',
			trigger: ['blur'],
			validate: (rule, value, callback) => {
			    console.log(rule, value);
				return true
			}

		}],
		gradename: [{
			required: true,
			message: '请输入年级',
			trigger: ['change'],
		}]
		,
		time: [{
			required: true,
			message: '请输入实施时间',
			trigger: ['blur'],
		}]
		,
		creator: [{
			required: true,
			message: '请输入作者名',
			trigger: ['blur'],
		}]
	})
	return {
		rules
	}
}