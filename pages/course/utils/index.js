import dayjs from "dayjs";

// 分隔时间
const splitTime = (startTime, endTime) => {
    // if (!dayjs(startTime).isValid() || !dayjs(endTime).isValid()) return "-";
    // let st = dayjs(startTime).format("MM/DD");
    // let et = dayjs(endTime).format("MM/DD");
    // return `${st} - ${et}`
    return dayjs(startTime).format("YYYY/MM/DD")
};

// 课程类别样式映射表
const categoryStyleMap = [
    {
        value: "主题课程",
        itemStyle: {
            background: "#FDF2EA",
            color: "#F0A14D",
        },
    },
    {
        value: "项目课程",
        itemStyle: {
            background: "#EDF2FE",
            color: "#5283F7",
        },
    },
    {
        value: "领域/特色课程",
        itemStyle: {
            background: "#FCEDEE",
            color: "#ED6F72",
        },
    },
    {
        value: "生活课程",
        itemStyle: {
            background: "#F0F1FC",
            color: "#6E74E6",
        },
    },
    {
        value: "STEM课程",
        itemStyle: {
            background: "#EDF8F0",
            color: "#54BA6A",
        },
    },
    {
        value: "体育课程",
        itemStyle: {
            background: "#F0F1F6",
            color: "#7278A6",
        },
    },
    {
        value: "学习活动",
        itemStyle: {
            background: "#FDF2EA",
            color: "#F0A14D",
        },
    },
];
// 课程类别映射表
const categoryMap = uni.getStorageSync('CATEGORY_LIST') || [];
export const gradeStyleMap = (id) => {

    if (categoryMap.length == 0) {
        // uni.$u.toast('课程类别数据为空, 请刷新')
        return
    }
    let style = {};
    let t = categoryMap.find((item) => item.id == id)?.value
    categoryStyleMap.forEach((item) => {
        if (item.value == t) {
            style = item.itemStyle;
        }
    });
    return JSON.stringify(style) != '{}' ? style : null;
};
export const getCategoryMap = (id) => {

    return categoryMap.find((item) => item.id == id)?.value
};

export default {
    splitTime,
    gradeStyleMap,
    getCategoryMap
}