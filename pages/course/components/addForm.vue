<template>
	<view>
		<view class="add-form-title">
			<view>{{ title }}</view>
			<view @click="sendForm">完成</view>
		</view>
		<view style="padding-left: 20rpx;">
			<up-form ref="formRef" labelPosition="left" :model="formData" labelWidth="80" :rules="rules">
				<up-form-item :style="addFormItemStyle" label="主题名称" prop="title" required>
					<up-input cursorSpacing="100" v-model="formData.title" placeholder="请输入内容" border="none" />
				</up-form-item>
				<up-form-item :style="addFormItemStyle" label="学期" prop="termname" @click="openPicker('学期')" required>
					<up-input v-model="formData.termname" disabled disabledColor="#fff" placeholder="请输入内容"
						border="none" />
					<template #right>
						<up-icon name="arrow-down" />
					</template>
				</up-form-item>
				<up-form-item :style="addFormItemStyle" label="年级" @click="openPicker('年级')" prop="gradename" required>
					<up-input v-model="formData.gradename" disabled disabledColor="#fff" placeholder="请输入内容"
						border="none" />
					<template #right>
						<up-icon name="arrow-down" />
					</template>
				</up-form-item>
				<up-form-item :style="addFormItemStyle" label="实施时间" @click="isCalendar = true" prop="time" required>
					<up-input v-model="formData.time" disabled disabledColor="#fff" placeholder="请输入内容" border="none" />
					<template #right>
						<up-icon name="arrow-down" />
					</template>
				</up-form-item>
				<up-form-item :style="addFormItemStyle" label="创作者" prop="creator" required>
					<up-input v-model="formData.creator" placeholder="请输入内容" border="none" />
				</up-form-item>
			</up-form>
		</view>
		<up-picker :show="isPicker" :defaultIndex="[0]" :columns="columns" @cancel="closePup" @confirm="pickerConfirm"
			keyName="label"></up-picker>
		<up-calendar :show="isCalendar" mode="range" @cancel=" isCalendar = false" @confirm="calendarConfirm"
			allowSameDay closeOnClickOverlay></up-calendar>
	</view>
</template>

<script setup>
import { reactive, ref, onMounted } from 'vue';
import { enumList, dictionaryList } from '@/api/index.js';
import { addCourse, updataCourse } from '@/api/api.js';
import utils from '../utils/addForm.js';

let isPicker = ref(false)
let isCalendar = ref(false)
let formRef = ref(null);
const props = defineProps({
	title: String,
	data: Object
})

const columns = reactive([
	[{
		label: '',
		id: ''
	}]
])
let istermAge = ref('')

let formData = reactive({
	title: '',
	startAt: '',
	endAt: '',
	time: '',
	gradeId: '',
	gradename: "",
	term: '',
	termname: '',
	creator: ''
})
onMounted(() => {
	if (props.data) {
		const { title, startAt, endAt, gradeId, term, creator } = props.data
		formData.title = title
		formData.startAt = startAt
		formData.endAt = endAt
		if (startAt.split(' ')[0] && endAt.split(' ')[0]) formData.time = startAt.split(' ')[0] + ' 至 ' + endAt.split(' ')[0]
		formData.gradeId = gradeId
		formData.term = term
		formData.creator = creator
	}
})

let addFormItemStyle = {
	height: '115rpx',
	justifyContent: 'center'
}


const { rules } = utils()

function closePup() {
	columns[0] = []
	isPicker.value = false
}

// 新建课程 / 修改课程
async function sendForm() {
	formRef.value.validate().then(valid => {
		if (valid) {
			let { title, startAt, endAt, gradeId, term, creator } = formData
			if (props.title == '新增课程') addList({ title, startAt, endAt, gradeId, term, creator })
			if (props.title == '编辑课程') editList({ title, startAt, endAt, gradeId, term, creator, id: props.data.id })
			closePup()
			// setTimeout(() => , 100)
		} else {
			uni.$u.toast('请检查是否有漏填')
		}
	})
}

async function addList(data) {
	const res = await addCourse(data)
	general(res, '新增')
}

async function editList(data) {
	const res = await updataCourse(data)
	general(res, '修改')
}

// 通用
function general(res, text) {
	if (res.status == 0) {
		emit('confirm')
		uni.$u.toast(text + '成功')
		return
	}
	uni.$u.toast(res?.message || text + '失败')
}


function pushList(obj) {
	columns[0] = []
	Object.keys(obj).forEach(key => {
		termList.value.push({
			label: obj[key],
			id: Number(key)
		})
	})
}
let termList = ref([])  // 学期
let gradeList = ref([]) // 年级
// 获取学期列表内容
const getenumList = async () => {
	let res = await enumList()

	const obj = res.data.SubjectTermEnum
	formData.termname = obj[props.data.term]
	pushList(obj)
}

// 获取年纪列表内容
const getAgeList = async () => {
	columns[0] = []
	let res = await dictionaryList({
		current: 1,
		pageSize: 0,
		category: 3,
	})
	let list = res.data
	if (props.data.gradeId) {
		let item = list.find((e) => e.id == props.data.gradeId)
		formData.gradename = item.value
	}

	list.forEach(key => {
		gradeList.value.push({
			label: key.value,
			id: key.id
		})
	})


}

function pickerConfirm(e) {
	if (istermAge.value == '学期') {
		formData.termname = e.value[0].label
		formData.term = e.value[0].id
	}
	if (istermAge.value == '年级') {
		formData.gradeId = e.value[0].id
		formData.gradename = e.value[0].label
	}
	isPicker.value = false
}
function calendarConfirm(e) {
	// 返回的是一个数组 e
	formData.startAt = e[0]
	formData.endAt = e[e.length - 1]
	formData.time = `${e[0]} 至 ${e[e.length - 1]}`
	isCalendar.value = false
}
function openPicker(e) {
	isPicker.value = true
	istermAge.value = e
	if (e == '学期') columns[0] = termList.value
	if (e == '年级') columns[0] = gradeList.value
}
getenumList()
getAgeList()
const emit = defineEmits(['confirm'])
</script>
<script>
export default {
	options: { styleIsolation: 'shared' }  // 解除样式隔离
}
</script>
<style lang="scss" scoped>
::v-deep .u-form-item {
	border-bottom: 1px solid#eee;
}

::v-deep .u-form-item:last-child {
	border-bottom: none;
}

.add-form-title {
	display: flex;
	align-items: center;
	justify-content: space-between;

	view:first-child {
		font-size: 34rpx;
		font-weight: 600;
	}

	view:last-child {
		font-size: 30rpx;
		font-weight: 500;
		color: rgba(63, 121, 255, 1);

	}

}
</style>
