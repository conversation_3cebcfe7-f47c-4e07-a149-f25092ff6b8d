<template>
    <view>
        <view class="iconAction">
            <view @click="generate('AI')">
                <view class="">AI生成</view>
            </view>
            <view @click="generate('artificial')">
                <view >手动生成</view>
            </view>
        </view>
    </view>
</template>

<script setup>
import { reactive, ref, onMounted } from 'vue';
const generate = (type) => {
    emit('change', type)
}
const emit = defineEmits(['change'])
</script>
<!-- <script>
export default {
	options: { styleIsolation: 'shared' }  // 解除样式隔离
}
</script> -->
<style lang="scss" scoped>
.iconAction {
    &>view {
        height: 100rpx;
        display: flex;
        align-items: center;
        font-size: 30rpx;
        font-weight: 400;
        letter-spacing: 0rpx;
        line-height: 100rpx;
        color: rgba(51, 51, 51, 1);
        text-align: left;
        vertical-align: middle;
        border-bottom: 1px solid #EEE;
        image {
            width: 40rpx;
            height: 40rpx;
            margin-right: 28rpx;
        }
    }
    &>view:last-child {

        border-bottom: 0;

    }
}
</style>
