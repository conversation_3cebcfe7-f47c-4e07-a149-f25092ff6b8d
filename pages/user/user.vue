<template>
  <view
    class="layout"
    @touchmove.stop.prevent="() => {}"
  >
    <view :style="{ height: menuFill() + 'px' }"></view>
    <view class="user_content">
      <view class="user_content_img">
        <up-avatar
          @tap="isPopup = true"
          class="user_img"
          :src="
            user_info?.header?.uri || 'https://c.mypacelab.com/logo/logo.png'
          "
          size="90"
          mode="aspectFill"
        ></up-avatar>
        <view class="user_content_img_jia">
          <image src="/static/icon/user_add.svg" />
        </view>
      </view>
      <view class="user_content_text">
        <view
          >{{ user_info.name }}<text v-if="nickname">({{ nickname }})</text>
          {{ staging }}</view
        >
        <view>{{ user_info.note }}</view>
        <view>当前班级：{{ classTitme || "-" }}</view>
        <view>当前班级Id：{{ classId || "-" }}</view>
        <view>当前用户Id：{{ userId || "-" }}</view>
      </view>
    </view>
    <template
      v-for="(item, index) in actionList"
      :key="index"
    >
      <view
        class="action_list"
        v-if="item.key !== 'ptfu' && item.key !== 'yszc'"
      >
        <up-cell
          size="large"
          :title="item.title"
          isLink
          :border="false"
          @click="tapList(item)"
        >
        </up-cell>
      </view>
    </template>
    <up-modal
      :show="isLogout"
      title="注销登录"
      content="您是否注销登录，注销后需要重新登录"
      showCancelButton
      @cancel="isLogout = false"
      @confirm="logout"
    ></up-modal>
    <up-picker
      ref="uPicker"
      :show="isClassShow"
      :columns="columns"
      :defaultIndex="[0, 0]"
      keyName="title"
      :loading="pickerLoading"
      @confirm="classConfirm"
      @change="classChange"
      @cancel="isClassShow = false"
    />
    <view class="bottom_text">
      <template
        v-for="(item, index) in actionList"
        :key="index"
      >
        <text
          v-if="item.key == 'ptfu' || item.key == 'yszc'"
          @click="tapList(item)"
          >《{{ item.title }}》</text
        >
      </template>
    </view>
    <view class="logout">
      <text @click="tapList({ key: 'logout' })">退出登录</text>
    </view>
    <Popup
      :show="isPopup"
      @close="isPopup = false"
    >
      <Popup-item
        icon="camera-fill"
        @tap="changeAvatar"
        >修改头像</Popup-item
      >
      <view style="height: 32rpx" />
    </Popup>
    <!-- 上传头像不用显示 -->
    <Upload
      style="display: none"
      ref="UploadRef"
      type="image"
      :fileCategory="10"
      :count="1"
      :value="[]"
      @callback="changeAvatarCallback"
    />
  </view>
</template>

<script setup>
import { ref, reactive, computed } from "vue";
import { onShow, onShareAppMessage } from "@dcloudio/uni-app";
import { checks, menuFill, sharePageObj, headleLogger } from "@/utils/index.js";
import { clearToken } from "@/utils/token.js";
import { getclassList, getSchoolList, updateAvatarOrNickName } from "@/api";
import { getUserInfo } from "@/api/login.js";
import { useCurrentClassId } from "@/utils/index.js";
import Upload from "@/components/Upload/Upload.vue";
let user_info = ref(uni.getStorageSync("USER_INFO"));
let staging = process.env.NODE_ENV == "development" ? "dev" : "";
let currentTitle = ref(""); // 当前班级名称
let currentClassId = ref(""); // 当前班级id
let currentSchoolId = ref(""); // 当前学校id
let isLogout = ref(false);
let isClassShow = ref(false);
let columns = reactive([[]]);
let uPicker = ref(null);
let pickerLoading = ref(false);
let isPopup = ref(false);
const externalLink = "https://work.weixin.qq.com/kfid/kfc33433ad794129a42";

const classTitme = computed(() => {
  return currentTitle.value;
});

let nickname = ref("");

const userId = computed(() => {
  return uni.getStorageSync("USER_INFO").id;
});

const classId = computed(() => {
  return currentClassId.value;
});
const actionList = [
  {
    title: "修改昵称",
    key: "changenickname",
    url: "/guidePage/changeNickname/changeNickname",
  },
  {
    title: "修改密码",
    key: "changePassword",
    url: "/privacyPolicy/updatePwd/updatePwd",
  },
  {
    title: "联系客服",
    key: "lxkf",
    url: "https://work.weixin.qq.com/kfid/kfc33433ad794129a42",
  },
  {
    title: "切换班级",
    key: "changeClass",
    url: "",
  },
  {
    title: "操作指引",
    key: "czzy",
    url: "/guidePage/guidePage",
  },
  {
    title: "平台服务协议",
    key: "ptfu",
    url: "/privacyPolicy/privacy/privacyPolicy?type=yh",
  },
  {
    title: "隐私政策",
    key: "yszc",
    url: "/privacyPolicy/privacy/privacyPolicy?type=ys",
  },
];

let UploadRef = ref(null);
const changeAvatarCallback = async (list) => {
  console.log("list", list);
  const params = {
    id: uni.getStorageSync("USER_INFO").id,
    headerId: list[0].id,
  };
  // 把id给头像接口保存到后端
  try {
    const res = await updateAvatarOrNickName(params);
    if (res.status == 0) {
      const res = await getUserInfo();
      uni.$u.toast("修改头像成功");
      uni.setStorageSync("USER_INFO", res.data);
      user_info.value = uni.getStorageSync("USER_INFO");
      isPopup.value = false;
    }
  } catch (error) {
    console.log(error);
    uni.$u.toast("修改头像失败");
  }
};
let changeAvatar = () => {
  UploadRef.value.selectImage();
};

function tapList(item) {
  switch (item.key) {
    case "logout":
      isLogout.value = true;
      break;

    case "changeClass":
      isClassShow.value = true;
      break;
    case "lxkf":
      // #ifdef MP-WEIXIN
      console.log("联系客服");
      wx.openCustomerServiceChat({
        extInfo: { url: externalLink },
        corpId: "ww1da4260b4b85c0bd",
        success(res) {},
        fail(res) {
          console.log("失败:", res);
        },
      });
      // #endif
      // #ifdef H5
      window.open(externalLink);
      // #endif

      break;
    default:
      uni.navigateTo({
        url: item.url,
      });
  }
}
function logout() {
  isLogout.value = false;
  uni.navigateTo({
    url: "/pages/login/login",
  });
  clearToken(true);
}
async function classConfirm(e) {
  const { value, values, indexs } = e;
  console.log(e);

  useCurrentClassId(value[1].id);
  currentTitle.value = value[1].title;
  currentClassId.value = value[1].id;
  // 确保学校信息也同步更新
  if (value[0]) {
    currentSchoolId.value = value[0].id;
    // 同步更新学校名称到本地存储
    uni.setStorageSync("CURRENT_SCHOOL_TITLE", value[0].title);
  }
  isClassShow.value = false;
}
const classChange = async (e) => {
  const { columnIndex, value } = e;

  console.log(e);
  if (columnIndex === 0) {
    pickerLoading.value = true;
    let res = await useCurrentClassId(null, value[columnIndex].id);
    // 更新当前学校ID和名称，确保数据一致性
    currentSchoolId.value = value[columnIndex].id;
    // 同步更新学校名称到本地存储
    uni.setStorageSync("CURRENT_SCHOOL_TITLE", value[columnIndex].title);
    columns.pop();
    classList(value[columnIndex].id);
    console.log(res);
    pickerLoading.value = false;
  }
};
// 获取班级
const classList = async (schoolId) => {
  let user = uni.getStorageSync("USER_INFO");
  currentClassId.value = user.currentClassId;
  const data = {
    schoolId,
  };
  const fn = schoolId ? getclassList(data) : getclassList();
  const res = await fn;
  console.log(res);

  const list = res.data;
  if (res.status == 0) {
    currentTitle.value = list.find(
      (item) => item.id == currentClassId.value
    )?.title;
    columns.push(list);
  }
};
// 获取学校
const schoolList = async () => {
  columns.length = 0;
  let user = uni.getStorageSync("USER_INFO");
  currentSchoolId.value = user.currentSchoolId;
  const res = await getSchoolList();
  if (res.status == 0) {
    columns[0] = res.data;
    let val = res.data.find((item) => item.id == user.currentSchoolId);
    let index = res.data.findIndex((item) => item.id == user.currentSchoolId);
    columns[0].splice(index, 1);
    columns[0].unshift(val);
    console.log("school", columns[0]);

    classList();
  }
};
//解决在微信小程序端点击tabbar的底层逻辑并不是触发uni.switchTab。所以误认为拦截无效，此类场景的解决方案是在tabbar页面的页面生命周期onShow中处理。
onShow(() => {
  checks();
  //   classList();
  schoolList();
  nickname.value = uni.getStorageSync("USER_INFO").nickname;
  // #ifdef MP-WEIXIN
  headleLogger("我的");
  // #endif
});
onShareAppMessage(() => sharePageObj());
</script>
<script>
export default {
  options: { styleIsolation: "shared" }, // 解除样式隔离
};
</script>

<style lang="scss" scoped>
.layout {
  overflow: hidden;
  box-sizing: border-box;
  background: #f2f2f2;
  height: 100%;
  background: url("https://c.mypacelab.com/vxmp/img/start_backgroud_3x.png")
    no-repeat;
  background-size: cover;

  // :deep(.u-picker__view__column__item) {
  //   line-height: initial !important;
  // }
  // :deep(.u-picker__view__column:last-child .u-picker__view__column__item) {
  //   line-height: 44px !important;
  //   text-align: center !important;
  // }
  // :deep(.u-line-1) {
  //   text-overflow: initial;
  //   word-break: initial;
  //   display: flex !important;
  //   -webkit-line-clamp: initial;
  //   -webkit-box-orient: initial !important;
  // }

  .bottom_text {
    position: fixed;
    bottom: 40rpx;
    width: 100%;
    text-align: center;
    // #ifdef H5
    bottom: 70px;
    // #endif
    font-size: 24rpx;
    font-weight: 400;
    color: rgba(22, 93, 255, 1);
  }

  .user_content {
    height: 380rpx;
    background-position: center;
    background-size: cover;
    display: flex;
    align-items: center;
    position: relative;

    .img {
      position: absolute;
      top: 0;
      left: 0;
      z-index: -1;
    }

    .user_content_img {
      margin: 0 30rpx 0 25rpx;
      position: relative;
      &_jia {
        position: absolute;
        bottom: 0rpx;
        right: 0rpx;
        image {
          width: 40rpx;
          height: 40rpx;
        }
      }
    }

    .user_content_text {
      flex: 1;
      color: #000;
      font-size: 35rpx;

      > view:not(:first-child) {
        font-size: 25rpx;
        font-weight: 400;
        margin-top: 10rpx;
      }
    }
  }

  .action_list {
    background-color: #fff;
    margin: 24rpx 32rpx;
    border-radius: 28rpx;
    overflow: hidden;
  }

  .logout {
    height: 80rpx;
    line-height: 80rpx;
    // width: 100vw;
    position: fixed;
    bottom: 110rpx;
    left: 0;
    width: 100%;
    text-align: center;
    // #ifdef H5
    bottom: 110px;
    // #endif
    color: rgba(22, 93, 255, 1);
    font-size: 30rpx;
    font-weight: 600;
    box-sizing: border-box;
  }
}
</style>
