<template>
  <view
    class="layout"
    @touchmove.stop.prevent="() => {}"
  >
    <view :style="{ height: menuFill() + 'px' }"></view>
    <view
      class="title"
      v-if="isPassword"
    >
      <view class="phone_code">手机密码登录</view>
      <view class="tips">仅限企业员工内部使用，请联系管理员开启登录权限</view>
    </view>
    <view
      class="content"
      :style="[!isPassword ? 'margin: auto 0' : '']"
    >
      <up-form
        v-if="isPassword"
        :rules="rules"
        :model="userData"
        ref="form1"
      >
        <up-form-item prop="mobile">
          <up-input
            class="input-style"
            fontSize="14px"
            placeholder="请输入手机号"
            v-model="userData.mobile"
            border="none"
            shape="circle"
          >
            <template #prefix>
              <up-icon
                style="margin: 0 16rpx"
                size="48rpx"
                name="/static/img/login/login-phone.png"
              ></up-icon>
            </template>
          </up-input>
        </up-form-item>
        <up-form-item prop="pwd">
          <up-input
            class="input-style"
            fontSize="14px"
            type="password"
            placeholder="请输入密码"
            v-model="userData.pwd"
            border="none"
            shape="circle"
          >
            <template #prefix>
              <up-icon
                style="margin: 0 16rpx"
                size="48rpx"
                name="/static/img/login/login-password.png"
              ></up-icon>
            </template>
          </up-input>
        </up-form-item>
      </up-form>

      <!-- 一键登录 -->
      <view v-else>
        <view class="logo">
          <image
            class="logo-img"
            src="https://c.mypacelab.com/logo/logo.png"
            mode="scaleToFill"
          />
          <view class="logo-tit">欢迎来到幼立方</view>
          <view class="tips"
            >仅限企业员工内部使用，请联系管理员开启登录权限</view
          >
        </view>
        <!-- 手机快捷登录 -->
        <up-button
          v-if="!isRadio"
          class="b_btn"
          color="#367CFF"
          type="primary"
          shape="circle"
          text="手机号快捷登录"
          @click="getphonenumberFn"
        />
        <up-button
          v-else
          class="b_btn"
          color="#367CFF"
          type="primary"
          shape="circle"
          text="手机号快捷登录"
          open-type="getPhoneNumber"
          @getphonenumber="getphonenumberFn"
        />
      </view>

      <!-- 隐私协议 -->
      <view class="protocol">
        <up-checkbox
          size="40rpx"
          name="agree"
          activeColor="#165dff"
          usedAlone
          shape="circle"
          v-model:checked="isRadio"
        />

        <view>
          同意
          <text @click="seeToPrivacyPolicy('yh')">《平台服务协议》</text>
          和
          <text @click="seeToPrivacyPolicy('ys')">《隐私政策》</text>
        </view>
      </view>

      <up-button
        v-if="isPassword"
        class="b_btn"
        color="#367CFF"
        type="primary"
        shape="circle"
        text="登录"
        @click="sendLogin"
      />
      <view
        class="btm"
        @click="handoff"
        >{{ handoffText }}</view
      >
    </view>
    <up-button
      shape="circle"
      text="我是家长"
      class="parent-btn"
      color="#367CFF"
      @tap="toParent"
    />
  </view>
</template>

<script setup>
import { reactive, ref, onMounted } from "vue";
import { onUnload, onShow } from "@dcloudio/uni-app";
import config from "@/common/config.js";
// import { getsms, login, loginPwd, getUserInfo } from "@/api/login.js";
import { wxLogin, getPhoneNumber, getUserInfo, loginPwd } from "@/api/login.js";
import { menuFill } from "@/utils/index.js";
import { setToken } from "@/utils/token.js";
let isPassword = ref(false); // 是否密码登录
let handoffText = ref("使用账号密码登录");
let timer = null;
let isRadio = ref(false); // 是否同意隐私
const TOKEN_NAME = config[config.DEFINE_ENV].TOKEN_NAME; // token名称
const APP_ID = config[config.DEFINE_ENV].APPID; // appid
let isPhoneText = ref("手机密码登录"); // 切换登录描述文字

let userData = reactive({
  mobile: "",
  countryCode: "+86",
  code: "",
  pwd: "",
});

// 验证规则
const rules = reactive({
  mobile: [
    {
      message: "请填写手机号",
      trigger: ["change", "blur"],
      required: true,
    },
    {
      validator: (rule, value, callback) => {
        return uni.$u.test.mobile(value);
      },
      message: "手机号码不正确",
      // 触发器可以同时用blur和change
      trigger: ["change", "blur"],
    },
  ],
  code: {
    type: "number",
    required: true,
    message: "请填写验证码",
    trigger: ["blur"],
  },
  pwd: {
    required: true,
    message: "请输入密码",
    trigger: ["blur"],
  },
});

// 静默登录
const wxLoginApi = async () => {
  const data = await uni.login();
  const res = await wxLogin({
    appId: APP_ID,
    code: data?.code,
  });
  const { user, token } = res.data;
  if (res.status == 0) {
    if (token?.token) {
      setUserInfo(token, user);
      return;
    }
    let { openId, unionId } = user;
    return Promise.resolve({ openId, unionId });
  } else {
    uni.$u.toast(res?.error?.stack);
  }
};

const getphonenumberFn = async (e) => {
  if (!isRadio.value) {
    uni.$u.toast("请您先阅读隐私政策和平台协议并同意后再登录");
    return;
  }
  const code = e.detail.code;
  const { openId, unionId } = await wxLoginApi();
  console.log(openId, unionId);

  const res = await getPhoneNumber({
    appId: APP_ID,
    code,
    openId,
    unionId,
  });
  if (res.status === 0) {
    setUserInfo(res?.data?.token, res?.data?.user);
  } else {
    uni.$u.toast(res?.error?.stack);
  }
};

// 跳转到家长端
const toParent = () => {
  uni.navigateToMiniProgram({
    appId: "wxd63b44bba4940859",
    path: "pages/login/login",
    success(res) {
      // 打开成功
    },
  });
};

// 查看隐私政策
function seeToPrivacyPolicy(type) {
  uni.navigateTo({
    url: `/privacyPolicy/privacy/privacyPolicy?type=${type}`,
  });
}

function handoff() {
  handoffText.value = isPassword.value
    ? "使用账号密码登录"
    : "使用手机一键登录";
  isPhoneText.value = isPassword.value
    ? "使用手机一键登录"
    : "使用账号密码登录";
  isPassword.value = !isPassword.value;
}

// 登录
const sendLogin = async () => {
  const { mobile, countryCode, code, pwd } = userData;
  // 判断是什么登录
  let res;
  if (!isRadio.value) {
    uni.$u.toast("请您先同意隐私政策和平台协议！");
    return;
  }
  res = await loginPwd({
    mobile,
    pwd,
  });

  if (res.status == 0) {
    const { token, user } = res.data;
    // //token判断环境的token
    setUserInfo({ token }, user);
  } else {
    uni.showToast({
      title: res.message,
      icon: "error",
    });
  }
};

const setUserInfo = async (o, u = {}) => {
  const user = u;
  setToken(o.token);
  let me = await getUserInfo();
  const { currentClassId, classIds, currentSchoolId, currentTerm, header } = me.data;
  user.currentClassId = currentClassId;
  user.classIds = classIds;
  user.currentSchoolId = currentSchoolId;
  user.currentTerm = currentTerm;
  if (header) user.header = header;
  uni.setStorageSync("USER_INFO", user);
  uni.removeStorageSync("USER_CLEAR");
  uni.reLaunch({
    url: "/pages/index/index",
  });
};

onShow(() => {
  // 如果已经登录直接跳转到首页
  if (uni.getStorageSync(TOKEN_NAME)) {
    uni.reLaunch({
      url: "/pages/index/index",
    });
  }
});

onMounted(() => {
  if (!uni.getStorageSync("USER_CLEAR")) wxLoginApi();
});

onUnload(() => {
  clearInterval(timer);
  timer = null;
});
</script>

<style lang="scss" scoped>
@import "@/common/css/index.scss";

.parent-btn {
  width: 300rpx !important;
  height: 100rpx !important;
  position: fixed !important;
  bottom: calc(env(safe-area-inset-bottom) + 64rpx);
  left: 50%;
  transform: translateX(-50%);
  font-size: 34rpx;
  font-weight: 500;
}

.layout {
  width: 100vw;
  height: 100vh;
  box-sizing: border-box;
  background-color: $input-text-color;
  background: url("https://c.mypacelab.com/vxmp/img/start_backgroud_3x.png")
    no-repeat;
  background-size: cover;
  display: flex;
  align-items: center;
  flex-direction: column;
}

.title {
  padding-left: 60rpx;
  min-height: 120rpx;
  overflow: hidden;
  margin-bottom: 64rpx;
  margin-top: 152rpx;

  .tips {
    width: 85%;
    color: #969799;
    font-size: 26rpx;
    line-height: 40rpx;
    margin-top: 20rpx;
  }
}

.phone_code {
  color: rgba(51, 51, 51, 1);
  font-weight: 600;
  font-size: 56rpx;
  line-height: 60rpx;
}

.content {
  width: 100vw;
  padding: 0 60rpx;
  box-sizing: border-box;

  .b_btn {
    height: 100rpx;
    font-size: 34rpx;
    font-weight: 500;
  }

  .logo {
    display: flex;
    align-items: center;
    flex-direction: column;
    margin-bottom: 140rpx;
    .logo-img {
      width: 180rpx;
      height: 180rpx;
      border-radius: 50%;
      margin-bottom: 45rpx;
    }
    .logo-tit {
      font-size: 36rpx;
      font-weight: 600;
      color: rgba(51, 51, 51, 1);
    }
    .tips {
      width: 100%;
      color: #969799;
      text-align: center;
      font-size: 26rpx;
      line-height: 40rpx;
      margin-top: 20rpx;
    }
  }

  .input-style {
    height: 100rpx;
    background: #ffffff;

    .coedStyle {
      width: 200rpx;
      height: 64rpx;
      margin-right: 16rpx;
    }
  }

  .protocol {
    margin-top: 48rpx;
    font-size: 24rpx;
    margin-bottom: 120rpx;
    display: flex;
    justify-content: center;
    align-items: center;

    .icon {
      width: 40rpx;
      height: 40rpx;
      border-radius: 50%;
      background-color: white;
      margin-right: 16rpx;
    }

    .activeIcon {
      background-color: #165dff;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    text {
      color: #165dff;
    }
  }
}

.btm {
  color: blue;
  font-size: 28rpx;
  text-align: center;
  margin-top: 48rpx;
  font-weight: 400;
}

::v-deep .u-form-item__body__right__message {
  margin-left: 70rpx !important;
}
</style>
