<template>
  <BaseLayout2
    navTitle=""
    :autoBack="false"
    :footerStyle="{
      display: 'none',
    }"
  >
    <view class="authorize-container">
      <image
        class="logo"
        src="https://c.mypacelab.com/logo/logo.png"
        mode="aspectFit"
      />
      <view class="title">授权登录</view>
      <view class="desc">授权后将获得以下权限</view>

      <view class="auth-info">
        <view class="info-item">
          <uni-icons
            type="person"
            size="20"
          ></uni-icons>
          <text>获得您的公开信息（昵称、头像等）</text>
        </view>
      </view>

      <view class="btn-group">
        <up-button
          class="btn btn-primary"
          :disabled="isDisabled"
          @click="handleAuthorize"
          >同意授权</up-button
        >
        <up-button
          class="btn btn-default"
          :disabled="isDisabled"
          @click="handleCancel"
          >取消</up-button
        >
      </view>

      <view class="privacy">
        授权即表示已阅读并同意
        <text
          class="link"
          @click="toPrivacy"
          >《隐私政策》</text
        >
      </view>
    </view>
  </BaseLayout2>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import BaseLayout2 from "@/components/base-layout/base-layout2.vue";
import { confirmQrCode, openQrCode } from "@/api/login.js";
import { split } from "lodash-es";

let payload = ref(null);
let isDisabled= ref(false);
onLoad((options) => {
  let params = decodeURIComponent(options.scene)
  console.log(params.split("=")[1]);
  payload.value = params.split("=")[1];
})
// 同意授权
const handleAuthorize = async () => {
  // TODO: 处理授权逻辑
  if (!payload.value) {
    return;
  }
  const res = await confirmQrCode({ payload: payload.value });
  if (res.status == 0) {
    
    uni.showToast({
      title: "授权成功",
      icon: "success",
    });
    setTimeout(() => {
      uni.reLaunch({
        url: "/pages/index/index",
      });
    }, 1500);
  }
};

// 取消授权
const handleCancel = () => {
  // 关闭所有页面，跳回主页面
  uni.reLaunch({
    url: "/pages/index/index",
  });
};
// 使用二维码，防止多名用户扫
const useQrCode = async () => {
  const res = await openQrCode({ payload: payload.value });
  console.log(res);
  if (res.status == 500) {
    uni.$u.toast(res?.error?.stack);
    isDisabled.value = true;
  }
};

// 查看隐私政策
const toPrivacy = () => {
  uni.navigateTo({
    url: "/privacyPolicy/privacy/privacyPolicy?type=ys",
  });
};
onMounted(() => {
  useQrCode();
})
</script>

<style lang="scss" scoped>
.authorize-container {
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;

  .logo {
    width: 200rpx;
    height: 200rpx;
    border-radius: 50%;
    margin: 60rpx 0;
  }

  .title {
    font-size: 36rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
  }

  .desc {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 40rpx;
  }

  .auth-info {
    width: 100%;
    padding: 20rpx 0;
    border-radius: 8rpx;
    margin-bottom: 40rpx;
    background: rgba(255, 255, 255, 0.8);

    .info-item {
      display: flex;
      align-items: center;
      padding: 0 30rpx;
      gap: 16rpx;
      font-size: 28rpx;
      color: #666;
    }
  }

  .user-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 60rpx;

    .avatar {
      width: 120rpx;
      height: 120rpx;
      border-radius: 60rpx;
      margin-bottom: 20rpx;
    }

    .name {
      font-size: 32rpx;
      color: #333;
    }
  }

  .btn-group {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 20rpx;
    margin-bottom: 40rpx;

    .btn {
      width: 100%;
      height: 88rpx;
      border-radius: 44rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32rpx;

      &.btn-primary {
        background: #2979ff;
        color: #fff;
      }

      &.btn-default {
        border: 1px solid #c3c3c3;
        background: #f5f5f5;
        color: #666;
      }
    }
  }

  .privacy {
    font-size: 24rpx;
    color: #999;

    .link {
      color: #2979ff;
    }
  }
}
</style>
