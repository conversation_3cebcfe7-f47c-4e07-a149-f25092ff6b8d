<template>
  <view class="example">
    <view
      class="example-btn"
      @tap="allSkip"
      >全部跳过</view
    >
    <!-- <image
      class="example-image"
      :src="imgList[current]"
      mode="widthFix"
      style="width: 100%"
      @tap="onNextImg"
    ></image> -->
    <image
      v-for="(item, index) in imgList"
      class="example-image"
      :src="item"
      mode="widthFix"
      style="width: 100%"
      @tap="onNextImg"
    ></image>
  </view>
</template>

<script setup>
import { reactive, ref } from "vue";
// import { onLoad } from "@dcloudio/uni-app";
let current = ref(0);
const imgList = reactive([
  "https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/guide/useGuide_1.png",
  "https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/guide/useGuide_02.png",
  "https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/guide/useGuide_3.png",
  "https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/guide/useGuide_4.png",
  "https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/guide/useGuide_5.png",
  "https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/guide/useGuide_6.png",
  "https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/guide/useGuide_7.png",
  "https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/guide/useGuide_8.png",
]);
imgList.reverse();
const onNextImg = () => {
  // 删除队尾
  imgList.pop();
  // 当队尾为空时。跳回主页
  if (imgList.length === 0) {
    uni.switchTab({
      url: "/pages/user/user",
    });
    return;
  }
  // console.log(imgList);
  // if (current.value >= imgList.length - 1) {
  //   uni.switchTab({
  //     url: "/pages/user/user",
  //   });
  //   return;
  // }
  // current.value++;
};
const onSkip = () => {
  // 删除队尾
  imgList.pop();
  // 当队尾为空时。跳回主页
  console.log(imgList);
  // if (current.value >= imgList.length - 1) {
  //   uni.switchTab({
  //     url: "/pages/user/user",
  //   });
  //   return;
  // }
  // current.value++;
};

const allSkip = () => {
  uni.switchTab({
    url: "/pages/user/user",
  });
};
</script>

<style scoped lang="scss">
.example {
  height: 100%;
  box-sizing: border-box;
  // padding: 32rpx;
  &-image {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 100;
  }
  &-btn {
    width: 192rpx;
    height: 66rpx;
    line-height: 66rpx;
    text-align: center;
    box-sizing: border-box;
    border: 1px solid white;
    border-radius: 34rpx;
    font-size: 30rpx;
    font-weight: 500;
    color: white;
    position: fixed;
    top: 200rpx;
    right: 40rpx;
    z-index: 101;
  }
}
</style>
