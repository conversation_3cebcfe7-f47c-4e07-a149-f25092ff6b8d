<template>
  <BaseLayout2
    nav-title="修改昵称"
    :footerStyle="{
      padding: '16rpx 32rpx',
      paddingBottom: 'calc(16rpx + env(safe-area-inset-bottom))',
      background: '#fff',
    }"
  >
    <view style="padding: 0 32rpx">
      <up-form
        labelPosition="left"
        :model="params"
        ref="form1"
      >
        <up-form-item
          label="昵称："
          labelWidth="110rpx"
          borderBottom
        >
          <up-input
            v-model="params.nickname"
            placeholder="请输入昵称"
          />
        </up-form-item>
      </up-form>
    </view>
    <template #footer>
      <up-button
        type="primary"
        text="保存"
        shape="circle"
        color="#367CFF"
        @tap="saveInfo"
      />
    </template>
  </BaseLayout2>
</template>

<script setup>
import { reactive } from "vue";
import BaseLayout2 from "@/components/base-layout/base-layout2.vue";
import { updateAvatarOrNickName } from "@/guidePage/api";
import { getUserInfo } from "@/api/login.js";

const params = reactive({
  id: uni.getStorageSync("USER_INFO").id,
  nickname: uni.getStorageSync("USER_INFO").nickname || "",
});

const saveInfo = async () => {
  console.log(params);
  if (params.nickname == "") {
    return uni.showToast({
      title: "请输入昵称!",
      icon: "none",
    });
  }
  const res = await updateAvatarOrNickName(params);
  if (res.status === 0) {
    const res2 = await getUserInfo();
    if (res2.status === 0) {
      uni.showToast({
        title: "修改成功",
        icon: "success",
      });
      uni.setStorageSync("USER_INFO", res2.data);
      uni.navigateBack();
    }
  }
  console.log(res);
};
</script>

<style lang="scss" scoped></style>
