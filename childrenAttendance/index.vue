<template>
  <BaseLayout nav-title="出勤记录" :content-style="{ padding: '0' }">
    <view class="container">
      <!-- 头部信息 -->
      <view class="header">
        <view class="class-info">
          <text class="class-name">{{ className }}</text>
          <view class="summary-filter" @click="handleSummaryClick">
            <text class="summary-text">本学期出勤汇总</text>
          </view>
        </view>
      </view>

      <!-- 日期范围选择器 -->
      <view class="date-picker-section">
        <uni-datetime-picker
          type="daterange"
          v-model="selectedDateRange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleDateRangeChange"
          :border="false"
          :clear-icon="false"
        >
          <template v-slot:default>
            <view class="date-container">
              <text class="date">{{ formatDateRange }}</text>
              <up-icon name="arrow-down" size="24rpx" color="#999" />
            </view>
          </template>
        </uni-datetime-picker>
      </view>

      <!-- 出勤记录列表 -->
      <view class="attendanceRecord" v-if="attendanceList.length > 0">
        <view v-for="item in attendanceList" :key="item.date" class="date-group">
          <view class="attendanceRecord-item" @click="handleItemClick(item)">
            <view class="attendanceRecord-item-content">
              <view class="content-item date-item">
                <view class="item-label">日期:</view>
                <view class="item-value"
                  >{{ item.dateDisplay }}&nbsp;{{ item.dayOfWeek }}&nbsp;{{ item.year }}年</view
                >
              </view>
              <view class="content-item">
                <view class="item-label">入园:</view>
                <view class="item-value">
                  (总: {{ item.fullCount }}人, 已入园: {{ item.presentCount }}, 缺勤:
                  {{ item.absentCount }})
                </view>
              </view>
              <view class="content-item">
                <view class="item-label">记录:</view>
                <view class="item-value">{{ item.updateUserName }}, {{ item.updateTime }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view v-else-if="!isLoading" class="empty">暂无出勤记录</view>

      <!-- 加载状态 -->
      <view v-if="isLoading" class="loading">加载中...</view>


    </view>
  </BaseLayout>
</template>

<script setup>
import BaseLayout from '@/components/base-layout/base-layout.vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { ref, computed } from 'vue'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import { getAttendanceRecordList } from './api'
import { getclassList } from '@/api/classApi.js'

// 设置为中文
dayjs.locale('zh-cn')

// 页面状态
const className = ref('-')
const classId = ref('')
const attendanceList = ref([])
const isLoading = ref(false)
const showDatePicker = ref(false)

// 日期范围状态
const selectedDateRange = ref([
  dayjs().subtract(7, 'day').format('YYYY-MM-DD'), // 默认最近7天
  dayjs().format('YYYY-MM-DD')
])

// 计算格式化的日期范围显示
const formatDateRange = computed(() => {
  if (selectedDateRange.value && selectedDateRange.value.length === 2) {
    const startDate = dayjs(selectedDateRange.value[0]).format('MM月DD日')
    const endDate = dayjs(selectedDateRange.value[1]).format('MM月DD日')
    return `${startDate} - ${endDate}`
  }
  return '选择日期范围'
})

// 处理日期范围变化
const handleDateRangeChange = (e) => {
  if (e && e.length === 2) {
    selectedDateRange.value = e
    showDatePicker.value = false
    // 重新获取数据
    fetchAttendanceRecords()
  }
}

// 获取出勤记录列表
const fetchAttendanceRecords = async () => {
  try {
    isLoading.value = true

    const res = await getAttendanceRecordList({
      classId: classId.value,
      startDate: selectedDateRange.value[0],
      endDate: selectedDateRange.value[1]
    })

    if (res && res.data) {
      // 处理数据
      attendanceList.value = processAttendanceData(res.data)
    }
  } catch (error) {
    console.error('获取出勤记录失败', error)
    uni.showToast({
      title: '获取出勤记录失败',
      icon: 'none'
    })
  } finally {
    isLoading.value = false
  }
}

// 处理出勤数据
const processAttendanceData = (data) => {
  return data
    .map((record) => {
      const date = dayjs(record.attendanceDate)
      const dateDisplay = date.format('MM月DD日')
      const dayOfWeek = date.format('dddd')
      const year = date.format('YYYY')
      const updateTime = dayjs(record.updateTime).format('YYYY.MM.DD HH:mm:ss')

      return {
        date: record.attendanceDate,
        dateDisplay,
        dayOfWeek,
        year,
        fullCount: record.fullCount,
        presentCount: record.presentCount,
        absentCount: record.absentCount,
        updateUserName: record.updateUserName,
        updateTime
      }
    })
    .sort((a, b) => {
      return dayjs(b.date).valueOf() - dayjs(a.date).valueOf()
    })
}

// 处理列表项点击事件
const handleItemClick = (item) => {
  uni.navigateTo({
    url: `/childrenAttendance/attendanceRecordDetail?className=${encodeURIComponent(
      className.value
    )}&attendanceDate=${item.date}`
  })
}

// 处理本学期出勤汇总点击事件
const handleSummaryClick = () => {
  uni.navigateTo({
    url: `/childrenAttendance/attendanceRecordDetail?className=${encodeURIComponent(
      className.value
    )}&type=summary`
  })
}



// 获取班级名称
const getClassName = async () => {
  try {
    const userInfo = uni.getStorageSync('USER_INFO')
    const res = await getclassList({ schoolId: userInfo.currentSchoolId })
    if (res.status === 0 && res.data) {
      const classInfo = res.data.find(item => item.id === classId.value)
      if (classInfo) {
        className.value = classInfo.title
      }
    }
  } catch (error) {
    console.error('获取班级名称失败:', error)
  }
}

// 页面加载
onLoad(async () => {
  const userInfo = uni.getStorageSync('USER_INFO')
  classId.value = userInfo.currentClassId

  // 获取班级名称
  await getClassName()

  if (classId.value) {
    fetchAttendanceRecords()
  } else {
    uni.showToast({
      title: '缺少班级参数',
      icon: 'none'
    })
  }
})

// 页面显示时更新数据
onShow(async () => {
  const userInfo = uni.getStorageSync('USER_INFO')

  // 检查班级是否发生变化
  if (userInfo.currentClassId !== classId.value) {
    classId.value = userInfo.currentClassId
    // 更新班级名称
    await getClassName()
    // 重新获取数据
    fetchAttendanceRecords()
  }
})
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  .class-info {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 24rpx;
    .class-name {
      font-size: 28rpx;
      color: #333;
      font-weight: bold;
    }
    .summary-filter {
      display: flex;
      align-items: center;
      margin-left: auto;
      padding: 8rpx 16rpx;
      border-radius: 8rpx;
      background: rgba(24, 144, 255, 0.1);

      .summary-text {
        font-size: 28rpx;
        color: #1890ff;
      }
    }
  }
}



.date-picker-section {
  padding: 0 32rpx 20rpx 32rpx;
  .date-container {
    display: flex;
    align-items: center;
    padding: 16rpx 20rpx;
    border-radius: 12rpx;
    gap: 8rpx;
    background: #f8f9fa;
    border: 1rpx solid #e8e8e8;
    .date {
      font-size: 28rpx;
      color: rgba(128, 128, 128, 1);
      font-weight: normal;
    }
  }
}

.attendanceRecord {
  padding: 8rpx 32rpx 0 32rpx;

  .date-group {
    margin-bottom: 20rpx;
  }

  .attendanceRecord-item {
    background: #fff;
    margin-bottom: 20rpx;
    border-radius: 12rpx;
    box-shadow: 4rpx 8rpx 16rpx #eee;
    padding: 32rpx;
    border-radius: 28rpx;
    position: relative;
  }

  .attendanceRecord-item-content {
    .content-item {
      display: flex;
      margin-bottom: 16rpx;
      font-size: 28rpx;

      &:last-child {
        margin-bottom: 0;
      }

      &.date-item {
        .item-value {
          font-weight: 600;
          color: #333;
        }
      }

      .item-label {
        color: #666;
        width: 80rpx;
        flex-shrink: 0;
      }

      .item-value {
        color: #333;
        flex: 1;
        word-break: break-all;
      }
    }
  }
}

.empty {
  text-align: center;
  padding: 120rpx 0;
  color: #999;
  font-size: 28rpx;
}

.loading {
  text-align: center;
  padding: 60rpx 0;
  color: #999;
  font-size: 28rpx;
}
</style>
