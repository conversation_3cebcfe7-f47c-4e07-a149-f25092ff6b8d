<template>
  <view class="page-container2">
    <!-- 自定义导航栏 -->
    <up-navbar
      :title="pageTitle"
      :safeAreaInsetTop="true"
      :fixed="true"
      :placeholder="true"
      bgColor="transparent"
      :autoBack="true"
    >
    </up-navbar>

    <view class="container" :class="{ 'has-export-btn': isClassSummaryPage }">
      <!-- 顶部信息栏 -->
      <view class="header-bar">
        <view class="class-info">
          <text class="class-title">{{ isClassSummaryPage ? schoolName : className }}</text>
        </view>
        <view
          v-if="!isSummaryPage || isClassSummaryPage"
          class="date-filter"
          @click="showDatePicker = true"
        >
          <text class="date-text">{{ formatDate }}</text>
          <image class="change" src="/static/game/change.svg" />
        </view>
      </view>

      <!-- 加载状态 -->
      <view class="loading-container" v-if="isLoading">
        <view class="loading-content">
          <view class="loading-spinner"></view>
          <text class="loading-text">正在加载数据...</text>
        </view>
      </view>

      <!-- 出勤记录表格 -->
      <view class="table-container" v-else-if="attendanceRecords.length > 0">
        <view class="table-content">
          <!-- 表头 -->
          <view class="table-header">
            <view class="header-cell name-cell">{{ isClassSummaryPage ? '班级' : '姓名' }}</view>
            <view v-if="!isSummaryPage && !isClassSummaryPage" class="header-cell status-cell"
              >出勤状态</view
            >
            <view v-if="!isSummaryPage && !isClassSummaryPage" class="header-cell sick-type-cell"
              >病假类型</view
            >
            <view v-if="!isSummaryPage && !isClassSummaryPage" class="header-cell remark-cell"
              >备注</view
            >
            <view v-if="isSummaryPage" class="header-cell count-cell">出勤天数</view>
            <view v-if="isSummaryPage" class="header-cell count-cell">事假天数</view>
            <view v-if="isSummaryPage" class="header-cell count-cell">病假天数</view>
            <view v-if="isClassSummaryPage" class="header-cell count-cell">应出勤</view>
            <view v-if="isClassSummaryPage" class="header-cell count-cell attendance-cell">今日出勤</view>
            <view v-if="isClassSummaryPage" class="header-cell count-cell">事假</view>
            <view v-if="isClassSummaryPage" class="header-cell count-cell">病假</view>
            <view v-if="isClassSummaryPage" class="header-cell count-cell reason-cell">病假事由</view>
          </view>
          <!-- 表体 -->
          <view class="table-body">
            <view class="table-row" v-for="(record, index) in attendanceRecords" :key="index">
              <view class="body-cell name-cell">{{
                isClassSummaryPage ? record.className : record.title
              }}</view>
              <view v-if="!isSummaryPage && !isClassSummaryPage" class="body-cell status-cell">
                <view class="status-tag" :class="getStatusClass(record.state)">
                  {{ record.stateDesc }}
                </view>
              </view>
              <view v-if="!isSummaryPage && !isClassSummaryPage" class="body-cell sick-type-cell">{{
                record.sickLeaveTypeDesc || '-'
              }}</view>
              <view v-if="!isSummaryPage && !isClassSummaryPage" class="body-cell remark-cell">{{
                record.remark || '-'
              }}</view>
              <view v-if="isSummaryPage" class="body-cell count-cell">{{
                record.presentCount || 0
              }}</view>
              <view v-if="isSummaryPage" class="body-cell count-cell">{{
                record.personalLeaveCount || 0
              }}</view>
              <view v-if="isSummaryPage" class="body-cell count-cell">{{
                record.sickLeaveCount || 0
              }}</view>
              <view v-if="isClassSummaryPage" class="body-cell count-cell">{{
                record.expectedCount || 0
              }}</view>
              <view v-if="isClassSummaryPage" class="body-cell count-cell attendance-cell">{{
                record.actualCount || 0
              }}</view>
              <view v-if="isClassSummaryPage" class="body-cell count-cell">{{
                record.personalLeaveCount || 0
              }}</view>
              <view v-if="isClassSummaryPage" class="body-cell count-cell">{{
                record.sickLeaveCount || 0
              }}</view>
              <view v-if="isClassSummaryPage" class="body-cell count-cell reason-cell">{{
                record.sickLeaveReason || '-'
              }}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-else>
        <text>暂无出勤记录</text>
      </view>
    </view>

    <!-- 班级出勤汇总导出按钮 -->
    <view v-if="isClassSummaryPage" class="export-container">
      <up-button
        type="primary"
        text="导出Excel"
        :loading="isExporting"
        @click="handleExport"
        shape="circle"
        size="large"
      />
    </view>

    <!-- 日期选择器组件 -->
    <up-datetime-picker
      v-if="!isSummaryPage || isClassSummaryPage"
      :show="showDatePicker"
      v-model="selectedTimestamp"
      mode="date"
      :minDate="minDate"
      :maxDate="maxDate"
      @confirm="dateConfirm"
      @cancel="showDatePicker = false"
    ></up-datetime-picker>
  </view>
</template>

<script setup>
import { onLoad, onShow } from '@dcloudio/uni-app'
import { ref, computed } from 'vue'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import {
  getAttendanceRecordDetail,
  getAttendanceSummary,
  getClassAttendanceSummary,
  exportAttendanceSummary
} from './api'
import { getclassList } from '@/api/classApi.js'

// 设置为中文
dayjs.locale('zh-cn')

// 页面状态
const className = ref('-')
const schoolName = ref('-')
const classId = ref('')
const attendanceRecords = ref([])
const isLoading = ref(false)
const showDatePicker = ref(false)
const selectedTimestamp = ref(Date.now())
const isSummaryPage = ref(false)
const isClassSummaryPage = ref(false)
const termStartDate = ref('')
const termEndDate = ref('')
const isExporting = ref(false)

// 设置最小日期为一年前，允许查看历史数据
const minDate = ref(dayjs().subtract(1, 'year').startOf('day').valueOf())
// 设置最大日期为今天
const maxDate = ref(dayjs().endOf('day').valueOf())

// 添加选择的日期字符串，用于API调用
const selectedDate = computed(() => {
  const date = dayjs(selectedTimestamp.value)
  if (!date.isValid()) {
    console.error('Invalid timestamp:', selectedTimestamp.value)
    return dayjs().format('YYYY-MM-DD') // 返回当前日期作为默认值
  }
  return date.format('YYYY-MM-DD')
})

// 页面标题
const pageTitle = computed(() => {
  if (isSummaryPage.value) {
    return '学期出勤汇总'
  } else if (isClassSummaryPage.value) {
    return '全员出勤统计'
  } else {
    return '每日出勤记录'
  }
})

// 格式化日期显示
const formatDate = computed(() => {
  const date = dayjs(selectedTimestamp.value)
  if (!date.isValid()) {
    console.error('Invalid timestamp for display:', selectedTimestamp.value)
    const now = dayjs()
    const weekDay = now.format('dddd')
    const dateStr = now.format('YYYY年MM月DD日')
    return `${dateStr} ${weekDay}`
  }
  const weekDay = date.format('dddd')
  const dateStr = date.format('YYYY年MM月DD日')
  return `${dateStr} ${weekDay}`
})

// 格式化学期日期范围显示
const formatTermRange = computed(() => {
  if (termStartDate.value && termEndDate.value) {
    const startDate = dayjs(termStartDate.value).format('YYYY年MM月DD日')
    const endDate = dayjs(termEndDate.value).format('YYYY年MM月DD日')
    return `${startDate} - ${endDate}`
  }
  return isLoading.value ? '加载中...' : '学期时间范围'
})

// 获取状态样式类
const getStatusClass = (state) => {
  switch (state) {
    case 1: // 已入园
      return 'status-attended'
    case 2: // 已离园
      return 'status-departed'
    case 3: // 事假
      return 'status-personal-leave'
    case 4: // 病假
      return 'status-sick-leave'
    case 5: // 无记录
      return 'status-no-record'
    default:
      return 'status-default'
  }
}

// 获取出勤记录详情
const fetchAttendanceRecords = async () => {
  try {
    isLoading.value = true

    if (isSummaryPage.value) {
      // 获取学期出勤汇总
      const params = {
        classId: classId.value
      }

      console.log('学期汇总API请求参数:', params)

      const res = await getAttendanceSummary(params)

      console.log('学期汇总API响应:', res)

      if (res && res.data) {
        termStartDate.value = res.data.termStartDate
        termEndDate.value = res.data.termEndDate
        attendanceRecords.value = res.data.childList || []
        console.log('学期开始时间:', termStartDate.value)
        console.log('学期结束时间:', termEndDate.value)
        console.log('学期时间范围:', formatTermRange.value)
      }
    } else if (isClassSummaryPage.value) {
      // 获取班级出勤汇总
      const userInfo = uni.getStorageSync('USER_INFO')
      const params = {
        attendanceDate: selectedDate.value,
        schoolId: userInfo.currentSchoolId
      }

      console.log('班级汇总API请求参数:', params)

      const res = await getClassAttendanceSummary(params)

      console.log('班级汇总API响应:', res)

      if (res && res.data) {
        attendanceRecords.value = res.data || []
      }
    } else {
      // 获取每日出勤记录
      const params = {
        classId: classId.value,
        attendanceDate: selectedDate.value
      }

      console.log('每日记录API请求参数:', params)

      const res = await getAttendanceRecordDetail(params)

      console.log('每日记录API响应:', res)

      if (res && res.data) {
        attendanceRecords.value = res.data
      }
    }
  } catch (error) {
    console.error('获取出勤记录失败', error)
    uni.showToast({
      title: '获取出勤记录失败',
      icon: 'none'
    })
  } finally {
    isLoading.value = false
  }
}

// 日期确认事件
const dateConfirm = (event) => {
  console.log('Date picker confirmed with event:', event)

  // 确保timestamp是有效的 - 处理对象格式 {value: timestamp, mode: "date"}
  let timestamp
  if (typeof event === 'object' && event !== null && 'value' in event) {
    timestamp = event.value
  } else {
    timestamp = event
  }

  if (timestamp && !isNaN(timestamp)) {
    selectedTimestamp.value = timestamp
    console.log('Selected timestamp set to:', selectedTimestamp.value)
    console.log('Formatted date:', selectedDate.value)
  } else {
    console.error('Invalid timestamp received:', event)
    // 设置为当前日期
    selectedTimestamp.value = Date.now()
  }

  showDatePicker.value = false

  // 添加短暂延迟确保UI更新后再获取数据
  setTimeout(() => {
    fetchAttendanceRecords()
  }, 100)
}

// 获取文件类型
const getFileType = (fileName) => {
  if (!fileName) return 'xlsx'
  const extension = fileName.split('.').pop().toLowerCase()
  const typeMap = {
    'pdf': 'pdf',
    'doc': 'doc',
    'docx': 'docx',
    'xls': 'xls',
    'xlsx': 'xlsx',
    'ppt': 'ppt',
    'pptx': 'pptx',
    'txt': 'txt'
  }
  return typeMap[extension] || 'xlsx'
}

// 导出Excel
const handleExport = async () => {
  try {
    isExporting.value = true

    const userInfo = uni.getStorageSync('USER_INFO')
    const params = {
      attendanceDate: selectedDate.value,
      schoolId: userInfo.currentSchoolId
    }

    console.log('导出API请求参数:', params)

    uni.showLoading({
      title: '导出中...',
      mask: true
    })

    // 直接调用导出接口，接口返回二进制数据
    const res = await exportAttendanceSummary(params)

    console.log('导出API响应:', res)

    // 接口直接返回二进制数据，处理文件下载
    if (res) {
      // 生成文件名
      const fileName = `班级出勤汇总_${selectedDate.value}.xlsx`

      // #ifdef H5
      // H5环境下创建下载链接
      const blob = new Blob([res], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      uni.showToast({
        title: '导出成功',
        icon: 'success'
      })
      // #endif

      // #ifndef H5
      // 非H5环境下，需要先将二进制数据转换为临时文件
      const fs = uni.getFileSystemManager()
      const filePath = `${uni.env.USER_DATA_PATH}/${fileName}`

      fs.writeFile({
        filePath: filePath,
        data: res,
        encoding: 'binary',
        success: () => {
          uni.openDocument({
            filePath: filePath,
            showMenu: true,
            fileType: getFileType(fileName),
            success: function () {
              console.log('打开文档成功')
              uni.showToast({
                title: '导出成功',
                icon: 'success'
              })
            },
            fail: function (err) {
              console.error('打开文档失败:', err)
              uni.showToast({
                title: '打开文件失败',
                icon: 'none'
              })
            }
          })
        },
        fail: (err) => {
          console.error('保存文件失败:', err)
          uni.showToast({
            title: '保存文件失败',
            icon: 'none'
          })
        }
      })
      // #endif
    } else {
      uni.showToast({
        title: '导出失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('导出失败', error)
    uni.showToast({
      title: '导出失败',
      icon: 'none'
    })
  } finally {
    uni.hideLoading()
    isExporting.value = false
  }
}

// 获取班级名称
const getClassName = async () => {
  try {
    const userInfo = uni.getStorageSync('USER_INFO')
    const res = await getclassList({ schoolId: userInfo.currentSchoolId })
    if (res.status === 0 && res.data) {
      const classInfo = res.data.find((item) => item.id === classId.value)
      if (classInfo) {
        className.value = classInfo.title
      }
    }
  } catch (error) {
    console.error('获取班级名称失败:', error)
  }
}

// 页面加载
onLoad(async (options) => {
  try {
    className.value = options.className ? decodeURIComponent(options.className) : '大一班'
  } catch (error) {
    console.error('解码className失败:', error)
    className.value = options.className || '大一班'
  }
  const userInfo = uni.getStorageSync('USER_INFO')
  classId.value = userInfo.currentClassId

  // 获取学校名称
  const savedSchoolTitle = uni.getStorageSync('CURRENT_SCHOOL_TITLE')
  schoolName.value = savedSchoolTitle || '学校名称'

  // 获取班级名称
  await getClassName()

  // 判断页面类型
  if (options.type === 'summary') {
    isSummaryPage.value = true
    isClassSummaryPage.value = false
  } else if (options.type === 'classSummary') {
    isSummaryPage.value = false
    isClassSummaryPage.value = true
    // 班级汇总默认使用当前日期
    selectedTimestamp.value = Date.now()
  } else {
    isSummaryPage.value = false
    isClassSummaryPage.value = false
    // 如果传入了日期参数，使用传入的日期
    if (options.attendanceDate) {
      const date = dayjs(options.attendanceDate)
      if (date.isValid()) {
        selectedTimestamp.value = date.valueOf()
      } else {
        console.error('Invalid date:', options.attendanceDate)
        selectedTimestamp.value = Date.now() // 使用当前日期作为默认值
      }
    }
  }

  console.log(
    '页面类型:',
    isSummaryPage.value ? '学期汇总' : isClassSummaryPage.value ? '班级汇总' : '每日记录'
  )
  console.log('Selected date:', selectedDate.value)
  console.log('Class ID:', classId.value)

  if (classId.value) {
    fetchAttendanceRecords()
  } else {
    uni.showToast({
      title: '缺少班级参数',
      icon: 'none'
    })
  }
})

// 页面显示时更新数据
onShow(async () => {
  const userInfo = uni.getStorageSync('USER_INFO')

  // 检查班级是否发生变化
  if (userInfo.currentClassId !== classId.value) {
    classId.value = userInfo.currentClassId
    // 更新班级名称
    await getClassName()
    // 如果不是班级汇总页面，重新获取数据
    if (!isClassSummaryPage.value) {
      fetchAttendanceRecords()
    }
  }

  // 更新学校名称（可能在其他页面切换了学校）
  const savedSchoolTitle = uni.getStorageSync('CURRENT_SCHOOL_TITLE')
  if (savedSchoolTitle) {
    schoolName.value = savedSchoolTitle
  }
})
</script>

<script>
export default {
  options: { styleIsolation: 'shared', multipleSlots: true }
}
</script>

<style lang="scss" scoped>
.page-container2 {
  // display: flex;
  // flex-direction: column;
  height: 100vh;
  background: #f5f5f5 url('https://c.mypacelab.com/vxmp/img/history_background_3x.png') no-repeat
    center top;
  background-size: contain;
}

.container {
  flex: 1;
  min-height: 0;
  margin: 20rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  &.has-export-btn {
    padding-bottom: 100rpx; /* 为导出按钮预留空间 */
  }
}

// 顶部信息栏
.header-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;

  .class-info {
    .class-title {
      font-size: 28rpx;
      font-weight: bold;
      color: #333;
    }
  }

  .date-filter {
    display: flex;
    align-items: center;
    padding: 12rpx 20rpx;
    .date-text {
      font-size: 24rpx;
      color: rgba(128, 128, 128, 1);
      margin-right: 8rpx;
    }

    .change {
      width: 24rpx;
      height: 24rpx;
    }
  }
}

// 统计表格
.table-container {
  flex: 1;
  min-height: 0;
  border-radius: 28rpx;
  overflow: hidden;
  background: linear-gradient(0deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)),
    linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
  box-shadow: 4rpx 8rpx 16rpx rgba(0, 0, 0, 0.02);
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .table-content {
    flex: 1;
    min-height: 0;
    overflow-y: auto;
    box-sizing: border-box;
  }

  .table-header {
    display: flex;
    background: #fff;
    border-bottom: 1rpx solid rgba(247, 247, 247, 1);
    position: sticky;
    top: 0;
    z-index: 10;

    .header-cell {
      padding: 24rpx 8rpx;
      font-size: 24rpx;
      font-weight: 400;
      color: rgba(128, 128, 128, 1);
      text-align: center;
      background: #fff;

      &:last-child {
        border-right: none;
      }

      &.name-cell {
        flex: 1;
        text-align: left;
        padding-left: 20rpx;
      }

      &.status-cell {
        flex: 1.2;
      }

      &.sick-type-cell {
        flex: 1.2;
      }

      &.remark-cell {
        flex: 1.5;
        text-align: left;
      }

      &.count-cell {
        flex: 1;
      }

      &.attendance-cell {
        flex: 1.3;
      }

      &.reason-cell {
        flex: 1.5;
      }
    }
  }

  .table-body {
    .table-row {
      display: flex;
      border-bottom: 1rpx solid rgba(247, 247, 247, 1);

      &:last-child {
        border-bottom: none;
      }

      .body-cell {
        padding: 20rpx 8rpx;
        font-size: 24rpx;
        color: rgba(51, 51, 51, 1);
        text-align: center;
        display: flex;
        align-items: center;

        &:last-child {
          border-right: none;
        }

        &.name-cell {
          flex: 1;
          text-align: left;
          font-weight: 500;
          padding-left: 20rpx;
        }

        &.status-cell {
          flex: 1.2;
          justify-content: center;

          .status-tag {
            padding: 8rpx 16rpx;
            border-radius: 16rpx;
            font-size: 20rpx;
            font-weight: 500;

            &.status-attended {
              background: #f6ffed;
              color: #52c41a;
            }

            &.status-departed {
              background: #e6f7ff;
              color: #1890ff;
            }

            &.status-personal-leave {
              background: #fff7e6;
              color: #faad14;
            }

            &.status-sick-leave {
              background: #fff2f0;
              color: #ff4d4f;
            }

            &.status-no-record {
              background: #f5f5f5;
              color: #999;
            }

            &.status-default {
              background: #f5f5f5;
              color: #666;
            }
          }
        }

        &.sick-type-cell {
          flex: 1.2;
          justify-content: center;
          color: #666;
          font-size: 22rpx;
        }

        &.remark-cell {
          flex: 1.5;
          text-align: left;
          color: #666;
          word-break: break-all;
        }

        &.count-cell {
          flex: 1;
          justify-content: center;
          font-weight: 500;
        }

        &.attendance-cell {
          flex: 1.3;
          justify-content: center;
          font-weight: 500;
        }

        &.reason-cell {
          flex: 1.5;
          justify-content: center;
          font-weight: 500;
        }
      }
    }
  }
}

// 加载状态
.loading-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400rpx;

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;

    .loading-spinner {
      width: 60rpx;
      height: 60rpx;
      border: 4rpx solid #f3f3f3;
      border-top: 4rpx solid #3f79ff;
      border-radius: 50%;
      animation: loading-spin 1s linear infinite;
      margin-bottom: 20rpx;
    }

    .loading-text {
      font-size: 28rpx;
      color: #666;
    }
  }
}

@keyframes loading-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 空状态
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  color: #999;
  font-size: 28rpx;
}

.export-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 32rpx;
  background: #fff;
  border-top: 1rpx solid #eee;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}
</style>
