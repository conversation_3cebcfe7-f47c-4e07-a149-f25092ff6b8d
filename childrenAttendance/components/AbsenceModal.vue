<template>
  <!-- 请假弹窗 -->
  <up-popup :show="show" mode="bottom" :round="20" @close="handleClose">
    <view class="leave-modal-content">
      <view class="leave-modal-header">
        <text class="leave-modal-title">标记缺勤</text>
        <text class="close-btn" @click="handleClose">×</text>
      </view>
      <view class="leave-modal-body">
        <view class="selected-child" v-if="selectedChild">
          <view class="child-avatar">
            <image
              class="avatar-img"
              :src="getChildAvatar(selectedChild)"
              mode="aspectFill"
            ></image>
          </view>
          <text class="child-name">请选择 {{ selectedChild.title }} 缺勤的原因</text>
        </view>
        <!-- Tab 切换 -->
        <view class="tab-section">
          <view class="tab-buttons">
            <view
              class="tab-btn"
              :class="{ active: activeTab === 'absence' }"
              @click="switchTab('absence')"
            >
              <text>缺勤原因</text>
            </view>
            <view
              class="tab-btn"
              :class="{ active: activeTab === 'status' }"
              @click="switchTab('status')"
            >
              <text>状态修改</text>
            </view>
          </view>
        </view>

        <!-- 缺勤原因选择 -->
        <view v-if="activeTab === 'absence'" class="form-section">
          <view class="section-title">缺勤原因</view>
          <view class="absence-type-buttons">
            <view
              class="absence-type-btn"
              :class="{ active: selectedAbsenceType === 'personal' }"
              @click="selectAbsenceType('personal')"
            >
              <text>事假</text>
            </view>
            <view
              class="absence-type-btn"
              :class="{ active: selectedAbsenceType === 'sick' }"
              @click="selectAbsenceType('sick')"
            >
              <text>病假</text>
            </view>
          </view>
        </view>

        <!-- 状态修改选择 -->
        <view v-if="activeTab === 'status'" class="form-section">
          <view class="section-title">状态修改</view>
          <view class="status-type-buttons">
            <view
              class="status-type-btn"
              :class="{ active: selectedStatusType === 'present' }"
              @click="selectStatusType('present')"
            >
              <text>出勤</text>
            </view>
            <view
              class="status-type-btn"
              :class="{ active: selectedStatusType === 'departed' }"
              @click="selectStatusType('departed')"
            >
              <text>离园</text>
            </view>
          </view>
        </view>

        <!-- 事假事由输入框 -->
        <view v-if="activeTab === 'absence' && selectedAbsenceType === 'personal'" class="form-section">
          <view class="section-title">事假事由</view>
          <up-textarea
            v-model="personalLeaveReason"
            placeholder="请输入事假事由"
            autoHeight
            confirmType="done"
            :cursorSpacing="100"
          />
        </view>

        <!-- 病假类型和备注 -->
        <view v-if="activeTab === 'absence' && selectedAbsenceType === 'sick'" class="form-section">
          <view class="section-title">病假类型</view>
          <up-picker
            :show="showSickTypePicker"
            :columns="sickTypeColumns"
            keyName="dictItemName"
            @confirm="onSickTypeConfirm"
            @cancel="showSickTypePicker = false"
          />
          <view class="picker-input" @click="showSickTypePicker = true">
            <text :class="{ placeholder: !selectedSickType }">
              {{ selectedSickType ? selectedSickType.dictItemName : '请选择病假类型' }}
            </text>
            <up-icon name="arrow-down" size="24rpx" color="#999" />
          </view>
        </view>

        <view v-if="activeTab === 'absence' && selectedAbsenceType === 'sick'" class="form-section">
          <view class="section-title">病假备注</view>
          <up-textarea
            v-model="sickLeaveRemark"
            placeholder="请输入病假备注"
            autoHeight
            confirmType="done"
            :cursorSpacing="100"
          />
        </view>

        <!-- 确认按钮 -->
        <view class="confirm-btn-section">
          <button class="confirm-btn" @click="handleSubmit">提交</button>
        </view>
      </view>
    </view>
  </up-popup>
</template>

<script setup>
import { ref, watch } from 'vue'
import { getLeaveTypes } from '../api'

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  selectedChild: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['close', 'submit'])

// Tab 相关状态
const activeTab = ref('absence') // 'absence' 或 'status'

// 缺勤相关状态
const selectedAbsenceType = ref('') // 'personal' 或 'sick'
const personalLeaveReason = ref('') // 事假事由
const sickLeaveRemark = ref('') // 病假备注
const selectedSickType = ref(null) // 选中的病假类型
const showSickTypePicker = ref(false) // 病假类型选择器显示状态
const sickTypeColumns = ref([[]]) // 病假类型选择器列

// 状态修改相关状态
const selectedStatusType = ref('') // 'present' 或 'departed'

// 监听弹窗显示状态，重置数据
watch(
  () => props.show,
  (newVal) => {
    if (newVal && props.selectedChild) {
      resetForm()
      fetchSickTypes()
    }
  }
)

// 重置表单
const resetForm = () => {
  activeTab.value = 'absence'
  selectedAbsenceType.value = ''
  personalLeaveReason.value = ''
  sickLeaveRemark.value = ''
  selectedSickType.value = null
  showSickTypePicker.value = false
  selectedStatusType.value = ''
}

// 获取儿童头像
const getChildAvatar = (child) => {
  // 优先使用 headers 中的头像
  if (child?.headers?.[0]?.uri) {
    return child.headers[0].uri + '?x-oss-process=image/resize,m_fill,w_600'
  }

  // 其次使用 childAvatar
  if (child.childAvatar) {
    return child.childAvatar + '?x-oss-process=image/resize,m_fill,w_600'
  }

  // 最后使用默认头像
  return child.sex === 2
    ? 'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_girl.png'
    : 'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_boy.png'
}

// 获取病假类型列表
const fetchSickTypes = async () => {
  try {
    const res = await getLeaveTypes({ code: 'sickLeaveType' })
    if (res && res.data) {
      sickTypeColumns.value = [res.data]
    }
  } catch (error) {
    console.error('获取病假类型失败', error)
  }
}

// 切换 Tab
const switchTab = (tab) => {
  activeTab.value = tab
  // 切换 tab 时重置相关数据
  if (tab === 'absence') {
    selectedStatusType.value = ''
  } else if (tab === 'status') {
    selectedAbsenceType.value = ''
    personalLeaveReason.value = ''
    sickLeaveRemark.value = ''
    selectedSickType.value = null
  }
}

// 选择缺勤类型
const selectAbsenceType = (type) => {
  selectedAbsenceType.value = type
  // 切换类型时重置相关数据
  if (type === 'personal') {
    sickLeaveRemark.value = ''
    selectedSickType.value = null
  } else if (type === 'sick') {
    personalLeaveReason.value = ''
  }
}

// 选择状态类型
const selectStatusType = (type) => {
  selectedStatusType.value = type
}

// 病假类型选择确认
const onSickTypeConfirm = (event) => {
  const { value } = event
  selectedSickType.value = value[0]
  showSickTypePicker.value = false
}

// 处理关闭
const handleClose = () => {
  emit('close')
}

// 处理提交
const handleSubmit = () => {
  if (!props.selectedChild) return

  let state, remark, sickLeaveTypeCode, submitType

  if (activeTab.value === 'absence') {
    // 缺勤原因提交
    // 验证必填项并提示
    if (!selectedAbsenceType.value) {
      uni.$u.toast('请选择缺勤原因')
      return
    }

    if (selectedAbsenceType.value === 'sick' && !selectedSickType.value) {
      uni.$u.toast('请选择病假类型')
      return
    }

    if (selectedAbsenceType.value === 'personal') {
      state = 3 // 事假
      remark = personalLeaveReason.value
      sickLeaveTypeCode = ''
      submitType = 'absence'
    } else if (selectedAbsenceType.value === 'sick') {
      state = 4 // 病假
      remark = sickLeaveRemark.value
      sickLeaveTypeCode = selectedSickType.value?.dictItemCode || ''
      submitType = 'absence'
    }
  } else if (activeTab.value === 'status') {
    // 状态修改提交
    if (!selectedStatusType.value) {
      uni.$u.toast('请选择状态')
      return
    }

    if (selectedStatusType.value === 'present') {
      state = 1 // 出勤
    } else if (selectedStatusType.value === 'departed') {
      state = 2 // 离园
    }
    remark = ''
    sickLeaveTypeCode = ''
    submitType = 'status'
  }

  emit('submit', {
    childId: props.selectedChild.childId,
    state,
    remark,
    sickLeaveTypeCode,
    submitType,
    absenceType: selectedAbsenceType.value,
    statusType: selectedStatusType.value
  })
}
</script>

<style lang="scss" scoped>
// 请假弹窗样式
.leave-modal-content {
  background: #fff;
  max-height: 70vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .leave-modal-header {
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .leave-modal-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }

    .close-btn {
      font-size: 48rpx;
      color: #999;
      font-weight: 300;
      line-height: 1;
      padding: 10rpx;

      &:active {
        color: #666;
      }
    }
  }

  .leave-modal-body {
    flex: 1;
    padding: 30rpx;

    .selected-child {
      display: flex;
      align-items: center;
      padding: 20rpx;
      background-color: #f5f5f5;
      border-radius: 12rpx;
      margin-bottom: 30rpx;

      .child-avatar {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 20rpx;

        .avatar-img {
          width: 100%;
          height: 100%;
        }
      }

      .child-name {
        font-size: 28rpx;
        font-weight: 600;
        color: #333;
      }
    }

    .tab-section {
      margin-bottom: 30rpx;

      .tab-buttons {
        display: flex;
        background-color: #f8f9fa;
        border-radius: 12rpx;
        padding: 6rpx;

        .tab-btn {
          flex: 1;
          padding: 20rpx;
          text-align: center;
          font-size: 28rpx;
          color: #666;
          border-radius: 8rpx;
          transition: all 0.3s ease;

          &.active {
            background: linear-gradient(135deg, #52c41a, #73d13d);
            color: #fff;
            font-weight: 600;
            box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.3);
          }

          &:active {
            transform: scale(0.98);
          }
        }
      }
    }

    .form-section {
      margin-bottom: 30rpx;

      .section-title {
        font-size: 28rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 20rpx;
        text-align: left;
      }

      .absence-type-buttons,
      .status-type-buttons {
        display: flex;
        gap: 20rpx;

        .absence-type-btn,
        .status-type-btn {
          flex: 1;
          padding: 24rpx 32rpx;
          background-color: #f8f9fa;
          border: 2rpx solid #e8e8e8;
          border-radius: 12rpx;
          text-align: center;
          font-size: 28rpx;
          color: #666;
          transition: all 0.3s ease;

          &.active {
            background-color: #e6f7ff;
            border-color: #1890ff;
            color: #1890ff;
          }

          &:active {
            transform: scale(0.98);
          }
        }
      }

      .picker-input {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20rpx;
        background-color: #f8f9fa;
        border-radius: 8rpx;
        border: 2rpx solid #e8e8e8;

        .placeholder {
          color: #999;
        }
      }
    }

    .confirm-btn-section {
      margin-top: 40rpx;
      padding-top: 30rpx;
      border-top: 1rpx solid #f0f0f0;
      .confirm-btn {
        width: 100%;
        background: linear-gradient(135deg, #1890ff, #40a9ff);
        color: #fff;
        font-size: 30rpx;
        font-weight: 600;
        border-radius: 12rpx;
        border: none;
        transition: all 0.3s ease;

        &:disabled {
          background: #f5f5f5;
          color: #ccc;
        }

        &:not(:disabled):active {
          transform: scale(0.98);
        }
      }
    }
  }
}
</style>
