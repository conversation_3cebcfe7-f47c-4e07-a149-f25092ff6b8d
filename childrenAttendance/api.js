import { request } from '@/common/request.js'

// 获取班级儿童考勤列表 儿童+基本数据
export function getChildrenAttendanceList(data) {
  return request({
    url: '/jsapi/business/attendance/class',
    method: 'POST',
    data
  })
}

// 保存考勤信息
export function manualSave(data) {
  return request({
    url: '/jsapi/business/attendance/manualSave',
    method: 'POST',
    headers: {
      user_id: uni.getStorageSync('USER_INFO').userId
    },
    data
  })
}

// 获取请假类型字典 sickLeaveType
export function getLeaveTypes(data) {
  return request({
    url: '/jsapi/business/material/getDictByCode',
    method: 'GET',
    data
  })
}

// 出勤记录列表 /business/attendance/classList
export function getAttendanceRecordList(data) {
  return request({
    url: '/jsapi/business/attendance/classList',
    method: 'POST',
    data
  })
}

// 出勤记录详情 /business/attendance/detail
export function getAttendanceRecordDetail(data) {
  return request({
    url: '/jsapi/business/attendance/classDetail',
    method: 'POST',
    data
  })
}

// 获取本学期出勤汇总 /business/attendance/summary/term  
export function getAttendanceSummary(data) {
  return request({
    url: '/jsapi/business/attendance/summary/term',
    method: 'POST',
    data
  })
}

// 班级出勤汇总 /business/attendance/summary/classList 
export function getClassAttendanceSummary(data) {
  return request({
    url: '/jsapi/business/attendance/summary/classList',
    method: 'POST',
    data
  })
}

// excel导出接口：/business/attendance/summary/export   (excel导出)班级出勤汇总（学校id，出勤日期）
export function exportAttendanceSummary(data) {
  return request({
    url: '/jsapi/business/attendance/summary/export',
    method: 'POST',
    data,
    responseType: 'arraybuffer' // 设置响应类型为二进制数据
  })
}
