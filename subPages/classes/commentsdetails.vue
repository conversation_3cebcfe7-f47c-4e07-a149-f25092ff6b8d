<template>
  <BaseLayout
    nav-title="帖子详情"
    :content-style="{ padding: '0 30upx' }"
    @leftClick="goback"
    @scrolltolower="loadMore"
  >
    <view
      :class="{ 'art': comment.type == 'article', 'comment-layout': true }"
      @click.stop="()=>{}"
    >
      <CirlItem
        v-if="comment.type != 'article'"
        :comment="comment"
        :classTitle="classTitle"
        @delMoment="delMoment"
        @editMoment="editMoment"
        @pinnedAction="getCommentInfo"
      />
      <CirlAtrInfo
        v-if="comment.type == 'article'"
        :comment="comment"
        @delMoment="delMoment"
        @editMoment="editMoment"
        @pinnedAction="getCommentInfo"
      />
    </view>
  </BaseLayout>
</template>

<script setup>
  import { ref, onMounted } from "vue";
  import { onShow, onLoad } from "@dcloudio/uni-app";
  import CirlItem from "./components/CirlItem.vue";
  import CirlAtrInfo from "./components/CirlAtrInfo.vue";
  import { getInfo } from "@/api/classApi.js";
  import BaseLayout from "@/components/base-layout/base-layout.vue";

  const comment = ref({}); // 帖子详情
  const id = ref(""); // 帖子id
  const classTitle = ref(""); // 帖子id

  // 获取帖子详情
  const getCommentInfo = async () => {
    const res = await getInfo({
      id: id.value,
    });
    comment.value = res.data;
  };

  // 编辑帖子
  const editMoment = () => {
    uni.setStorageSync("_commentIdup", comment.value.id);
    const params = `?type=${comment.value.type}&classTitle=${classTitle.value}&classId=${comment.value.classId}`;
    const url = `/subPages/classes/post${params}`;
    uni.navigateTo({ url });
  };

  // 删除帖子
  const delMoment = () => {
    uni.setStorageSync("_deletId", id.value);
    uni.navigateBack();
  };

  onShow(() => {
    getCommentInfo();
    wx.hideShareMenu({
      menus: ['shareAppMessage', 'shareTimeline']
    })
  });

  onLoad((options) => {
    id.value = options.id;
    classTitle.value = options.classTitle;
  });
</script>

<style lang="scss" scoped>
  .comment-layout {
    background: transparent;

    ::v-deep .circle {
      box-shadow: unset !important;
      background: transparent;
      padding: 0;
      margin: 0;
      .cont {
        background: transparent;
      }
    }

    &.art {
      ::v-deep.circle {
        background-image: transparent;
        background: transparent;
      }
    }
  }
</style>
