<template>
  <view>
    <view v-if="backisShow">
      <page-container :show="backisShow" :overlay="false" @beforeleave="onBeforeLeave">
      </page-container>
    </view>
    <BaseLayout nav-title="发帖" navClass="layoutTit" :content-style="{ padding: '0 30upx' }" :autoBack="false"
      :footerStyle="{ paddingBottom: 'env(safe-area-inset-bottom)', background: '#fff' }" @leftClick="backModal">
      <view class="_layout">
        <view class="tit">发贴至{{ classTitle }}班级圈</view>
        <view class="sub-tit" v-if="!isPostAtr">
          可选择活动，系统自动填充文案与图片
        </view>
        <view class="sub-cont" v-if="!isPostAtr">
          <up-cell-group :border="false">
            <up-cell :isLink="true" title="选择活动" @click="goTheme" :titleStyle="{ 'font-weight': 600 }" />
          </up-cell-group>
          <PopPhoto ref="popPhotoRef" v-if="mode == 'image'" @togAiLoading="togAiLoading" />
          <PopVideo ref="popVideoRef" v-if="mode == 'video'" @togAiLoading="togAiLoading"
            @upfileStatus="upfileStatusChange" />
        </view>
        <PopAtr ref="popAtrRef" v-if="isPostAtr" @addBtn="showPop" :classId="classId" />
      </view>
      <template #footer>
        <view class="sur-btn">
          <text :class="{ 'btn-txt-disabled': !canNext, 'btn-txt': true }" @click="submitPush">
            立即发布
          </text>
        </view>
      </template>
    </BaseLayout>

    <!-- 保存草稿确认 -->
    <up-modal :show="isShowModal" showCancelButton @cancel="modalCancle" @confirm="modalSure">
      <view class="modlCont">将此次编辑保留？</view>
    </up-modal>

    <!-- 添加段落弹窗	 -->
    <PopAtrType ref="popAtrTypeRef" @surChose="postSelect"></PopAtrType>


    <!-- 发帖确认弹窗 -->
    <up-modal :show="isShowPostModal" showCancelButton @cancel="isShowPostModal = false" @confirm="surePost">
      <view class="modlCont">确认发布吗？</view>
    </up-modal>
  </view>
</template>
<script setup>
import { ref, reactive, computed, nextTick, onUnmounted } from "vue";
import { onShow, onLoad } from "@dcloudio/uni-app";
import { checks, checkMediaType } from "@/utils/index.js";
import { draft, publish, generatePostContentByAi, getInfo } from "@/api/classApi.js";
import PopPhoto from "./components/PopPhoto.vue";
import PopVideo from "./components/PopVideo.vue";
import PopAtr from "./components/PopAtr.vue";
import { usePostComment } from "./stores/postComment.js";
import BaseLayout from "@/components/base-layout/base-layout.vue";
import PopAtrType from "./components/PopAtrType.vue";
import { createProvider } from "./hooks/useProvide.js";

const postStore = usePostComment();
const mode = ref("");
const classTitle = ref(""); // 班级标题
const classId = ref("");
const popPhotoRef = ref(null);
const popVideoRef = ref(null);
const popAtrTypeRef = ref(null);
const popAtrRef = ref(null);
const isPostAtr = computed(() => "article" === mode.value); // 是否是文章帖子
const isShowModal = ref(false);
const backisShow = ref(true); //默认为true
const apiStauts = ref(false);
const upfileStatus = ref(true); // 视频上传文件成功标识(只有视频)

// 帖子详情
const comment = reactive({
  id: "",
  title: "",
  content: "",
  type: "", // 类型，image图片，video视频，article文章
  classId: "", //班级ID
  attachments: [],
  modules: [],
});

const isShowPostModal = ref(false);

const canNext = computed(() => {
  return mode.value === "image" || mode.value === "article" || upfileStatus.value;
});

// 提取重复校验逻辑
const validateAttachments = (comment) => {
  if (comment.type === "image" && comment.attachments.length > 9) {
    uni.$u.toast("图片数量超过限制（9张），请删减后重试");
    return false;
  }
  if (comment.type === "video" && comment.attachments.length > 1) {
    uni.$u.toast("视频数量超过限制（1个），请删减后重试");
    return false;
  }
  return true;
};

// 提取内容和标题校验逻辑
const validateTitleAndContent = (comment) => {
  if (comment.type !== "article") {
    if (comment.title === "" || comment.content === "") {
      uni.$u.toast("内容或标题不能为空");
      return false;
    }
  } else {
    if (comment.title === "") {
      uni.$u.toast("文章标题不能为空");
      return false;
    }
  }
  return true;
};

// 点击立即发布按钮
const submitPush = async () => {
  if (apiStauts.value || !canNext.value) return;
  const _comment = await getCommentInfo();

  if (!validateAttachments(_comment)) {
    apiStauts.value = false;
    return;
  }

  if (!validateTitleAndContent(_comment)) {
    return;
  }

  isShowPostModal.value = true;
};

const surePost = () => {
  isShowPostModal.value = false;
  submit();
};

// 展示选择添加段落弹窗
const showPop = () => {
  popAtrTypeRef.value.open();
};

// 选择添加段落类型
const postSelect = (type) => {
  postStore.setStoreComment(comment);
  popAtrRef.value.postSelect(type);
};

const onBeforeLeave = (res) => {
  backisShow.value = false;
  isShowModal.value = true;
  //间隔10ms否则太快会有问题
  setTimeout(() => (backisShow.value = false), 10);
};

// ai润色
const togAiLoading = (flag) => {
  if (flag) {
    uni.showLoading({ title: "AI生成中, 请勿操作..." });
  } else {
    uni.hideLoading();
  }
};

// 保存草稿
const saveDraft = async () => {
  const _comment = getCommentInfo();
  const res = await draft(_comment);
  const _upCommentID = res.data;
  uni.setStorageSync("_upCommentID", _upCommentID);
  return Promise.resolve(res);
};

// 获取当前编辑帖子
const getCommentInfo = () => {
  let _comment;
  switch (mode.value) {
    case "image":
      _comment = popPhotoRef.value.getComment();
      break;
    case "video":
      _comment = popVideoRef.value.getComment();
      break;
    case "article":
      _comment = popAtrRef.value.getComment();
      break;
    default:
      return {};
  }
  _comment.classId = classId.value;
  _comment.type = mode.value;
  return _comment;
};
// 发布
const submit = async () => {
  const _comment = await getCommentInfo();
  if (!validateAttachments(_comment)) return;

  try {
    apiStauts.value = true;
    const res = await publish(_comment);
    uni.$u.toast("发布成功");
    if (comment.id || res.data) uni.setStorageSync("_upCommentID", comment.id||res.data);
    backisShow.value = false;
    setTimeout(() => {
      uni.navigateBack();
    }, 100);
  } catch (e) {
    console.log(e);
  } finally {
    apiStauts.value = false;
  }
};

// 返回圈子列表
const backModal = () => {
  backisShow.value = false;
  isShowModal.value = true;
};

// 跳转选择主题页面
const goTheme = async () => {
  // 缓存中存下当前的帖子
  await getCommentInfo();
  uni.navigateTo({
    url: `/subPages/classes/choseTheme?classId=${classId.value}`,
  });
};

// 调用ai生成帖子内容
const generateMomentCont = async (actObj) => {
  uni.showLoading({ title: "AI生成中, 请勿操作..." });
  try {
    const res = await generatePostContentByAi(actObj);
    comment.content = res.data || comment.content;
  } catch (e) {
    console.log("ai生成帖子报错");
  } finally {
    uni.hideLoading();
  }
};

const modalSure = async () => {
  // 判断视频是否上传完成
  if (!canNext.value) {
    uni.$u.toast("视频上传中，请稍后再试");
    return;
  }
  const res = await saveDraft();
  uni.setStorageSync("_upCommentID", comment?.id || res?.data);
  uni.navigateBack();
};

const modalCancle = () => {
  isShowModal.value = true;
  uni.navigateBack();
};

// 从接口重新获取帖子详情
const getCommentApi = async (id) => {
  const { data } = await getInfo({ id });
  Object.assign(comment, data);
  await nextTick();
  if (comment.type === "image") {
    popPhotoRef.value?.setFileLists();
  } else if (comment.type === "video") {
    popVideoRef.value?.setFileLists();
  } else if (comment.type === "article") {
    postStore.setStoreComment(comment);
  }
};

/**
 * 处理刷新事件
 *
 * @function handleRefresh
 */
const handleRefresh = () => {
  apiStauts.value = false;
  backisShow.value = true;

  // 文章类型需判断从store中取，否则从接口获取
  if (comment.type === "article" && uni.getStorageSync("upCommStore")) {
    uni.removeStorageSync("upCommStore");
    uni.removeStorageSync("_commentIdup");
    Object.assign(comment, postStore.getComment);
  }

  // 如果从缓存中获取是否需要更新帖子且更新帖子内容
  const _commentIdup = uni.getStorageSync("_commentIdup");
  if (_commentIdup) {
    uni.removeStorageSync("_commentIdup");
    getCommentApi(_commentIdup);
    return;
  }

  // 判断是否许可ai生成帖子内容
  const actObj = uni.getStorageSync("_actObj");
  if (actObj && ["image", "video"].includes(comment.type)) {
    uni.removeStorageSync("_actObj");
    comment.title = actObj.name;
    const actattachment = actObj.resources
      .filter((item) => checkMediaType(item.uri) === mode.value)
      .map((item) => ({
        url: item.uri,
        type: mode.value,
      }));
    comment.attachments = [...actattachment];

    if (comment.type === "image") {
      popPhotoRef.value?.setFileLists();
    } else if (comment.type === "video") {
      popVideoRef.value?.setFileLists();
    }
    generateMomentCont(actObj);
  }
};

onShow(() => {
  console.log('post onShow', '<<<<<<<<<<<<')
  handleRefresh();
  checks();
  // // 添加事件监听
  // uni.$on("refreshPostPage", handleRefresh);
  // handleRefresh();
});

/**
 * 更新文件上传状态
 *
 * @param flag 状态值，通常是一个布尔值，表示上传是否成功
 */
const upfileStatusChange = (flag) => {
  upfileStatus.value = flag;
};

onLoad((options) => {
  mode.value = options.type;
  classTitle.value = options.classTitle;
  classId.value = Number(options.classId);
  Object.assign(comment, {
    id: "",
    title: "",
    content: "",
    type: mode.value, // 类型，image图片，video视频，article文章
    classId: classId.value, //班级ID
    attachments: [],
    modules: [],
  });
});

// 创建帖子发布者
createProvider(comment);

onUnmounted(() => {
  uni.$off("refreshPostPage", handleRefresh);
});

</script>
<style lang="scss">
.layoutTit {
  .u-navbar__content__title {
    font-weight: 600;
  }
}
</style>

<style lang="scss" scoped>
.modlCont {
  text-align: center;
}

._layout {
  height: 100%;
  box-sizing: border-box;

  .title {
    @include selflex(x, between, center);
    font-size: 34upx;
    color: rgba(51, 51, 51, 1);
    padding: 0;
    margin-top: 30upx;
  }

  .tit {
    font-size: 28upx;
    font-weight: 600;
    letter-spacing: 0px;
    color: rgba(51, 51, 51, 1);
    padding-bottom: 20upx;
    padding-top: 45upx;
  }

  .sub-tit {
    font-size: 24upx;
    font-weight: 400;
    letter-spacing: 0px;
    color: rgba(128, 128, 128, 1);
    padding-bottom: 30upx;
  }

  .sub-cont {
    border-radius: 28upx;
    padding: 0 28upx 28upx 28upx;
    min-height: calc(100% - 200upx);
    @include selfshaow;

    ::v-deep .u-input {
      padding-left: 0 !important;
      padding-right: 0 !important;
      font-weight: 600;
    }

    ::v-deep .u-textarea {
      padding: 0 !important;
      // background-color: red;
      margin-left: -12upx;
    }

    ::v-deep .u-textarea__count {
      padding: 0 !important;
    }

    ::v-deep .u-cell__body {
      padding-left: 0 !important;
      padding-right: 0 !important;
    }
  }
}

.sur-btn {
  box-sizing: border-box;
  width: 100vw;
  background-color: #ffffff;
  margin: 0 -30upx;
  padding: 15upx 30upx;
  @include selflex(x, between, center);
  z-index: 2;

  .btn-txt {
    width: 100%;
    height: 80upx;
    line-height: 80upx;
    border-radius: 44px;
    background: rgba(54, 124, 255, 1);
    font-size: 30upx;
    font-weight: 600;
    letter-spacing: 0upx;
    color: rgba(255, 255, 255, 1);
    text-align: center;

    &.btn-txt-disabled {
      filter: grayscale(100%);
    }
  }
}
</style>