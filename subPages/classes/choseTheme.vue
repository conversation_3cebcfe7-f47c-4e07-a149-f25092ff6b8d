<template>
  <base-layout navTitle="选择主题" containerClass="actThem" :footerStyle="{ padding: 0 }" :contentStyle="{ padding: 0 }"
    @scrolltolower="reachBottm">
    <view class="theme-wrap">
      <view class="theme-item" v-for="(item, idx) in subJectArr" :key="idx" @click="goPageAct(item)">
        <!-- <up-image class="imglab" :width="65" :height="65" mode="aspectFit" radius="14"
          src="https://s.mypacelab.com/202503/HtSjhmnWb9zv6988a04f615ad8977c0c79dd6fdd0aae.png" :lazy-load="true" /> -->
        <view class="theme-item-info">
          <view class="theme-item-info-tit">
            <text class="theme-item-info-tit-txt"> {{item.title}} </text>
            <!-- <text class="theme-item-info-tit-lab">学习活动</text> -->
          </view>
          <template v-if="item.schoolClass">
            <view class="theme-item-info-sub-tit">
              <text class="text-with-line">{{item.schoolClass.nickname}}</text>
              <text class="text-with-line">{{item.schoolClass.title}}</text>
              <text v-if="item.schoolClass.extra">{{item.schoolClass.extra.masterTitle}}</text>
            </view>
            <view class="theme-item-info-name"> {{item.schoolClass.schoolTitle}} </view>
          </template>
          <!-- <view class="theme-item-info-pri">
            <text class="text-with-line">活动：2/10</text>
            <text>评价：2/10</text>
          </view>
          <view class="theme-item-info-date"> 最新实施日期：{{item.updatedAt}} </view> -->
        </view>
      </view>
    </view>
    <up-loadmore :status="loading" loadmore-text="上拉或点击加载更多" @loadmore="loadMore" />
    <u-gap :height="8"></u-gap>
  </base-layout>
</template>
<script setup>
  import { reactive, ref, computed, toRaw } from "vue";
  import { onShow, onShareAppMessage, onLoad } from "@dcloudio/uni-app";
  import { checks } from "@/utils/index.js";
  import baseLayout from "@/components/base-layout/base-layout.vue";
  import { getsubJectList } from "@/api/classApi.js";

  const classId = ref("");
  const fromPage = ref(""); // 来源页面
  const subJectArr = ref([]); // 主题列表
  const loading = ref("loadmore"); // loading状态 loadmore: 加载更多 nomore: 没有更多 loading: 加载中
  const paramsPage = reactive({
    pageSize: 10,
    currentPage: 1,
  });

  //  获取主题列表
  const getSubArr = async () => {
    if (loading.value == "loading" || loading.value == "nomore") {
      return;
    }
    loading.value = "loading";
    try {
      const res = await getsubJectList({
        classId: classId.value,
        pageSize: paramsPage.pageSize,
        currentPage: paramsPage.currentPage,
      });
      if (paramsPage.currentPage == 1) {
        subJectArr.value = [...res.data];
      } else {
        subJectArr.value = [...toRaw(subJectArr.value), ...res.data, ...res.data, ...res.data, ...res.data];
      }
      if (res.total > subJectArr.value.length) {
        loading.value = "loadmore";
      } else {
        loading.value = "nomore";
      }
    } catch (e) {
      const tempPage = paramsPage.currentPage - 1;
      paramsPage.currentPage = tempPage < 1 ? 1 : tempPage;
      loading.value = "loadmore";
    }
  };
  // 点击加载
  const loadMore = () => {
    paramsPage.currentPage = paramsPage.currentPage + 1
    getSubArr();
  };

  // 滚动到底部
  const reachBottm = () => {
    loadMore();
  };

  // 跳转选择活动页面
  const goPageAct = (subObj) => {
    uni.setStorageSync('_theam', subObj)
    const fromParam = fromPage.value ? `&from=${fromPage.value}` : ''
    uni.navigateTo({
      url: `/subPages/classes/choseAct?subjectId=${subObj.id}${fromParam}`,
    })
  }

  onShow(() => {
    paramsPage.currentPage = 1;
    loading.value = "loadmore";
    getSubArr();
  });

  onLoad((options) => {
    classId.value = options.classId;
    fromPage.value = options.from || '';
  });
</script>

<style lang="scss">
  .actThem {
    background: #f5f5f5 url("https://c.mypacelab.com/vxmp/img/classDeatils_bg_3x.png") no-repeat center top !important;
    background-size: contain !important;
  }
</style>

<style lang="scss" scoped>
  .theme-wrap {
    padding: 4upx 30upx;

    .theme-item {
      @include selflex(x, start, start);
      @include selfshaow;
      padding: 28upx;
      margin-top: 24upx;
      background-color: #fff;
      border-radius: 28upx;

      .imglab {
        border-radius: 24upx;
        margin-right: 28upx;
      }

      &-info {
        flex-grow: 1;
        font-size: 24upx;
        line-height: 34upx;
        color: rgba(128, 128, 128, 1);

        @each $var in tit,
        sub-tit,
        name,
        pri {
          &-#{$var} {
            margin-bottom: 10upx;
          }
        }

        &-tit {
          font-size: 30upx;
          font-weight: 600;
          letter-spacing: 0;
          line-height: 36upx;
          color: rgba(51, 51, 51, 1);
          @include selflex(x, start, center);

          &-txt {
            width: 370upx;
            flex-grow: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          &-lab {
            border-radius: 5upx;
            padding: 2upx 6upx;
            background: rgba(240, 161, 77, 0.12);
            font-size: 20upx;
            font-weight: 500;
            color: rgba(240, 161, 77, 1);
            text-align: center;
          }
        }
      }
    }
  }
</style>
