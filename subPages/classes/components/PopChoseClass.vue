<template>
	<view class="slef-pop">
		<Popup :show="show" @close="show = false">
			<view class="wrap">
				<view class="tit">
					<text>请选班级 </text>
					<text class="sure" @click="sure">确定</text>
				</view>
				<view class="cell">
					<up-radio-group iconPlacement="right" v-model="classId">
						<view class="class-item" v-for="(item, idx) in classArr" :key="idx">
							<!-- <up-image
								width="88upx"
								height="88upx"
								class="img"
								mode="aspectFit"
								radius="8"
								src="https://s.mypacelab.com/202503/HtSjhmnWb9zv6988a04f615ad8977c0c79dd6fdd0aae.png"
								:lazy-load="true"
							></up-image> -->
							<view class="info">
								<text class="tit">{{ item.title }}</text>
								<text class="up-line-2 cont"> {{ item.girl + item.boy }}位 学生 </text>
							</view>
							<view class="radio">
								<up-radio shape="circle" :name="item.id"></up-radio>
							</view>
						</view>
					</up-radio-group>
				</view>
			</view>
		</Popup>
	</view>
</template>

<script setup>
import { reactive, ref, defineExpose } from 'vue'
import { getclassList } from "@/api/classApi.js";

const show = ref(false)
const classArr = ref([])
const classId = ref(null)
const emit = defineEmits('chose')
let initClassId = ''

// 弹出打开
const open = (schoolId, _classId) => {
	show.value = true
	classId.value = Number(_classId)
  initClassId = _classId
	getClassArr(schoolId)
}
// 确定选择
const sure = () => {
	show.value = false
  if(initClassId!=classId.value){
    emit('chose', { id: classId.value })
  }
}

// 获取班级列表
const getClassArr = async schoolId => {
	const params = { schoolId }
	const res = await getclassList(params)
	classArr.value = res.data
}
defineExpose({ open })
</script>

<style lang="scss" scoped>
.slef-pop ::v-deep .u-popup__content {
	background-color: transparent;
}
.wrap {
	& > .tit {
		@include selflex(x, between, center);
		font-weight: 600;
		letter-spacing: 0px;
		line-height: 47upxpx;
		color: rgba(51, 51, 51, 1);
		font-size: 32upx;
		text-align: center;
		font-family: PingFangSC-regular;
		padding-bottom: 37upx;
		.sure {
			font-size: 30upx;
			color: rgba(63, 121, 255, 1);
		}
	}
	.cell {
		min-height: 550upx;
		overflow: auto;
	}
	.class-item {
		@include selfshaow;
		@include selflex(x, start, center);
		font-family: PingFangSC-regular;
		box-sizing: border-box;
		margin-top: 20upx;
		width: 100%;
		height: 136upx;
		background-color: #fff;
		border-radius: 28upx;
		padding: 28upx;
		.img {
			width: 44upx;
			height: 44upx;
			flex-shrink: 0;
		}
		.info {
			margin-left: 20upx;
			flex-grow: 1;
			.tit {
				line-height: 46upx;
				color: rgba(0, 0, 0, 1);
				font-size: 30upx;
				text-align: left;
			}
			.cont {
				margin-top: 14upx;
				color: rgba(128, 128, 128, 1);
				font-size: 24upx;
				text-align: left;
			}
		}
		.radio {
			width: 50upx;
			::v-deep .u-radio-group--row {
				width: 50upx;
			}
		}
	}
}
</style>
