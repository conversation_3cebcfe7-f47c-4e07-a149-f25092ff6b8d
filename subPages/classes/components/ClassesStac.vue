<template>
	<up-gap height="15"></up-gap>
	<uni-table border stripe emptyText="暂无更多数据">
		<!-- 表头行 -->
		<uni-tr>
			<uni-th width="80" align="center">日期</uni-th>
			<uni-th width="80" align="center">姓名</uni-th>
			<uni-th width="80" align="left">地址</uni-th>
			<uni-th width="80" align="left">地址</uni-th>
			<uni-th width="80" align="left">地址</uni-th>
		</uni-tr>
		<!-- 表格数据行 -->
		<uni-tr>
			<uni-td>2020-10-20</uni-td>
			<uni-td>Jeson</uni-td>
			<uni-td>北京市海淀区</uni-td>
			<uni-td>北京市海淀区</uni-td>
			<uni-td>北京市海淀区</uni-td>
		</uni-tr>
	</uni-table>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { onShow, onShareAppMessage } from '@dcloudio/uni-app'
import { checks } from '@/utils/index.js'
import { getAiTemplateList } from '@/api/aiTemplate.js'

const tabArr = ref([{ name: '班级列表' }, { name: '班级统计' }])

onShow(() => {
	checks()
})
</script>

<style lang="scss" scoped>
.class-item {
	font-family: PingFangSC-regular;
	box-sizing: border-box;
	margin-top: 20upx;
	width: 100%;
	height: 170upx;
	background-color: #fff;
	border-radius: 4upx;
	padding: 20upx;
	display: flex;
	.img {
		flex: 1;
		flex-shrink: 0;
		flex-grow: 0;
	}
	.info {
		margin-left: 20upx;
		.tit {
			line-height: 46upx;
			color: rgba(0, 0, 0, 1);
			font-size: 32upx;
			text-align: left;
		}
		.cont {
			color: rgba(102, 102, 102, 1);
			font-size: 28upx;
			text-align: left;
		}
	}
}
</style>
