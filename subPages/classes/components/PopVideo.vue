<template>
  <up-input :placeholder="mode == 'page' ? '填写标题' : '请输入段落标题，不写则不显示'"
    :placeholderStyle="{ color: 'rgba(177, 179, 181, 1)' }" border="bottom" clearable class="ipt"
    v-model="comment.title" />
  <up-gap height="10" />
  <view>
    <up-textarea 
      class="input-post" 
      height="30vh" 
      border="none"
      :placeholder="mode == 'page' ? '添加正文' : '请输入段落描述的文字，不写则不显示'" 
      :placeholderStyle="{
        'font-size': '28upx',
        color: 'rgba(177, 179, 181, 1)',
      }" 
      :maxlength="-1" 
      v-model="comment.content" 
    />
  </view>
  <up-gap height="20" />
  <view class="tag" @click="aiPolishi">
    <view class="tag-btn">
      <image class="ai-img" src="https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/class_post_photo.png" mode="scaleToFill" />
      <text>AI文字润色</text>
    </view>
  </view>
  <up-gap height="10" />

  <view @click="uploadTap">
    <up-upload 
      v-if="!fileList[0]" 
      :fileList="fileList" 
      @afterRead="selfAfter" 
      @delete="deletePic" 
      :maxCount="1"
      :compressed="false"
      accept="video" 
      :previewFullImage="true"
    >
      <view class="upimg">
        <image class="upimg-con" src="/static/icon/u-icon.png" mode="scaleToFill" />
        <view class="txt">
          <text>上传视频</text>
          <text class="desc">(仅限5分钟内)</text>
        </view>
      </view>
    </up-upload>
  </view>

  <view class="file-item-content" :style="{ width: isFullWidth ? '100%' : '70%' }">
    <video v-if="fileList[0]" class="file-item-video" :src="fileList[0].url" style="width: 100%" id="myVideo1" loop
      controls show-mute-btn auto-pause-if-open-native auto-pause-if-navigate @loadedmetadata="onVideoLoaded"></video>
    <view class="u-upload__deletable" @click="deletePic({ index: 0 })">
      <u-icon class="icn" color="#fff" size="10" name="close"></u-icon>
    </view>
    <view class="u-upload_sucess" v-if="fileList[0]?.status == 'success'">
      <text class="icn sucs">✓</text>
    </view>
    <view class="upload_loading" v-if="fileList[0]?.status == 'uploading'">
      <u-loading-icon text="上传中" textSize="12"></u-loading-icon>
    </view>
  </view>
</template>
<script setup>
import { reactive, ref, watch, toRaw } from "vue";
import useUploadClasses from "../hooks/useUploadClasses.js";
import useComment from "../hooks/useComment.js";
import { polishiPostByAi } from "@/api/classApi.js";

const props = defineProps({
  // 类型 art发文章  page单独发照片帖子
  mode: {
    type: String,
    default: "page",
  },
});

const emit = defineEmits(["togAiLoading", "stroragePost", 'upfileStatus']);
const { fileList, afterRead, deletePic, setFileList } = useUploadClasses();
const { comment, getComment, getCommentFileList } = useComment();
const isFullWidth = ref(true); //是否宽屏

const uploadTap = () => {
  emit("upfileStatus", false);
}

const selfAfter = async (file) => {
  const fileSize = file?.file?.size;
  const maxSize = 50 * 1024 * 1024; // 最大50m
  if (fileSize > maxSize) {
    uni.showToast({
      title: "视频大小不能超过50M",
      icon: "none",
    });
    emit("upfileStatus", true);
    return;
  }
  if (file.file.duration > 300) {
    uni.showToast({
      title: "视频长度不能超过5分钟",  
      icon: "none",
    });
    emit("upfileStatus", true);
    return;
  }

  console.log('压缩前=====>：', file);
  // const filepath = file?.file?.url;
  // try {
  //   const compressedResult = await new Promise((resolve, reject) => {
  //     uni.compressVideo({
  //       src: filepath,
  //       quality: 'highest', // 最高质量压缩
  //       fps: 30, // 帧率30fps
  //       resolution: 1, // 保持原始分辨率
  //       success: resolve,
  //       fail: reject
  //     });
  //   });
  //   const _file = {
  //     ...file.file,
  //     tempFilePath: compressedResult.tempFilePath,
  //     path: compressedResult.tempFilePath,
  //     url: compressedResult.tempFilePath,
  //     size: compressedResult.size,
  //   }
  //   file.file = _file; //更新文件信息

  //   console.log('压缩后:', compressedResult);
  // } catch (e) {
  //   console.error('视频压缩失败:', e);
  // } finally {
  //   // uni.hideLoading();
  // }
  afterRead(file);
};

// 视频加载设置图片的宽高比例
const onVideoLoaded = (event) => {
  const { width, height } = event.detail;
  if (width / height > 1) {
    //宽屏
    isFullWidth.value = true;
  } else {
    // 窄屏
    isFullWidth.value = false;
  }
};

// ai润色
const aiPolishi = async () => {
  emit("togAiLoading", true);
  try {
    const content = comment.content;
    const res = await polishiPostByAi({ content });
    comment.content = res.data;
  } catch (e) {
    console.log(e);
  } finally {
    emit("togAiLoading", false);
  }
};

// 判断所有视频是否上传成功
const getuploadSucessFlag = () => {
  if (fileList.value.length==0) {
    return true;
  } 
  return fileList.value.every((item) => item.status == "success");
};

// 设置帖子附件列表
const setFileLists = () => {
  setFileList(getCommentFileList());
};

// 监听上传的文件设置帖子附件
watch(fileList, (newFileList) => {
  comment.attachments = newFileList.map((item) => ({
    type: "video", //类型,image图片，video视频
    url: item.url, //附件地址
    description: item.description || "", //附件描述
  }));
  // 判断所有视频是否上传成功
  const uploadSucessFlag = newFileList.every((item) => item.status == "success");
  // 判断文件列表情况
  if (newFileList.length == 0 || uploadSucessFlag) {
    emit("upfileStatus", true);
  }
});

defineExpose({ getComment, getuploadSucessFlag, setFileLists});
</script>

<style lang="scss" scoped>
.file-item-content {
  width: 100%;
  position: relative;
  border-radius: 5upx;
  overflow: hidden;

  .file-item-video {
    display: block;
    border-radius: 5upx;
  }

  .u-upload__deletable {
    position: absolute;
    top: 0;
    right: 0;
    background-color: #373737;
    height: 30upx;
    width: 30upx;
    border-bottom-left-radius: 200upx;
    z-index: 4;
    display: flex;
    justify-content: center;
    align-items: center;
    align-items: center;
    justify-content: center;
  }

  .upload_loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 3;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .u-upload_sucess {
    z-index: 2;
    position: absolute;
    bottom: 0;
    right: 0;
    display: flex;
    flex-direction: row;
    border-style: solid;
    border-top-color: transparent;
    border-left-color: transparent;
    border-bottom-color: #5ac725;
    border-right-color: #5ac725;
    border-width: 18upx;
    align-items: center;
    justify-content: center;

    .sucs {
      transform: scale(0.7) translate(6px, 5px);
      position: absolute;
    }
  }
}

.ipt {
  padding-left: 0 !important;
  padding-right: 0 !important;

  ::v-deep .uni-input-input {
    font-weight: 900;
    letter-spacing: 0px;
    color: rgba(51, 51, 51, 1);
  }
}

.input-post {
  min-height: 20vh;
  font-size: 28upx;
  padding: 0 !important;

  ::v-deep .u-input {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
}

.tag {
  @include selflex(x, end, center);

  &-btn {
    @include selflex(x, center, center);
    padding: 16upx 20upx;
    border-radius: 34upx;
    background: rgba(54, 124, 255, 0.06);
    font-size: 24upx;
    font-weight: 500;
    letter-spacing: 0upx;
    color: rgba(63, 121, 255, 1);

    .ai-img {
      width: 28upx;
      height: 23upx;
      margin-right: 8upx;
    }
  }
}

.upimg {
  width: 148upx;
  height: 148upx;
  border-radius: 8.46upx;
  background: #f5f5f5;
  border: 1upx dashed rgba(217, 217, 217, 1);
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  align-content: center;

  .upimg-con {
    width: 37upx;
    height: 31upx;
  }

  .txt {
    @include selflex(y, center, center);
    width: 100%;
    padding-top: 10upx;
    text-align: center;
    font-size: 22upx;
    line-height: 28upx;
    font-weight: 400;
    letter-spacing: 0upx;
    color: rgba(128, 128, 128, 1);

    .desc {
      font-size: 18upx;
      font-weight: 400;
      color: rgba(128, 128, 128, 1);
    }
  }
}
</style>