<template>
	<Popup :show="show" @close="show = false">
		<view class="wrap">
			<view class="tit">
				<text>请选择发帖类型 </text>
			</view>
			<view class="type-wrap">
				<view class="type-wrap-item" @click="choseType('image')">
					<view class="type-wrap-item-img">
						<image class="type-wrap-item-img-slef" src="https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/class_post_photo.png" mode="aspectFit"></image>
					</view>
					<view class="type-wrap-item-txt">发图片</view>
				</view>
				<view class="type-wrap-item" @click="choseType('video')">
					<view class="type-wrap-item-img">
						<image class="type-wrap-item-img-slef" src="https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/class_post_video.png" mode="aspectFit"></image>
					</view>
					<view class="type-wrap-item-txt">发视频</view>
				</view>
				<view class="type-wrap-item" @click="choseType('article')">
					<view class="type-wrap-item-img">
						<image class="type-wrap-item-img-slef" src="https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/class_post_art.png" mode="aspectFit"></image>
					</view>
					<view class="type-wrap-item-txt">发文章</view>
				</view>
			</view>
		</view>

		<u-gap height="20"></u-gap>
	</Popup>
</template>

<script setup>
import { reactive, ref, defineExpose } from 'vue'
import { onShow, onShareAppMessage, onLoad } from '@dcloudio/uni-app'
import { checks } from '@/utils/index.js'
import { getAiTemplateList } from '@/api/aiTemplate.js'
import { usePostComment } from '../stores/postComment.js'

const psotStore = usePostComment();
const emit = defineEmits(['choseType'])
const show = ref(false)

const open = () => {
	show.value = true
}
// 选择发帖类型
const choseType = type => {
  // 清空发帖类型
  psotStore.initData()
	show.value = false
	emit('surChose', type)
}
defineExpose({ open })

</script>

<style lang="scss" scoped>
.wrap {
	& > .tit {
		@include selflex(x, between, center);
		font-weight: 600;
		letter-spacing: 0px;
		line-height: 47upxpx;
		color: rgba(51, 51, 51, 1);
		font-size: 32upx;
		text-align: center;
		font-family: PingFangSC-regular;
		padding-bottom: 37upx;
	}
	.type-wrap {
		@include selflex(x, around, center);
		&-item {
			padding-top: 100upx;
			&-img {
				width: 150upx;
				height: 150upx;
				background: rgba(255, 255, 255, 1);
				box-shadow: 0upx 2upx 55upx rgba(0, 0, 0, 0.04);
				border-radius: 50%;
				@include selflex(x, center, center);
				.type-wrap-item-img-slef {
					width: 78upx;
					height: 78upx;
				}
			}
			&-txt {
				font-size: 30upx;
				font-weight: 400;
				letter-spacing: 0upx;
				line-height: 50upx;
				color: rgba(128, 128, 128, 1);
				text-align: center;
				padding-top: 20upx;
				padding-bottom: 100upx;
			}
		}
	}
}
</style>
