<!-- 新增学生 -->
<template>
  <view style="padding-bottom: env(safe-area-inset-bottom)">
    <view class="add-form-title">
      <view>{{ textTitle }}</view>
      <view v-if="isEdit" @click="sendForm">完成</view>
      <view v-else @click="putItem">完成</view>
    </view>
    <view class="tip">如需批量上传，请至网页版操作。</view>
    <view>
      <up-form
        ref="formRef"
        labelPosition="left"
        labelWidth="120"
        :model="formData"
        :rules="rules"
      >
        <up-form-item
          :style="addFormItemStyle"
          label="儿童姓名"
          prop="title"
          required
        >
          <up-input
            v-model="formData.title"
            style="height: 100%"
            placeholder="请输入内容"
            border="none"
          />
        </up-form-item>
        <up-form-item
          :style="addFormItemStyle"
          label="性别"
          prop="sex"
          required
        >
          <up-radio-group
            v-model="formData.sex"
            placement="row"
            @change="groupChange"
          >
            <up-radio
              shape="circle"
              label="男"
              :name="1"
              style="width: 150rpx"
            ></up-radio>
            <up-radio
              shape="circle"
              label="女"
              :name="2"
              style="width: 190rpx"
            ></up-radio>
          </up-radio-group>
        </up-form-item>
        <up-form-item
          :style="addFormItemStyle"
          label="出生年月"
          prop="birthday"
          required
        >
          <uni-datetime-picker
            type="date"
            :border="false"
            :clear-icon="true"
            v-model="formData.birthday"
          />
          <!-- 用于触发验证 -->
          <up-input style="display: none" v-model="formData.birthday" />
        </up-form-item>
        <up-form-item
          :style="addFormItemStyle"
          label="入园时间"
          prop="entryTime"
          required
        >
          <uni-datetime-picker
            type="date"
            :border="false"
            :clear-icon="true"
            v-model="formData.entryTime"
          />
          <!-- 用于触发验证 -->
          <up-input style="display: none" v-model="formData.entryTime" />
        </up-form-item>
        <up-form-item
          :style="addFormItemStyle"
          label="是否独生子女"
          prop="isSingle"
          required
        >
          <up-radio-group
            v-model="formData.isSingle"
            placement="row"
            @change="groupChange"
            required
          >
            <up-radio
              shape="circle"
              label="是"
              :name="1"
              style="width: 150rpx"
            ></up-radio>
            <up-radio
              shape="circle"
              label="否"
              :name="0"
              style="width: 190rpx"
            ></up-radio>
          </up-radio-group>
        </up-form-item>
        <up-form-item
          :style="addFormItemStyle"
          label="是否有发展症状"
          prop="symptoms"
          required
        >
          <up-radio-group
            v-model="formData.symptoms"
            placement="column"
            @change="groupChange"
          >
            <up-radio shape="circle" label="正常儿童" :name="1"></up-radio>
            <up-radio shape="circle" label="疑似特殊" :name="2"></up-radio>
            <up-radio shape="circle" label="诊断特殊" :name="3"></up-radio>
          </up-radio-group>
        </up-form-item>
        <!-- <up-form-item :style="addFormItemStyle" label="父亲手机号" prop="fatherMobile">
                    <up-input v-model="formData.fatherMobile" style="height: 100%;" placeholder="请输入内容" border="none" />
                </up-form-item>
                <up-form-item :style="addFormItemStyle" label="母亲手机号" prop="motherMobile">
                    <up-input v-model="formData.motherMobile" style="height: 100%;" placeholder="请输入内容" border="none" />
                </up-form-item> -->
      </up-form>
    </view>
  </view>
</template>

<script setup>
import { reactive, ref, watch, onUnmounted } from "vue";
import { addChildrenItem, putChildrenItem } from "@/api/children.js";
import Rules from "./rules.js";
const { rules } = Rules();

let props = defineProps({
  editData: {
    type: Object,
    default: () => ({}),
  },
});

let formData = reactive({
  sex: 1,
  birthday: "",
  entryTime: "",
  isSingle: "",
  symptoms: 0,
  // fatherMobile: '',
  // motherMobile: '',
});
let formRef = ref(null);
let isEdit = ref(true);
let addFormItemStyle = {
  minHeight: "110rpx",
  justifyContent: "center",
  borderBottom: "1rpx solid #eee",
};
let textTitle = ref("新增儿童");

watch(
  () => props.editData,
  (newVal, oldVal) => {
    if (JSON.stringify(newVal) == "{}") {
      textTitle.value = "新增儿童";
      isEdit.value = true;
      formData.title = "";
      formData.sex = "";
      formData.birthday = "";
      formData.entryTime = "";
      formData.isSingle = "";
      formData.symptoms = "";
      // formData.fatherMobile = ''
      // formData.motherMobile = ''
    }
    if (newVal && JSON.stringify(newVal) != "{}") {
      textTitle.value = "编辑儿童";
      isEdit.value = false;
      console.log("%c  watch::::", "color:red", newVal);
      const { title, sex, birthday, entryTime, isSingle, symptoms } = newVal;
      formData.title = title;
      formData.sex = sex;
      formData.birthday = birthday;
      formData.entryTime = entryTime;
      formData.isSingle = isSingle;
      formData.symptoms = symptoms;
      // formData.fatherMobile = fatherMobile
      // formData.motherMobile = motherMobile
      formData.id = newVal.id;
    }
  },
  { deep: true, immediate: true }
);

const groupChange = (e) => {
  console.log(e);
};

const sendForm = () => {
  formRef.value
    .validate()
    .then(async (valid) => {
      if (valid) {
        uni.$u.toast("基本信息成功");
        const res = await addChildrenItem(formData);
        if (res.status == 0) {
          uni.$u.toast("添加成功");
          emit("successSend");
        }
      } else {
        uni.$u.toast("请检查必填项");
      }
    })
    .catch((e) => {
      uni.$u.toast("活动基本信息校验失败");
    });
};

const putItem = async () => {
  const res = await putChildrenItem(formData);
  if (res.status == 0) {
    uni.$u.toast("编辑成功");
    emit("successSend");
  }
};
const emit = defineEmits(["successSend"]);
</script>
<script>
export default {
  options: { styleIsolation: "shared" }, //解除样式隔离
};
</script>

<style lang="scss" scoped>
.tip {
  margin-top: 10rpx;
  color: #999;
  font-size: 26rpx;
}
:deep(.u-form-item__body__left__content) {
  flex-direction: row-reverse;
  flex: initial;

  .u-form-item__body__left__content__required {
    right: -16rpx;
    left: initial;
  }
}

.add-form-title {
  display: flex;
  align-items: center;
  justify-content: space-between;

  view:first-child {
    font-size: 34rpx;
    font-weight: 600;
  }

  view:last-child {
    font-size: 30rpx;
    font-weight: 500;
    color: rgba(63, 121, 255, 1);
  }
}
</style>
