<!-- 圈子 -->
<template>
  <view class="circle">
    <view class="circle-item">

      <view class="circle-info" @click="goToCommentsDetailPage">
        <view class="tit top-tip">
          <view>
            <text class="tag" v-if="comment.status == 'draft'">未发布</text>
            <text class="circle-info-cont moment-tit">{{comment.title}}</text>
          </view>
          <uni-icons
            type="more-filled"
            @click="showEdit"
            size="18"
            color="rgba(128, 128, 128, 1)"
          ></uni-icons>
        </view>

        <template v-for="(item,idx) in comment.modules" :key="idx">
          <view class="tit" v-if="item.title">
            <text class="circle-info-cont moment-sub-tit">{{item.title}}</text>
          </view>
          <view class="tit" v-if="item.content">
            <text class="circle-info-cont">{{item.content}}</text>
          </view>
          <MomentImage @playVideo="videoPlay" :comment="item" :commentType="'article'" :artComment="comment" :artIdx="idx" :canPlay="true" />
        </template>

        <view class="priz">
          <view class="time"> {{formatDate(comment.publishTime || comment.updatedAt, 'MM-DD HH:mm')}} </view>
          <view>
            <uni-icons type="hand-up" size="18" color="rgba(153, 153, 153, 1)"></uni-icons>
            <text class="priz-num">{{comment.likeCount || 0}}</text>
          </view>
        </view>

        <view class="cont prz" v-if="comment.likeCount>0">
          <uni-icons type="heart" size="18" color="rgba(153, 153, 153, 1)"></uni-icons>
          <template v-for="(item,idx) in comment.likes" :key="idx">
            <text class="priz-num">{{item.allRelationshipText}} {{idx==comment.likes.length?'':','}}</text>
          </template>
        </view>
      </view>
    </view>
  </view>
  <!-- 帖子操作弹窗 -->
  <PopTactons ref="popTactonsRef" @delMoment="delMoment" @editMoment="editMoment" @pinnedAction="pinnedAction" />
</template>
<script setup>
  import { reactive, ref, toRaw, getCurrentInstance } from 'vue'
  import { onShow, onShareAppMessage, onLoad } from '@dcloudio/uni-app'
  import PopTactons from './PopTactons.vue'
  import MomentImage from "./MomentImage.vue"
  import { formatDate } from "@/utils/index.js";

  let props = defineProps({
    comment: {
      type: Object,
      default: () => ({})
    },
    classTitle: {
      type: String,
      default: ''
    }
  })
  const emit = defineEmits(['editMoment', 'delMoment', 'pinnedAction'])
  const popTactonsRef = ref(null)
  const { proxy: _this } = getCurrentInstance()
  const curvideo = ref(null);
  // 展示帖子操作弹窗
  const showEdit = () => {
    const commentData = toRaw(props.comment)
    popTactonsRef.value.open(commentData)
  }
  // 删除帖子回调
  const delMoment = (id) => {
    emit('delMoment', id)
  }
  // 编辑帖子回调
  const editMoment = () => {
    emit('editMoment')
  }
  // 帖子置顶回调
  const pinnedAction = () => {
    emit('pinnedAction')
  }
  // 跳转帖子详情
  const goToCommentsDetailPage = () => {
    const pages = getCurrentPages();
    const currentPagePath = pages[pages.length - 1].route;
    if (currentPagePath.indexOf('subPages/classes/commentsdetails') > -1) {
      return
    } else {
      uni.navigateTo({
        url: `/subPages/classes/commentsdetails?id=${props.comment.id}&classTitle=${props.classTitle}`,
      })
    }
  }

  const videoPlay = (_curvideo) => {
    // 播放视频
    if (curvideo.value) {
      curvideo.value.pause();
    }
    curvideo.value = _curvideo
  }
</script>

<style lang="scss" scoped>
  .circle {
    @include selfshaow;
    padding: 28upx;
    margin-top: 20upx;
    background-color: #fff;
    border-radius: 28upx;

    .circle-item {
      .circle-item-top {
        @include selflex(x, start, center);

        .name {
          @include selflex(x, between, center);
          margin-left: 20upx;
          flex-grow: 1;

          .nic {
            font-size: 30upx;
            font-weight: 600;
            letter-spacing: 0upx;
            line-height: 36upx;
            color: rgba(51, 51, 51, 1);
          }
        }
      }

      .circle-info {
        .time {
          line-height: 34rpx;
          color: rgba(153, 153, 153, 1);
          font-size: 24rpx;
        }

        .tit {
          @include selflex(x, start, start);
          // align-items: baseline;
          font-size: 28upx;
          font-weight: 400;
          letter-spacing: 0upx;
          line-height: 48upx;
          color: rgba(51, 51, 51, 1);
          margin: 12upx 0;
          &.top-tip{
            justify-content: space-between;
          }

          .tag {
            display: inline-flex;
            flex-shrink: 0;
            font-size: 20upx;
            font-weight: 500;
            line-height: 1;
            color: rgba(255, 255, 255, 1);
            border-radius: 6upx;
            background: rgba(255, 154, 59, 1);
            padding: 6upx;
            margin-right: 10upx;
            margin-top: 12upx;
          }

          .circle-info-cont {
            word-break: break-all;

            &.moment-tit {
              font-weight: 600;
              font-size: 44upx;
              line-height: 64upx;
              padding-bottom: 10upx;
            }

            &.moment-sub-tit {
              font-size: 32upx;
              font-weight: 600;
              padding-bottom: 5upx;
              padding-top: 28upx;
              white-space: pre-line;
            }
          }
        }

        .priz {
          @include selflex(x, between, center);
          font-size: 24upx;
          font-weight: 400;
          letter-spacing: 0upx;
          line-height: 30upx;
          color: rgba(153, 153, 153, 1);
          padding-top: 26upx;
          padding-bottom: 26upx;
        }

        .priz-num {
          margin-left: 4upx;
          font-size: 24upx;
          font-weight: 400;
          letter-spacing: 0px;
          line-height: 24upx;
          color: rgba(128, 128, 128, 1);
        }
      }
    }
  }
</style>