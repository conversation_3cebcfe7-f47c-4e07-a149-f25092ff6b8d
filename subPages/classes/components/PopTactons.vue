<template>
  <Popup :show="show" @close="show = false">
    <view
      class="pop-item"
      @click.stop="pinPost"
      v-if="comment.status =='published'"
    >
      <up-icon
        class="top"
        name="download"
        color="rgba(51, 51, 51, 1)"
        size="16"
      ></up-icon>
      <text class="act">{{pinText}}</text>
    </view>
    <view
      class="pop-item"
      @click.stop="editMomment"
      v-if="comment.status =='draft'"
    >
      <up-icon name="edit-pen" color="rgba(51, 51, 51, 1)" size="16"></up-icon>
      <text class="act">编辑</text>
    </view>
    <view class="pop-item" @click.stop="showModal">
      <up-icon name="trash" color="rgba(51, 51, 51, 1)" size="16"></up-icon>
      <text class="act">删除</text>
    </view>
    <u-gap height="20"></u-gap>
  </Popup>
  <!-- 删除段落确认弹窗 -->
  <up-modal
    :show="isDelModal"
    title="温馨提示"
    showCancelButton
    @cancel="isDelModal = false"
    @confirm="delMomment"
  >
    <view class="modlCont">您是否确定删除当前帖子?</view>
  </up-modal>
</template>
<script setup>
  import { reactive, ref, computed } from "vue";
  import { onShow, onShareAppMessage } from "@dcloudio/uni-app";
  import { pin, deleteMoment, getInfo, unpin } from "@/subPages/classes/api.js";

  const emit = defineEmits(["delMoment", "editMoment", "pinnedAction"]);
  const show = ref(false);
  const comment = ref({}); // 当前操作的帖子
  const isDelModal = ref(false);

  const open = (commentData) => {
    show.value = true;
    comment.value = commentData;
  };

  // 显示的文本
  const pinText = computed(() =>
    comment.value.pinned == "1"
      ? "取消置顶"
      : comment.value.pinned == "0"
      ? "置顶"
      : ""
  );

  const pinPost = (event) => {
    if (comment.value.pinned == 1) {
      unpinPostComments();
    } else {
      pinPostComments();
    }
  };

  // 帖子置顶
  const pinPostComments = async () => {
    const res = await pin({ id: comment.value.id });
    uni.$u.toast("置顶成功");
    show.value = false;
    emit("pinnedAction");
  };

  // 取消置顶
  const unpinPostComments = async () => {
    const res = await unpin({ id: comment.value.id });
    uni.$u.toast("操作成功");
    show.value = false;
    emit("pinnedAction");
  };

  // 展示删除帖子
  const showModal = () => {
    isDelModal.value = true;
    show.value = false;
  };

  // 删除帖子
  const delMomment = async () => {
    const res = await deleteMoment({ id: comment.value.id });
    emit("delMoment", comment.value.id);
    show.value = false;
    uni.$u.toast("删除成功");
  };

  // 编辑帖子
  const editMomment = () => {
    emit("editMoment");
    show.value = false;
  };

  // 获取帖子详情
  const getCommentInfo = async () => {
    const res = await getInfo({ id: comment.value.id });
    comment.value = res.data;
  };

  defineExpose({ open });
</script>

<style lang="scss" scoped>
  .modlCont {
    text-align: center;
  }
  .pop-item {
    @include selflex(x, start, center);
    height: 88upx;

    .top {
      transform: rotate(180deg);
    }

    .act {
      margin-left: 30upx;
      font-size: 28upx;
      font-weight: 400;
      letter-spacing: 0px;
      color: rgba(51, 51, 51, 1);
    }
  }
</style>
