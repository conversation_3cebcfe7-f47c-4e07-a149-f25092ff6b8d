<template>
  <up-input
    placeholder="请输入文章标题"
    class="inpt"
    clearable
    :border="'none'"
    v-model="comment.title"
  />
  <view class="span-item" v-for="(item, idx) in comment.modules" :key="idx">
    <view class="span-item-top">
      <text class="span-item-top-tit">{{item.title}}</text>
      <view class="">
        <uni-icons
          color="rgba(102, 102, 102, 1)"
          class="icdel"
          type="compose"
          size="18"
          @click="editParagraph(idx, item.type, comment.classId)"
        />
        <uni-icons
          color="rgba(102, 102, 102, 1)"
          class="icdel"
          type="trash"
          size="18"
          @click="selfDelPara(idx)"
        />
        <uni-icons
          color="rgba(102, 102, 102, 1)"
          class="icdel"
          type="arrow-down"
          size="18"
          @click="moveParagraph(-1,idx)"
        />
        <uni-icons
          color="rgba(102, 102, 102, 1)"
          class="icdel"
          type="arrow-up"
          size="18"
          @click="moveParagraph(1,idx)"
        />
      </view>
    </view>
    <view class="span-item-cont"> {{item.content}} </view>
    <!-- 图片视频展示 -->
    <MomentImage :comment="item" :commentType="comment.type" />
  </view>

  <up-gap height="12"></up-gap>
  <view class="addBtn" @click="showPop"> + 添加 </view>

  <!-- 删除段落确认弹窗 -->
  <up-modal
    :show="isDelModal"
    title="温馨提示"
    showCancelButton
    @cancel="isDelModal = false"
    @confirm="sureDel"
  >
    <view class="modlCont">您是否确定删除当前段落?</view>
  </up-modal>
</template>
<script setup>
  import { ref, toRaw } from "vue";

  import useComment from "../hooks/useComment.js";
  import MomentImage from "./MomentImage.vue";
  // import { usePostComment } from "../stores/postComment.js";

  const emit = defineEmits(["addBtn"]);
  const {
    comment,
    getComment,
    addParagraph,
    moveParagraph,
    delParagraph,
  } = useComment();

  const props = defineProps({
    classId: {
      type: String,
      default: "",
    },
  });

  const isDelModal = ref(false);
  const delIndex = ref(null);

  // 展示是否删除帖子弹窗
  const selfDelPara = (idx) => {
    isDelModal.value = true;
    delIndex.value = idx;
  };

  // 删除段落
  const sureDel = () => {
    isDelModal.value = false;
    delParagraph(delIndex.value);
  };

  // 展示选择添加段落弹窗
  const showPop = () => {
    emit("addBtn");
  };

  // 选择添加段落类型
  const postSelect = (type) => {
    uni.removeStorageSync("stopShow");
    const orderIndex = addParagraph(type); // 当前编辑的段落
    //跳转添加段落页面
    uni.navigateTo({
      url: `/subPages/classes/postType?type=${type}&orderIndex=${orderIndex}&classId=${props.classId}&editype=add&id=${comment.id}`,
    });
  };

  // 编辑段落
  const editParagraph = (orderIndex, type, classId) => {
    uni.removeStorageSync("stopShow");
    //跳转添加段落页面
    uni.navigateTo({
      url: `/subPages/classes/postType?type=${type}&orderIndex=${orderIndex}&classId=${classId||props.classId}&editype=edit&id=${comment.id}`,
    });
  };

  defineExpose({ getComment, postSelect });
</script>

<style lang="scss" scoped>
  .modlCont {
    text-align: center;
  }
  .inpt {
    @include selfshaow;
    background-color: #fff;
    font-size: 30upx;
    height: 110upx;
    border-radius: 28upx;
    padding-left: 20upx !important;
    padding-right: 20upx !important;

    ::v-deep .u-input__content__field-wrapper__field {
      font-size: 30upx !important;
    }
  }

  .span-item {
    // @include selflex(y, start, center);
    margin-top: 24upx;
    background-color: #fff;
    padding: 28upx;
    border-radius: 28upx;

    .span-item-top {
      @include selflex(x, between, center);
      width: 100%;
      line-height: 40upx;

      &-tit {
        color: rgba(0, 0, 0, 1);
        font-size: 30upx;
        font-weight: 600;
        white-space: nowrap;
        flex-grow: 1;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .icdel {
        margin-left: 14upx;
      }
    }

    .span-item-cont {
      width: 100%;
      margin: 30upx 0 14upx 0;
      font-size: 28upx;
      font-weight: 400;
      letter-spacing: 0px;
      line-height: 46upx;
      color: rgba(51, 51, 51, 1);
    }

    .span-item-imgarr {
      width: 100%;
      @include selflex(x, start, center);

      &.img2 {
        .imgItem {
          width: 49%;
          padding-right: 1%;

          &::last-child {
            padding-right: 0upx;
          }
        }
      }

      &.imgmore {
        flex-wrap: wrap;

        .imgItem {
          width: 32.33%;
          padding-right: 1%;
          margin-bottom: 1%;

          &::last-child {
            padding-right: 0upx;
          }
        }
      }

      .imglab {
        width: 100%;
      }
    }
  }

  .addBtn {
    width: 690upx;
    line-height: 110upx;
    margin: 0 auto;
    border-radius: 28upx;
    @include selfshaow;
    text-align: center;
    font-size: 30upx;
    font-weight: 500;
    letter-spacing: 0px;
    color: rgba(63, 121, 255, 1);
    text-align: center;
    vertical-align: middle;
  }

  .post-btn {
    padding: 22upx 0;
    height: 64upx;

    ::v-deep .uicon-plus {
      font-size: 20upx !important;
    }

    ::v-deep .u-button__text {
      font-size: 24upx !important;
    }
  }
</style>
