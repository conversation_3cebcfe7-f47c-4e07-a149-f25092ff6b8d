<!-- 活动列表 -->
<template>
  <view :class="{ 'act-item': true, active: isExplad }" @click="togExplad">
    <view class="act-item-tit">
      <text>{{ actData.subjectStage.title }}</text>
      <up-icon class="arrow" name="arrow-up" color="rgba(102, 102, 102, 1)" size="16" />
    </view>
    <view v-if="isExplad" @click.stop="() => { }">
      <view class="act-item-desc">
        带 <text class="blue">*</text> 活动为未完成教案或评价的活动
      </view>

      <view class="act-item-wrap">
        <view class="act-item-wrap-tr">
          <view class="act-item-wrap-th"></view>
          <view class="act-item-wrap-th">活动名称</view>
          <view class="act-item-wrap-th">日期</view>
          <view class="act-item-wrap-th">形式</view>
          <view class="act-item-wrap-th"></view>
        </view>
        <view class="act-item-wrap-tr" v-for="(item, _idx) in actData.subjectActivities" :key="_idx"
          @tap.stop="groupChange(item.id)">
          <view class="act-item-wrap-th">
            {{ _idx + 1 > 9 ? _idx + 1 : `0${_idx + 1}` }}
          </view>
          <view class="act-item-wrap-th black">
            {{ item.name }}<text class="blue">*</text>
          </view>
          <view class="act-item-wrap-th black">
            {{ item.implementedAt && formatDate(item.implementedAt, "MM-DD") }}
          </view>
          <view class="act-item-wrap-th">
            <view class="tag-blue"> {{ item.organizationFormStr }} </view>
          </view>
          <view class="act-item-wrap-th">
            <up-radio-group v-model="_actId" placement="row" @change="groupChange">
              <up-radio shape="circle" :name="item.id" />
            </up-radio-group>
          </view>
        </view>
      </view>
    </view>
  </view>
  <!-- 选择标题确认框 -->
  <up-modal :show="isShowModal" showCancelButton @cancel="modalCancle" @confirm="modalSure">
    <view class="modlCont">确认用此活动让AI生成班级圈吗？</view>
  </up-modal>
</template>
<script setup>
  import { reactive, ref, computed, toRaw, watch } from "vue";
  import { formatDate } from "@/utils/index.js";

  const emit = defineEmits(["change"]);
  const props = defineProps({
    // 班级
    actData: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    actId: {
      type: [String, Number],
      default: "",
    },
    // 来源页面
    fromPage: {
      type: String,
      default: "",
    },
  });

  const idx = ref(0);
  const isExplad = ref(false); // 是否展开
  const _actId = ref(toRaw(props.actId));
  const isShowModal = ref(false);
  let changeVal = ref(null);

  watch(
    () => props.actId,
    (val) => {
      _actId.value = val;
    }
  );

  // 切换当前是否展开
  const togExplad = () => {
    isExplad.value = !isExplad.value;
  };
  
  let _tempActId = ""
  const groupChange = (val) => {
    _tempActId = _actId.value
    _actId.value = val;
    changeVal.value = val;

    // 如果来源是师幼互动，直接确认选择，不显示弹窗
    if (props.fromPage === 'teacherChildInteraction') {
      modalSure();
    } else {
      isShowModal.value = true;
    }
  };

  const modalSure = () => {
    const findActObj = props.actData.subjectActivities.find(
      (item) => item.id == changeVal.value
    );
    isShowModal.value = false;
    emit("change", _actId.value, toRaw(findActObj) || null);
  };

  const modalCancle = () => {
    isShowModal.value = false;
    _actId.value = props.actId;
  };
</script>

<style lang="scss" scoped>
  .modlCont {
    text-align: center;
  }
  .blue {
    color: rgba(63, 121, 255, 1);
  }

  .black {
    color: rgba(51, 51, 51, 1) !important;
  }

  @each $var in "blue", "orange", "red", "green", "violet" {
    .tag-#{$var} {
      display: inline-block;
      border-radius: 8upx;
      font-size: 24upx;
      font-weight: 500;
      line-height: 33upx;
      padding: 4upx 14upx;

      @if $var == "blue" {
        color: rgba(82, 131, 247, 1);
        background-color: rgba(82, 131, 247, 0.12);
      }

      @if $var == "orange" {
        color: rgba(240, 145, 77, 1);
        background-color: rgba(240, 145, 77, 0.12);
      }

      @if $var == "red" {
        color: rgba(237, 111, 114, 1);
        background-color: rgba(237, 111, 114, 0.12);
      }

      @if $var == "green" {
        color: rgba(84, 186, 106, 1);
        background-color: rgba(84, 186, 106, 0.12);
      }

      @if $var == "violet" {
        color: rgba(110, 116, 230, 1);
        background-color: rgba(110, 116, 230, 0.12);
      }
    }
  }

  .act-item {
    @include selfshaow;
    padding: 28upx;
    margin-top: 24upx;
    background-color: #fff;
    border-radius: 28upx;

    &-tit {
      @include selflex(x, between, start);
      font-size: 30upx;
      font-weight: 600;
      letter-spacing: 0px;
      line-height: 36upx;
      color: rgba(51, 51, 51, 1);
      text-align: left;
    }

    &-desc {
      font-size: 22upx;
      line-height: 30upx;
      color: rgba(128, 128, 128, 1);
      padding: 12upx 0 24upx 0;
      border-bottom: 1upx solid #f3f3f3;
    }

    &-wrap {
      &-tr {
        padding: 16upx 0;
        border-bottom: 1upx solid #f3f3f3;
        @include selflex(x, start, center);

        &:last-child {
          border-width: 0;
        }
      }
      display: flex;
      flex-wrap: wrap;

      &-th {
        font-size: 28upx;
        line-height: 40upx;
        color: rgba(128, 128, 128, 1);

        &:nth-child(1) {
          width: 50upx;
        }

        &:nth-child(2) {
          width: 300upx;
        }

        &:nth-child(3) {
          width: 100upx;
        }

        &:nth-child(4) {
          width: 100upx;
        }

        &:nth-child(5) {
          width: 50upx;

          ::v-deep .u-radio-group--row {
            width: 50upx;
          }
        }
      }
    }

    &.active {
      .arrow {
        transform: rotate(180deg);
      }

      .act-item-desc {
        display: block;
      }
    }
  }
</style>
