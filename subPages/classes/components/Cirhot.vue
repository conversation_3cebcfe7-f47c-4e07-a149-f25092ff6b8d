<!-- 圈子 -->
<template>
  <view class="hop-tip" @click="goToCommentsDetailPage">
    <text class="top">置顶</text>
    <text class="hop-tip-inf">
      {{ pinnedData.title || pinnedData.content }}
    </text>
  </view>
</template>
<script setup>
  const props = defineProps({
    // 班级
    pinnedData: {
      type: Object,
      required: true,
    },
  })
  // 跳转帖子详情
  const goToCommentsDetailPage = () => {
    uni.navigateTo({
      url: `/subPages/classes/commentsdetails?id=${props.pinnedData.id}`,
    })
  }
</script>

<style lang="scss"
  scoped>
  .hop-tip {
    display: flex;
    align-items: center;
    padding-bottom: 24upx;
    border-bottom: 1upx solid rgba(238, 238, 238, 1);
    padding-top: 24upx;

    &:last-child {
      border-width: 0;
    }

    .top {
      font-size: 20upx;
      font-weight: 500;
      letter-spacing: 0upx;
      color: rgba(237, 111, 114, 1);
      background: rgba(237, 111, 114, 0.12);
      padding: 6upx;
      border-radius: 5upx;
      margin-right: 15upx;
    }

    .hop-tip-inf {
      font-size: 28upx;
      font-weight: 400;
      letter-spacing: 0upx;
      line-height: 30upx;
      color: rgba(51, 51, 51, 1);
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      word-break: break-all;
      flex: 1;
    }
  }
</style>