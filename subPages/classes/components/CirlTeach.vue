<!-- 圈子 -->
<template>
  <view class="proItem shadow" @click="showTeachInfo">
    <up-image class="imglab" width="88upx" height="88upx" mode="aspectFit" radius="50"
      src="https://s.mypacelab.com/202503/HtSjhmnWb9zv6988a04f615ad8977c0c79dd6fdd0aae.png"
      :lazy-load="true"></up-image>
    <view class="info">
      <view class="name"> {{teach.name}} </view>
     <!-- <view class="intrus">
         小一班 <text class="line">｜</text> 
        {{ teach.post }}
      </view>-->
    </view>
  </view>

  <Popup :show="show" @close="show = false">
    <view class="tit">
      <text>教师详情</text>
    </view>
    <view class="proItem">
      <up-image class="imglab" width="88upx" height="88upx" mode="aspectFit" radius="50"
        src="https://s.mypacelab.com/202503/HtSjhmnWb9zv6988a04f615ad8977c0c79dd6fdd0aae.png"
        :lazy-load="true"></up-image>
      <view class="info">
        <view class="name"> {{teachInfo.name}} </view>
        <view class="intrus"> 
        <!-- 小一班 <text class="line">｜</text> -->
        {{ teach.post }} 
        </view>
      </view>
      <view class="bord">
        <text>{{teachInfo.mobile}}</text>
        <uni-icons type="phone" size="15" color="rgba(102, 102, 102, 1)" @click="makePhoneCall"></uni-icons>
      </view>
      <view class="classnam" @click="tabChang('班级圈')"> {{classTitle}}的主页></view>
    </view>
  </Popup>
</template>
<script setup>
  import { reactive, ref } from 'vue'
  import { onShow, onShareAppMessage, onLoad } from '@dcloudio/uni-app'
  import { getTeachInfo } from "@/api/classApi.js";

  const props = defineProps({
    teach: {
      type: Object,
      default: () => {}
    },
    classTitle: {
      type: String,
      default: ''
    },
    tabChang: {
      type: Function,
      default: () => () => {}
    }
  })
  const show = ref(false)
  const teachInfo = ref({})

  // 展示教师详情
  const showTeachInfo = async () => {
    show.value = true;
    const res = await getTeachInfo({ id: props.teach.id })
    teachInfo.value = res.data
  }

  // 拨打电话
  const makePhoneCall = () => {
    uni.makePhoneCall({
      phoneNumber: teachInfo.value.mobile, // 需要拨打的电话号码
      success() {
        console.log('拨打成功');
      },
      fail(err) {
        console.error('拨打失败', err);
      }
    });
  }
</script>

<style lang="scss" scoped>
  .tit {
    @include selflex(x, between, center);
    font-weight: 600;
    letter-spacing: 0px;
    line-height: 47upxpx;
    color: rgba(51, 51, 51, 1);
    font-size: 32upx;
    text-align: center;
    font-family: PingFangSC-regular;
    padding-bottom: 37upx;
  }

  .proItem {
    &.shadow {
      @include selfshaow;
    }

    margin-top: 24upx;
    display: flex;
    flex-wrap: wrap;
    border-radius: 16upx;
    background-color: rgba(255, 255, 255, 1);
    color: rgba(128, 128, 128, 1);
    font-size: 28upx;
    text-align: left;
    padding: 28upx;

    &>.imglab {
      margin-right: 24upx;
    }

    .line {
      color: rgba(204, 204, 204, 1);
    }

    .name {
      font-size: 30upx;
      font-weight: 600;
      letter-spacing: 0upx;
      line-height: 36upx;
      color: rgba(51, 51, 51, 1);
      padding-bottom: 18upx;
      padding-top: 20upx;
    }

    .intrus {
      font-size: 24upx;
      font-weight: 400;
      letter-spacing: 0upx;
      line-height: 28upx;
      color: rgba(128, 128, 128, 1);
    }

    .bord {
      width: 100%;
      @include selflex(x, between, center);
      font-size: 28upx;
      font-weight: 400;
      letter-spacing: 0upx;
      color: rgba(51, 51, 51, 1);
      padding-top: 32upx;
      margin-top: 32upx;
      border-top: 1upx solid rgba(238, 238, 238, 1);
    }

    .classnam {
      width: 100%;
      font-size: 30upx;
      font-weight: 500;
      letter-spacing: 0upx;
      line-height: 30upx;
      color: rgba(63, 121, 255, 1);
      text-align: center;
      padding: 118upx 0 90upx 0;
    }
  }
</style>