<!-- 图片圈子 -->
<template>
  <view v-if="comment.attachments" class="momWrap">
    <!-- 没有图片 -->
    <view class="cont" v-if="comment.attachments.length == 0 && commentType != 'article' && comment.content">
      <view class="tits"> {{ comment.content }} </view>
    </view>
    <view class="cont nopad">
      <view class="image-item" :class="comment.attachments.length == 1 ? 'singe' : ''"
        v-for="(itm, idx) in comment.attachments" :key="idx">
        <image class="imglab" mode="aspectFill" :src="itm.url + '?x-oss-process=image/resize,m_fill,w_150'"
          v-if="itm.type == 'image'" @click.stop="previewImg(idx)" />
        
        <view class="videowarp" v-show="itm.type == 'video'">
          <video 
            v-if="itm.type == 'video'"   
            :id="`video_${itm.url}`"
            @longpress="downloadVideo(itm.url)" 
            @play="videoPlay(`video_${itm.url}`)"
            class="videoSelf" 
            style="width: 100%"
            :src="itm.url" 
            :poster="`${itm.url}?x-oss-process=video/snapshot,t_800`" 
            loop controls show-mute-btn
            auto-pause-if-open-native auto-pause-if-navigate 
            preload="auto"
          >
          </video>
          <view class="disablecPlay" v-if="!canPlay"></view>
        </view>
      </view>
    </view>
  </view>
</template>
<script setup>
import { reactive, ref, toRaw, onMounted, getCurrentInstance} from "vue"; 
const emit = defineEmits(["playVideo"]);
const props = defineProps({
  comment: {
    type: Object,
    default: () => { },
  },
  commentType: {
    type: String,
    default: "",
  },
  artComment: {
    // 文章类型的原始帖子
    type: Object,
    default: null,
  },
  artIdx: {
    // 文章类型的moduls 索引
    type: Number,
    default: 0,
  },
  canPlay: {
    type: Boolean,
    default: true,
  },
});

const { proxy:_this } = getCurrentInstance()

const videoPlay = (videoid)=> {
  emit('playVideo', uni.createVideoContext(videoid, _this));
}

onMounted(() => {
  props.comment.attachments?.forEach((itm, idx) => {
    if (itm.type == "video") {
      console.log("video", itm.url);
      const video = uni.createVideoContext(`video_${idx}`);
      video.src = itm.url;
    }
  });
});

const previewImg = (_idx) => {
  let idx = _idx;
  let _lists = toRaw(props.comment.attachments);
  if (props.artComment) {
    const _modules = toRaw(props.artComment.modules);
    _lists = _modules.reduce((pre, curModules, reduceIdx) => {
      if (reduceIdx == props.artIdx) {
        idx = pre.length + _idx;
      }
      return [...pre, ...curModules.attachments];
    }, []);
  } else {
    _lists = toRaw(props.comment.attachments);
  }

  let current = 0;
  const urls = [];
  const lists = toRaw(_lists);
  let imageIndex = 0;
  for (var i = 0; i < lists.length; i++) {
    const item = lists[i];
    if (item.type && item.type === "image") {
      let url = item.url || item.thumb;
      urls.push(`${url}?i=${i}`);
      if (i === idx) {
        current = imageIndex;
      }
      imageIndex += 1;
    }
  }
  if (urls.length < 1) {
    return;
  }
  uni.previewImage({
    urls: urls,
    current: current,
    fail() {
      // toast("预览图片失败");
    },
  });
};

const downloadVideo = (url) => {
  console.log('下载视频', url);
  uni.getSetting({
    success: (res) => {
      if (!res.authSetting['scope.writePhotosAlbum']) {
        uni.authorize({
          scope: 'scope.writePhotosAlbum',
          success: () => {
            uni.downloadFile({
              url: url,
              success: (res) => {
                if (res.statusCode === 200) {
                  uni.saveImageToPhotosAlbum({
                    filePath: res.tempFilePath,
                    success: () => uni.$u.toast('视频已保存到相册'),
                    fail: () => uni.$u.toast('保存到相册失败')
                  });
                }
              },
              fail: () => uni.$u.toast('下载失败')
            });
          },
          fail: () => uni.$u.toast('授权失败，无法保存视频')
        });
      } else {
        uni.downloadFile({
          url: url,
          success: (res) => {
            if (res.statusCode === 200) {
              uni.saveImageToPhotosAlbum({
                filePath: res.tempFilePath,
                success: () => uni.$u.toast('视频已保存到相册'),
                fail: () => uni.$u.toast('保存到相册失败')
              });
            }
          },
          fail: () => uni.$u.toast('下载失败')
        });
      }
    }
  });
};

</script>
<style lang="scss" scoped>
.momWrap {
  width: 100%;
  box-sizing: border-box;
}

.cont {
  width: 100%;
  @include selflex(x, start, start);
  flex-wrap: wrap;
  border-radius: 24upx;
  background: #ffffff;
  padding: 14upx;

  &.nopad {
    padding: 0;
  }

  .tits {
    white-space: pre-line;
    font-size: 28upx;
  }

  .image-item {
    width: calc(100% / 3 - 10upx);
    margin: 0 5upx 10upx 5upx;
    height: 200upx;

    &.singe {
      width: 100%;
      box-sizing: border-box;
      height: 400upx;
      margin-bottom: 0;
    }

    .imglab {
      width: 100%;
      height: 100%;
      border-radius: 4upx;
      line-height: 1;
      display: flex;
    }

    .videowarp {
      width: 100%;
      height: 400upx;
      border-radius: 4upx;
      position: relative;

      .disablecPlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0);
        z-index: 10;
      }

      .videoSelf {
        width: 100%;
        height: 400upx;
      }
    }
  }
}
</style>
