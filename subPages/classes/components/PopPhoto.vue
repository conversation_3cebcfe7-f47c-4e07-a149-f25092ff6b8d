<template>
  <up-input :placeholder="mode == 'page' ? '填写标题' : '请输入段落标题，不写则不显示'"
    :placeholderStyle="{color: 'rgba(177, 179, 181, 1)'}" border="bottom" clearable class="ipt"
    v-model="comment.title" />
  <up-gap height="10" />
  <view>
    <up-textarea class="input-post" border="none" height="30vh" style="text-align: justify"
      :placeholderStyle="{'font-size': '28upx', color: 'rgba(177, 179, 181, 1)'}"
      :placeholder="mode == 'page' ? '添加正文' : '请输入段落描述的文字，不写则不显示'" :maxlength="-1" v-model="comment.content" />
  </view>
  <up-gap height="10" />
  <view class="tag">
    <view class="tag-btn" @click="aiPolishi">
      <image class="ai-img" src="/static/icon/ai.png" mode="scaleToFill" />
      <text>AI文字润色</text>
    </view>
  </view>
  <up-gap height="10" />
  <view>
    <up-upload 
      :fileList="fileList"
      :maxCount="9"
      :width="102"
      :height="102"
      @afterRead="selfAfter" 
      @delete="deletePic" 
      name="photo" 
      multiple 
      accept="image"
    >
      <view class="upimg">
        <image class="upimg-con" src="/static/icon/u-icon.png" mode="scaleToFill" />
        <view class="txt"> 添加照片 </view>
      </view>
    </up-upload>
  </view>
</template>
<script setup>
  import { watch } from "vue";
  import useUploadClasses from "../hooks/useUploadClasses.js";
  import useComment from "../hooks/useComment.js";
  import { polishiPostByAi } from "@/api/classApi.js";

  const emit = defineEmits(["togAiLoading"]);
  const props = defineProps({
    // 类型 art发文章  page单独发照片帖子
    mode: {
      type: String,
      default: "page",
    },
  });
  const { fileList, afterRead, deletePic, setFileList } = useUploadClasses();
  const { comment, getComment, getCommentFileList } = useComment();
  

  const selfAfter = async (file) => {
    // 检查当前文件列表长度加上新选择的文件是否超过9张
    if (fileList.value.length + 1 > 9) {
      uni.showToast({
        title: "最多只能选择9张图片",
        icon: "none",
      });
      return;
    }
    
    const curFile = file?.file[0];
    const fileSize = curFile.size;
    const maxSize = 10 * 1024 * 1024; // 最大10m
    if (fileSize > maxSize) {
      uni.showToast({
        title: "图片大小不能超过10M",
        icon: "none",
      });
      return;
    }
    // 确保在图片上传后更新 comment 对象的 attachments 属性
    comment.attachments = fileList.value.map((item) => ({
      type: "image", //类型,image图片，video视频
      url: item.url, //附件地址
      description: item.description || "", //附件描述
      status: item.status ? item.status : 'uploading',
      message: item.message ? item.message : '上传中',
    }));
    afterRead(file);
  }

  // ai润色
  const aiPolishi = async () => {
    emit("togAiLoading", true);
    try {
      const res = await polishiPostByAi({ content: comment.content });
      comment.content = res.data;
    } catch (e) {
      console.log(e);
    } finally {
      emit("togAiLoading", false);
    }
  };


  // 监听上传的文件设置帖子附件
  watch(fileList, (newFileList) => {
    comment.attachments = newFileList.map((item) => ({
      type: "image", //类型,image图片，video视频
      url: item.url, //附件地址
      description: item.description || "", //附件描述
    }));
  }, {deep:true, immediate: true });

  // 重新设置帖子附件
  const setFileLists = () => {
    setFileList(getCommentFileList());
    console.log('重新设置帖子附件:', comment.attachments);
  };

  defineExpose({ getComment, setFileLists });
</script>
<style lang="scss" scoped>
  /* 调整删除按钮大小 */
  ::v-deep .u-upload__deletable {
    transform: scale(1.4) !important;
  }

  .ipt {
    padding-left: 0 !important;
    padding-right: 0 !important;

    ::v-deep .uni-input-input {
      letter-spacing: 0px;
      color: rgba(51, 51, 51, 1);
    }
  }

  .input-post {
    min-height: 20vh;
    font-size: 28upx;
    padding: rpx !important;
  }

  .tag {
    @include selflex(x, end, center);

    &-btn {
      @include selflex(x, center, center);
      padding: 16upx 20upx;
      border-radius: 34upx;
      background: rgba(54, 124, 255, 0.06);
      font-size: 24upx;
      font-weight: 500;
      letter-spacing: 0upx;
      color: rgba(63, 121, 255, 1);

      .ai-img {
        width: 28upx;
        height: 23upx;
        margin-right: 8upx;
      }
    }
  }

  .upimg {
    width: 204upx;
    height: 204upx;
    border-radius: 8.46upx;
    background: #f5f5f5;
    border: 1upx dashed rgba(217, 217, 217, 1);
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    align-content: center;

    .upimg-con {
      width: 37upx;
      height: 31upx;
    }

    .txt {
      width: 100%;
      padding-top: 10upx;
      text-align: center;
      font-size: 22upx;
      line-height: 28upx;
      font-weight: 400;
      letter-spacing: 0upx;
      color: rgba(128, 128, 128, 1);
    }
  }
</style>