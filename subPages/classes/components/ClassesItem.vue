<template>
	<view class="class-item">
		<view class="info" @click="goCircle">
			<view class="tit">
				<view><text class="name">小一班</text><text> 李云朵</text></view>
				<up-icon name="more-dot-fill" color="rgba(128, 128, 128, 1)"></up-icon>
			</view>
			<view class="info-itm">
				29位 学生 <text class="line">｜</text> 4条 观察记录 <text class="line">｜</text> 10条 班级圈
			</view>
			<view class="info-itm">
				<up-tag text="薄弱领域" plain plainFill borderColor="#fff" size="mini"> </up-tag>
				数学、社会情感、语言
			</view>
			<view class="info-itm"> 活动： 2/10 <text class="line">｜</text> 评价：2/10 </view>
		</view>
	</view>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { onShow, onShareAppMessage } from '@dcloudio/uni-app'
import { checks } from '@/utils/index.js'
import { getAiTemplateList } from '@/api/aiTemplate.js'

const tabArr = ref([{ name: '班级列表' }, { name: '班级统计' }])

//跳转圈子
const goCircle = () => {
	uni.navigateTo({
		url: '/pages/classes/circle',
	})
}
onShow(() => {
	checks()
})
</script>

<style lang="scss" scoped>
.class-item {
	@include selfshaow;
	font-family: PingFangSC-regular;
	box-sizing: border-box;
	margin-top: 24upx;
	width: 100%;
	background-color: #fff;
	border-radius: 28upx;
	padding: 28upx;
	font-size: 24upx;
	line-height: 28upx;
	color: rgba(128, 128, 128, 1);
	.line {
		color: rgba(204, 204, 204, 1);
	}

	.info {
		.tit {
			@include selflex(x, between, center);
			line-height: 36upx;
			width: 100%;
			.name {
				font-size: 30upx;
				font-weight: 600;
				letter-spacing: 0upx;
				color: rgba(51, 51, 51, 1);
			}
		}
		.info-itm {
			padding-top: 14upx;
		}
	}
}
</style>
