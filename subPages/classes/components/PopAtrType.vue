<template>
  <Popup :show="show" @close="show = false" v-if="show">
    <view class="selfModal">
      <view class="pop-item" @click="sure('image')">
        <image
          class="img"
          src="@/static/icon/photo1.png"
          mode="aspectFill"
        ></image>
        <text class="act">图片</text>
      </view>
      <view class="pop-item" @click="sure('video')">
        <image
          class="img"
          src="@/static/icon/video1.png"
          mode="aspectFill"
        ></image>
        <text class="act">视频</text>
      </view>
      <u-gap height="20"></u-gap>
    </view>
  </Popup>
</template>

<script setup>
  import { ref, defineExpose } from "vue";

  const emit = defineEmits(["surChose"]);
  const show = ref(false);

  const open = () => {
    show.value = true;
  };
  const sure = (val) => {
    show.value = false;
    emit("surChose", val);
  };
  defineExpose({ open });
</script>

<style lang="scss" scoped>
  .selfModal {
    .pop-item {
      @include selflex(x, start, center);
      height: 88upx;
      .img {
        width: 40upx;
        height: 40upx;
      }
      .act {
        margin-left: 30upx;
        font-size: 28upx;
        font-weight: 400;
        letter-spacing: 0px;
        color: rgba(51, 51, 51, 1);
      }
    }
  }
</style>
