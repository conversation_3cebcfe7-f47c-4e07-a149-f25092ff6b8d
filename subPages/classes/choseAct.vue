<template>
  <base-layout
    navTitle="选择活动"
    containerClass="actThem"
    :footerStyle="{ padding: 0 }"
    :contentStyle="{ padding: 0 }"
    @leftClick="goback"
  >
    <view class="theme-info">
      <view class="theme-info-title">
        <text class="theme-info-title-txt">{{theamObj.title}}</text>
        <!-- <text class="theme-info-title-tag">学习活动</text> -->
      </view>
      <view class="theme-info-cont" v-if="theamObj.schoolClass">
        <text class="text-with-line">
          {{theamObj.schoolClass.nickname}}-{{theamObj.schoolClass.title}}
        </text>
        <text v-if="theamObj.schoolClass.extra">
          {{theamObj.schoolClass.extra.masterTitle}}
        </text>
      </view>
      <!-- // <view class="theme-info-date"> 2024.10.08-2024.11.02 </view> -->
    </view>
    <view class="act-wrap">
      <ActItem
        v-for="(item, idx) in actArr"
        :key="idx"
        :actData="item"
        :actId="actId"
        :fromPage="fromPage"
        @change="actIdChange"
      />
    </view>
    <u-gap :height="8"></u-gap>
  </base-layout>
</template>

<script setup>
  import { reactive, ref, computed } from "vue";
  import { onShow, onLoad } from "@dcloudio/uni-app";
  import { checks } from "@/utils/index.js";
  import { enumList } from "@/api/index.js";
  import {
    getActList,
    generatePostContentByAi,
  } from "@/api/classApi.js";
  import ActItem from "@/subPages/classes/components/ActItem.vue";

  const subjectId = ref(""); // 主题ID
  const fromPage = ref(""); // 来源页面
  const actArr = ref([]); // 活动列表
  const actId = ref("");
  const actTypeArr = ref({});
  const theamObj = ref({});

  // 根据活动主题id获取活动列表
  const getActArr = async () => {
    const res = await getActList({ subjectId: subjectId.value });
    actArr.value = res.data.map((item) => {
      item.subjectActivities = item.subjectActivities.map((_item) => {
        return {
          organizationFormStr: actTypeArr.value[_item.organizationForm],
          ..._item,
        };
      });
      return { ...item };
    });
  };

  // 返回选择主题页面
  const goback = () => {
    uni.navigateBack();
  };

  // 返回到发帖页面
  const choseAct = () => {
    uni.navigateBack({
      delta: 2,
    });
  };

  // 主题选择改变
  const actIdChange = async (_actId, actObj) => {
    actId.value = _actId;
    uni.setStorageSync("_actObj", actObj);
    choseAct();
  };
  // 活动形式
  const getenumList = async () => {
    let res = await enumList();
    actTypeArr.value = res.data.SubjectActivityOrganizationFormEnumDesc;
  };

  onShow(() => {
    checks();
    getActArr();

    theamObj.value = uni.getStorageSync("_theam") || {};
  });

  onLoad((options) => {
    subjectId.value = options.subjectId;
    fromPage.value = options.from || '';
    getenumList();
  });
</script>

<style lang="scss">
  .actThem {
    background: #f5f5f5
      url("https://c.mypacelab.com/vxmp/img/classDeatils_bg_3x.png") no-repeat
      center top !important;
    background-size: contain !important;
  }
</style>

<style lang="scss" scoped>
  .theme-info {
    padding: 25upx 30upx 16upx 30upx;
    font-size: 24upx;
    font-weight: 400;
    line-height: 34upx;
    color: rgba(128, 128, 128, 1);
    @each $var in tit, cont {
      &-#{$var} {
        margin-bottom: 10upx;
      }
    }
    &-date {
      padding-bottom: 16upx;
      border-bottom: 1upx solid rgba(238, 238, 238, 1);
    }
    &-title {
      @include selflex(x, start, center);
      padding-bottom: 6upx;
      font-size: 44upx;
      font-weight: 600;
      letter-spacing: 0px;
      line-height: 56upx;
      color: rgba(51, 51, 51, 1);
      &-txt {
        max-width: 520upx;
        flex-shrink: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      &-tag {
        margin-left: 8upx;
        border-radius: 8upx;
        background: rgba(253, 242, 234, 1);
        font-size: 24upx;
        line-height: 1;
        font-weight: 700;
        color: rgba(240, 161, 77, 1);
        padding: 8upx 16upx;
      }
    }
  }

  .act-wrap {
    padding: 4upx 30upx;
  }
</style>
