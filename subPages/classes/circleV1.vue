<template>
  <BaseLayout
    :nav-title="curTab"
    :content-style="{ padding: '0' }"
    @leftClick="goback"
    @scrolltolower="loadMore"
    containerClass="cirBaseLayout"
  >
    <view class="class-wrap">
      <template v-if="curTab === '班级圈'">
        <view class="pro">
          <Cirhot
            v-for="(item, idx) in pinnedArr"
            :key="idx"
            :pinnedData="item"
          />
        </view>
        <CirlItem
          v-for="(item, idx) in commentArr"
          :key="item.id"
          :comment="item"
          @delMoment="delMoment"
          @editMoment="editMoment(item)"
          @pinnedAction="pinnedAction(item)"
          :classTitle="classInfo.title"
          :canPlay="false"
        />
      </template>

      <template v-if="curTab == '教师'">
        <view class="cont-teach">
          <view
            class="cont-teach-noData"
            v-if="!teachArr && teachArr.length == 0"
            >暂无数据</view
          >
          <CirlTeach
            v-for="(item, key) in teachArr"
            :key="item.id"
            :teach="item"
            :classTitle="classInfo?.title"
            :tabChang="tabChang"
          />
        </view>
      </template>

      <template v-if="curTab == '儿童'">
        <view class="cont-teach">
          <Children :classId="classId" />
        </view>
      </template>
    </view>
    <up-loadmore
      v-if="curTab === '班级圈'"
      :status="loading"
      loadmore-text="上拉或点击加载更多"
      @loadmore="loadMore"
    />
    <!-- 发帖类型弹窗 -->
    <PopPostType
      ref="popPostTypeRef"
      @surChose="postSelect"
    />

    <view
      class="plusBtn"
      v-if="curTab === '班级圈'"
      @click="showPopPostType"
    />
  </BaseLayout>
</template>
<script setup>
import { reactive, ref, toRaw, nextTick } from "vue";
import { onShow, onLoad, onReachBottom } from "@dcloudio/uni-app";
import { checks } from "@/utils/index.js";

import Cirhot from "./components/Cirhot.vue";
import CirlItem from "./components/CirlItem.vue";
import CirlTeach from "./components/CirlTeach.vue";
import Children from "./components/Children/children.vue";
import useSystemBar from "@/utils/useSystemBar";
import PopPostType from "./components/PopPostType.vue";
import BaseLayout from "@/components/base-layout/base-layout.vue";
import {
  getclassDetail,
  pinned,
  teacherList,
  getTeachList,
  getInfo,
} from "@/api/classApi.js";

const classId = ref(null); // 班级id
const schoolId = ref(null); // 学校id
const classInfo = ref({});
const pinnedArr = ref([]);
const commentArr = ref([]); // 帖子列表
const teachArr = ref([]); // 教师列表
const curTab = ref("");
const popPostTypeRef = ref(null);
const loading = ref("loadmore"); // loading状态 loadmore: 加载更多 nomore: 没有更多 loading: 加载中
const paramsPage = reactive({
  pageSize: 10,
  currentPage: 1,
});
const isOne = ref(false); // 是否第一次加载

//下拉加载函数
const loadMore = () => {
  if (loading.value == "loading") {
    return;
  }
  paramsPage.currentPage = paramsPage.currentPage + 1;
  if (curTab.value == "班级圈") {
    getCommentArr();
  }
};
// 返回
const goback = () => {
  uni.setStorageSync("post", true);
  // uni.navigateBack();  重复的返回
};
// 获取班级详情
const getClassInfo = async () => {
  classInfo.value = null;
  const res = await getclassDetail(classId.value);
  classInfo.value = res.data;
};
// 查看置顶帖子
const getPinnedArr = async () => {
  const res = await pinned({ classId: classId.value });
  pinnedArr.value = res.data;
};
//跳转发帖
const postSelect = (type) => {
  uni.navigateTo({
    url: `/subPages/classes/post?type=${type}&classTitle=${classInfo.value.title}&classId=${classInfo.value.id}`,
  });
};
// tab 改变
const tabChang = async (name) => {
  paramsPage.pageSize = 10;
  paramsPage.currentPage = 1;
  curTab.value = name;
  loading.value = "loadmore";
  if (curTab.value == "班级圈") {
    getCommentArr();
  }
  if (curTab.value == "教师") {
    getTeachArr();
  }
};
// 页面返回逻辑处理
const pageBackInit = async () => {
  // 尝试获取需要删除的帖子 ID
  const delId = uni.getStorageSync("_deletId");
  if (delId) {
    // 若存在需要删除的帖子 ID，移除存储中的该 ID 并调用删除帖子函数
    uni.removeStorageSync("_deletId");
    delMoment(delId);
    return;
  }
  // 尝试获取需要更新的帖子 ID
  const commentId = uni.getStorageSync("_upCommentID");
  // 移除存储中的更新帖子 ID
  uni.removeStorageSync("_upCommentID");
  // 查找当前帖子列表中是否存在需要更新的帖子
  const findCom = commentArr.value.find((item) => item.id == commentId);
  if (commentId && findCom) {
    // 若存在需要更新的帖子且在列表中找到，重新获取该帖子信息并更新列表
    const res = await getInfo({ id: commentId });
    const _comment = res.data;
    const _arr = [];
    commentArr.value.forEach((item) => {
      _arr.push(item.id == commentId ? _comment : item);
    });
    commentArr.value = [..._arr];
  } else if (commentId && !findCom) {
    // 若存在需要更新的帖子但不在列表中，重新获取该帖子信息并添加到列表
    const res = await getInfo({ id: commentId });
    const _comment = res.data;
    if (_comment.status === "draft") {
      // 若帖子状态为草稿，将其添加到列表头部
      commentArr.value.unshift(_comment);
    } else {
      // 找出所有草稿帖子的数量
      const draftCount = commentArr.value.filter(item => item.status === "draft").length;
      // 将非草稿帖子添加到草稿帖子之后
      commentArr.value.splice(draftCount, 0, _comment);
    }
  } else if (commentArr.value.length == 0) {
    // 若帖子列表为空，重置分页参数并重新获取帖子列表
    console.log("没有更新");
    paramsPage.pageSize = 10;
    paramsPage.currentPage = 1;
    loading.value = "loadmore";
    getCommentArr();
  }
};
//发帖类型弹窗展示
const showPopPostType = () => {
  popPostTypeRef.value.open();
};

// 教师查看帖子列表
const getCommentArr = async () => {
  if (loading.value == "loading") {
    return;
  }
  if (paramsPage.currentPage == 1) {
    commentArr.value = [];
  }
  loading.value = "loading";
  const params = {
    pageSize: paramsPage.pageSize,
    currentPage: paramsPage.currentPage,
    classId: classId.value,
  };
  try {
    console.log("params", paramsPage.currentPage, "commentArr,班级圈");
    const res = await teacherList(params);
    isOne.value = true;
    commentArr.value = [...toRaw(commentArr.value), ...res.data];
    if (res.metadata.count > commentArr.value.length) {
      loading.value = "loadmore";
    } else {
      loading.value = "nomore";
    }
  } catch (e) {
    const tempPage = paramsPage.currentPage - 1;
    paramsPage.currentPage = tempPage < 1 ? 1 : tempPage;
    loading.value = "loadmore";
  }
};
// 获取当前班级教师列表
const getTeachArr = async () => {
  if (loading.value == "loading") {
    return;
  }
  loading.value = "loading";
  const params = {
    classId: classId.value,
    schoolId: schoolId.value,
  };
  try {
    const res = await getTeachList(params);
    teachArr.value = [...res.data];
    loading.value = "nomore";
  } catch (e) {
    loading.value = "loadmore";
  }
};
// 删除帖子
const delMoment = (id) => {
  commentArr.value = commentArr.value.filter((item) => item.id != id);
  getPinnedArr();
};
// 编辑帖子
const editMoment = (comment) => {
  uni.setStorageSync("_commentIdup", comment.id);
  const params = `?type=${comment.type}&classTitle=${classInfo.value.title}&classId=${classInfo.value.id}`;
  uni.navigateTo({ url: `/subPages/classes/post${params}` });
};
// 置顶帖子切换 getPinnedArr
const pinnedAction = async ({ commentId }) => {
  const findCom = commentArr.value.find((item) => item.id == commentId);
  // 找到列表中的帖子
  if (findCom) {
    const res = await getInfo({ id: commentId });
    const _comment = res.data;
    const _arr = [];
    commentArr.value.forEach((item) => {
      _arr.push(item.id == commentId ? _comment : item);
    });
    commentArr.value = [..._arr];
    return;
  }
  getPinnedArr();
};

// 初始化页面数据
const initPage = () => {
  getClassInfo();
  getPinnedArr();
  tabChang(curTab.value);
};
onShow(() => {
  checks();
  if (curTab.value == "班级圈") {
    getPinnedArr();
    isOne.value && pageBackInit();
  }
  // #ifdef MP-WEIXIN
  wx.hideShareMenu({
    menus: ["shareAppMessage", "shareTimeline"],
  });
  // #endif
});

onLoad((options) => {
  classId.value = options.id;
  schoolId.value = options.schoolId;
  curTab.value = options.type;
  initPage();
});
</script>
<style lang="scss" scoped>
.labwarp {
  padding-left: 8upx;
}
.plusBtn {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  background: url("https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/course_addClass.png")
    no-repeat 100%;
  background-size: 140% 140%;
  background-position: -13rpx -7rpx;
  position: fixed;
  z-index: 99;
  right: 32rpx;

  /* #ifdef H5 */
  bottom: 15%;
  /* #endif */

  /* #ifdef MP-WEIXIN */
  bottom: 4%;
  /* #endif */
}

.cont-teach {
  padding: 0 30upx;
  &-noData {
    margin-top: 200rpx;
    font-size: 28rpx;
    color: #ccc;
    text-align: center;
  }
}

.nav {
  @include selflex(x, between, center);
  font-size: 34upx;
  color: rgba(51, 51, 51, 1);
  padding: 0 30upx;
  margin-top: 30upx;
}

.line {
  color: rgba(204, 204, 204, 1);
}

.title {
  @include selflex(x, between, start);
  @include selfshaow;
  margin: 28upx auto 0 auto;
  width: 690upx;
  box-sizing: border-box;
  padding: 28upx;
  background: #fff;
  border-radius: 28upx;
  font-size: 24upx;
  line-height: 28upx;
  color: rgba(128, 128, 128, 1);

  .imglab {
    flex-shrink: 0;
  }

  .info {
    margin-left: 24upx;
    flex: 1;

    .cls {
      margin-bottom: 16upx;

      .cls-name {
        font-size: 30upx;
        font-weight: 600;
        letter-spacing: 0upx;
        line-height: 36upx;
        color: rgba(51, 51, 51, 1);
      }
    }

    .bld {
      font-weight: 600;
      color: rgba(51, 51, 51, 1);
    }
  }

  .img {
    width: 30upx;
    height: 30upx;
  }
}

.tab {
  @include selflex(x, around, center);
  margin: 32upx 0;

  &-item {
    font-size: 30upx;
    font-weight: 400;
    letter-spacing: 0upx;
    line-height: 46upx;
    border-radius: 52upx;
    color: rgba(82, 82, 82, 1);
    background: rgba(63, 121, 255, 0.01);
    padding: 8upx 20upx;

    &.act {
      background: rgba(63, 121, 255, 1);
      color: #fff;
      font-weight: 600;
    }
  }
}

.class-wrap {
  // padding: 0 30upx;

  .pro {
    @include selfshaow;
    margin: 20upx 30upx 0 30upx;
    background-color: #fff;
    border-radius: 28upx;
    padding: 0 28upx;
  }

  ::v-deep .u-icon__icon {
    font-size: 44upx;
    line-height: 100upx;
    margin-left: 50%;
    transform: translateX(-50%);
  }
}

// }
</style>
