// 发文章帖子
import { defineStore } from 'pinia'
let initComment = {
  id: '',
  title: '',
  content: '',
  type: '', // 类型，image图片，video视频，article文章
  classId: '', //班级ID
  // 附件
  attachments: [
    // { url: 'https://s.mypacelab.com/202503/111.mp4', type: 'video' }
  ],
  modules: [
    // {
    //   title: '', //模块标题
    //   content: '', //模块内容
    //   type: '', //类型，image图片，video视频，article文章
    //   attachments: [], //附件列表
    //   orderIndex: '',
    // },
  ],
}

export const usePostComment = defineStore('postComment', {
  state: () => ({
    storeComment: initComment
  }),
  // Getters
  getters: {
    getComment: (state) => state.storeComment,
  },
  // Actions
  actions: {
    setStoreComment(comment) {
      this.storeComment = JSON.parse(JSON.stringify(comment))
    },
    // 初始化帖子
    initData() {
      this.storeComment = {
        id: '',
        title: '',
        content: '',
        type: '',
        classId: '',
        attachments: [], // 附件
        modules: [], // 段落
      }
    }
  }
})