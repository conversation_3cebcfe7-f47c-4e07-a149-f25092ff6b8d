import { toRaw } from 'vue';
import { useProviderContext } from "./useProvide.js";

const useComment = () => {
  // 帖子内容
  const comment = useProviderContext()

  // 重新设置帖子内容数据
  const setInitData = (_comment) => {
    if (!_comment || typeof finalComment !== 'object'){
      throw new Error('传入参数应为对象类型，请检查传入参数是否正确。');
    }
    for (const key in _comment) {
      if (key === 'modules') {
        comment[key] = Array.isArray(finalComment.modules) ? [...finalComment.modules] : []
        continue;
      }
      if (key === 'attachments') {
        comment[key] = finalComment.attachments || [];
        continue;
      }
      comment[key] = _comment[key];
    }
  };

  // 获取帖子附件列表
  const getCommentFileList = () => {
    const attachments = (comment?.attachments && Array.isArray(comment.attachments)) ? comment.attachments: [];
    const fileList = attachments.map(item => ({
      type: item.type, // 类型,image图片，video视频
      url: item.url, // 附件地址
      status: item.staus || 'success',
      description: item.description || '', // 附件描述
    }))
    return fileList
  }

  // 获取帖子内容
  const getComment = () => {
    return JSON.parse(JSON.stringify(toRaw(comment)))
  }
  // 添加段落
  const addParagraph = (type) => {
    const orderIndex = comment.modules.length
    comment.modules.push({
      title: '',
      content: '',
      type,
      attachments: [],
      orderIndex,
    })
    return orderIndex
  }
  // 段落中的元素上下移动
  const moveParagraph = (type, idx) => {
    // 当前要操作的元素是第一个或者是最后一个不操作
    if ((type == 1 && idx == 0) || (type == -1 && idx == comment.modules.length - 1)) {
      uni.$u.toast('当前段落不可进行次操作')
      return
    }
    const modArr = toRaw(comment.modules);
    [modArr[idx - type], modArr[idx]] = [modArr[idx], modArr[idx - type]]; // 解构赋值交换元素
    comment.modules = modArr.map((item, idx) => ({
      ...item,
      orderIndex: idx + 1
    }));
  }
  // 删除当前段落
  const delParagraph = (idx) => {
    const modArr = toRaw(comment.modules);
    comment.modules = modArr
      .filter((_item, index) => index != idx)
      .map((item, idx) => ({ ...item, orderIndex: idx + 1 }));
  }
  return {
    comment,
    setInitData,
    getComment,
    addParagraph,
    moveParagraph,
    delParagraph,
    getCommentFileList,
  };
};
export default useComment