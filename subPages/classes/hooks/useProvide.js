
import { provide, inject, reactive, isReactive } from 'vue';

const key = new Date().getTime();
/**
 * 创建一个提供程序上下文
 *
 * @param provideData 提供程序的上下文对象
 * @returns 返回包含响应式状态的对象
 */
export function createProvider(provideData = {}) {
  const state = isReactive(provideData) ? provideData : reactive(provideData);
  provide(key, provideData)
  return {
    state
  }
}

/**
 * 获取提供程序上下文
 *
 * @returns 返回由提供程序提供的上下文对象或默认值
 */
export function useProviderContext() {
  return inject(key)
}

