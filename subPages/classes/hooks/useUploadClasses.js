import {
  ref,
  nextTick
} from 'vue';
import {
  fetchClient,
  handleUpload
} from '@/common/utils/fileUpload'
import {
  v1 as uuidv1
} from 'uuid'

import {
  onShow
} from '@dcloudio/uni-app'

const useUploadClasses = (_fileList = []) => {
  const fileList = ref(_fileList);
  const client = ref(null);
  // 上传图片api
  const selfUpload = async file => {
    try {
      // #ifdef MP-WEIXIN
      const _params = {
        ...file,
        path: file.url
      }
      // #endif

      // #ifdef H5
      const _params = {
        ...file.file,
        path: file.file.path || file.file.tempFilePath
      }
      // #endif

      const res = await handleUpload(client.value, _params)
      if (res.uri) {
        fileList.value = fileList.value.map(item => {
          return {
            ...item,
            status: item.uid === file.uid ? 'success' : item.status,
            message: item.uid === file.uid ? '' : item.message,
            url: item.uid === file.uid ? res.uri : item.url,
          }
        })
      }
    } catch (e) {
      console.log(e)
      fileList.value = fileList.value.map(item => {
        return {
          ...item,
          status: 'failed',
          message: '上传失败'
        }
      })
    }
  }
  // 添加完图片
  const afterRead = file => {
    let choseFileArr = [].concat(file.file).map(item => ({
      ...item,
      uid: uuidv1()
    }))
    fileList.value = [...fileList.value, ...choseFileArr].map(item => ({
      ...item,
      status: item.status ? item.status : 'uploading',
      message: item.message ? item.message : '上传中',
    }))
    // 上传图片
    choseFileArr.forEach(item => selfUpload(item))
  }
  // 删除图片
  const deletePic = e => {
    fileList.value = fileList.value.filter((_, idx) => e.index != idx)
  }
  // 设置fileList值
  const setFileList = (arr = []) => {
    fileList.value = [...arr]
  }
  onShow(() => {
    fetchClient(_client => (client.value = _client))
  })

  return {
    fileList,
    afterRead,
    deletePic,
    setFileList
  };
};

export default useUploadClasses