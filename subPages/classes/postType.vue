<template>
  <view>
    <BaseLayout
      :nav-title="pageTit"
      :content-style="{ padding: '0 30upx' }"
      :autoBack="false"
      navClass="layoutTit"
      @leftClick.stop="goback"
    >
      <view class="_layout">
        <view class="sub-tit">
          <text>可选择活动，系统自动填充文案与图片</text>
        </view>
        <view class="sub-cont">
          <up-cell-group :border="false">
            <up-cell
              :isLink="true"
              title="选择活动"
              :titleStyle="{ 'font-weight': 600 }"
              @click="selfClick"
            />
          </up-cell-group>
          <PopPhoto
            v-show="mode == 'image'"
            mode="art"
            ref="popPhotoRef"
            :comment="sectionComment"
            @togAiLoading="togAiLoading"
          />
          <PopVideo
            v-show="mode == 'video'"
            mode="art"
            :comment="sectionComment"
            ref="popVideoRef"
            @togAiLoading="togAiLoading"
            @upfileStatus="upfileStatusChange"
          />
        </view>
      </view>
      <template #footer>
        <view class="sur-btn" @tap.stop="sure">
          <text 
            class="btn-txt" 
            :class="{ 'btn-txt-disabled': !canNext }" 
          >
          确定
        </text>
        </view>
      </template>
    </BaseLayout>


    <!-- 离开前确认弹窗 -->
    <up-modal :show="isShowModal" showCancelButton @cancel="modalCancle" @confirm="surePost">
      <view class="modlCont">将此次编辑保留？</view>
    </up-modal>
  </view>


  <view v-if="backisShow" class="hiden">
    <page-container :show="backisShow" :overlay="false" @beforeleave="onBeforeLeave" />
  </view>
</template>

<script setup>
  import { reactive, ref, computed, toRaw, nextTick } from "vue";
  import { onShow, onLoad, onHide, onUnload } from "@dcloudio/uni-app";
  import { checks } from "@/utils/index.js";
  import PopPhoto from "./components/PopPhoto.vue";
  import PopVideo from "./components/PopVideo.vue";
  import BaseLayout from "@/components/base-layout/base-layout.vue";
  import { usePostComment } from "./stores/postComment.js";
  import { generatePostContentByAi } from "@/api/classApi.js";
  import { checkMediaType } from "@/utils/index.js";
  import { createProvider } from "./hooks/useProvide.js";

  const psotStore = usePostComment();
  const mode = ref(""); // 当前的模式
  const pageTit = ref(""); // 页面标题
  const orderIndex = ref(""); // 当前编辑的段落
  const popPhotoRef = ref(null);
  const popVideoRef = ref(null);
  const classId = ref("");
  const aiLoading = ref(false);
  const editType = ref("");
  const upfileStatus = ref(true); // 视频上传文件成功标识(只有视频)
  const hasSave = ref(false); // 已经存标识
  const backisShow = ref(true); // 返回弹窗二次确认标识
  const isShowModal = ref(false);
  const clickTheam = ref(false);

  const sectionComment = reactive({
    id: "",
    title: "",
    content: "",
    type: "", // 类型，image图片，video视频，article文章
    classId: "", //班级ID
    attachments: [],
    modules: [],
  });  // 当前的段落

  const selfClick = ()=>{
    clickTheam.value = true;
    goTheme();
  }

  const onBeforeLeave = async() => {
    isShowModal.value = true; // 二次确认展示
    clickTheam.value = false;
    nextTick(() => {
      //间隔10ms否则太快会有问题
      setTimeout(() => (backisShow.value = false), 50);
    })
  };

  const surePost = () => {
    isShowModal.value = false;
    sure();
  };
  const modalCancle = () => {
    isShowModal.value = false;
    nextTick(() => {
      //太快会有问题，所以延迟50ms执行
      setTimeout(() => uni.navigateBack(), 50)
    })
  };

   // 监听视频上传状态
   const upfileStatusChange = (flag) => {
    upfileStatus.value = flag;
  };

  const canNext = computed(() => {
    if (mode.value == "image" || mode.value == "article" ) {
      return true;
    }else{
      return upfileStatus.value;
    }
  });

  // ai润色
  const togAiLoading = (flag=false) => {
    if(flag){
      uni.showLoading({
        title: "AI生成中, 请勿操作...",
      });
    }
    if(!flag){
      uni.hideLoading();
    }
    aiLoading.value = flag;
  };

  // 获取编辑的段落内容
  const getCurMomment = () => {
    let _comment;
    if (mode.value == "image") {
      _comment = popPhotoRef.value.getComment();
      _comment.type = "image";
    }
    if (mode.value == "video") {
      _comment = popVideoRef.value.getComment();
      _comment.type = "video";
    }
    return _comment;
  };

  // 完成编辑段落
  const sure = async() => {
    if (!canNext.value) return;
    hasSave.value = true;
    backisShow.value = false;
    nextTick(() => {
      // 保存当前段落内容到store中，并返回上一页
      const storeComment = toRaw(psotStore.getComment);
      storeComment.modules[orderIndex.value] = getCurMomment();
      psotStore.setStoreComment(storeComment);
      uni.setStorageSync("upCommStore", 1);
      uni.$emit('refreshPostPage');// 发送刷新通知
      setTimeout(() => uni.navigateBack(), 50)
    })
  };

  // 返回取消当前段落编辑
  const goback = () => {
    backisShow.value = false;
    isShowModal.value = true;
  };
 
  // 跳转选择主题页面
  const goTheme = () => {
    if (!clickTheam.value) {
      return;
    }
    uni.removeStorageSync("stopShow");
    uni.navigateTo({
      url: `/subPages/classes/choseTheme?classId=${classId.value}`,
    });
  };

  // 调用ai生成帖子内容
  const generateMomentCont = async (actObj) => {
    try {
      uni.showLoading({
        title: "AI生成中, 请勿操作...",
      });
      const res = await generatePostContentByAi(actObj);
      sectionComment.content = res.data || sectionComment.content;
      await nextTick();
    } catch (e) {
      console.log("ai生成帖子报错");
    } finally {
      // aiLoading.value = false;
      uni.hideLoading();
    }
  };

  onShow(() => {
    checks(); // 页面其他状态判断
    // 如果从选择活动页面跳转过来需要调用ai生成内容
    const actObj = uni.getStorageSync("_actObj");
    if (actObj) {
      aiLoading.value = true;
      uni.removeStorageSync("_actObj");
      sectionComment.title = actObj.name;
      const actattachment = actObj.resources
        .filter((item) => checkMediaType(item.uri) == mode.value)
        .map((item) => ({
          url: item.uri,
          type: "image",
        }));
      sectionComment.attachments = [ ...actattachment ];
      if (sectionComment.type == "image") {
        popPhotoRef.value?.setFileLists();
      }
      if (sectionComment.type == "video") {
        popVideoRef.value?.setFileLists();
      }
      generateMomentCont(actObj);
    }
  });

   // 初始化处理获取当前编辑的段落
  const pageLoad = () => {
    const storeComment = toRaw(psotStore.getComment);
    let findComment = storeComment.modules.find(
      (_, idx) => idx == orderIndex.value
    );

    for (let key in findComment) {
      if (findComment.hasOwnProperty(key)) {
        sectionComment[key] = findComment[key];
        if (key == "classId") {
          sectionComment[key] = findComment[key] || classId.value;
        }
      }
    }
  };

  onLoad(async(options) => {
    // 设置页面标题和类型
    mode.value = options.type;
    pageTit.value = options.type == "image" ? "添加图片至文章" : "添加视频至文章";
    orderIndex.value = Number(options.orderIndex); // 当前编辑的段落索引
    classId.value = Number(options.classId);
    editType.value = options.editype; // 编辑类型
    hasSave.value = false;
    pageLoad();
    await nextTick();
    if (sectionComment.type == "image") {
      popPhotoRef.value?.setFileLists();
    }
    if (sectionComment.type == "video") { 
      popVideoRef.value?.setFileLists();
    }
  });
  createProvider(sectionComment); // 创建帖子发布者

  onUnload(() => {
    // 没有点保存就直接删除当前段落内容
    if (editType.value == "add" && !hasSave.value) {
      const storeComment = toRaw(psotStore.getComment);
      storeComment.modules = storeComment.modules.filter((_, idx) => idx !== orderIndex.value);
      psotStore.setStoreComment(storeComment);
      uni.setStorageSync("upCommStore", 1);
      uni.$emit('refreshPostPage');// 发送刷新通知
    }
  });
</script>
<style lang="scss">
  .layoutTit {
    .u-navbar__content__title {
      font-weight: 600;
    }
  }
</style>
<style lang="scss" scoped>
  ._layout {
    height: 100%;
    box-sizing: border-box;

    .title {
      @include selflex(x, between, center);
      font-size: 34upx;
      color: rgba(51, 51, 51, 1);
      padding: 0;
      margin-top: 30upx;
    }

    .sub-tit {
      margin: 30upx 0 12upx;
      font-size: 24upx;
      font-weight: 400;
      letter-spacing: 0upx;
      color: rgba(128, 128, 128, 1);
    }

    .sub-cont {
      opacity: 1;
      border-radius: 28upx;
      padding: 0 28upx 28upx 28upx;
      @include selfshaow;
      min-height: calc(100% - 120upx);

      ::v-deep .u-input {
        padding-left: 0 !important;
        padding-right: 0 !important;
        font-weight: 600;
      }
      ::v-deep .u-textarea {
        padding: 0 !important;
      }

      ::v-deep .u-cell__body {
        padding: {
          left: 0;
          right: 0;
        }
      }
    }
  }
  .sur-btn {
    box-sizing: border-box;
    width: 100vw;
    background-color: #ffffff;
    margin: 0 -30upx;
    padding: 30upx 30upx 50upx 30upx;
    @include selflex(x, between, center);
    z-index: 2;

    .btn-txt {
      width: 100%;
      height: 80upx;
      line-height: 80upx;
      border-radius: 44px;
      background: rgba(54, 124, 255, 1);
      font-size: 30upx;
      font-weight: 600;
      letter-spacing: 0upx;
      color: rgba(255, 255, 255, 1);
      text-align: center;
      &.btn-txt-disabled{
        filter: grayscale(100%);
      }
    }
  }
  .hiden{
    display: none;
    position: relative;
    z-index: -9999;
    height: 0;
    width: 0;
  }
</style>