import { request, generateUrl, TOKEN_NAME } from '@/common/request.js'

// 上传录音文件
export function uploadAudioFile(file, type = 351) {
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: '/file/upload',
      filePath: file,
      name: 'file',
      formData: {
        fileCategory: type // 351为一对一倾听录音文件类型
      },
      success: (res) => {
        try {
          const data = JSON.parse(res.data)
          resolve(data)
        } catch (e) {
          reject(e)
        }
      },
      fail: (err) => {
        reject(err)
      }
    })
  })
}

// 师幼互动评价 相关接口
// 分页列表 /business/interactionEvaluation/pageList
export function getInteractionEvaluationList(data) {
  return request({
    url: `/jsapi/business/interactionEvaluation/pageList`,
    method: 'POST',
    data
  })
}


// 开始录音 /business/interactionEvaluation/beginAudio
export function beginAudio(data) {
  return request({
    url: `/jsapi/business/interactionEvaluation/beginAudio`,
    method: 'POST',
    data
  })
}

// 结束语音录制 /business/interactionEvaluation/endAudio
export function endAudio(data) {
  return request({
    url: `/jsapi/business/interactionEvaluation/endAudio`,
    method: 'POST',
    data
  })
}

// 上传录音转译文本 - 新版本支持ASR列表批量上传
// 参数: { businessId: number, asrList: Array<{asrContent, index, startTime, endTime}> }
export function uploadAudioAsr(data) {
  return request({
    url: `/jsapi/business/interactionEvaluation/uploadAudioAsr`,
    method: 'POST',
    data
  })
}

// // 生成AI分析报告  /business/interactionEvaluation/generateInteractionEvaluation
// export function generateInteractionEvaluation(data) {
//   return request({
//     url: `/jsapi/business/interactionEvaluation/generateInteractionEvaluation`,
//     method: 'POST',
//     data
//   })
// }

// 师幼互动评价详情  /business/interactionEvaluation/detailInfo
export function getInteractionEvaluationDetail(id) {
  return request({
    url: `/jsapi/business/interactionEvaluation/detailInfo?interactionId=${id}`,
    method: 'GET'
  })
}

// 上传录音文件 /business/interactionEvaluation/uploadAudioVideo
export const uploadAudioVideo = async (tempFilePath, interactionId, index, duration) => {
  return new Promise((resolve, reject) => {
    // 使用generateUrl生成完整URL
    const fullUrl = generateUrl('/jsapi/business/interactionEvaluation/uploadAudioVideo')

    console.log('开始上传录音文件:', {
      url: fullUrl,
      filePath: tempFilePath,
      interactionId,
      index,
      duration,
      fileExists: !!tempFilePath
    })

    uni.uploadFile({
      url: fullUrl,
      filePath: tempFilePath,
      name: 'file', // 文件字段名，对应API文档中的file参数
      header: {
        Authorization: uni.getStorageSync(TOKEN_NAME) || ''
        // 不要手动设置Content-Type，uni.uploadFile会自动设置为multipart/form-data
      },
      formData: {
        interactionId: String(interactionId), // 确保是字符串
        index: String(index), // 确保是字符串
        duration: String(duration) // 确保是字符串
      },
      success: (res) => {
        try {
          console.log('上传成功原始响应：', res)

          if (res.statusCode === 200) {
            const data = JSON.parse(res.data)
            console.log('上传成功解析后数据：', data)
            resolve(data)
          } else {
            console.error('上传失败，状态码：', res.statusCode)
            reject(new Error(`上传失败，状态码：${res.statusCode}`))
          }
        } catch (e) {
          console.error('解析响应数据失败：', e)
          reject(e)
        }
      },
      fail: (error) => {
        console.error('上传失败：', error)
        reject(error)
      }
    })
  })
}

// 查询AI分析报告  /business/interactionEvaluation/queryAnalysis
export function queryAnalysis(id) {
  return request({
    url: `/jsapi/business/interactionEvaluation/queryAnalysis?id=${id}`,
    method: 'GET'
  })
}
// global/sysContent/getInfo/{id}
export function getSysContent(id) {
  return request({
    url: `/jsapi/global/sysContent/getInfo/${id}`,
    method: 'GET'
  })
}

// 获取字典 /business/material/getDictByCode
export function getDictByCode(data) {
  return request({
    url: `/jsapi/business/material/getDictByCode`,
    method: 'GET',
    data
  })
}

// 导出报告 /business/interactionEvaluation/exportWord?interactionId=
export function exportWord(id) {
  return request({
    url: `/jsapi/business/interactionEvaluation/exportWord?interactionId=${id}`,
    method: 'GET'
  })
}

// /business/interactionEvaluation/delete/{interactionId} POST
export function deleteInteractionEvaluation(id) {
  return request({
    url: `/jsapi/business/interactionEvaluation/delete/${id}`,
    method: 'POST'
  })
}
