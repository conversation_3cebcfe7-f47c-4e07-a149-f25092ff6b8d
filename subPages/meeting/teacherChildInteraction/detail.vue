<template>
  <view class="detail-page">
    <up-navbar
      title="师幼互动评价详情"
      :safeAreaInsetTop="true"
      :fixed="true"
      :placeholder="true"
      bgColor="transparent"
      :autoBack="true"
    />
    <view class="page-content">
      <view class="observation-detail">
        <!-- 班级信息卡片 -->
        <view class="form-item-card">
          <view class="form-label">班级信息</view>
          <view class="form-content">{{ detailData.className || '未知班级' }}</view>
        </view>

        <!-- 活动名称卡片 -->
        <view class="form-item-card">
          <view class="form-label">活动名称</view>
          <view class="form-content">{{ detailData.subjectActivityName || '无标题' }}</view>
        </view>

        <!-- 活动类型卡片 -->
        <view class="form-item-card">
          <view class="form-label">活动类型</view>
          <view class="form-content">{{ getActivityTypeName(detailData.activityType) }}</view>
        </view>

        <!-- 授课老师卡片 -->
        <view class="form-item-card">
          <view class="form-label">授课老师</view>
          <view class="form-content">{{ getPartnerNames(detailData.partnerVoList) }}</view>
        </view>

        <!-- 录音文件卡片 -->
        <view class="form-item-card" v-if="detailData.audioVideoUrl">
          <view class="form-label">
            <text>录音文件</text>
            <view class="view-text-btn" @click="viewAudioText">
              <text>查看转文字</text>
              <up-icon name="arrow-right" size="20" color="#3f79ff"></up-icon>
            </view>
          </view>
          <view class="form-content">
            <!-- 音频播放器 -->
            <view class="audio-player">
              <view class="progress-container">
                <view class="progress-bar" @click="onProgressBarClick" @touchend="onProgressBarTouch">
                  <view class="progress-fill" :style="{ width: progressPercent + '%' }"></view>
                  <view class="progress-thumb" :style="{ left: progressPercent + '%' }"></view>
                </view>
              </view>

              <view class="player-controls">
                <view class="time-display">{{ formatDuration(currentTime) }}</view>

                <!-- 后退30秒按钮 -->
                <view class="control-btn" @click="seekBackward">
                  <image src="/subPages/meeting/teacherChildInteraction/static/kt.svg" class="control-icon"></image>
                </view>

                <!-- 播放/暂停按钮 -->
                <view class="play-control" @click="togglePlayAudio">
                  <view class="play-btn" :class="{ playing: isPlaying, loading: isLoading }">
                    <view v-if="isLoading" class="loading-spinner"></view>
                    <up-icon v-else-if="!isPlaying" name="play-right" color="#ffffff" size="23"></up-icon>
                    <up-icon v-else name="pause" color="#ffffff" size="23"></up-icon>
                  </view>
                </view>

                <!-- 快进30秒按钮 -->
                <view class="control-btn" @click="seekForward">
                  <image src="/subPages/meeting/teacherChildInteraction/static/kj.svg" class="control-icon"></image>
                </view>

                <view class="time-display">{{ formatDuration(totalDuration) }}</view>
              </view>
            </view>

            <!-- 转文字内容 -->
            <view class="audio-text" v-if="showAudioText && (audioText || detailData.asrContent)">
              {{ audioText || detailData.asrContent }}
            </view>
          </view>
        </view>

        <!-- AI录音分析报告卡片 -->
        <view class="form-label">
          <text>AI录音分析报告</text>
          <view class="export-btn" @click="exportReport"> 导出报告 </view>
        </view>
        <view class="form-item-card" v-if="aiAnalysisReport">
          <view class="form-content">
            <view class="ai-report-content">
              <view class="activity-overview">
                <view class="overview-title">活动概述</view>
                <view class="activity-sections" :class="{ collapsed: isReportCollapsed }">
                  <view class="activity-section">
                    <view class="section-content">{{ aiAnalysisReport.activityOverview }}</view>
                  </view>
                </view>
                <view class="collapse-btn" @click="toggleReportCollapse">
                  <text>{{ isReportCollapsed ? '展开' : '收起' }}</text>
                  <up-icon
                    :name="isReportCollapsed ? 'arrow-down' : 'arrow-up'"
                    size="20"
                    color="#666"
                  ></up-icon>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view
          class="form-item-card"
          v-if="aiAnalysisReport && aiAnalysisReport.excitingInteractiveClip"
        >
          <view class="form-content">
            <view class="ai-report-content">
              <view class="activity-overview">
                <view class="overview-title">精彩互动片段</view>
                <view class="activity-sections" :class="{ collapsed: isAnalysisReportCollapsed }">
                  <view class="activity-section">
                    <view class="section-content" v-html="formatImprovementSuggestion(aiAnalysisReport.excitingInteractiveClip)"></view>
                  </view>
                </view>
                <view class="collapse-btn" @click="toggleAnalysisReportCollapse">
                  <text>{{ isAnalysisReportCollapsed ? '展开' : '收起' }}</text>
                  <up-icon
                    :name="isAnalysisReportCollapsed ? 'arrow-down' : 'arrow-up'"
                    size="20"
                    color="#666"
                  ></up-icon>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 活动关键词云图卡片 -->
        <view class="form-item-card" v-if="aiAnalysisReport && aiAnalysisReport.wordFrequencyCount">
          <view class="form-label">活动关键词云图</view>
          <view class="form-content">
            <view class="word-cloud-content">
              <view v-if="wordCloudLoading" class="word-cloud-loading">
                <view class="loading-text">词云图生成中...</view>
              </view>
              <view v-else-if="wordCloudData.series && wordCloudData.series.length > 0" class="charts-box">
                <qiun-data-charts type="word" :opts="wordCloudOpts" :chartData="wordCloudData" />
              </view>
              <view v-else class="word-cloud-empty">
                <view class="empty-text">暂无词云数据</view>
              </view>
            </view>
          </view>
        </view>

        <!-- 词频统计卡片 -->
        <view class="form-item-card" v-if="aiAnalysisReport && aiAnalysisReport.wordFrequencyCount">
          <view class="form-label">词频统计</view>
          <view class="form-content">
            <view class="word-frequency-content">
              <view class="frequency-table" :class="{ collapsed: isWordFrequencyCollapsed }">
                <view class="table-header">
                  <view class="header-item">单词</view>
                  <view class="header-item">词频</view>
                  <view class="header-item">单词</view>
                  <view class="header-item">词频</view>
                </view>
                <view class="table-body">
                  <view class="table-row" v-for="(row, index) in getWordFrequencyRows()" :key="index">
                    <view class="table-cell">{{ row.left.name }}</view>
                    <view class="table-cell">{{ row.left.textSize }}</view>
                    <view class="table-cell">{{ row.right?.name || '' }}</view>
                    <view class="table-cell">{{ row.right?.textSize || '' }}</view>
                  </view>
                </view>
              </view>
              <view class="collapse-btn" @click="toggleWordFrequencyCollapse">
                <text>{{ isWordFrequencyCollapsed ? '展开' : '收起' }}</text>
                <up-icon
                  :name="isWordFrequencyCollapsed ? 'arrow-down' : 'arrow-up'"
                  size="20"
                  color="#666"
                ></up-icon>
              </view>
            </view>
          </view>
        </view>

        <!-- 提问统计卡片 -->
        <view class="form-item-card" v-if="aiAnalysisReport && aiAnalysisReport.questionStatistics">
          <view class="form-label">提问统计</view>
          <view class="form-content">
            <view class="question-statistics-content">
              <view class="statistics-sections" :class="{ collapsed: isQuestionStatisticsCollapsed }">
                <view class="statistics-section">
                  <view class="section-content" v-html="formatImprovementSuggestion(aiAnalysisReport.questionStatistics)"></view>
                </view>
              </view>
              <view class="collapse-btn" @click="toggleQuestionStatisticsCollapse">
                <text>{{ isQuestionStatisticsCollapsed ? '展开' : '收起' }}</text>
                <up-icon
                  :name="isQuestionStatisticsCollapsed ? 'arrow-down' : 'arrow-up'"
                  size="20"
                  color="#666"
                ></up-icon>
              </view>
            </view>
          </view>
        </view>

        <!-- 改进建议卡片 -->
        <view
          class="form-item-card"
          v-if="aiAnalysisReport && aiAnalysisReport.improvementSuggestion"
        >
          <view class="form-label">改进建议</view>
          <view class="form-content">
            <view class="improvement-suggestions-content">
              <view
                class="suggestions-list"
                :class="{ collapsed: isImprovementSuggestionsCollapsed }"
              >
                <view class="suggestion-item">
                  <view class="suggestion-content" v-html="formatImprovementSuggestion(aiAnalysisReport.improvementSuggestion)"></view>
                </view>
              </view>
              <view class="collapse-btn" @click="toggleImprovementSuggestionsCollapse">
                <text>{{ isImprovementSuggestionsCollapsed ? '展开' : '收起' }}</text>
                <up-icon
                  :name="isImprovementSuggestionsCollapsed ? 'arrow-down' : 'arrow-up'"
                  size="20"
                  color="#666"
                ></up-icon>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { onLoad } from '@dcloudio/uni-app'
import { ref, onBeforeUnmount } from 'vue'
import { useQueryParams } from '@/common/hooks/useQueryParams.js'
import { getInteractionEvaluationDetail, queryAnalysis, getSysContent } from './api'
import QiunDataCharts from './qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue'
// URL query
const { params } = useQueryParams('id')
const detailData = ref({})
// AI 报告
const aiAnalysisReport = ref({})
// 转文字
const audioText = ref('')
const innerAudioContext = ref(null)
const isPlaying = ref(false)
const showQRCode = ref(false)
const qrCodeUrl = ref('')
const detailId = ref('')
const showAudioText = ref(false)
const currentTime = ref(0)
const totalDuration = ref(0)
const progressPercent = ref(0)
const isLoading = ref(false)
const lastPlayPosition = ref(0) // 记录上次播放位置
const progressTimer = ref(null) // 进度更新定时器
const isReportCollapsed = ref(true)
const isWordFrequencyCollapsed = ref(true)
const isAnalysisReportCollapsed = ref(true)
const isQuestionStatisticsCollapsed = ref(true)
const isImprovementSuggestionsCollapsed = ref(true)
// 词云图数据
const wordCloudData = ref({})
const wordCloudLoading = ref(false)
const wordCloudOpts = ref({
  color: [
    '#1890FF',
    '#91CB74',
    '#FAC858',
    '#EE6666',
    '#73C0DE',
    '#3CA272',
    '#FC8452',
    '#9A60B4',
    '#ea7ccc'
  ],
  padding: undefined,
  enableScroll: false,
  extra: {
    word: {
      type: 'normal',
      autoColors: false
    }
  }
})
// 加载数据
onLoad((options) => {
  if (options.id) {
    detailId.value = options.id
    fetchDetailData(options.id)
  } else {
    uni.showToast({
      title: '缺少必要参数',
      icon: 'none'
    })
  }
})

// 获取详情数据
const fetchDetailData = async (id) => {
  // 基本详情数据
  const res = await getInteractionEvaluationDetail(id)
  if (res.status === 0 && res.data) {
    detailData.value = res.data

    // 设置音频总时长
    if (res.data.duration) {
      totalDuration.value = res.data.duration
      console.log('设置音频总时长:', res.data.duration)
    }

    // 如果有音频文件，初始化播放器
    if (res.data.audioVideoUrl) {
      // 延迟初始化，确保DOM已渲染
      setTimeout(() => {
        initAudioPlayer()
      }, 100)
    }
  }
  // AI报告数据
  const res2 = await queryAnalysis(id)
  if (res2.status === 0 && res2.data) {
    aiAnalysisReport.value = res2.data
    // 处理词云图数据
    await generateWordCloudData()
  }
}

// 格式化时长
const formatDuration = (seconds) => {
  if (!seconds || seconds === 0) return '00:00'
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 初始化音频播放器
const initAudioPlayer = () => {
  if (innerAudioContext.value) {
    innerAudioContext.value.destroy()
    innerAudioContext.value = null
  }

  if (!detailData.value.audioVideoUrl) {
    return false
  }

  // 创建新的音频上下文
  innerAudioContext.value = uni.createInnerAudioContext()
  // 拼接完整的音频地址
  const audioUrl = `https://s.mypacelab.com/${detailData.value.audioVideoUrl}`
  innerAudioContext.value.src = audioUrl

  console.log('初始化音频播放器，地址:', audioUrl)

  // 设置总时长（如果有duration字段）
  if (detailData.value.duration) {
    totalDuration.value = detailData.value.duration
  }

  // 监听音频可以播放事件
  innerAudioContext.value.onCanplay(() => {
    console.log('音频可以播放')
    isLoading.value = false
    // 获取音频总时长
    if (innerAudioContext.value.duration && !totalDuration.value) {
      totalDuration.value = Math.floor(innerAudioContext.value.duration)
    }
  })

  // 监听音频开始播放事件
  innerAudioContext.value.onPlay(() => {
    console.log('开始播放录音')
    isPlaying.value = true
    isLoading.value = false
    // 开始更新进度
    startProgressTimer()
  })

  // 监听音频暂停事件
  innerAudioContext.value.onPause(() => {
    console.log('录音播放暂停')
    isPlaying.value = false
    // 记录暂停位置
    lastPlayPosition.value = currentTime.value
    stopProgressTimer()
  })

  // 监听音频播放结束事件
  innerAudioContext.value.onEnded(() => {
    console.log('录音播放结束')
    isPlaying.value = false
    currentTime.value = 0
    progressPercent.value = 0
    lastPlayPosition.value = 0
    stopProgressTimer()
  })

  // 监听音频停止事件
  innerAudioContext.value.onStop(() => {
    console.log('录音播放停止')
    isPlaying.value = false
    stopProgressTimer()
  })

  // 监听音频播放错误事件
  innerAudioContext.value.onError((res) => {
    console.log('播放录音失败:', res.errMsg)
    isPlaying.value = false
    isLoading.value = false
    stopProgressTimer()
    uni.showToast({
      title: '播放录音失败',
      icon: 'none'
    })
  })

  // 监听音频时间更新事件
  innerAudioContext.value.onTimeUpdate(() => {
    if (innerAudioContext.value) {
      currentTime.value = Math.floor(innerAudioContext.value.currentTime || 0)
      if (totalDuration.value > 0) {
        progressPercent.value = (currentTime.value / totalDuration.value) * 100
      }
    }
  })

  return true
}

// 播放/暂停音频
const togglePlayAudio = () => {
  if (!detailData.value.audioVideoUrl) {
    uni.showToast({
      title: '录音文件不存在',
      icon: 'none'
    })
    return
  }

  // 如果正在播放，则暂停
  if (isPlaying.value && innerAudioContext.value) {
    innerAudioContext.value.pause()
    return
  }

  // 如果没有音频上下文，先初始化
  if (!innerAudioContext.value) {
    if (!initAudioPlayer()) {
      return
    }
  }

  // 显示loading状态
  isLoading.value = true

  // 如果有上次播放位置，跳转到该位置
  if (lastPlayPosition.value > 0) {
    innerAudioContext.value.seek(lastPlayPosition.value)
  }

  // 开始播放
  innerAudioContext.value.play()
}

// 启动进度更新定时器
const startProgressTimer = () => {
  stopProgressTimer()
  progressTimer.value = setInterval(() => {
    if (!innerAudioContext.value || !isPlaying.value) {
      stopProgressTimer()
      return
    }
    // 进度更新在 onTimeUpdate 事件中处理
  }, 100)
}

// 停止进度更新定时器
const stopProgressTimer = () => {
  if (progressTimer.value) {
    clearInterval(progressTimer.value)
    progressTimer.value = null
  }
}

// 快进30秒
const seekForward = () => {
  if (!innerAudioContext.value || !totalDuration.value) return

  const newTime = Math.min(currentTime.value + 30, totalDuration.value)
  innerAudioContext.value.seek(newTime)
  currentTime.value = newTime
  progressPercent.value = (newTime / totalDuration.value) * 100
  lastPlayPosition.value = newTime
}

// 后退30秒
const seekBackward = () => {
  if (!innerAudioContext.value) return

  const newTime = Math.max(currentTime.value - 30, 0)
  innerAudioContext.value.seek(newTime)
  currentTime.value = newTime
  progressPercent.value = totalDuration.value > 0 ? (newTime / totalDuration.value) * 100 : 0
  lastPlayPosition.value = newTime
}

// 点击进度条跳转
const onProgressBarClick = (event) => {
  if (!innerAudioContext.value || !totalDuration.value) return

  console.log('进度条点击事件:', event)

  // 使用uni-app的方式获取元素信息
  const query = uni.createSelectorQuery()
  query.select('.progress-bar').boundingClientRect((rect) => {
    if (!rect) {
      console.log('无法获取进度条位置信息')
      return
    }

    console.log('进度条位置信息:', rect)
    console.log('点击事件详情:', event.detail)

    // 检查是否有有效的点击坐标
    if (!event.detail || typeof event.detail.x !== 'number') {
      console.log('无效的点击坐标信息')
      return
    }

    // 获取点击位置相对于进度条的百分比
    const clickX = event.detail.x - rect.left
    const progressBarWidth = rect.width

    // 确保计算结果有效
    if (progressBarWidth <= 0) {
      console.log('进度条宽度无效')
      return
    }

    const clickPercent = Math.max(0, Math.min(100, (clickX / progressBarWidth) * 100))

    // 计算对应的时间位置
    const newTime = (clickPercent / 100) * totalDuration.value

    // 跳转到指定位置
    innerAudioContext.value.seek(newTime)
    currentTime.value = newTime
    progressPercent.value = clickPercent
    lastPlayPosition.value = newTime

    console.log('进度条点击跳转到:', newTime, '秒, 百分比:', clickPercent)
  }).exec()
}

// 触摸事件处理（备用方案）
const onProgressBarTouch = (event) => {
  if (!innerAudioContext.value || !totalDuration.value) return

  console.log('进度条触摸事件:', event)

  // 如果点击事件已经处理过了，就不重复处理
  if (event.detail && event.detail.x) {
    return
  }

  // 使用触摸坐标作为备用方案
  if (event.changedTouches && event.changedTouches.length > 0) {
    const touch = event.changedTouches[0]

    const query = uni.createSelectorQuery()
    query.select('.progress-bar').boundingClientRect((rect) => {
      if (!rect) return

      const clickX = touch.clientX - rect.left
      const progressBarWidth = rect.width

      if (progressBarWidth <= 0) return

      const clickPercent = Math.max(0, Math.min(100, (clickX / progressBarWidth) * 100))
      const newTime = (clickPercent / 100) * totalDuration.value

      innerAudioContext.value.seek(newTime)
      currentTime.value = newTime
      progressPercent.value = clickPercent
      lastPlayPosition.value = newTime

      console.log('进度条触摸跳转到:', newTime, '秒')
    }).exec()
  }
}

// 切换报告展开/收起状态
const toggleReportCollapse = () => {
  isReportCollapsed.value = !isReportCollapsed.value
}

// 切换词频统计展开/收起状态
const toggleWordFrequencyCollapse = () => {
  isWordFrequencyCollapsed.value = !isWordFrequencyCollapsed.value
}

// 切换AI分析报告展开/收起状态
const toggleAnalysisReportCollapse = () => {
  isAnalysisReportCollapsed.value = !isAnalysisReportCollapsed.value
}

// 切换提问统计展开/收起状态
const toggleQuestionStatisticsCollapse = () => {
  isQuestionStatisticsCollapsed.value = !isQuestionStatisticsCollapsed.value
}

// 切换改进建议展开/收起状态
const toggleImprovementSuggestionsCollapse = () => {
  isImprovementSuggestionsCollapsed.value = !isImprovementSuggestionsCollapsed.value
}

// 获取活动类型名称
const getActivityTypeName = (activityType) => {
  const typeMap = {
    1: '活动类型1',
    2: '活动类型2'
  }
  return typeMap[activityType] || '未知类型'
}

// 获取授课老师名称
const getPartnerNames = (partnerVoList) => {
  if (!partnerVoList || !Array.isArray(partnerVoList) || partnerVoList.length === 0) {
    return '未知老师'
  }
  return partnerVoList.map((partner) => partner.partnerName).join('、')
}

// 格式化文本内容，按编号换行显示并增加间距
const formatImprovementSuggestion = (suggestion) => {
  if (!suggestion) return ''

  let formatted = suggestion.trim()

  // 定义编号模式
  const patterns = [
    /(\d+\.\s*)/g,                           // 数字编号：1. 2. 3.
    /([一二三四五六七八九十]+、\s*)/g,        // 中文编号：一、二、三、
    /(片段[一二三四五六七八九十\d]+：)/g       // 片段编号：片段一：片段二：
  ]

  // 应用所有模式
  patterns.forEach(pattern => {
    formatted = formatted.replace(pattern, '|||SPLIT|||$1')
  })

  // 如果有分割标记，说明需要格式化
  if (formatted.includes('|||SPLIT|||')) {
    // 按分割标记分割文本
    const parts = formatted.split('|||SPLIT|||').filter(part => part.trim())

    // 将每个部分包装在div中
    const formattedParts = parts.map(part => `<div class="text-item">${part.trim()}</div>`)

    return formattedParts.join('')
  }

  // 如果没有匹配的编号，直接返回原文本
  return formatted
}

// 处理词频统计数据为表格行
const getWordFrequencyRows = () => {
  if (!aiAnalysisReport.value || !aiAnalysisReport.value.wordFrequencyCount) return []

  try {
    let wordFrequencyStr = aiAnalysisReport.value.wordFrequencyCount

    // 修复JSON格式：为属性名添加引号
    wordFrequencyStr = wordFrequencyStr.replace(/(\w+):/g, '"$1":')

    const wordFrequency = JSON.parse(wordFrequencyStr)
    if (!Array.isArray(wordFrequency)) return []

    const rows = []
    for (let i = 0; i < wordFrequency.length; i += 2) {
      rows.push({
        left: wordFrequency[i],
        right: wordFrequency[i + 1] || null
      })
    }
    return rows
  } catch (e) {
    console.error('解析词频统计数据失败:', e)
    console.error('原始数据:', aiAnalysisReport.value.wordFrequencyCount)
    return []
  }
}

// 生成词云图数据
const generateWordCloudData = async () => {
  if (!aiAnalysisReport.value || !aiAnalysisReport.value.wordFrequencyCount) {
    wordCloudLoading.value = false
    return
  }

  wordCloudLoading.value = true

  try {
    await new Promise(resolve => setTimeout(resolve, 100))

    let wordFrequencyStr = aiAnalysisReport.value.wordFrequencyCount

    // 修复JSON格式：为属性名添加引号
    wordFrequencyStr = wordFrequencyStr.replace(/(\w+):/g, '"$1":')

    const wordFrequency = JSON.parse(wordFrequencyStr)
    if (!Array.isArray(wordFrequency)) {
      wordCloudLoading.value = false
      return
    }

    // 获取所有频率值，用于计算映射范围
    const frequencies = wordFrequency.map((item) => parseInt(item.textSize) || 0)
    const minFreq = Math.min(...frequencies)
    const maxFreq = Math.max(...frequencies)

    // 词云图textSize范围：12-20
    const minTextSize = 12
    const maxTextSize = 20

    // 转换为词云图需要的格式，将频率映射到textSize范围
    const series = wordFrequency.map((item) => {
      const frequency = parseInt(item.textSize) || 0
      let mappedTextSize = minTextSize

      // 如果频率范围大于0，进行线性映射
      if (maxFreq > minFreq) {
        mappedTextSize =
          minTextSize + ((frequency - minFreq) / (maxFreq - minFreq)) * (maxTextSize - minTextSize)
      } else {
        // 如果所有频率相同，使用中间值
        mappedTextSize = (minTextSize + maxTextSize) / 2
      }

      return {
        name: item.name,
        textSize: Math.round(mappedTextSize),
        data: undefined
      }
    })

    wordCloudData.value = {
      series: series
    }

    await new Promise(resolve => setTimeout(resolve, 200))

  } catch (e) {
    console.error('生成词云图数据失败:', e)
    console.error('原始数据:', aiAnalysisReport.value.wordFrequencyCount)
  } finally {
    wordCloudLoading.value = false
  }
}

// 导出报告
const exportReport = () => {
  console.log('导出报告功能')
  uni.showToast({
    title: '导出报告功能开发中',
    icon: 'none'
  })
}
// 查看转文本
const viewAudioText = async () => {
  showAudioText.value = !showAudioText.value

  // 如果是展开状态且还没有获取转文字内容，则获取
  if (showAudioText.value && !audioText.value) {
    try {
      const res = await getSysContent(detailData.value.asrContent)
      if (res.status === 0 && res.data) {
        audioText.value = res.data.content
      }
    } catch (e) {
      console.error('获取转文字内容失败:', e)
      // 如果API调用失败，直接显示asrContent
      audioText.value = detailData.value.asrContent
    }
  }
}

// 组件卸载前清理资源
onBeforeUnmount(() => {
  // 停止进度定时器
  stopProgressTimer()

  // 销毁音频实例
  if (innerAudioContext.value) {
    if (isPlaying.value) {
      innerAudioContext.value.stop()
    }
    innerAudioContext.value.destroy()
    innerAudioContext.value = null
  }

  // 重置状态
  isPlaying.value = false
  isLoading.value = false
  currentTime.value = 0
  progressPercent.value = 0
  lastPlayPosition.value = 0
})
</script>

<style lang="scss" scoped>
.detail-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.page-content {
  padding-top: 20rpx;
}

.observation-detail {
  padding: 32rpx;
  background-color: #f5f5f5;

  // 表单卡片样式，与updateOneToOneListening页面保持一致
  .form-item-card {
    border-radius: 28rpx;
    padding: 28rpx;
    background: #fff;
    margin-bottom: 24rpx;
    display: flex;
    flex-direction: column;

    .form-content {
      font-size: 28rpx;
      color: #666;
      line-height: 1.6;
      word-break: break-all;

      // 音频播放器样式
      .audio-player {
        margin-top: 24rpx;

        .progress-container {
          margin-bottom: 24rpx;

          .progress-bar {
            width: 100%;
            height: 8rpx;
            background-color: #e5e5e5;
            border-radius: 4rpx;
            overflow: visible;
            position: relative;
            cursor: pointer;

            .progress-fill {
              height: 100%;
              background-color: #3f79ff;
              border-radius: 4rpx;
              transition: width 0.1s ease;
            }

            .progress-thumb {
              position: absolute;
              top: 50%;
              width: 16rpx;
              height: 16rpx;
              background-color: #3f79ff;
              border-radius: 50%;
              transform: translate(-50%, -50%);
              transition: left 0.1s ease;
              box-shadow: 0 2rpx 6rpx rgba(63, 121, 255, 0.3);
            }
          }
        }

        .player-controls {
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 20rpx;

          .time-display {
            font-size: 28rpx;
            color: #333;
            font-weight: 500;
            min-width: 80rpx;
          }

          .control-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 12rpx;
            transition: all 0.3s ease;

            .control-icon {
              width: 40rpx;
              height: 40rpx;
            }

            &:active {
              background-color: #e5e5e5;
              transform: scale(0.95);
            }
          }

          .play-control {
            display: flex;
            justify-content: center;

            .play-btn {
              width: 80rpx;
              height: 80rpx;
              border-radius: 50%;
              background-color: #3f79ff;
              display: flex;
              align-items: center;
              justify-content: center;
              transition: all 0.3s ease;

              &.playing {
                background-color: #3f79ff;
                animation: pulse 2s infinite;
              }

              &.loading {
                background-color: #3f79ff;
                animation: none;
              }

              &:active {
                transform: scale(0.95);
              }

              .loading-spinner {
                width: 20rpx;
                height: 20rpx;
                border: 2rpx solid rgba(255, 255, 255, 0.3);
                border-top: 2rpx solid #ffffff;
                border-radius: 50%;
                animation: spin 1s linear infinite;
              }
            }
          }
        }
      }

      // 音频文本样式
      .audio-text {
        padding: 20rpx;
        background-color: #f8f8f8;
        border-radius: 10rpx;
        font-size: 28rpx;
        color: #333;
        line-height: 1.6;
        margin-top: 16rpx;
      }

      // 分析内容样式
      .analysis-content {
        font-size: 28rpx;
        color: #333;
        line-height: 1.6;
        white-space: pre-wrap;
      }

      // 资源列表样式
      .resources-list {
        display: flex;
        flex-wrap: wrap;
        gap: 20rpx;

        .resource-item {
          width: calc(33.33% - 14rpx);

          .resource-image {
            width: 100%;
            height: 200rpx;
            border-radius: 8rpx;
            object-fit: cover;
          }

          .resource-file {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20rpx;
            background-color: #f8f8f8;
            border-radius: 8rpx;
            height: 200rpx;

            .file-name {
              font-size: 24rpx;
              color: #666;
              margin-top: 10rpx;
              text-align: center;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              line-clamp: 2;
              -webkit-box-orient: vertical;
            }
          }
        }
      }

      // 词频统计样式
      .word-frequency-content {
        .frequency-table {
          .table-header {
            display: flex;
            background-color: #f8f9fa;
            border-radius: 8rpx 8rpx 0 0;
            padding: 16rpx 0;

            .header-item {
              flex: 1;
              text-align: center;
              font-size: 28rpx;
              font-weight: 600;
              color: #333;
            }
          }

          .table-body {
            .table-row {
              display: flex;
              border-bottom: 1rpx solid #e9ecef;
              padding: 16rpx 0;

              &:last-child {
                border-bottom: none;
              }

              .table-cell {
                flex: 1;
                text-align: center;
                font-size: 28rpx;
                color: #666;
                line-height: 1.4;
              }
            }
          }

          &.collapsed {
            max-height: 400rpx;
            overflow: hidden;
            position: relative;

            &::after {
              content: '';
              position: absolute;
              bottom: 0;
              left: 0;
              right: 0;
              height: 80rpx;
              background: linear-gradient(transparent, #fff);
              pointer-events: none;
            }
          }
        }

        .collapse-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8rpx;
          padding: 16rpx;
          margin-top: 16rpx;
          background-color: #f8f9fa;
          border-radius: 12rpx;
          cursor: pointer;
          transition: background-color 0.3s ease;

          text {
            font-size: 26rpx;
            color: #666;
          }

          &:active {
            background-color: #e9ecef;
          }
        }
      }

      // 提问统计样式
      .question-statistics-content {
        .statistics-sections {
          .statistics-section {
            margin-bottom: 32rpx;

            &:last-child {
              margin-bottom: 0;
            }

            .section-content {
              // 格式化文本项样式
              :deep(.text-item) {
                margin-bottom: 16rpx;
                padding-bottom: 8rpx;

                &:last-child {
                  margin-bottom: 0;
                  padding-bottom: 0;
                }
              }
            }

            .section-title {
              font-size: 30rpx;
              font-weight: 600;
              color: #333;
              margin-bottom: 16rpx;
            }

            .statistics-list {
              .statistics-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 12rpx 0;
                border-bottom: 1rpx solid #f0f0f0;

                &:last-child {
                  border-bottom: none;
                }

                .item-label {
                  font-size: 28rpx;
                  color: #666;
                }

                .item-value {
                  font-size: 28rpx;
                  color: #333;
                  font-weight: 500;
                }
              }
            }

            .question-examples {
              .example-category {
                margin-bottom: 24rpx;

                &:last-child {
                  margin-bottom: 0;
                }

                .category-title {
                  font-size: 28rpx;
                  font-weight: 600;
                  color: #333;
                  margin-bottom: 12rpx;
                }

                .example-list {
                  .example-item {
                    font-size: 26rpx;
                    color: #666;
                    line-height: 1.6;
                    margin-bottom: 8rpx;
                    padding-left: 16rpx;
                    position: relative;

                    &:before {
                      content: '•';
                      position: absolute;
                      left: 0;
                      color: #999;
                    }

                    &:last-child {
                      margin-bottom: 0;
                    }
                  }
                }
              }
            }
          }

          &.collapsed {
            max-height: 400rpx;
            overflow: hidden;
            position: relative;

            &::after {
              content: '';
              position: absolute;
              bottom: 0;
              left: 0;
              right: 0;
              height: 80rpx;
              background: linear-gradient(transparent, #fff);
              pointer-events: none;
            }
          }
        }

        .collapse-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8rpx;
          padding: 16rpx;
          margin-top: 16rpx;
          background-color: #f8f9fa;
          border-radius: 12rpx;
          cursor: pointer;
          transition: background-color 0.3s ease;

          text {
            font-size: 26rpx;
            color: #666;
          }

          &:active {
            background-color: #e9ecef;
          }
        }
      }

      // 改进建议样式
      .improvement-suggestions-content {
        .suggestions-list {
          .suggestion-item {
            margin-bottom: 24rpx;

            &:last-child {
              margin-bottom: 0;
            }

            .suggestion-title {
              font-size: 28rpx;
              font-weight: 600;
              color: #333;
              margin-bottom: 12rpx;
            }

            .suggestion-content {
              font-size: 28rpx;
              color: #666;
              line-height: 1.6;
              text-align: justify;
              word-break: break-all;

              // 格式化文本项样式
              :deep(.text-item) {
                margin-bottom: 16rpx;
                padding-bottom: 8rpx;

                &:last-child {
                  margin-bottom: 0;
                  padding-bottom: 0;
                }
              }
            }
          }

          &.collapsed {
            max-height: 400rpx;
            overflow: hidden;
            position: relative;

            &::after {
              content: '';
              position: absolute;
              bottom: 0;
              left: 0;
              right: 0;
              height: 80rpx;
              background: linear-gradient(transparent, #fff);
              pointer-events: none;
            }
          }
        }

        .collapse-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8rpx;
          padding: 16rpx;
          margin-top: 16rpx;
          background-color: #f8f9fa;
          border-radius: 12rpx;
          cursor: pointer;
          transition: background-color 0.3s ease;

          text {
            font-size: 26rpx;
            color: #666;
          }

          &:active {
            background-color: #e9ecef;
          }
        }
      }

      // AI录音分析报告样式
      .ai-report-content {
        .activity-overview {
          .overview-title {
            font-size: 30rpx;
            font-weight: 600;
            color: #333;
            margin-bottom: 20rpx;
          }

          .activity-sections {
            .activity-section {
              margin-bottom: 24rpx;

              .section-title {
                font-size: 28rpx;
                font-weight: 600;
                color: #333;
                margin-bottom: 12rpx;
                line-height: 1.4;
              }

              .section-content {
                font-size: 28rpx;
                color: #666;
                line-height: 1.6;
                text-align: justify;
                word-break: break-all;

                // 格式化文本项样式
                :deep(.text-item) {
                  margin-bottom: 16rpx;
                  padding-bottom: 8rpx;

                  &:last-child {
                    margin-bottom: 0;
                    padding-bottom: 0;
                  }
                }
              }
            }

            &.collapsed {
              max-height: 400rpx;
              overflow: hidden;
              position: relative;

              &::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                height: 80rpx;
                background: linear-gradient(transparent, #fff);
                pointer-events: none;
              }
            }
          }

          .collapse-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8rpx;
            padding: 16rpx;
            margin-top: 16rpx;
            background-color: #f8f9fa;
            border-radius: 12rpx;
            cursor: pointer;
            transition: background-color 0.3s ease;

            text {
              font-size: 26rpx;
              color: #666;
            }

            &:active {
              background-color: #e9ecef;
            }
          }
        }
      }
    }
  }

  .word-frequency-chart {
    width: 100%;
    height: 400rpx;
    object-fit: contain;
    border-radius: 16rpx;
  }

  // 词云图样式
  .word-cloud-content {
    position: relative;

    .word-cloud-loading {
      width: 100%;
      height: 500rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f8f9fa;
      border-radius: 12rpx;
      margin: 20rpx 0;

      .loading-text {
        font-size: 28rpx;
        color: #666;
      }
    }

    .word-cloud-empty {
      width: 100%;
      height: 500rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f8f9fa;
      border-radius: 12rpx;
      margin: 20rpx 0;

      .empty-text {
        font-size: 28rpx;
        color: #999;
      }
    }

    .charts-box {
      width: 100%;
      height: 500rpx;
      margin: 20rpx 0;
      position: relative;
      z-index: auto;
      overflow: visible;
      border-radius: 12rpx;
      background-color: #fff;

      :deep(.chartsview) {
        position: relative;
        z-index: auto;
        width: 100%;
        height: 100%;
      }

      :deep(canvas) {
        position: relative;
        z-index: auto;
      }
    }
  }

  // 动画效果
  @keyframes pulse {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
    100% {
      transform: scale(1);
    }
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
}
.form-label {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  min-width: 160rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .view-text-btn {
    display: flex;
    align-items: center;
    gap: 8rpx;

    text {
      font-size: 28rpx;
      color: #3f79ff;
      font-weight: 400;
    }
  }

  .export-btn {
    font-size: 26rpx;
    color: #3f79ff;
    font-weight: 400;
  }
}
/* QR Code Modal Styles */
.qr-code-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;

  .qr-code-content {
    width: 80%;
    background-color: #fff;
    border-radius: 16rpx;
    padding: 40rpx;
    position: relative;

    .qr-code-close {
      position: absolute;
      top: 20rpx;
      right: 20rpx;
      width: 40rpx;
      height: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .qr-code-title {
      font-size: 28rpx;
      color: #333;
      margin-bottom: 30rpx;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .qr-code-image {
      width: 400rpx;
      height: 400rpx;
      margin: 0 auto;
      display: block;
    }

    .qr-code-tip {
      font-size: 24rpx;
      color: #666;
      text-align: center;
      margin-top: 30rpx;
    }
  }
}
</style>
