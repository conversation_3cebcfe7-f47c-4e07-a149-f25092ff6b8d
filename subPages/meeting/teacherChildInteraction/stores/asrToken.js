import { defineStore } from 'pinia'
import { ref } from 'vue'
import { getByteDanceToken } from '../api/meeting'
import { getUserInfo } from '@/api/login'

const SUPPORTED_SERVICES = ['bytedance']

const validateService = (service) => {
  if (!SUPPORTED_SERVICES.includes(service)) {
    console.warn(`Service ${service} is not supported yet, fallback to bytedance`)
    return 'bytedance'
  }
  return service
}

export const useAsrTokenStore = defineStore('teacherChildInteractionAsrToken', () => {
  const token = ref('')
  const appid = ref('')
  const userId = ref('')
  const cluster = ref('volcengine_streaming_common')

  const initTokenInfo = async (service = 'bytedance') => {
    try {
      service = validateService(service)
      const userInfo = (await getUserInfo()).data
      const tokenInfo = (await getByteDanceToken()).data

      token.value = tokenInfo.token
      appid.value = tokenInfo.appid
      userId.value = userInfo.id.toString()
      // 默认使用火山引擎的集群
      // cluster.value = tokenInfo.cluster || ''
    } catch (error) {
      console.error('Failed to initialize ASR token info:', error)
      throw error
    }
  }

  return {
    token,
    appid,
    uid: userId,
    cluster,
    initTokenInfo
  }
})
