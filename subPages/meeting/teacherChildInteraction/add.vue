<template>
  <base-layout
    nav-title="新增师幼互动评价"
    containerClass="template-container"
    navBgColor="transparent"
    :footerStyle="{
      display: 'none'
    }"
    :contentStyle="{
      padding: '0'
    }"
  >
    <view class="addObservation">
      <view class="addObservation-form">
        <up-form
          ref="formRef"
          labelPosition="top"
          :model="formData"
          :rules="rules"
          labelWidth="80"
          labelStyle="{fontSize: 30rpx; fontWeight: 600;"
          labelAlign="left"
        >
          <up-form-item
            :style="formItemStyle"
            label="记录班级"
            prop="schoolClassId"
            @click="onShowPicker('schoolClassId')"
          >
            <view>
              {{ getOptionLabelByKeyAndId('schoolClassId', formData.schoolClassId) }}
              <text
                v-if="!getOptionLabelByKeyAndId('schoolClassId', formData.schoolClassId)"
                class="placeholder"
                >请输入记录班级</text
              >
            </view>

            <template #right>
              <up-icon name="arrow-down" />
            </template>
          </up-form-item>
          <up-form-item
            :style="formItemStyle"
            label="活动名称"
            prop="subjectActivityName"
            @click="activityInputMode === 'select' ? goSelectActivity() : null"
          >
            <input
              v-if="activityInputMode === 'input'"
              v-model="formData.subjectActivityName"
              placeholder="请输入活动名称"
              class="activity-input"
              @input="onActivityNameInput"
            />
            <view v-else class="activity-select-mode">
              {{ formData.subjectActivityName || '' }}
              <text v-if="!formData.subjectActivityName" class="placeholder">请选择活动名称</text>
            </view>
            <template #right>
              <view class="activity-mode-switch" @click.stop="toggleActivityInputMode">
                <text class="mode-text">{{ activityInputMode === 'input' ? '手填' : '选择' }}</text>
                <up-icon :name="activityInputMode === 'input' ? 'list' : 'arrow-down'" />
              </view>
            </template>
          </up-form-item>
          <up-form-item
            :style="formItemStyle"
            label="活动类型"
            prop="activityType"
            @click="onShowPicker('activityType')"
          >
            <view>
              {{ getOptionLabelByKeyAndId('activityType', formData.activityType) }}
              <text
                v-if="!getOptionLabelByKeyAndId('activityType', formData.activityType)"
                class="placeholder"
                >请选择活动类型</text
              >
            </view>
            <template #right>
              <up-icon name="arrow-down" />
            </template>
          </up-form-item>
          <up-form-item
            :style="formItemStyle"
            label="授课教师"
            prop="teach"
            @click="onShowPicker('teach')"
          >
            <view>
              {{ formData.teach ? getOptionLabelByKeyAndId('teachList', formData.teach) : '' }}
              <text
                v-if="!formData.teach || !getOptionLabelByKeyAndId('teachList', formData.teach)"
                class="placeholder"
                >请选择授课教师</text
              >
            </view>
            <template #right>
              <up-icon name="arrow-down" />
            </template>
          </up-form-item>
        </up-form>
      </view>
    </view>
    <!-- 开始上课按钮 / 录音状态 -->
    <view class="start-class-section">
      <!-- 未开始录音 -->
      <view v-if="audioRecordStatus === 'idle'" class="idle-state">
        <view class="start-button" :class="{ disabled: isStartingClass }" @click="handleStartClick">
          <view class="button-icon">
            <text class="button-text">{{ isStartingClass ? '正在开始...' : recorderText }}</text>
          </view>
        </view>
        <text class="tip-text">点击开始录制师幼互动过程</text>
      </view>

      <!-- 录音中状态  -->
      <view
        class="recording-state"
        v-if="audioRecordStatus === 'recording' || audioRecordStatus === 'paused'"
      >
        <view class="recording-time">{{ formatRecordingTime(totalDisplayTime) }}</view>
        <view class="recording-indicator">
          <view class="pulse-circle" :class="{ paused: audioRecordStatus === 'paused' }">
            <view class="inner-circle" />
          </view>
        </view>

        <!-- 语音转文本显示区域 -->
        <view class="speech-text-content">
          <textarea
            v-model="speechText"
            placeholder="正在识别语音内容..."
            disabled
            auto-height
            class="speech-textarea"
          />
        </view>

        <view class="recording-actions">
          <view
            class="action-button pause"
            :class="{ disabled: isSegmentTransition }"
            @click="
              isSegmentTransition
                ? null
                : audioRecordStatus === 'paused'
                ? continueRecording()
                : pauseRecording()
            "
          >
            {{
              isSegmentTransition ? '准备中...' : audioRecordStatus === 'paused' ? '继续' : '暂停'
            }}
          </view>
          <view class="action-button stop" @click="stopRecording"> 结束录音 </view>
        </view>
        <!-- <text class="recording-tip">最长录制10分钟，将自动结束</text> -->
      </view>
    </view>
    <up-picker
      :show="isPickerShow"
      :defaultIndex="[0]"
      :columns="activeOption"
      @confirm="pickerConfirm"
      @cancel="isPickerShow = false"
      keyName="label"
    ></up-picker>

    <up-calendar
      :show="isCalendarShow"
      @close="isCalendarShow = false"
      @confirm="calendarConfirm"
      allowSameDay
      closeOnClickOverlay
    ></up-calendar>

    <niceui-popup-select
      ref="popupSelectRef"
      :value="children"
      :columns="activeOption[0]"
      :selectValue="optionsList.ClassnameSelectValue"
      :option="{ label: 'label', value: 'id' }"
      @confirm="pickerConfirm"
      :multiple="true"
    ></niceui-popup-select>
  </base-layout>
</template>

<script setup>
import BaseLayout from '@/components/base-layout/base-layout.vue'
import NiceuiPopupSelect from './niceui-popup-select/niceui-popup-select.vue'
import { scenes, getInitFormData, childrenFilter, rules } from './data'
import { computed, ref, reactive, nextTick, onMounted, onBeforeUnmount } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { useQueryParams } from '@/common/hooks/useQueryParams.js'
import { getclassList } from '@/api'
import { getDICT } from '@/utils'
import { getChildrenList } from '@/api/children'
import { getDictByCode } from './api'
import dayjs from 'dayjs'
import { listByClass } from '@/api/game.js'
import { getTeachList } from '@/api/classApi.js'
import useTeacherChildRecorderV3 from './hooks/useTeacherChildRecorderV3.js'

// 单选选择控制器
const isPickerShow = ref(false)
const isCalendarShow = ref(false)
// 多选选择控制器
const popupSelectRef = ref()

const togglePopUpSelect = (show) => {
  console.log('切换多选组件:', show, '当前激活的选项键:', activeOptionKey.value)
  if (show) {
    // 确保选择器能够正确显示已经选中的值
    if (activeOptionKey.value === 'childIds' && formData.value.children.length > 0) {
      console.log('设置已选中的倾听对象:', formData.value.children)
      // 从children中提取childId作为预设值
      optionsList.ClassnameSelectValue = formData.value.children.map((child) => child.childId)
    }
    popupSelectRef.value.showPopup()
  } else {
    popupSelectRef.value.closePopup()
  }
}

/** URL query */
const { params } = useQueryParams('observationRecordScene', 'id')
/** 表单 */
const formRef = ref()

/** 表单数据 */
const formData = ref(
  getInitFormData({
    observationType: params.value.observationRecordScene,
    audioText: '', // 录音转文字内容
    analysisEvaluation: '', // 分析评价
    representationResources: [], // 表征作品
    activityLocation: '', // 初始化为空字符串，表示未选择
    activityType: '', // 活动类型
    subjectActivityId: '', // 活动ID
    subjectActivityName: '', // 活动名称
    children: [], // 初始化为空数组
    childNames: [] // 初始化为空数组
  })
)

// 使用新的录音hook
const {
  audioRecordStatus,
  totalDisplayTime,
  speechText,
  recorderText,
  isSegmentTransition,
  isStartingClass,
  startClass: startRecording,
  pauseRecording,
  continueRecording,
  stopRecording,
  formatRecordingTime
} = useTeacherChildRecorderV3(formData, getOptionLabelByKeyAndId)

/** 选项 */
const optionsList = reactive({
  // 场景选项
  observationType: scenes,
  // 用户班级列表
  schoolClassId: [],
  // 当前班级学生列表
  childIds: [],
  // 班级区域列表
  activityLocation: [],
  // 授课教师列表
  teachList: [],
  // 活动类型列表（通过字典接口获取）
  activityType: [],
  // 观察对象选中的值
  ClassnameSelectValue: [],
  matrixList: []
})
/** 当前选项 */
const activeOptionKey = ref('')
/** 活动名称输入模式：'select' 选择模式，'input' 手填模式 */
const activityInputMode = ref('input')
/** 表单样式 */
const formItemStyle = `
  border-radius: 28rpx;
  padding: 0 28rpx;
  background: #fff;
  margin-bottom: 24rpx;
`

// 切换活动名称输入模式
const toggleActivityInputMode = () => {
  if (activityInputMode.value === 'input') {
    activityInputMode.value = 'select'
    // 切换到选择模式时，清空活动名称和ID，准备重新选择
    formData.value.subjectActivityName = ''
    formData.value.subjectActivityId = ''
  } else {
    activityInputMode.value = 'input'
    // 切换到输入模式时，清空活动ID，保留活动名称
    formData.value.subjectActivityId = ''
  }
}

// 处理活动名称输入
const onActivityNameInput = (e) => {
  // 手填模式下，只设置活动名称，不设置ID
  formData.value.subjectActivityName = e.detail.value
  formData.value.subjectActivityId = ''
}

// 跳转选择活动页面
const goSelectActivity = async () => {
  if (!formData.value.schoolClassId) {
    uni.showToast({
      title: '请先选择班级',
      icon: 'none'
    })
    return
  }

  // 缓存当前表单数据
  uni.setStorageSync('_teacherChildInteractionFormData', formData.value)

  // 跳转到选择主题页面
  uni.navigateTo({
    url: `/subPages/classes/choseTheme?classId=${formData.value.schoolClassId}&from=teacherChildInteraction`
  })
}

// 检查是否有选择的活动数据
const checkSelectedActivity = () => {
  const actObj = uni.getStorageSync('_actObj')
  if (actObj) {
    console.log('检测到选择的活动:', actObj)
    console.log('活动完整对象:', JSON.stringify(actObj, null, 2))

    // 获取活动名称，可能是name或其他字段
    const activityName = actObj.name || actObj.title || actObj.label || `活动${actObj.id}`
    const activityId = actObj.id

    console.log('解析后的活动ID:', activityId)
    console.log('解析后的活动名称:', activityName)

    // 恢复表单数据
    const cachedFormData = uni.getStorageSync('_teacherChildInteractionFormData')
    if (cachedFormData) {
      console.log('恢复缓存的表单数据:', cachedFormData)
      // 合并表单数据，保留活动选择
      formData.value = {
        ...cachedFormData,
        subjectActivityId: activityId,
        subjectActivityName: activityName
      }
      uni.removeStorageSync('_teacherChildInteractionFormData')
    } else {
      // 如果没有缓存数据，直接设置活动信息
      formData.value.subjectActivityId = activityId
      formData.value.subjectActivityName = activityName
    }

    // 选择活动后切换到选择模式
    activityInputMode.value = 'select'

    console.log('设置活动后的表单数据:', formData.value)
    console.log('subjectActivityId:', formData.value.subjectActivityId)
    console.log('subjectActivityName:', formData.value.subjectActivityName)

    // 清除缓存的活动数据
    uni.removeStorageSync('_actObj')

    uni.showToast({
      title: `已选择活动: ${activityName}`,
      icon: 'success'
    })
  }
}

// 处理开始上课按钮点击
const handleStartClick = () => {
  console.log('点击开始上课按钮')
  console.log('isStartingClass:', isStartingClass.value)
  console.log('audioRecordStatus:', audioRecordStatus.value)

  if (isStartingClass.value) {
    console.log('正在开始上课，忽略点击')
    return
  }

  console.log('调用 startRecording')
  startRecording()
}

// 调试函数：检查表单状态
const debugFormData = () => {
  console.log('=== 当前表单状态 ===')
  console.log('schoolClassId:', formData.value.schoolClassId)
  console.log('teach:', formData.value.teach)
  console.log(
    'subjectActivityId:',
    formData.value.subjectActivityId,
    '(类型:',
    typeof formData.value.subjectActivityId,
    ')'
  )
  console.log('subjectActivityName:', formData.value.subjectActivityName)
  console.log('表单验证关键字段检查:')
  console.log('- schoolClassId 有效:', !!formData.value.schoolClassId)
  console.log('- teach 有效:', !!formData.value.teach)
  console.log(
    '- subjectActivityId 有效:',
    !!formData.value.subjectActivityId &&
      formData.value.subjectActivityId !== '' &&
      formData.value.subjectActivityId !== null
  )
  console.log('- subjectActivityName 有效:', !!formData.value.subjectActivityName)
}

// 页面显示时检查选择的活动
onShow(() => {
  checkSelectedActivity()
  // 延迟一下再检查表单状态
  setTimeout(() => {
    debugFormData()
  }, 500)
})

// 初始化页面数据
onMounted(() => {
  initPageData()
})

// 组件卸载前清理资源
onBeforeUnmount(() => {
  // 注意：音频播放相关的清理已经在 useTeacherChildRecorderV3.js 中处理
  // 这里不需要额外的清理逻辑
})

// 选择器确认选择
const pickerConfirm = ({ value, fullValue }) => {
  switch (activeOptionKey.value) {
    case 'observationRecordScene':
      formData.value.observationRecordScene = value[0].id
      break
    case 'schoolClassId':
      formData.value.schoolClassId = value[0].id
      updateChildList()
      updateClassAreaList()
      updateTeachList()
      break
    case 'childIds':
      // 确保我们正确处理选择的倾听对象
      const selectedChildren = childrenFilter(fullValue)

      // 将处理后的数据设置到formData中
      formData.value.children = selectedChildren
      formData.value.childNames = selectedChildren.map((item) => item.childName)

      // 确保至少选择了一个倾听对象
      if (selectedChildren.length > 0) {
        formData.value.childId = selectedChildren[0].childId
        console.log('设置倾听对象ID:', formData.value.childId, selectedChildren)
      } else {
        console.log('警告: 没有选择倾听对象')
      }

      togglePopUpSelect(false)
      break
    case 'activityLocation':
      console.log('选择活动地点:', value[0], 'ID:', value[0].id)
      // 直接使用数字型ID，避免字符串比较问题
      formData.value.activityLocation = Number(value[0].id)
      console.log('活动地点ID设置后:', formData.value.activityLocation)
      break
    case 'activityType':
      console.log('选择活动类型:', value[0], 'ID:', value[0].id)
      formData.value.activityType = value[0].id
      console.log('活动类型ID设置后:', formData.value.activityType)
      break
    case 'teach':
      console.log('选择授课教师:', value[0], 'ID:', value[0].id)
      formData.value.teach = value[0].id
      console.log('授课教师ID设置后:', formData.value.teach)
      console.log('当前formData完整信息:', formData.value)
      break
    default:
      break
  }

  isPickerShow.value = false
}

function getOptionLabelByKeyAndId(key, id) {
  try {
    if (id === undefined || id === null) {
      return ''
    }

    if (typeof id === 'string' || typeof id === 'number') {
      if (!optionsList[key]) {
        // 移除频繁的日志打印，只在开发时需要时打开
        // console.log(`getOptionLabelByKeyAndId: 选项列表 ${key} 不存在`)
        return id
      }
      const target = optionsList[key].find((item) => item.id == id)

      return target ? target.label : id
    }

    if (Array.isArray(id)) {
      if (!optionsList[key]) {
        return id.join(',')
      }
      const ts = id.map((m) => optionsList[key].find((item) => item.id == m)).filter(Boolean)
      if (ts.length) {
        return ts.map((v) => v.label).join(',')
      }
      return ''
    }

    return id
  } catch (error) {
    console.error('getOptionLabelByKeyAndId出错:', error, 'key:', key, 'id:', id)
    return ''
  }
}

const activeOption = computed(() => {
  console.log('activeOption 计算属性被调用，activeOptionKey:', activeOptionKey.value)

  // 处理键名映射
  let optionKey = activeOptionKey.value
  if (activeOptionKey.value === 'teach') {
    optionKey = 'teachList'
  }

  console.log('映射后的键名:', optionKey)
  console.log('optionsList[optionKey]:', optionsList[optionKey])

  return [optionsList[optionKey] || []]
})

const multipleKeys = ['childIds']

function onShowPicker(key) {
  console.log('显示选择器:', key)

  if (key === 'childIds' && !formData.value.schoolClassId) {
    uni.$u.toast('请先选择班级')
    return
  }

  if (key === 'activityLocation' && !formData.value.schoolClassId) {
    uni.$u.toast('请先选择班级')
    return
  }

  if (key === 'teach' && !formData.value.schoolClassId) {
    uni.$u.toast('请先选择班级')
    return
  }

  if (key === 'teach') {
    console.log('显示教师选择器，当前教师列表:', optionsList.teachList)
    console.log('当前选中的教师ID:', formData.value.teach)
  }

  activeOptionKey.value = key
  if (multipleKeys.includes(key)) {
    console.log('显示多选选择器:', key, '当前选项:', optionsList[key])
    nextTick(() => {
      togglePopUpSelect(true)
    })
  } else {
    isPickerShow.value = true
  }

  return
}

function updateChildList() {
  console.log('正在更新班级学生列表，班级ID:', formData.value.schoolClassId)

  getChildrenList({
    current: 1,
    pageSize: 500,
    classId: formData.value.schoolClassId
  })
    .then((response) => {
      console.log('获取到班级学生列表:', response.data)

      optionsList.childIds = response.data.map((d) => ({
        ...d,
        label: d.title,
        name: d.title,
        checked: false
      }))

      console.log('处理后的班级学生列表:', optionsList.childIds)
    })
    .catch((err) => {
      console.error('获取班级学生列表失败:', err)
    })
}

function updateClassAreaList() {
  console.log('更新班级区域列表，班级ID:', formData.value.schoolClassId)

  if (!formData.value.schoolClassId) {
    console.log('未选择班级，无法获取区域列表')
    return
  }

  // 使用listByClass接口获取区域列表
  listByClass({
    pageNo: 1,
    pageSize: 999, // 一次获取所有区域
    classId: formData.value.schoolClassId
  })
    .then((res) => {
      if (res.status === 0 && res.data.records) {
        console.log('获取到区域列表:', res.data.records)

        // 处理区域列表数据
        optionsList.activityLocation = res.data.records.map((item) => ({
          id: item.id,
          label: item.area || item.areaAlias || `区域${item.id}`
        }))

        console.log('处理后的区域列表:', optionsList.activityLocation)
      } else {
        console.error('获取区域列表失败:', res.message)
        optionsList.activityLocation = []
      }
    })
    .catch((err) => {
      console.error('调用区域列表接口失败:', err)
      optionsList.activityLocation = []
    })
}

function updateTeachList() {
  console.log('更新班级教师列表，班级ID:', formData.value.schoolClassId)

  if (!formData.value.schoolClassId) {
    console.log('未选择班级，无法获取教师列表')
    return
  }

  // 获取当前用户信息，用于获取schoolId
  const userInfo = uni.getStorageSync('USER_INFO')
  if (!userInfo || !userInfo.currentSchoolId) {
    console.error('无法获取学校ID')
    uni.showToast({
      title: '获取学校信息失败',
      icon: 'none'
    })
    return
  }

  const params = {
    classId: formData.value.schoolClassId,
    schoolId: userInfo.currentSchoolId
  }

  console.log('调用getTeachList API，参数:', params)

  getTeachList(params)
    .then((res) => {
      console.log('获取教师列表API响应:', res)

      if (res.status === 0 && res.data) {
        console.log('获取到教师列表:', res.data)
        console.log('教师列表数据结构检查:', res.data.length > 0 ? res.data[0] : '空数组')

        // 检查数据是否为空
        if (!Array.isArray(res.data) || res.data.length === 0) {
          console.warn('教师列表为空')
          optionsList.teachList = []
          uni.showToast({
            title: '该班级暂无教师',
            icon: 'none'
          })
          return
        }

        // 处理教师列表数据
        optionsList.teachList = res.data.map((item) => ({
          id: item.id,
          label: item.name || item.nickname || `教师${item.id}`,
          name: item.name || item.nickname || `教师${item.id}`,
          nickname: item.nickname || '',
          post: item.post || ''
        }))

        console.log('处理后的教师列表:', optionsList.teachList)

        // 验证处理后的数据
        if (optionsList.teachList.length === 0) {
          console.warn('处理后的教师列表为空')
          uni.showToast({
            title: '教师数据处理异常',
            icon: 'none'
          })
        }
      } else {
        console.error('获取教师列表失败:', res.message || '未知错误')
        optionsList.teachList = []
        uni.showToast({
          title: res.message || '获取教师列表失败',
          icon: 'none'
        })
      }
    })
    .catch((err) => {
      console.error('获取教师列表失败:', err)
      optionsList.teachList = []
      uni.showToast({
        title: '网络错误，获取教师列表失败',
        icon: 'none'
      })
    })
}

/**
 * 初始化当前页面数据
 */
function initPageData() {
  try {
    /** 初始化字典 */
    getDICT('all')
      .then((dics) => {
        try {
          const ObservationRecordSceneEnumDesc = dics['ObservationRecordSceneEnumDesc']
          if (ObservationRecordSceneEnumDesc) {
            optionsList.observationRecordScene = Object.keys(
              dics['ObservationRecordSceneEnumDesc']
            ).map((key) => {
              return {
                label: ObservationRecordSceneEnumDesc[key],
                id: key
              }
            })
          }

          getclassList()
            .then((r) => {
              try {
                const curClassid = uni.getStorageSync('USER_INFO').currentClassId
                optionsList.schoolClassId = r.data.map((d) => ({
                  ...d,
                  label: d.title
                }))
                formData.value.schoolClassId = optionsList.schoolClassId.find(
                  (item) => item.id == curClassid
                )?.id

                if (formData.value.schoolClassId) {
                  updateChildList()
                  updateClassAreaList()
                  updateTeachList()
                }
              } catch (error) {
                console.error('班级初始化错误:', error)
              }
            })
            .catch((err) => {
              console.error('获取班级列表错误:', err)
            })
        } catch (innerError) {
          console.error('字典处理错误:', innerError)
        }
      })
      .catch((err) => {
        console.error('获取字典错误:', err)
      })

    /** 获取活动类型字典 */
    getDictByCode({ code: 'interactionActivityType' })
      .then((res) => {
        try {
          console.log('活动类型字典响应:', res)
          if (res && res.data && Array.isArray(res.data)) {
            optionsList.activityType = res.data.map((item) => ({
              id: item.dictItemCode,
              label: item.dictItemName
            }))
            console.log('活动类型字典处理完成:', optionsList.activityType)
          } else {
            console.warn('活动类型字典数据格式异常:', res)
            // 如果接口返回异常，使用默认数据
            optionsList.activityType = [
              { id: '1', label: '活动类型1' },
              { id: '2', label: '活动类型2' }
            ]
          }
        } catch (innerError) {
          console.error('活动类型字典处理错误:', innerError)
          // 出错时使用默认数据
          optionsList.activityType = [
            { id: '1', label: '活动类型1' },
            { id: '2', label: '活动类型2' }
          ]
        }
      })
      .catch((err) => {
        console.error('获取活动类型字典错误:', err)
        // 接口调用失败时使用默认数据
        optionsList.activityType = [
          { id: '1', label: '活动类型1' },
          { id: '2', label: '活动类型2' }
        ]
      })
  } catch (outerError) {
    console.error('初始化页面数据错误:', outerError)
  }
}

const calendarConfirm = (e) => {
  // Add current time to the date
  const selectedDate = e[0]
  const now = dayjs()
  const currentTime = now.format('HH:mm:ss')
  formData.value.observationTime = `${selectedDate} ${currentTime}`
  isCalendarShow.value = false
}

initPageData()
</script>

<style lang="scss" scoped>
.placeholder {
  color: rgb(192, 196, 204);
  font-size: 30rpx;
  font-weight: 400;
}

.activity-select-mode {
  flex: 1;
}

.activity-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 32rpx;
  color: #333;
}

.activity-mode-switch {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: #f5f5f5;
  border-radius: 20rpx;

  .mode-text {
    font-size: 24rpx;
    color: #666;
  }
}
.addObservation-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  width: calc(100vw - 48rpx);
  padding: 12rpx 24rpx;
  padding-bottom: calc(env(safe-area-inset-bottom) + 12rpx);
  height: 80rpx;
  line-height: 80rpx;
  background: #fff;

  .addObservation-button-save {
    background: #3e82f4;
    color: #fff;
    width: 100%;
    height: 100%;
    text-align: center;
    border-radius: 44rpx;
  }
}

.addObservation {
  padding: 0 32rpx 32rpx 32rpx;
  // #ifdef H5
  padding-bottom: 32rpx;
  // #endif
  &-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 88rpx;
    font-size: 24rpx;

    .addObservation-title-right-box {
      display: flex;
      align-items: center;
      font-size: 26rpx;
      font-weight: 400;
      color: rgba(128, 128, 128, 1);
      image {
        margin-right: 10rpx;
        margin-top: 5rpx;
      }
    }

    .addObservation-title-right {
      width: 32rpx;
      height: 32rpx;
      margin-left: 12rpx;
    }
  }
}

.matric-title {
  height: 80rpx;
  line-height: 80rpx;
}

.selectable-content {
  width: 100%;
  height: 110rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  color: #3f79ff;
  font-size: 30rpx;
  font-weight: 500;
  border-radius: 28rpx;
}

.recorder-container {
  width: 100%;
}

.content-section,
.analysis-section {
  width: 100%;
}

.textarea-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-top: 10rpx;
}

.add-works {
  color: #3f79ff;
  font-size: 28rpx;
  padding: 10rpx 0;
}

/* 录音相关样式 */
.record-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  width: 100%;
  height: 90rpx;
  background-color: #f0f5ff;
  border-radius: 8rpx;
  color: #3e82f4;
  font-size: 28rpx;

  image {
    width: 36rpx;
    height: 36rpx;
  }
}

.recording-section {
  background-color: #f8f8f8;
  padding: 20rpx;
  border-radius: 8rpx;
  margin-bottom: 20rpx;

  .recording-time {
    font-size: 48rpx;
    color: #3e82f4;
    text-align: center;
    font-weight: bold;
  }

  .recording-status {
    font-size: 24rpx;
    color: #666;
    text-align: center;
    margin: 10rpx 0 20rpx;
    display: flex;
    justify-content: center;
    align-items: center;

    .recording-dots {
      display: flex;

      .dot {
        animation: dotFlashing 1.5s infinite;
        animation-fill-mode: both;
        margin-left: 2rpx;

        &:nth-child(2) {
          animation-delay: 0.5s;
        }

        &:nth-child(3) {
          animation-delay: 1s;
        }
      }
    }
  }

  .recording-actions {
    display: flex;
    justify-content: space-between;
    margin: 0 40rpx;

    .stop-btn {
      background-color: #3e82f4;
      color: #fff;
      padding: 12rpx 30rpx;
      border-radius: 30rpx;
      font-size: 28rpx;
    }

    .cancel-btn {
      background-color: #f5f5f5;
      color: #666;
      padding: 12rpx 30rpx;
      border-radius: 30rpx;
      font-size: 28rpx;
      border: 1px solid #ddd;
    }
  }

  .recording-tip {
    font-size: 22rpx;
    color: #999;
    text-align: center;
    margin-top: 16rpx;
  }
}

.audio-result {
  background-color: #fff;
  border-radius: 8rpx;
  margin-bottom: 20rpx;

  .audio-file {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx;
    background-color: #f0f5ff;
    border-radius: 10rpx;
    margin-bottom: 20rpx;

    .file-info {
      flex: 1;
      overflow: hidden;

      .file-name {
        font-size: 28rpx;
        font-weight: 500;
        color: #333;
        margin-bottom: 4rpx;
        word-break: break-all;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 400rpx;
      }

      .file-size {
        font-size: 24rpx;
        color: #999;
      }
    }

    .file-actions {
      display: flex;
      align-items: center;
      gap: 16rpx;

      .play-btn,
      .delete-btn {
        width: 50rpx;
        height: 50rpx;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .play-btn {
        background-color: #3e82f4;
      }

      .delete-btn {
        background-color: #ff6b6b;
      }
    }
  }
}

@keyframes dotFlashing {
  0% {
    opacity: 0.2;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.2;
  }
}

.audio-prep {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  width: 100%;

  .filename-input {
    width: 100%;
    margin-bottom: 16rpx;

    .filename-hint {
      font-size: 24rpx;
      color: #999;
      display: block;
      margin-top: 6rpx;
    }
  }

  .record-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10rpx;
    width: 100%;
    height: 90rpx;
    background-color: #f0f5ff;
    border-radius: 8rpx;
    color: #3e82f4;
    font-size: 28rpx;

    image {
      width: 36rpx;
      height: 36rpx;
    }
  }
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.play-btn {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #3e82f4;
  transition: all 0.3s;
}

.play-btn.playing {
  background-color: #ff6b6b;
  animation: pulse 2s infinite;
}

/* 新增文本输入区域样式 */
.text-input-area {
  margin-top: 20rpx;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.start-btn {
  width: 130rpx;
  height: 130rpx;
  line-height: 130rpx;
  border-radius: 50%;
  color: #fff;
  background: #3e82f4;
  text-align: center;
  position: fixed;
  right: 50%;
  transform: translateX(50%);
  bottom: 200rpx;
  z-index: 99;
}

/* 开始上课按钮样式 - 参考ClassRecorder组件 */
.start-class-section {
  background: #fff;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  margin: 40rpx 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;

  .idle-state {
    display: flex;
    flex-direction: column;
    align-items: center;

    .start-button {
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
      margin-bottom: 30rpx;

      &.disabled {
        cursor: not-allowed;
        opacity: 0.6;

        .button-icon {
          background: linear-gradient(135deg, #ccc, #999) !important;
          box-shadow: 0 8rpx 24rpx rgba(153, 153, 153, 0.3) !important;

          &:active {
            transform: none !important;
          }
        }
      }

      .button-icon {
        width: 160rpx;
        height: 160rpx;
        border-radius: 50%;
        background: linear-gradient(135deg, #337eff, #4a90e2);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20rpx;
        box-shadow: 0 8rpx 24rpx rgba(51, 126, 255, 0.3);
        transition: all 0.3s ease;

        &:active {
          transform: scale(0.95);
        }

        .button-text {
          font-size: 32rpx;
          font-weight: 600;
          color: #fff;
        }
      }
    }

    .tip-text {
      font-size: 26rpx;
      color: #999;
      text-align: center;
    }
  }

  .recording-state {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;

    .recording-time {
      font-size: 48rpx;
      font-weight: 600;
      color: #f24957;
      margin-bottom: 40rpx;
      font-family: 'Courier New', monospace;
    }

    .recording-indicator {
      margin-bottom: 40rpx;

      .pulse-circle {
        width: 160rpx;
        height: 160rpx;
        border-radius: 50%;
        background: #f24957;
        display: flex;
        align-items: center;
        justify-content: center;
        animation: pulse 2s infinite;

        &.paused {
          animation: none;
          background: #ffa726;
        }

        .inner-circle {
          width: 80rpx;
          height: 80rpx;
          background: #fff;
          border-radius: 50%;
        }
      }
    }

    .recording-actions {
      display: flex;
      justify-content: center;
      gap: 60rpx;
      margin-bottom: 30rpx;

      .action-button {
        padding: 20rpx 40rpx;
        border-radius: 50rpx;
        font-size: 28rpx;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;

        &.pause {
          background: #ffa726;
          color: #fff;

          &:active {
            background: #ff9800;
          }

          &.disabled {
            background: #ccc;
            color: #999;
            cursor: not-allowed;

            &:active {
              background: #ccc;
            }
          }
        }

        &.stop {
          background: #f24957;
          color: #fff;

          &:active {
            background: #d63447;
          }
        }
      }
    }

    .recording-tip {
      font-size: 24rpx;
      color: #999;
      text-align: center;
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(242, 73, 87, 0.7);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 20rpx rgba(242, 73, 87, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(242, 73, 87, 0);
  }
}

/* 语音转文本显示区域样式 */
.speech-text-content {
  margin-bottom: 20rpx;
  .speech-textarea {
    min-height: 120rpx;
    max-height: 300rpx;
    padding: 16rpx;
    border-radius: 8rpx;
    border: 1px solid #dee2e6;
    font-size: 28rpx;
    line-height: 1.5;
    color: #333;
    resize: none;
    &:disabled {
      background: #fff;
      color: #333;
    }
  }
}
</style>
