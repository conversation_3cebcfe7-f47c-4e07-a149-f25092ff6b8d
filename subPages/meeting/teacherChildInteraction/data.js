import { ref } from 'vue'
export const scenes = [
  {
    label: '学习',
    id: 'Learning'
  },
  {
    label: '生活',
    id: 'Life'
  },
  {
    label: '区域游戏',
    id: 'AreaGame'
  },
  {
    label: '体育活动',
    id: 'Sports'
  },
  {
    label: '户外自主游戏',
    id: 'OutdoorGame'
  }
]

export const getInitFormData = (args = {}) => ({
  observationType: '',
  schoolClassId: '',
  teach: '', // 授课教师字段
  observationTime: '',
  observationLocation: '',
  activityName: '',
  activityType: '', // 活动类型字段
  productDesc: '',
  observationBackground: '',
  observationPurpose: '',
  observationContent: '',
  analysis: '',
  support: '',
  reflection: '',
  childNames: [],
  children: [],
  picList: [], // 附件id
  activityLocation: '', // 新增活动地点字段
  ...args
})

export const rules = ref({
  title: { type: 'string', required: true, message: '请输入标题', trigger: ['blur'] },
  children: { type: 'array', min: 1, required: true, message: '请选择倾听对象', trigger: ['blur'] },
  teach: { type: 'string', required: true, message: '请选择授课教师', trigger: ['blur'] },
  observationTime: { required: true, message: '请选择倾听时间', trigger: ['blur'] },
  activityLocation: {
    type: 'string',
    required: true,
    message: '请输入活动地点',
    trigger: ['blur']
  },
  activityName: { type: 'string', required: true, message: '请输入活动名称', trigger: ['blur'] },
  activityType: { type: 'string', required: true, message: '请选择活动类型', trigger: ['blur'] },
  subjectActivityName: {
    type: 'string',
    required: true,
    message: '请选择活动名称',
    trigger: ['blur']
  },
  observationLocation: { required: true, message: '请输入观察地点', trigger: ['blur'] }
})

// 去除对象中所有为空的属性
export function removeEmpty(oj) {
  let obj = JSON.parse(JSON.stringify(oj))
  Object.keys(obj).forEach((key) => {
    if (
      obj[key] === null ||
      obj[key] === undefined ||
      obj[key] === '' ||
      (Array.isArray(obj[key]) && obj[key].length === 0)
    ) {
      delete obj[key]
    }
    if (key === 'childNames') {
      delete obj[key]
    }
  })
  return obj
}

// 整理孩子的信息
export function childrenFilter(arr) {
  // 判断是对象还是数组
  if (typeof arr === 'object' && !Array.isArray(arr)) {
    console.log('处理单个孩子对象:', arr)
    return {
      classId: arr.classId,
      childId: arr.id,
      childName: arr.name || arr.title || arr.label
    }
  }

  // 处理数组情况
  let array = JSON.parse(JSON.stringify(arr))
  let result = []

  console.log('处理孩子数组:', array)

  array.forEach((item) => {
    // 确保使用正确的属性名
    result.push({
      classId: item.classId,
      childId: item.id,
      childName: item.name || item.title || item.label
    })
  })

  console.log('处理后的结果:', result)
  return result
}

// 去重
export function removeDuplicates(array1 = [], array2 = [], type = 'childId') {
  const combinedArray = array1.concat(array2)
  const uniqueArray = combinedArray.reduce((accumulator, current) => {
    const exists = accumulator.some((obj) => obj[type] === current[type])
    if (!exists) {
      accumulator.push(current)
    }
    return accumulator
  }, [])

  return uniqueArray
}
