import { ref, reactive } from 'vue'
import { v4 as uuidv4 } from 'uuid'
import { TextEncoder, TextDecoder } from 'text-decoding'
import pako from 'pako'

// 协议常量
const PROTOCOL_VERSION = 0b0001
const DEFAULT_HEADER_SIZE = 0b0001

const PROTOCOL_VERSION_BITS = 4
const HEADER_BITS = 4
const MESSAGE_TYPE_BITS = 4
const MESSAGE_TYPE_SPECIFIC_FLAGS_BITS = 4
const MESSAGE_SERIALIZATION_BITS = 4
const MESSAGE_COMPRESSION_BITS = 4
const RESERVED_BITS = 8

// Message Type:
const CLIENT_FULL_REQUEST = 0b0001
const CLIENT_AUDIO_ONLY_REQUEST = 0b0010
const SERVER_FULL_RESPONSE = 0b1001
const SERVER_ACK = 0b1011
const SERVER_ERROR_RESPONSE = 0b1111

// Message Type Specific Flags
const NO_SEQUENCE = 0b0000  // no check sequence
const POS_SEQUENCE = 0b0001
const NEG_SEQUENCE = 0b0010
const NEG_SEQUENCE_1 = 0b0011

// Message Serialization
const NO_SERIALIZATION = 0b0000
const THRIFT = 0b0011
const CUSTOM_TYPE = 0b1111

// Message Compression
const NO_COMPRESSION = 0b0000
const JSON_SERIALIZATION = 0b0001
const GZIP_COMPRESSION = 0b0001

const lengthToBytesBigEndian = (payloadBytes) => {
  const length = payloadBytes.length;
  const buffer = new ArrayBuffer(4); // 创建 4 字节的缓冲区
  const view = new DataView(buffer);

  // 将长度按大端顺序写入缓冲区
  view.setInt32(0, length, false);  // false 表示大端字节顺序

  // 返回字节数组
  return new Uint8Array(buffer);
}


export const useBytedanceASR = (options) => {
  const {
    appid = ref(''),
    uid = ref(''),
    token = ref(''),
    cluster = ref(''),
    audioConfig = {},
    requestConfig,
    onAudioStreamComplete,
    onAsrDataReceived  // 新增：ASR数据接收回调
  } = options

  // 状态管理
  const status = ref('disconnected') // 'disconnected' | 'connecting' | 'connected' | 'error'
  const ws = ref(null)
  const retryCount = ref(0)
  const maxRetries = 3
  const retryInterval = 3000
  const sequence = ref(1)
  const reqId = ref('');
  const audioQueue = reactive([])
  const isProcessing = ref(false)
  const resultText = ref('')
  const previousResultText = ref('')
  const error = ref(null)

  const generateHeader = ({
    version = PROTOCOL_VERSION,
    messageType = CLIENT_FULL_REQUEST,
    messageTypeSpecificFlags = NO_SEQUENCE,
    serialMethod = JSON_SERIALIZATION,
    compressionType = GZIP_COMPRESSION,
    reservedData = 0x00,
    extensionHeader = []
  } = {}) => {
    // 验证协议头参数
    if (serialMethod !== 0b0001 || compressionType !== 0b0001) {
      throw new Error('Invalid serialization/compression method')
    }
    // Calculate the header size based on the extensionHeader
    const headerSize = Math.floor(extensionHeader.length / 4) + 1;

    // Create a byte array (Uint8Array)
    let header = [];

    // Add protocol version and header size
    header.push((version << 4) | headerSize);

    // Add message type and message type specific flags
    header.push((messageType << 4) | messageTypeSpecificFlags);

    // Add serialization method and compression type
    header.push((serialMethod << 4) | compressionType);

    // Add reserved data
    header.push(reservedData);

    // Add extension header (if any)
    header = header.concat(extensionHeader);

    return new Uint8Array(header);
  }

  // 构建请求参数
  const constructFullRequest = () => {
    if (!appid.value || !uid.value || !token.value) {
      throw new Error('构建请求参数失败')
    }

    const { format, sampleRate, numberOfChannels, } = audioConfig;

    const fullRequest = {
      app: {
        appid: appid.value,
        cluster: cluster.value,
        token: token.value
      },
      user: {
        uid: uid.value
      },
      request: {
        reqid: reqId.value,
        nbest: 1,
        workflow: requestConfig?.workflow || 'audio_in,resample,partition,vad,fe,decode,itn,nlu_punctuate',
        show_language: false,
        show_utterances: true,  // 开启语音停顿、分句、分词信息
        result_type: requestConfig?.resultType || 'single',  // 设置为single获取当前分句结果
        sequence: sequence.value
      },
      audio: {
        format: format.toLowerCase() === 'pcm' ? 'raw' : format,
        // codec: format === 'pcm' || format === 'wav' ? 'raw' : 'opus',
        rate: sampleRate || 16000,
        bits: 16,
        channel: numberOfChannels || 1,
        // language: 'zh-CN',
      }
    }

    console.log('fullRequest', fullRequest)

    return new TextEncoder().encode(JSON.stringify(fullRequest));
  }

  // 处理响应数据
  const parseResponse = (res) => {
    try {
      // 提取协议头信息
      // const protocol_version = res[0] >> 4;
      const headerSize = res[0] & 0x0f;
      const messageType = res[1] >> 4;
      // const message_type_specific_flags = res[1] & 0x0f;
      const serializationMethod = res[2] >> 4;
      const messageCompression = res[2] & 0x0f;
      // const reserved = res[3];

      // 计算头部扩展部分
      // const header_extensions = res.slice(4, header_size * 4);
      const payload = res.slice(headerSize * 4);

      let result = {};
      let payloadMsg = null;
      let payloadSize = 0;

      // 解析不同的 message_type
      if (messageType === SERVER_FULL_RESPONSE) {
        payloadSize = payload.slice(0, 4).reduce((acc, byte, index) => acc + (byte << (8 * (3 - index))), 0);
        payloadMsg = payload.slice(4);
      } else if (messageType === SERVER_ACK) {
        const seq = payload.slice(0, 4).reduce((acc, byte, index) => acc + (byte << (8 * (3 - index))), 0);
        result.seq = seq;

        if (payload.length >= 8) {
          payloadSize = payload.slice(4, 8).reduce((acc, byte, index) => acc + (byte << (8 * (3 - index))), 0);
          payloadMsg = payload.slice(8);
        }
      } else if (messageType === SERVER_ERROR_RESPONSE) {
        const code = payload.slice(0, 4).reduce((acc, byte, index) => acc + (byte << (8 * (3 - index))), 0);
        result.code = code;

        payloadSize = payload.slice(4, 8).reduce((acc, byte, index) => acc + (byte << (8 * (3 - index))), 0);
        payloadMsg = payload.slice(8);
      }

      if (payloadSize === 0) {
        return { error: new Error("payload size is 0") };
      }

      if (payloadMsg === null) {
        return result;
      }

      // 解压缩消息
      if (messageCompression === GZIP_COMPRESSION) {
        payloadMsg = pako.ungzip(payloadMsg);  // 使用 pako 库进行解压
      }

      // 反序列化消息
      if (serializationMethod === JSON_SERIALIZATION) {
        payloadMsg = JSON.parse(new TextDecoder().decode(payloadMsg));
      } else if (serializationMethod !== NO_SERIALIZATION) {
        payloadMsg = new TextDecoder().decode(payloadMsg);
      }

      result.payload = payloadMsg;
      result.payloadSize = payloadSize;
      return result;
    } catch (e) {
      console.error('响应解析失败:', e)
      error.value = `响应解析失败: ${e.message}`
      return null
    }
  }

  const generatePayload = (header, data) => {
    const payloadBytes = pako.gzip(data)
    const compressedByted = lengthToBytesBigEndian(payloadBytes);
    const requestPayload = new Uint8Array(header.length + compressedByted.length + payloadBytes.length)

    requestPayload.set(header);
    requestPayload.set(compressedByted, header.length);
    requestPayload.set(payloadBytes, header.length + compressedByted.length)

    return requestPayload;
  }

  // 重连逻辑
  const reconnect = async () => {
    if (retryCount.value >= maxRetries) {
      error.value = '重连次数超过限制，请检查网络后重试'
      status.value = 'error'
      return false
    }

    try {
      status.value = 'connecting'
      retryCount.value++
      uni.showToast({
        title: `语音识别服务断开，正在进行第 ${retryCount.value} 次重连...`,
        icon: 'none',
        duration: 2000
      })
      // 在重连前保存当前的识别结果
      previousResultText.value = resultText.value
      await connect()
      return true
    } catch (err) {
      uni.showToast({
        title: `重连失败，${err.message || '请检查网络后重试'}`,
        icon: 'none',
        duration: 2000
      })
      console.error('重连失败:', err)
      // 延迟后重试
      await new Promise(resolve => setTimeout(resolve, retryInterval))
      return await reconnect()
    }
  }

  // WebSocket连接管理
  const connect = async () => {
    reqId.value = uuidv4()
    return new Promise((resolve, reject) => {
      try {
        status.value = 'connecting'
        const requestParams = constructFullRequest()
        const fullClientRequest = generatePayload(generateHeader(), requestParams)

        // 建立WebSocket连接
        ws.value = uni.connectSocket({
          url: 'wss://openspeech.bytedance.com/api/v2/asr',
          header: {
            'Authorization': `Bearer; ${token.value}`,
          },
          success: (res) => {
            console.log('准备建立websocket连接...:', res)
          },
          fail: (err) => {
            console.error('WebSocket连接失败:', err)
            reject(new Error(`WebSocket连接失败: ${err.errMsg}`))
          }
        })

        // 添加连接超时处理
        const timeout = setTimeout(() => {
          reject(new Error('WebSocket连接超时'))
        }, 10000) // 10秒超时

        ws.value.onOpen(() => {
          console.log('WebSocket连接成功, 发送第一帧')
          clearTimeout(timeout) // 清理超时定时器

          ws.value.send({
            data: fullClientRequest.buffer, // 真机测试，需要使用 buffer 发送数据
            success: () => {
              console.log('初始请求发送成功，开始处理音频数据')
              status.value = 'connected'
              retryCount.value = 0
              resolve(true)
            },
            fail: (err) => {
              console.error('初始请求发送失败:', err)
              reject(new Error(`初始请求发送失败: ${err.errMsg}`))
            }
          })
        })

        ws.value.onMessage((res) => {
          if (res.data instanceof ArrayBuffer) {
            handleMessage(res.data)
          } else {
            console.error('收到非二进制格式消息:', typeof res.data)
          }
        })

        ws.value.onClose((res) => {
          console.log('WebSocket连接已关闭:', res)
          handleClose()
        })

        ws.value.onError((err) => {
          console.error('WebSocket错误:', err)
          handleError(err)
          reject(new Error(`WebSocket错误: ${err.errMsg}`))
        })

      } catch (error) {
        console.error('[ASR] 连接失败:', error)
        status.value = 'error'
        error.value = `连接失败: ${error.message}`
        reject(error)
      }
    })
  }

  // 音频数据处理
  const processQueue = async () => {
    if (isProcessing.value || !audioQueue.length) return

    // 检查连接状态
    if (status.value !== 'connected') {
      console.log('WebSocket未连接，尝试重连...')
      const reconnected = await reconnect()
      if (!reconnected) {
        throw new Error('WebSocket重连失败，无法发送音频数据')
      }
    }

    isProcessing.value = true
    const { data, isLast } = audioQueue.shift()

    try {
      const header = generateHeader({
        messageType: CLIENT_AUDIO_ONLY_REQUEST,
        messageTypeSpecificFlags: isLast ? NEG_SEQUENCE : NO_SEQUENCE
      })

      const audioOnlyRequest = generatePayload(header, data)

      ws.value.send({
        data: audioOnlyRequest.buffer, // 真机测试，需要使用 buffer 发送数据
        success: () => {
          console.log('音频数据发送成功')
        },
        fail: (err) => {
          console.error('音频数据发送失败:', err)
          throw new Error(`音频数据发送失败: ${err.errMsg}`)
        }
      })
      sequence.value = isLast ? -Math.abs(sequence.value) : sequence.value + 1
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      isProcessing.value = false
      processQueue()
    }
  }

  // 暴露的API
  const start = async () => {
    console.log('=== ASR start 被调用 ===')
    console.log('当前状态:', status.value)
    console.log('当前resultText:', resultText.value)

    error.value = null
    retryCount.value = 0
    resultText.value = ''
    previousResultText.value = ''

    console.log('重置后的resultText:', resultText.value)

    try {
      await connect()
      console.log('ASR连接成功，状态:', status.value)
      return true
    } catch (err) {
      console.error('启动ASR失败:', err)
      return false
    }
  }

  const stop = () => {
    if (ws.value) {
      ws.value.close({
        success: () => {
          status.value = 'disconnected'
          retryCount.value = maxRetries // 防止自动重连
        }
      })
    }
  }

  const sendAudio = (data, isLast = false) => {
    audioQueue.push({
      data: data,
      isLast: isLast
    })

    processQueue()
  }

  // 事件处理
  const handleMessage = (data) => {
    const response = parseResponse(new Uint8Array(data))
    // console.log('=== 完整ASR服务响应 ===')
    // console.log('完整响应数据:', JSON.stringify(response, null, 2))

    if (response.payload?.code === 1000) { // 音频格式解析成功
      // 处理单句结果模式
      if (response.payload.result && response.payload.result.length > 0) {
        const currentResult = response.payload.result[0]

        // 更新实时显示的文本
        const newText = previousResultText.value + (currentResult.text || '')
        console.log('=== ASR文本更新 ===')
        console.log('之前文本:', previousResultText.value)
        console.log('当前文本:', currentResult.text)
        console.log('合并后文本:', newText)
        resultText.value = newText

        // 收集ASR数据用于上传 - 处理utterances数组
        if (currentResult.utterances && currentResult.utterances.length > 0) {
          currentResult.utterances.forEach(utterance => {
            // 只收集确定的结果（definite === true）
            if (utterance.definite === true) {
              const asrData = {
                asrContent: utterance.text,
                startTime: utterance.start_time,
                endTime: utterance.end_time
              }
              // 通过回调传递ASR数据
              onAsrDataReceived?.(asrData)
            }
          })
        }
      }

      // 检查是否是最后一帧的响应
      if (response.payload?.sequence < 0) {
        // 在这里触发回调，因为已经收到服务器对最后一帧的响应
        onAudioStreamComplete?.(resultText.value)

        // 注意：在分段录音模式下，不要自动停止ASR服务
        // 因为可能还有下一段录音需要继续使用ASR服务
        // 只有在真正结束录音时才手动调用 stop()
        console.log('收到最后一帧响应，但保持ASR服务连接状态（分段录音模式）')
      }
    }
  }

  const handleClose = () => {
    status.value = 'disconnected'
    if (retryCount.value < maxRetries) {
      console.log('连接断开，准备重连...')
      reconnect().catch(err => {
        console.error('重连失败:', err)
        error.value = `重连失败: ${err.message}`
      })
    }
  }

  const handleError = (err) => {
    console.error('ASR Error:', err)
    status.value = 'error'
    error.value = `WebSocket错误: ${err.errMsg}`
    handleClose()
  }

  return {
    status,
    resultText,
    error,
    start,
    stop,
    sendAudio
  }
}
