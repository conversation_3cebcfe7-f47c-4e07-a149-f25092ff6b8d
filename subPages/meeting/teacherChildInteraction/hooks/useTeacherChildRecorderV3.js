import { ref, onUnmounted, onMounted, watch } from 'vue'
import { useRecordAuthStore } from './recordAuth'
import { beginAudio, endAudio, uploadAudioVideo, uploadAudioAsr } from '../api'
import { useBytedanceASR } from './useBytedanceASR.js'
import { useAsrTokenStore } from '../stores/asrToken.js'
import { storeToRefs } from 'pinia'

const recordAuthStore = useRecordAuthStore()
const asrTokenStore = useAsrTokenStore()
const { appid, uid, token, cluster } = storeToRefs(asrTokenStore)

/**
 * 师幼互动录音Hook V3 - 支持百度ASR和豆包服务切换
 * 支持PCM格式录音、实时语音转文本、暂停/继续录音
 */
export default function useTeacherChildRecorderV3(formData, getOptionLabelByKeyAndId) {
  // 录音时长配置 - 采用meeting模块的策略：60秒自动重启
  const RECORDING_CONFIG = {
    // 改为meeting模块的策略：60秒自动重启，保持ASR连接
    MAX_DURATION: 60000, // 60秒，与meeting模块一致

    // 保留原有配置用于兼容性
    SEGMENT_DURATION_SECONDS: 60,    // 改为60秒

    get SEGMENT_DURATION_MS() {
      return this.SEGMENT_DURATION_SECONDS * 1000 // 毫秒
    },

    get SEGMENT_DURATION_MINUTES() {
      return this.SEGMENT_DURATION_SECONDS / 60 // 分钟
    },

    ASR_UPLOAD_INTERVAL_MINUTES: 1, // 缩短ASR数据上传间隔到1分钟

    get ASR_UPLOAD_INTERVAL_MS() {
      return this.ASR_UPLOAD_INTERVAL_MINUTES * 60 * 1000 // 毫秒
    }
  }

  // #ifdef MP-WEIXIN
  // 录音相关状态变量 - 简化为meeting模块的策略
  let asrUploadTimer = null // ASR上传定时器
  let timer1 = null // 计时器

  // ASR数据收集
  const asrDataList = ref([]) // 存储ASR数据列表
  let currentAsrIndex = 0 // 当前ASR索引

  // 录音相关状态 - 简化状态管理
  const audioRecordStatus = ref('idle') // idle, recording, paused, completed
  const totalDisplayTime = ref(0) // 显示给用户的累加总时间（秒）
  const speechText = ref('')
  const recorderLoading = ref(false)
  const recorderText = ref('开始上课')
  const isManualStop = ref(false) // 是否手动停止录音（参考meeting模块）

  // 业务相关状态
  const interactionId = ref(null)
  const recordIndex = ref(-1) // 初始化为-1，参考meeting模块
  const startTime = ref(null)
  const endTime = ref(null)
  const uploadedFiles = ref([])
  const pendingUploads = ref([]) // 跟踪正在进行的上传任务
  const totalRecordingDuration = ref(0) // 所有录音段的总时长（秒）
  const isNavigating = ref(false) // 防止重复跳转的标志

  // 录音管理器
  const recorderManager = uni.getRecorderManager()

  // 录音配置 - 采用meeting模块的配置策略
  const options = {
    format: 'PCM', // ByteDance ASR使用PCM格式
    duration: RECORDING_CONFIG.MAX_DURATION, // 使用60秒时长
    sampleRate: 16000, // PCM标准采样率，单位Hz
    numberOfChannels: 1, // 录音通道数，单声道（PCM标准）
    encodeBitRate: 48000, // PCM标准编码码率
    frameSize: '4' // 指定帧大小，单位 KB
  }

  // 屏幕常亮控制函数
  const setKeepScreenOn = (state = false) => {
    try {
      uni.setKeepScreenOn({ keepScreenOn: state })
      console.log(`屏幕常亮设置: ${state ? '开启' : '关闭'}`)
    } catch (error) {
      console.error('设置屏幕常亮失败:', error)
    }
  }



  // ByteDance ASR服务
  const {
    start: startASR,
    sendAudio,
    stop: stopASR,
    status: asrConnectionStatus,
    resultText: asrResult
  } = useBytedanceASR({
    appid: appid,
    uid: uid,
    token: token,
    cluster: cluster,
    audioConfig: {
      format: options.format,
      sampleRate: options.sampleRate,
      numberOfChannels: options.numberOfChannels,
      encodeBitRate: options.encodeBitRate
    },
    onAudioStreamComplete: (result) => {
      speechText.value = result
    },
    onAsrDataReceived: (asrData) => {
      // 收集ASR数据
      console.log('收到ASR数据:', asrData)
      const dataWithIndex = {
        ...asrData,
        index: currentAsrIndex++
      }
      asrDataList.value.push(dataWithIndex)
      console.log('当前ASR数据列表长度:', asrDataList.value.length)
    }
  })

  // ASR数据上传函数（优化分段上传逻辑）
  const uploadAsrData = async () => {
    console.log('=== 开始上传ASR数据 ===')
    console.log('ASR数据列表长度:', asrDataList.value.length)
    console.log('interactionId:', interactionId.value)
    console.log('当前录音状态:', audioRecordStatus.value)

    const currentInteractionId = interactionId.value // 保存当前的 interactionId
    if (asrDataList.value.length === 0 || !currentInteractionId) {
      console.log('跳过上传：数据为空或无interactionId')
      return Promise.resolve() // 返回已解决的Promise
    }

    try {
      const uploadData = {
        businessId: currentInteractionId, // 使用保存的 interactionId
        asrList: [...asrDataList.value] // 复制当前数据
      }
      console.log('上传数据:', JSON.stringify(uploadData, null, 2))

      const result = await uploadAudioAsr(uploadData)
      console.log('ASR数据上传成功:', result)

      // 清空已上传的数据
      asrDataList.value = []
      console.log('=== ASR数据上传成功完成 ===')

      // 优化：在分段上传后，确保ASR服务状态正常
      if (asrConnectionStatus.value === 'connected') {
        console.log('ASR服务连接正常，可以继续接收数据')
      } else {
        console.warn('ASR服务连接异常，状态:', asrConnectionStatus.value)
      }

      return result
    } catch (error) {
      console.error('ASR数据上传失败:', error)
      console.log('=== ASR数据上传失败结束 ===')

      // 优化：上传失败时不清空数据，允许重试
      console.log('保留ASR数据以便重试，当前数据量:', asrDataList.value.length)

      throw error // 重新抛出错误，让调用方处理
    }
  }

  // 启动ASR定时上传（优化连接状态监控）
  const startAsrUploadTimer = () => {
    if (asrUploadTimer) {
      clearInterval(asrUploadTimer)
    }
    asrUploadTimer = setInterval(() => {
      // 在上传前检查ASR连接状态
      if (asrConnectionStatus.value !== 'connected') {
        console.warn('ASR服务连接异常，状态:', asrConnectionStatus.value)
        // 如果正在录音但ASR断开，尝试重新连接
        if (audioRecordStatus.value === 'recording') {
          console.log('录音进行中但ASR断开，尝试重新连接...')
          startASR().catch(error => {
            console.error('ASR重连失败:', error)
          })
        }
      } else {
        // 连接正常时上传数据
        uploadAsrData().catch(error => {
          console.error('定时ASR数据上传失败:', error)
        })
      }
    }, RECORDING_CONFIG.ASR_UPLOAD_INTERVAL_MS) // 使用配置的上传间隔
  }

  // 停止ASR定时上传
  const stopAsrUploadTimer = () => {
    if (asrUploadTimer) {
      clearInterval(asrUploadTimer)
      asrUploadTimer = null
    }
  }

  // 等待所有上传任务完成
  const waitForAllUploadsComplete = async () => {
    console.log('=== 等待所有上传任务完成 ===')
    console.log('当前正在进行的上传任务数量:', pendingUploads.value.length)

    if (pendingUploads.value.length > 0) {
      try {
        await Promise.all(pendingUploads.value)
        console.log('所有上传任务已完成')
      } catch (error) {
        console.error('部分上传任务失败:', error)
        // 即使部分上传失败，也继续执行后续流程
      }
    }

    // 清空上传任务列表
    pendingUploads.value = []
    console.log('=== 上传任务等待完成 ===')
  }



  // 统一的跳转首页函数
  const navigateToIndex = () => {
    console.log('=== navigateToIndex 被调用 ===')
    console.log('当前 isNavigating.value:', isNavigating.value)

    // 防止重复跳转
    if (isNavigating.value) {
      console.log('已在跳转中，跳过重复跳转')
      return
    }

    console.log('开始跳转到首页')
    isNavigating.value = true

    uni.redirectTo({
      url: '/subPages/meeting/teacherChildInteraction/index',
      success: () => {
        console.log('跳转首页成功')
      },
      fail: (error) => {
        console.error('跳转首页失败:', error)
        isNavigating.value = false // 跳转失败时重置标志
      }
    })
  }

  // 防抖标记
  const isStartingClass = ref(false)

  // 开始上课（开始录音）
  const startClass = async () => {
    // 防抖处理：如果正在开始上课，直接返回
    if (isStartingClass.value) {
      console.log('正在开始上课，忽略重复点击')
      return false
    }

    try {
      // 设置防抖标记
      isStartingClass.value = true

      // 检查表单数据
      console.log('=== 表单验证开始 ===')
      console.log('schoolClassId:', formData.value.schoolClassId)
      console.log('teach:', formData.value.teach)
      console.log('subjectActivityId:', formData.value.subjectActivityId)
      console.log('subjectActivityName:', formData.value.subjectActivityName)
      console.log('完整表单数据:', formData.value)

      if (
        !formData.value.schoolClassId ||
        !formData.value.teach ||
        !formData.value.subjectActivityName ||
        formData.value.subjectActivityName === '' ||
        formData.value.subjectActivityName === null ||
        formData.value.subjectActivityName === undefined
      ) {
        console.log('=== 表单验证失败 ===')
        console.log('缺少字段:')
        if (!formData.value.schoolClassId) console.log('- schoolClassId 为空')
        if (!formData.value.teach) console.log('- teach 为空')
        if (!formData.value.subjectActivityName) console.log('- subjectActivityName 为空')

        uni.showToast({
          title: '请完善表单信息',
          icon: 'none'
        })
        return false
      }

      console.log('=== 表单验证通过 ===')

      // 检查录音权限
      const hasAuth = await recordAuthStore.initAuth()
      if (!hasAuth) {
        uni.showToast({
          title: '需要录音权限',
          icon: 'none'
        })
        return false
      }

      recorderLoading.value = true

      // 调用beginAudio接口
      const beginAudioData = {
        classId: formData.value.schoolClassId,
        className: getOptionLabelByKeyAndId('schoolClassId', formData.value.schoolClassId),
        activityType: formData.value.activityType || 1,
        subjectActivityName: formData.value.subjectActivityName,
        // 只有在选择模式下且有活动ID时才传递subjectActivityId，否则传递空字符串
        subjectActivityId: (formData.value.subjectActivityId && formData.value.subjectActivityId !== '')
          ? formData.value.subjectActivityId
          : '',
        partnerList: [
          {
            partnerId: formData.value.teach,
            partnerName: getOptionLabelByKeyAndId('teachList', formData.value.teach)
          }
        ]
      }

      const beginResult = await beginAudio(beginAudioData)
      if (beginResult.status !== 0) {
        throw new Error(beginResult.message || '开始录音失败')
      }

      interactionId.value = beginResult.data
      startTime.value = Date.now()

      // 初始化ASR token（如果需要）
      if (!token.value) {
        await asrTokenStore.initTokenInfo()
      }

      // 初始化录音管理器事件监听
      initRecorderManagerEvents()

      // 开始ASR服务
      const asrStarted = await startASR()
      if (!asrStarted) {
        throw new Error('ASR服务启动失败')
      }

      // 开始录音 - 采用meeting模块的策略
      await startRecording()

      // 成功后重置防抖标记
      isStartingClass.value = false
      return true
    } catch (error) {
      console.error('开始录音失败:', error)
      uni.showToast({
        title: error.message || '开始录音失败',
        icon: 'none'
      })

      recorderLoading.value = false
      audioRecordStatus.value = 'idle'
      recorderText.value = '开始上课'

      return false
    } finally {
      recorderLoading.value = false
      // 重置防抖标记
      isStartingClass.value = false
    }
  }

  // 暂停录音 - 简化为meeting模块的策略
  const pauseRecording = async () => {
    if (audioRecordStatus.value === 'recording') {
      recorderManager.pause()
      audioRecordStatus.value = 'paused'

      // 暂停计时器
      if (timer1) {
        clearInterval(timer1)
        timer1 = null
      }

      recorderText.value = '录音已暂停'

      // 用户主动暂停时关闭屏幕常亮
      setKeepScreenOn(false)

      // 暂停时上传当前ASR数据
      await uploadAsrData()
    }
  }

  // 继续录音 - 简化为meeting模块的策略
  const continueRecording = () => {
    if (audioRecordStatus.value === 'paused') {
      console.log('=== 用户点击继续录音 ===')

      // 直接恢复录音
      recorderManager.resume()
      audioRecordStatus.value = 'recording'

      // 恢复计时器 - 确保不重复创建
      if (timer1) {
        clearInterval(timer1)
      }
      timer1 = setInterval(countDown, 1000)

      // 继续录音时重新开启屏幕常亮
      setKeepScreenOn(true)
    }
  }



  // 结束录音 - 采用meeting模块的策略
  const stopRecording = async () => {
    try {
      uni.showModal({
        title: '结束录音',
        content: '是否已经完成课堂录音？',
        confirmText: '是的',
        cancelText: '取消',
        success: async (res) => {
          if (res.confirm) {
            // 设置手动停止标记
            isManualStop.value = true

            // 用户确认后显示loading并执行完整流程
            uni.showLoading({
              title: '正在处理录音...',
              mask: true
            })

            try {
              await finishRecording()

              // 所有接口执行完毕后跳转到首页
              navigateToIndex()
            } catch (error) {
              console.error('录音处理失败:', error)
              uni.showToast({
                title: '录音处理失败: ' + (error.message || '未知错误'),
                icon: 'none',
                duration: 3000
              })
            } finally {
              uni.hideLoading()
            }
          }
        }
      })
    } catch (error) {
      console.error('结束录音失败:', error)
      uni.showToast({
        title: '结束录音失败',
        icon: 'none'
      })
    }
  }

  // 完成录音处理
  const finishRecording = async () => {
    // 保存当前的 interactionId，防止在处理过程中被清空
    const currentInteractionId = interactionId.value

    if (!currentInteractionId) {
      throw new Error('interactionId 为空，无法完成录音处理')
    }

    // 停止ASR服务
    stopASR()

    // 停止ASR定时上传
    stopAsrUploadTimer()

    endTime.value = Date.now()

    // 等待当前录音段处理完成（如果正在处理）
    let waitCount = 0
    while (audioRecordStatus.value === 'processing' && waitCount < 30) {
      console.log(`等待录音处理完成... ${waitCount + 1}/30秒`)
      await new Promise((resolve) => setTimeout(resolve, 1000))
      waitCount++
    }

    // 停止录音管理器（注意：这会触发 onStop 事件，进而调用 handleRecordingComplete）
    console.log('调用 recorderManager.stop() 停止录音')
    recorderManager.stop()
    recorderLoading.value = false
    recorderText.value = '开始上课'

    // 等待最后一段录音处理完成
    console.log('等待最后一段录音处理完成...')
    waitCount = 0
    while (audioRecordStatus.value === 'processing' && waitCount < 30) {
      console.log(`等待最后一段录音处理完成... ${waitCount + 1}/30秒`)
      await new Promise((resolve) => setTimeout(resolve, 1000))
      waitCount++
    }

    // 额外等待一点时间，确保 handleRecordingComplete 完全执行完毕
    console.log('额外等待确保录音处理完全完成...')
    await new Promise((resolve) => setTimeout(resolve, 2000))

    // 注意：不要在这里设置 'completed' 状态，因为 onStop 事件还需要处理最后一段录音
    // audioRecordStatus.value = 'completed'

    // 清理所有计时器
    timer1 && clearInterval(timer1)

    // 保存最后的语音转文本结果 - 简化为meeting模块的策略
    console.log('=== 保存最后的语音转文本 ===')
    console.log('最后文本:', speechText.value)

    // 使用当前的ASR结果作为最终文本
    const allAsrContent = speechText.value || ''
    console.log('最终文本:', allAsrContent)

    console.log('=== 开始执行结束录音流程 ===')
    console.log('当前token:', uni.getStorageSync('TOKEN_NAME'))
    console.log('等待完成后 totalRecordingDuration:', totalRecordingDuration.value, '秒')
    console.log('等待完成后 totalDisplayTime:', totalDisplayTime.value, '秒')
    console.log('需要处理的数据:', {
      interactionId: currentInteractionId,
      duration: totalDisplayTime.value,
      totalRecordingDuration: totalRecordingDuration.value,
      asrContent: allAsrContent,
      startTime: startTime.value,
      endTime: endTime.value,
      uploadedFilesCount: uploadedFiles.value.length,
      pendingAsrDataCount: asrDataList.value.length
    })

    try {
      // 第一步：并行执行上传操作（语音文本和语音文件）
      console.log('=== 第一步：并行上传语音文本和语音文件 ===')

      const uploadPromises = []

      // 1. 上传最后的ASR数据
      if (asrDataList.value.length > 0) {
        console.log('添加ASR数据上传任务')
        uploadPromises.push(uploadAsrData())
      }

      // 2. 等待所有语音文件上传完成
      console.log('等待所有语音文件上传完成...')
      uploadPromises.push(waitForAllUploadsComplete())

      // 等待所有上传操作完成
      await Promise.all(uploadPromises)
      console.log('所有上传操作完成')
      console.log('最终上传文件数量:', uploadedFiles.value.length)

      // 第二步：调用结束录音接口
      console.log('=== 第二步：调用endAudio接口 ===')
      console.log('使用总录音时长:', totalRecordingDuration.value, '秒')
      await endAudio({
        interactionId: currentInteractionId,
        duration: totalRecordingDuration.value // 使用所有录音段的总时长
      })
      console.log('endAudio接口调用成功')

      console.log('=== 结束录音流程完成 ===')

      // 设置完成状态
      audioRecordStatus.value = 'completed'

      // 注意：不要在这里调用 clearState()，因为可能还有异步上传任务在进行
      // clearState() 应该在跳转成功后或者在适当的时机调用

      console.log('录音处理完成，准备跳转首页')
    } catch (error) {
      console.error('结束录音流程失败:', error)
      throw error // 重新抛出错误，让外层catch处理
    }
  }

  // 初始化录音管理器事件监听 - 采用meeting模块的策略
  const initRecorderManagerEvents = () => {
    // 录音开始事件 - 参考meeting模块
    recorderManager.onStart(async () => {
      if (recordIndex.value === -1) {
        console.log("录音已启动，启动参数为", {
          duration: RECORDING_CONFIG.MAX_DURATION,
          sampleRate: options.sampleRate,
          numberOfChannels: options.numberOfChannels,
          encodeBitRate: options.encodeBitRate,
          format: options.format,
          frameSize: options.frameSize,
        })
      } else {
        console.log('录音自动重启成功')
      }

      // 如果ASR未连接，则启动ASR服务
      if (asrConnectionStatus.value !== 'connected') {
        await startASR()
      }

      recordIndex.value++
      console.log('录音开始')
      // 开始录音后，设置屏幕常亮，防止熄屏后录音失败
      setKeepScreenOn(true)
    })

    // 录音错误事件
    recorderManager.onError((res) => {
      console.error('录音错误:', res)
      audioRecordStatus.value = 'idle'

      // 根据错误类型决定是否需要关闭连接
      if (res.errMsg?.includes('system error') || res.errMsg?.includes('abort')) {
        // 只有在系统错误或中断时才关闭连接
        stopASR()
      }

      // 关闭屏幕常亮设置
      setKeepScreenOn(false)
      recorderText.value = '开始上课'
    })

    // 录音暂停事件
    recorderManager.onPause(() => {
      console.log('录音暂停')
      audioRecordStatus.value = 'paused'
    })

    // 录音继续事件
    recorderManager.onResume(() => {
      console.log('录音恢复')
      audioRecordStatus.value = 'recording'
    })

    // 中断结束事件（例如来电结束后）
    recorderManager.onInterruptionEnd(() => {
      console.log('录音中断结束，自动恢复录音')
      if (asrConnectionStatus.value === 'connected') {
        recorderManager.resume()
      }
    })
  }

  // 已录制完指定帧大小的文件，会回调录音分片结果数据（优化ASR数据发送）
  recorderManager.onFrameRecorded((res) => {
    // 检查录音状态和ASR连接状态
    if (audioRecordStatus.value !== 'recording') {
      console.log('录音状态非recording，跳过发送音频数据，当前状态:', audioRecordStatus.value)
      return
    }

    if (asrConnectionStatus.value !== 'connected') {
      console.log('ASR服务未连接，跳过发送音频数据，连接状态:', asrConnectionStatus.value)
      return
    }

    const frameBuffer = res.frameBuffer

    try {
      // 优化：参考meeting模块的做法，确保音频数据正确发送
      if (!frameBuffer || frameBuffer.byteLength === 0) {
        console.warn('收到空的音频帧，跳过发送')
        return
      }

      // 发送音频数据到ByteDance ASR服务
      // 注意：在分段录音时，不要将 isLastFrame 设为 true，这会导致ASR服务认为录音结束
      // 只有在真正结束录音时才发送 isLastFrame = true
      const isRealLastFrame = res.isLastFrame && audioRecordStatus.value === 'completed'
      sendAudio(frameBuffer, isRealLastFrame)
      console.log('音频数据发送成功，帧大小:', frameBuffer.byteLength, '字节', isRealLastFrame ? '[最后一帧]' : '')

      // 优化：在分段录音时保持ASR服务连续性
      if (res.isLastFrame && audioRecordStatus.value !== 'completed') {
        console.log('录音段结束，但保持ASR服务连接状态以确保连续转录')
        // 不发送结束信号，让ASR服务继续等待下一段音频数据
      }
    } catch (error) {
      console.error('发送ASR音频数据失败:', error)
      // 如果发送失败，可能是连接问题，记录详细信息
      console.error('ASR连接状态:', asrConnectionStatus.value)
      console.error('录音状态:', audioRecordStatus.value)
      console.error('音频帧信息:', {
        byteLength: frameBuffer?.byteLength || 0,
        isLastFrame: res.isLastFrame
      })
    }
  })

  // 结束录音事件 - 采用meeting模块的自动重启策略
  recorderManager.onStop(async (res) => {
    console.log("录音停止")
    audioRecordStatus.value = 'idle'

    // 处理录音文件
    await handleRecordingComplete(res)

    // 处理自动重启 - 关键逻辑，参考meeting模块
    if (!isManualStop.value) {
      console.log('达到最大时长，自动重启录音')
      try {
        await startRecording() // 自动重启录音
      } catch (error) {
        console.error('自动重启录音失败:', error)
        // 自动重启失败时的处理
        setKeepScreenOn(false)
        recorderText.value = '录音已停止'
      }
    } else {
      // 手动停止时关闭屏幕常亮
      setKeepScreenOn(false)
    }
  })

  // 处理录音完成 - 简化为meeting模块的策略
  const handleRecordingComplete = async (res) => {
    try {
      console.log('=== handleRecordingComplete 被调用 ===')
      console.log('录音结果:', res)

      // 检查录音时长是否太短
      if (!res || !res.duration || res.duration < 1000) {
        console.log('录音时长太短，跳过处理')
        return
      }

      // 获取录音文件的临时路径
      const tempFilePath = res.tempFilePath
      const duration = Math.round(res.duration / 1000)

      console.log('=== 录音文件信息 ===')
      console.log('临时文件路径:', tempFilePath)
      console.log('录音时长(毫秒):', res.duration)
      console.log('录音时长(秒):', duration)
      console.log('文件大小:', res.fileSize || '未知')

      // 累加录音时长到总时长 - 修复重复计时问题
      totalRecordingDuration.value += duration
      console.log(`录音段时长: ${duration}秒, 总时长: ${totalRecordingDuration.value}秒`)

      // 注意：不要在这里修改 totalDisplayTime，它由计时器管理

      // 异步上传录音文件 - 参考meeting模块的处理方式
      const currentInteractionId = interactionId.value
      if (currentInteractionId && uploadAudioVideo) {
        console.log(`开始异步上传录音文件 - 索引: ${recordIndex.value}, interactionId: ${currentInteractionId}`)

        // 将文件信息添加到队列，参考meeting模块
        const uploadTask = uploadAudioVideo(
          tempFilePath,
          currentInteractionId,
          recordIndex.value,
          duration
        )
          .then((uploadResult) => {
            console.log(`录音文件上传成功 - 索引: ${recordIndex.value}`, uploadResult)
            uploadedFiles.value.push(uploadResult)
            return uploadResult
          })
          .catch((error) => {
            console.error(`录音文件上传失败 - 索引: ${recordIndex.value}`, error)
            throw error
          })

        // 将上传任务添加到待处理列表
        pendingUploads.value.push(uploadTask)
      }

      console.log('=== handleRecordingComplete 完成 ===')
    } catch (error) {
      console.error('录音处理失败:', error)
      uni.showToast({
        title: '录音处理失败',
        icon: 'none'
      })
    }
  }

  // 开始录音函数 - 采用meeting模块的简化策略
  const startRecording = async () => {
    try {
      console.log('=== 开始录音 ===')

      // 重置手动停止标记
      isManualStop.value = false

      // 开始录音
      recorderManager.start(options)
      audioRecordStatus.value = 'recording'

      // 确保屏幕常亮
      setKeepScreenOn(true)

      // 启动计时器 - 确保不重复创建
      if (timer1) {
        clearInterval(timer1)
      }
      timer1 = setInterval(countDown, 1000)

      // 启动ASR定时上传
      startAsrUploadTimer()

      console.log('=== 录音已开始 ===')
      console.log('当前ASR连接状态:', asrConnectionStatus.value)
      console.log('当前录音状态:', audioRecordStatus.value)

      return true
    } catch (error) {
      console.error('启动录音失败:', error)

      // 录音启动失败时的处理
      audioRecordStatus.value = 'idle'
      recorderText.value = '开始上课'
      setKeepScreenOn(false)

      throw error
    }
  }

  // 录音计时 - 修复计时逻辑，参考meeting模块的策略
  function countDown() {
    // 只有在录音状态时才增加计时
    if (audioRecordStatus.value === 'recording') {
      // 只增加总显示时间，不重复计时
      totalDisplayTime.value++
    }

    // 更新显示文本 - 只显示总录音时间
    recorderText.value = `录音中 ${Math.floor(totalDisplayTime.value / 60)}:${(
      totalDisplayTime.value % 60
    )
      .toString()
      .padStart(2, '0')}`
  }




  // 清空状态 - 简化为meeting模块的策略
  function clearState() {
    console.log('=== clearState 被调用 ===')
    console.log('当前 pendingUploads 数量:', pendingUploads.value.length)

    // 清理计时器
    clearInterval(timer1)

    // 清理ASR相关状态
    stopAsrUploadTimer()
    asrDataList.value = []
    currentAsrIndex = 0

    // 关闭屏幕常亮
    setKeepScreenOn(false)

    // 重置手动停止标志
    isManualStop.value = false

    // 重置跳转标志
    isNavigating.value = false

    console.log('=== clearState 完成 ===')
  }
  // 监听ASR实时结果 - 简化为meeting模块的策略
  watch(asrResult, (newResult) => {
    console.log('=== ASR结果更新 ===')
    console.log('新结果:', newResult)
    console.log('当前录音状态:', audioRecordStatus.value)
    console.log('ASR连接状态:', asrConnectionStatus.value)

    // 只有在录音状态且有新结果时才更新
    if (newResult && audioRecordStatus.value === 'recording') {
      // 检查结果是否为空或只包含空白字符
      const trimmedResult = newResult.trim()
      if (trimmedResult) {
        speechText.value = trimmedResult
        console.log('更新speechText为:', speechText.value)
      } else {
        console.log('ASR结果为空白，跳过更新')
      }
    } else {
      console.log('跳过ASR结果更新，原因:', {
        hasResult: !!newResult,
        isRecording: audioRecordStatus.value === 'recording',
        asrConnected: asrConnectionStatus.value === 'connected'
      })
    }
  })
  onMounted(async () => {
    recordAuthStore.initAuth()
    // 初始化ASR token
    await asrTokenStore.initTokenInfo()
  })
  onUnmounted(() => {
    clearState()
    // 停止ASR服务
    stopASR()
    // 停止ASR定时上传
    stopAsrUploadTimer()
    // 确保关闭屏幕常亮
    setKeepScreenOn(false)
  })
  return {
    // 状态
    audioRecordStatus,
    totalDisplayTime, // 累加的总显示时间
    speechText,
    recorderLoading,
    recorderText,
    isStartingClass, // 防抖状态

    // 方法
    startClass,
    pauseRecording,
    continueRecording,
    stopRecording,

    // 工具方法
    formatRecordingTime: (seconds) => {
      const minutes = Math.floor(seconds / 60)
      const secs = seconds % 60
      return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
  }
}
