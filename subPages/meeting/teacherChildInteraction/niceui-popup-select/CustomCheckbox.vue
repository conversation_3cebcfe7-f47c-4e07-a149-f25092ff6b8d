<template>
  <view
    class="custom-checkbox"
    :class="[{ 'is-checked': item.checked }]"
    @click="toggle"
    :style="[labelStyle]"
  >
    <input
      type="checkbox"
      :checked="item.checked"
      @change="onChange"
    />
    <text
      class="checkmark"
      :style="[circleStyle]"
    ></text>
    <text class="serve-info">{{ label }}</text>
  </view>
</template>

<script setup>
import { computed, defineProps, defineEmits } from "vue";

// 定义 props
const props = defineProps({
  value: <PERSON><PERSON><PERSON>,
  disabled: {
    type: <PERSON>olean,
    default: false,
  },
  label: {
    type: String,
    default: "",
  },
  fontSize: {
    type: String,
    default: "",
  },
  color: {
    type: String,
    default: "",
  },
  circleSize: {
    type: String,
    default: "",
  },
  circleColor: {
    type: String,
    default: "",
  },
  item: {
    type: Object,
    default: () => ({}),
  },
});

// 定义 emits
const emit = defineEmits(["input", "toggle", "change"]);
// 计算属性
const isChecked = computed({
  get() {
    return props.value;
  },
  set(val) {
    emit("input", val);
  },
});

const labelStyle = computed(() => {
  const styles = {};
  if (props.fontSize) {
    styles.fontSize = props.fontSize;
  }
  if (props.color) {
    styles.color = props.color;
  }
  return styles;
});

const circleStyle = computed(() => {
  const styles = {};
  if (props.circleSize) {
    styles.transform = props.circleSize;
  }
  if (props.circleColor && props.item.checked) {
    styles.backgroundColor = props.circleColor;
  }
  return styles;
});

// 方法
const toggle = () => {
  if (!props.disabled) {
    props.item.checked = !props.item.checked;
    emit("toggle", props.item.checked);
  }
};

const onChange = (event) => {
  emit("change", event.target.checked);
};
</script>
<style scoped lang="scss">
.custom-checkbox {
  display: flex;
  align-items: center;
  position: relative;
  padding-left: 1rpx;
  margin-bottom: 0rpx;
  cursor: pointer;
  font-size: 32rpx;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  margin: 35rpx 0;
}

.custom-checkbox input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkmark {
  position: absolute;
  top: 1rpx;
  margin-bottom: 0rpx;
  right: 0;
  height: 40rpx;
  width: 40rpx;
  /* background-color: #eee; */
  border: 1rpx solid #ddd;
  border-radius: 50%;
}

.custom-checkbox:hover input .checkmark {
  background-color: #ccc;
}

.custom-checkbox input:checked .checkmark {
  background-color: blue;
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

.custom-checkbox input:checked .checkmark:after {
  display: block;
}

.custom-checkbox .checkmark:after {
  width: 12rpx;
  height: 19rpx;
  border: solid white;
  border-width: 0 5rpx 5rpx 0;
  top: 50%;
  left: 50%;
  transform: translate(-45%, -65%) rotate(45deg);
  // -webkit-transform: rotate(45deg);
  // -ms-transform: rotate(45deg);
  // transform: rotate(45deg);
}

.is-checked .checkmark {
  background-color: blue;
}
.is-checked .checkmark:after {
  display: block;
}

.big-area {
  width: 160rpx;
  height: 220rpx;
  position: absolute;
  z-index: 10;
  top: -155rpx;
  left: -85rpx;
}
.checkbox-all {
  margin-top: 160rpx;
  margin-left: 63rpx;
}
</style>
