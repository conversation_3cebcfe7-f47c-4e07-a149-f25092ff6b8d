/**
 * 音频相关工具函数
 * 参考 mp-recorder-player-main 项目
 */

import { fetchClient, handleUpload } from '@/common/utils/fileUpload'

const Second = 1000
const Minute = 60 * Second
const Hour = 60 * Minute

/**
 * 格式化时间显示
 * @param {number} num - 毫秒数
 * @param {boolean} noHour - 是否不显示小时
 * @returns {string} 格式化后的时间字符串
 */
export const formatClock = (num, noHour = false) => {
  const hour = Math.floor(num / Hour)
  const minute = Math.floor((num - hour * Hour) / Minute)
  const second = Math.ceil((num - hour * Hour - minute * Minute) / Second)

  return (noHour ? [minute, second] : [hour, minute, second])
    .map(formatNumber)
    .join(':')
}

/**
 * 格式化数字，小于10的数字前面补0
 * @param {number} n - 数字
 * @returns {string} 格式化后的字符串
 */
const formatNumber = (n) => {
  const s = n.toString()
  return s[1] ? s : '0' + s
}

/**
 * 显示错误消息
 * @param {string} title - 错误标题
 */
export const showErrMsg = (title = '未知错误') => {
  uni.showToast({
    title,
    icon: 'none',
    duration: 2300,
    mask: true
  })
}

/**
 * 检查录音权限
 * @returns {Promise<boolean>} 是否有录音权限
 */
export const checkRecordPermission = () => {
  return new Promise((resolve) => {
    uni.getSetting({
      success: (res) => {
        const auth = res.authSetting['scope.record']
        if (auth === true) {
          // 用户已经同意授权
          resolve(true)
        } else if (auth === false) {
          // 用户已拒绝授权
          uni.showModal({
            title: '需要录音权限',
            content: '请在设置中打开麦克风权限',
            confirmText: '去设置',
            success: (modalRes) => {
              if (modalRes.confirm) {
                uni.openSetting({
                  success: (settingRes) => {
                    resolve(!!settingRes.authSetting['scope.record'])
                  }
                })
              } else {
                resolve(false)
              }
            }
          })
        } else {
          // 首次发起授权
          uni.authorize({
            scope: 'scope.record',
            success: () => resolve(true),
            fail: () => resolve(false)
          })
        }
      },
      fail: () => resolve(false)
    })
  })
}

/**
 * 获取文件信息
 * @param {string} filePath - 文件路径
 * @returns {Promise<object>} 文件信息
 */
export const getFileInfo = (filePath) => {
  return new Promise((resolve, reject) => {
    uni.getFileInfo({
      filePath,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 录音配置选项
 */
export const recordOptions = {
  duration: 600000, // 最长10分钟
  sampleRate: 44100,
  numberOfChannels: 2, // 改为双声道
  encodeBitRate: 192000,
  format: 'mp3',
  frameSize: 50,
  audioSource: 'auto' // 自动选择音频源
}

/**
 * 错误消息映射
 */
export const errorMsgMap = {
  'operateRecorder:fail auth deny': '请在设置中授权麦克风权限',
  'operateRecorder:fail is recording or paused': '操作过快，请稍后再试',
  'operateRecorder:fail recorder not start': '录音未开始'
}

/**
 * 防抖函数 - 防止快速点击
 */
let lastClickTime = 0
export const preventFastClick = (gap = 800) => {
  const now = Date.now()
  if (now - lastClickTime < gap) {
    return true
  }
  lastClickTime = now
  return false
}

/**
 * 创建录音管理器实例
 * @param {object} callbacks - 回调函数集合
 * @returns {object} 录音管理器实例
 */
export const createRecorderManager = (callbacks = {}) => {
  const recorderManager = uni.getRecorderManager()
  
  // 监听录音开始事件
  recorderManager.onStart(() => {
    console.log('录音开始')
    callbacks.onStart && callbacks.onStart()
  })
  
  // 监听录音结束事件
  recorderManager.onStop((res) => {
    console.log('录音结束', res)
    callbacks.onStop && callbacks.onStop(res)
  })
  
  // 监听录音错误事件
  recorderManager.onError((err) => {
    console.error('录音错误:', err)
    const errorMsg = errorMsgMap[err.errMsg] || err.errMsg || '录音失败'
    showErrMsg(errorMsg)
    callbacks.onError && callbacks.onError(err)
  })
  
  // 监听录音暂停事件
  recorderManager.onPause(() => {
    console.log('录音暂停')
    callbacks.onPause && callbacks.onPause()
  })
  
  // 监听录音继续事件
  recorderManager.onResume(() => {
    console.log('录音继续')
    callbacks.onResume && callbacks.onResume()
  })
  
  // 监听录音中断开始事件
  recorderManager.onInterruptionBegin(() => {
    console.log('录音被中断')
    callbacks.onInterruptionBegin && callbacks.onInterruptionBegin()
  })
  
  // 监听录音中断结束事件
  recorderManager.onInterruptionEnd(() => {
    console.log('录音中断结束')
    callbacks.onInterruptionEnd && callbacks.onInterruptionEnd()
  })
  
  return recorderManager
}

/**
 * 创建音频播放器实例
 * @param {object} callbacks - 回调函数集合
 * @returns {object} 音频播放器实例
 */
export const createAudioPlayer = (callbacks = {}) => {
  const innerAudioContext = uni.createInnerAudioContext()
  
  // 监听音频播放事件
  innerAudioContext.onPlay(() => {
    console.log('开始播放')
    callbacks.onPlay && callbacks.onPlay()
  })
  
  // 监听音频暂停事件
  innerAudioContext.onPause(() => {
    console.log('播放暂停')
    callbacks.onPause && callbacks.onPause()
  })
  
  // 监听音频停止事件
  innerAudioContext.onStop(() => {
    console.log('播放停止')
    callbacks.onStop && callbacks.onStop()
  })
  
  // 监听音频自然播放结束事件
  innerAudioContext.onEnded(() => {
    console.log('播放结束')
    callbacks.onEnded && callbacks.onEnded()
  })
  
  // 监听音频播放错误事件
  innerAudioContext.onError((err) => {
    console.error('播放错误:', err)
    const errorMsg = {
      10001: '系统错误',
      10002: '网络错误',
      10003: '文件错误',
      10004: '格式错误',
      '-1': '未知错误'
    }[err.errCode] || '播放失败'
    
    showErrMsg(errorMsg)
    callbacks.onError && callbacks.onError(err)
  })
  
  // 监听音频播放进度更新事件
  innerAudioContext.onTimeUpdate(() => {
    callbacks.onTimeUpdate && callbacks.onTimeUpdate({
      currentTime: innerAudioContext.currentTime,
      duration: innerAudioContext.duration
    })
  })
  
  // 监听音频进入可播放状态事件
  innerAudioContext.onCanplay(() => {
    console.log('音频可以播放')
    callbacks.onCanplay && callbacks.onCanplay()
  })
  
  // 监听音频加载中事件
  innerAudioContext.onWaiting(() => {
    console.log('音频加载中')
    callbacks.onWaiting && callbacks.onWaiting()
  })
  
  return innerAudioContext
}

/**
 * 格式化文件大小
 * @param {number} size - 文件大小（字节）
 * @returns {string} 格式化后的文件大小
 */
export const formatFileSize = (size) => {
  if (size < 1024) {
    return size + 'B'
  } else if (size < 1024 * 1024) {
    return Math.round(size / 1024) + 'KB'
  } else {
    return Math.round(size / (1024 * 1024)) + 'MB'
  }
}

/**
 * 生成录音文件名
 * @param {string} prefix - 文件名前缀
 * @param {string} extension - 文件扩展名
 * @returns {string} 生成的文件名
 */
export const generateRecordFileName = (prefix = '录音', extension = 'mp3') => {
  const now = new Date()
  const timestamp = now.getFullYear().toString().slice(-2) +
    (now.getMonth() + 1).toString().padStart(2, '0') +
    now.getDate().toString().padStart(2, '0') +
    now.getHours().toString().padStart(2, '0') +
    now.getMinutes().toString().padStart(2, '0')

  return `${prefix}_${timestamp}.${extension}`
}

/**
 * 生成基于时间戳的录音文件名
 * @param {string} extension - 文件扩展名
 * @returns {string} 生成的文件名
 */
export const generateTimestampFileName = (extension = 'mp3') => {
  const timestamp = Date.now()
  return `recording_${timestamp}.${extension}`
}

/**
 * 上传录音文件
 * @param {string} tempFilePath - 临时文件路径
 * @param {string} fileName - 文件名
 * @param {number} duration - 录音时长（毫秒）
 * @param {number} fileSize - 文件大小（字节）
 * @returns {Promise<object>} 上传结果
 */
export const uploadAudioFile = async (tempFilePath, fileName, duration, fileSize) => {
  try {
    // 获取上传客户端
    let client = []
    await fetchClient((value) => {
      client = value
    })

    if (client.length === 0) {
      throw new Error('获取上传客户端失败')
    }

    // 获取文件信息
    const fileInfo = await getFileInfo(tempFilePath)
    console.log('录音文件信息:', fileInfo)

    // 确保文件对象包含正确的属性
    const file = {
      path: tempFilePath,
      category: 10, // 音频文件类型
      filename: fileName,
      name: fileName,
      size: fileInfo.size || fileSize || 0,
      duration: duration || 0,
      fileType: 'audio'
    }

    console.log('准备上传文件，完整文件信息:', file)

    // 使用Upload组件中的方式上传文件
    const resource = await handleUpload(client, file)
    console.log('文件上传结果:', resource)

    if (resource && resource.uri) {
      return {
        success: true,
        data: resource
      }
    } else {
      throw new Error('上传失败，未返回有效资源')
    }
  } catch (error) {
    console.error('录音上传失败:', error)
    return {
      success: false,
      error: error.message || '上传失败'
    }
  }
}

/**
 * 显示录音继续确认弹窗
 * @param {Function} onContinue - 继续录音回调
 * @param {Function} onFinish - 结束录音回调
 */
export const showContinueRecordingModal = (onContinue, onFinish) => {
  uni.showModal({
    title: '录音时间已达10分钟',
    content: '是否继续录音？点击"是"将上传当前录音并继续，点击"否"将结束录音。',
    confirmText: '是',
    cancelText: '否',
    success: (res) => {
      if (res.confirm) {
        onContinue && onContinue()
      } else {
        onFinish && onFinish()
      }
    }
  })
}
