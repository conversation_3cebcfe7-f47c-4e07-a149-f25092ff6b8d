/**
 * ASRService - Base class for Automatic Speech Recognition services
 * This class defines the common interface that all ASR implementations should follow
 */
export class ASRService {
    /**
     * Constructor for the ASRService base class
     * @param {Object} options - Configuration options for the ASR service
     */
    constructor(options = {}) {
        this._status = 'disconnected'; // 'disconnected' | 'connecting' | 'connected' | 'error'
        this._resultText = '';
        this.error = null;
        this.onAudioStreamComplete = options.onAudioStreamComplete || null;
        this.audioConfig = options.audioConfig || {};
    }

    /**
     * Initialize the ASR service with credentials
     * This method should be called after constructor when credentials are available
     * @param {Object} credentials - Authentication credentials for the ASR service
     * @returns {Promise<boolean>} - True if initialization was successful
     */
    async initialize(credentials) {
        throw new Error('Method initialize() must be implemented by subclass');
    }

    /**
     * Start the ASR service
     * @returns {Promise<boolean>} - True if start was successful
     */
    async start() {
        throw new Error('Method start() must be implemented by subclass');
    }

    /**
     * Stop the ASR service
     */
    stop() {
        throw new Error('Method stop() must be implemented by subclass');
    }

    /**
     * Send audio data to the ASR service
     * @param {ArrayBuffer} data - Audio data buffer
     * @param {boolean} isLast - Whether this is the last chunk of audio data
     */
    sendAudio(data, isLast = false) {
        throw new Error('Method sendAudio() must be implemented by subclass');
    }

    /**
     * Get the current status of the ASR service
     * @returns {string} - Current status ('disconnected' | 'connecting' | 'connected' | 'error')
     */
    get status() {
        return this._status;
    }

    /**
     * Set the current status of the ASR service
     * @param {string} value - New status value
     */
    set status(value) {
        this._status = value;
    }

    /**
     * Get the current recognition result
     * @returns {string} - Current recognition result text
     */
    get resultText() {
        return this._resultText;
    }

    /**
     * Set the current recognition result
     * @param {string} value - New result text value
     */
    set resultText(value) {
        this._resultText = value;
    }

    /**
     * Get the current error if any
     * @returns {string|null} - Current error message or null if no error
     */
    getError() {
        return this.error;
    }

    /**
     * Set callback for when audio stream processing is complete
     * @param {Function} callback - Callback function to be called with the final result
     */
    setOnAudioStreamComplete(callback) {
        this.onAudioStreamComplete = callback;
    }
}
