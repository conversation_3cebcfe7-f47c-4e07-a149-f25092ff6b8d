import { ByteDanceASR } from './BytedanceASR';

/**
 * ASR service types
 * @enum {string}
 */
export const ASRServiceType = {
  BYTEDANCE: 'bytedance',
  TENCENT: 'tencent',
  BAIDU: 'baidu',
  XUNFEI: 'xunfei'
};

/**
 * ASR service audio config
 * @type {object}
 */
export const DefaultASRServiceAudioConfig = {
  sampleRate: 16000,
  numberOfChannels: 1,
  format: 'PCM', // 音频格式 PCM，mp3，aac，wav
};

/**
 * Factory class for creating ASR service instances
 */
export class ASRFactory {
  /**
   * Create an ASR service instance
   * @param {string} type - ASR service type
   * @param {Object} options - Configuration options
   * @returns {ASRService} - ASR service instance
   */
  static createASRService(type, options = {}) {
    switch (type) {
      case ASRServiceType.BYTEDANCE:
        return new ByteDanceASR(options);
      case ASRServiceType.TENCENT:
        // TODO: Implement Tencent ASR service
        throw new Error('Tencent ASR service not implemented yet');
      case ASRServiceType.BAIDU:
        // TODO: Implement Baidu ASR service
        throw new Error('Baidu ASR service not implemented yet');
      case ASRServiceType.XUNFEI:
        // TODO: Implement Xunfei ASR service
        throw new Error('Xunfei ASR service not implemented yet');
      default:
        throw new Error(`Unknown ASR service type: ${type}`);
    }
  }
} 