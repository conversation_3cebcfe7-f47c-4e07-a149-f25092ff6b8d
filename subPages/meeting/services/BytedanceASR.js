import { ASRService } from './ASRService';
import { v4 as uuidv4 } from 'uuid';
import { TextEncoder, TextDecoder } from 'text-decoding';
import pako from 'pako';

// Protocol constants
const PROTOCOL_VERSION = 0b0001;
const DEFAULT_HEADER_SIZE = 0b0001;

const PROTOCOL_VERSION_BITS = 4;
const HEADER_BITS = 4;
const MESSAGE_TYPE_BITS = 4;
const MESSAGE_TYPE_SPECIFIC_FLAGS_BITS = 4;
const MESSAGE_SERIALIZATION_BITS = 4;
const MESSAGE_COMPRESSION_BITS = 4;
const RESERVED_BITS = 8;

// Message Type:
const CLIENT_FULL_REQUEST = 0b0001;
const CLIENT_AUDIO_ONLY_REQUEST = 0b0010;
const SERVER_FULL_RESPONSE = 0b1001;
const SERVER_ACK = 0b1011;
const SERVER_ERROR_RESPONSE = 0b1111;

// Message Type Specific Flags
const NO_SEQUENCE = 0b0000;  // no check sequence
const POS_SEQUENCE = 0b0001;
const NEG_SEQUENCE = 0b0010;
const NEG_SEQUENCE_1 = 0b0011;

// Message Serialization
const NO_SERIALIZATION = 0b0000;
const THRIFT = 0b0011;
const CUSTOM_TYPE = 0b1111;

// Message Compression
const NO_COMPRESSION = 0b0000;
const JSON_SERIALIZATION = 0b0001;
const GZIP_COMPRESSION = 0b0001;

/**
 * ByteDanceASR - Implementation of ASRService for ByteDance's ASR service
 */
export class ByteDanceASR extends ASRService {
  /**
   * Constructor for ByteDanceASR
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super(options);

    // Initialize properties
    this.ws = null;
    this.retryCount = 0;
    this.maxRetries = 3;
    this.retryInterval = 3000;
    this.sequence = 1;
    this.reqId = '';
    this.audioQueue = [];
    this.isProcessing = false;
    this.previousResultText = '';

    // Credentials (will be set later via initialize)
    this.appid = '';
    this.uid = '';
    this.token = '';
    this.cluster = '';

    // Request config
    this.requestConfig = options.requestConfig || {};
  }

  /**
   * Initialize the ASR service with credentials
   * @param {Object} credentials - Authentication credentials
   * @returns {Promise<boolean>} - True if initialization was successful
   */
  async initialize(credentials) {
    try {
      this.appid = credentials.appid || '';
      this.uid = credentials.uid || '';
      this.token = credentials.token || '';
      this.cluster = credentials.cluster || '';

      return true;
    } catch (error) {
      console.error('ByteDanceASR initialization failed:', error);
      this.error = `初始化失败: ${error.message}`;
      return false;
    }
  }

  /**
   * Convert length to big-endian bytes
   * @private
   * @param {Uint8Array} payloadBytes - Payload bytes
   * @returns {Uint8Array} - Big-endian bytes
   */
  lengthToBytesBigEndian(payloadBytes) {
    const length = payloadBytes.length;
    const buffer = new ArrayBuffer(4);
    const view = new DataView(buffer);

    // Write length in big-endian order
    view.setInt32(0, length, false);  // false indicates big-endian

    // Return byte array
    return new Uint8Array(buffer);
  }

  /**
   * Generate protocol header
   * @private
   * @param {Object} options - Header options
   * @returns {Uint8Array} - Header bytes
   */
  generateHeader({
    version = PROTOCOL_VERSION,
    messageType = CLIENT_FULL_REQUEST,
    messageTypeSpecificFlags = NO_SEQUENCE,
    serialMethod = JSON_SERIALIZATION,
    compressionType = GZIP_COMPRESSION,
    reservedData = 0x00,
    extensionHeader = []
  } = {}) {
    // Validate protocol header parameters
    if (serialMethod !== 0b0001 || compressionType !== 0b0001) {
      throw new Error('Invalid serialization/compression method');
    }

    // Calculate the header size based on the extensionHeader
    const headerSize = Math.floor(extensionHeader.length / 4) + 1;

    // Create a byte array
    let header = [];

    // Add protocol version and header size
    header.push((version << 4) | headerSize);

    // Add message type and message type specific flags
    header.push((messageType << 4) | messageTypeSpecificFlags);

    // Add serialization method and compression type
    header.push((serialMethod << 4) | compressionType);

    // Add reserved data
    header.push(reservedData);

    // Add extension header (if any)
    header = header.concat(extensionHeader);

    return new Uint8Array(header);
  }

  /**
   * Construct full request parameters
   * @private
   * @returns {Uint8Array} - Encoded request parameters
   */
  constructFullRequest() {
    if (!this.appid || !this.uid || !this.token) {
      throw new Error('构建请求参数失败');
    }

    const { format, sampleRate, numberOfChannels } = this.audioConfig;

    const fullRequest = {
      app: {
        appid: this.appid,
        cluster: this.cluster,
        token: this.token
      },
      user: {
        uid: this.uid
      },
      request: {
        reqid: this.reqId,
        nbest: 1,
        workflow: this.requestConfig?.workflow || 'audio_in,resample,partition,vad,fe,decode,itn,nlu_punctuate',
        show_language: this.requestConfig?.showLanguage || false,
        show_utterances: this.requestConfig?.showUtterances || false,
        result_type: this.requestConfig?.resultType || 'full',
        sequence: this.sequence
      },
      audio: {
        format: format.toLowerCase() === 'pcm' ? 'raw' : format,
        rate: sampleRate || 16000,
        bits: 16,
        channel: numberOfChannels || 1,
      }
    };

    console.log('fullRequest', fullRequest);

    return new TextEncoder().encode(JSON.stringify(fullRequest));
  }

  /**
   * Parse server response
   * @private
   * @param {Uint8Array} res - Response bytes
   * @returns {Object|null} - Parsed response or null if parsing failed
   */
  parseResponse(res) {
    try {
      // Extract protocol header information
      const headerSize = res[0] & 0x0f;
      const messageType = res[1] >> 4;
      const serializationMethod = res[2] >> 4;
      const messageCompression = res[2] & 0x0f;

      const payload = res.slice(headerSize * 4);

      let result = {};
      let payloadMsg = null;
      let payloadSize = 0;

      // Parse different message types
      if (messageType === SERVER_FULL_RESPONSE) {
        payloadSize = payload.slice(0, 4).reduce((acc, byte, index) => acc + (byte << (8 * (3 - index))), 0);
        payloadMsg = payload.slice(4);
      } else if (messageType === SERVER_ACK) {
        const seq = payload.slice(0, 4).reduce((acc, byte, index) => acc + (byte << (8 * (3 - index))), 0);
        result.seq = seq;

        if (payload.length >= 8) {
          payloadSize = payload.slice(4, 8).reduce((acc, byte, index) => acc + (byte << (8 * (3 - index))), 0);
          payloadMsg = payload.slice(8);
        }
      } else if (messageType === SERVER_ERROR_RESPONSE) {
        const code = payload.slice(0, 4).reduce((acc, byte, index) => acc + (byte << (8 * (3 - index))), 0);
        result.code = code;

        payloadSize = payload.slice(4, 8).reduce((acc, byte, index) => acc + (byte << (8 * (3 - index))), 0);
        payloadMsg = payload.slice(8);
      }

      if (payloadSize === 0) {
        return { error: new Error("payload size is 0") };
      }

      if (payloadMsg === null) {
        return result;
      }

      // Decompress message
      if (messageCompression === GZIP_COMPRESSION) {
        payloadMsg = pako.ungzip(payloadMsg);
      }

      // Deserialize message
      if (serializationMethod === JSON_SERIALIZATION) {
        payloadMsg = JSON.parse(new TextDecoder().decode(payloadMsg));
      } else if (serializationMethod !== NO_SERIALIZATION) {
        payloadMsg = new TextDecoder().decode(payloadMsg);
      }

      result.payload = payloadMsg;
      result.payloadSize = payloadSize;
      return result;
    } catch (e) {
      console.error('响应解析失败:', e);
      this.error = `响应解析失败: ${e.message}`;
      return null;
    }
  }

  /**
   * Generate payload with header and data
   * @private
   * @param {Uint8Array} header - Header bytes
   * @param {Uint8Array} data - Data bytes
   * @returns {Uint8Array} - Complete payload
   */
  generatePayload(header, data) {
    const payloadBytes = pako.gzip(data);
    const compressedByted = this.lengthToBytesBigEndian(payloadBytes);
    const requestPayload = new Uint8Array(header.length + compressedByted.length + payloadBytes.length);

    requestPayload.set(header);
    requestPayload.set(compressedByted, header.length);
    requestPayload.set(payloadBytes, header.length + compressedByted.length);

    return requestPayload;
  }

  /**
   * Reconnect to the ASR service
   * @private
   * @returns {Promise<boolean>} - True if reconnection was successful
   */
  async reconnect() {
    if (this.retryCount >= this.maxRetries) {
      this.error = '重连次数超过限制，请检查网络后重试';
      this.status = 'error';
      return false;
    }

    try {
      this.status = 'connecting';
      this.retryCount++;
      uni.showToast({
        title: `语音识别服务断开，正在进行第 ${this.retryCount} 次重连...`,
        icon: 'none',
        duration: 2000
      });

      // Save current recognition result before reconnecting
      this.previousResultText = this.resultText;
      await this.connect();
      return true;
    } catch (err) {
      uni.showToast({
        title: `重连失败，${err.message || '请检查网络后重试'}`,
        icon: 'none',
        duration: 2000
      });
      console.error('重连失败:', err);

      // Retry after delay
      await new Promise(resolve => setTimeout(resolve, this.retryInterval));
      return await this.reconnect();
    }
  }

  /**
   * Connect to the ASR service
   * @private
   * @returns {Promise<boolean>} - True if connection was successful
   */
  async connect() {
    this.reqId = uuidv4();
    return new Promise((resolve, reject) => {
      try {
        this.status = 'connecting';
        const requestParams = this.constructFullRequest();
        const fullClientRequest = this.generatePayload(this.generateHeader(), requestParams);

        // Establish WebSocket connection
        this.ws = uni.connectSocket({
          url: 'wss://openspeech.bytedance.com/api/v2/asr',
          header: {
            'Authorization': `Bearer; ${this.token}`,
          },
          success: (res) => {
            console.log('准备建立websocket连接...:', res);
          },
          fail: (err) => {
            console.error('WebSocket连接失败:', err);
            reject(new Error(`WebSocket连接失败: ${err.errMsg}`));
          }
        });

        // Add connection timeout handler
        const timeout = setTimeout(() => {
          reject(new Error('WebSocket连接超时'));
        }, 10000); // 10 seconds timeout

        this.ws.onOpen(() => {
          console.log('WebSocket连接成功, 发送第一帧');
          clearTimeout(timeout); // Clear timeout timer

          this.ws.send({
            data: fullClientRequest.buffer, // Use buffer for real device testing
            success: () => {
              console.log('初始请求发送成功，开始处理音频数据');
              this.status = 'connected';
              this.retryCount = 0;
              resolve(true);
            },
            fail: (err) => {
              console.error('初始请求发送失败:', err);
              reject(new Error(`初始请求发送失败: ${err.errMsg}`));
            }
          });
        });

        this.ws.onMessage((res) => {
          if (res.data instanceof ArrayBuffer) {
            this.handleMessage(res.data);
          } else {
            console.error('收到非二进制格式消息:', typeof res.data);
          }
        });

        this.ws.onClose((res) => {
          console.log('WebSocket连接已关闭:', res);
          this.handleClose();
        });

        this.ws.onError((err) => {
          console.error('WebSocket错误:', err);
          this.handleError(err);
          reject(new Error(`WebSocket错误: ${err.errMsg}`));
        });

      } catch (error) {
        console.error('[ASR] 连接失败:', error);
        this.status = 'error';
        this.error = `连接失败: ${error.message}`;
        reject(error);
      }
    });
  }
  /**
   * Process audio queue
   * @private
   * @returns {Promise<void>}
   */
  async processQueue() {
    if (this.isProcessing || !this.audioQueue.length) return;

    // Check connection status
    if (this.status !== 'connected') {
      console.log('WebSocket未连接，尝试重连...');
      const reconnected = await this.reconnect();
      if (!reconnected) {
        throw new Error('WebSocket重连失败，无法发送音频数据');
      }
    }

    this.isProcessing = true;
    const { data, isLast } = this.audioQueue.shift();

    try {
      const header = this.generateHeader({
        messageType: CLIENT_AUDIO_ONLY_REQUEST,
        messageTypeSpecificFlags: isLast ? NEG_SEQUENCE : NO_SEQUENCE
      });

      const audioOnlyRequest = this.generatePayload(header, data);

      this.ws.send({
        data: audioOnlyRequest.buffer, // Use buffer for real device testing
        success: () => {
          console.log('音频数据发送成功');
        },
        fail: (err) => {
          console.error('音频数据发送失败:', err);
          throw new Error(`音频数据发送失败: ${err.errMsg}`);
        }
      });
      this.sequence = isLast ? -Math.abs(this.sequence) : this.sequence + 1;
    } catch (err) {
      this.error = err.message;
      throw err;
    } finally {
      this.isProcessing = false;
      this.processQueue();
    }
  }

  /**
   * Start the ASR service
   * @returns {Promise<boolean>} - True if start was successful
   */
  async start() {
    this.error = null;
    this.retryCount = 0;
    this.resultText = '';
    this.previousResultText = '';
    try {
      await this.connect();
      return true;
    } catch (err) {
      console.error('启动ASR失败:', err);
      return false;
    }
  }

  /**
   * Stop the ASR service
   */
  stop() {
    if (this.ws) {
      this.ws.close({
        success: () => {
          this.status = 'disconnected';
          this.retryCount = this.maxRetries; // Prevent auto-reconnect
        }
      });
    }
  }

  /**
   * Send audio data to the ASR service
   * @param {ArrayBuffer} data - Audio data buffer
   * @param {boolean} isLast - Whether this is the last chunk of audio data
   */
  sendAudio(data, isLast = false) {
    this.audioQueue.push({
      data: data,
      isLast: isLast
    });

    this.processQueue();
  }

  /**
   * Handle incoming WebSocket message
   * @private
   * @param {ArrayBuffer} data - Message data
   */
  handleMessage(data) {
    const response = this.parseResponse(new Uint8Array(data));
    console.log('解析服务器消息:', response);

    if (response.payload?.code === 1000) { // Audio format parsing successful
      console.log('音频格式解析成功');
      // Append new recognition result to previous result
      this.resultText = this.previousResultText + (response.payload.result?.at(0)?.text || '');

      // Check if this is the response for the last frame
      if (response.payload?.sequence < 0) {
        console.log('收到结束标记');
        // Trigger callback because we've received the server's response to the last frame
        if (this.onAudioStreamComplete) {
          this.onAudioStreamComplete(this.resultText);
        }
        this.stop();
      }
    } else {
      console.log('收到错误消息:', response.payload.message);
    }
  }

  /**
   * Handle WebSocket close event
   * @private
   */
  handleClose() {
    this.status = 'disconnected';
    if (this.retryCount < this.maxRetries) {
      console.log('连接断开，准备重连...');
      this.reconnect().catch(err => {
        console.error('重连失败:', err);
        this.error = `重连失败: ${err.message}`;
      });
    }
  }

  /**
   * Handle WebSocket error event
   * @private
   * @param {Object} err - Error object
   */
  handleError(err) {
    console.error('ASR Error:', err);
    this.status = 'error';
    this.error = `WebSocket错误: ${err.errMsg}`;
    this.handleClose();
  }
}