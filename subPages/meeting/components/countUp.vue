<template>
  <view class="count-up">
    <text class="count-up-text">
      {{ formattedTime }}
    </text>
  </view>
</template>

<script setup>
import { useCountUp } from '../hooks/useCountup'
import { onMounted } from 'vue'

const props = defineProps({
  autoStart: {
    type: Boolean,
    default: false
  },
  initialValue: {
    type: Number,
    default: 0
  }
})

const { formattedTime, start, pause, stop, reset, resume } = useCountUp(props.initialValue)

// 如果设置了自动开始，则在组件挂载后开始计时
onMounted(() => {
  if (props.autoStart) {
    start()
  }
})

// 暴露方法给父组件
defineExpose({
  start,
  pause,
  stop,
  resume,
  reset
})
</script>

<style lang="scss" scoped>
.count-up {
  display: flex;
  justify-content: center;
  align-items: center;
}

.count-up-text {
  font-size: 48rpx;
  color: #000000;
  font-weight: 600;
}
</style>
