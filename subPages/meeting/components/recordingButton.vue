<template>
  <view class="recording-button-container" @tap="handleClick" :style="containerStyle">
    <view class="recording-button" :class="{
      active: modelValue !== 'notStart',
      inactive: modelValue === 'notStart',
    }">
      <text v-if="modelValue === 'notStart'" class="recording-button-text">{{ text }}</text>
      <up-icon :name="iconName" v-else class="recording-button-icon" color="#FFF" size="100rpx"></up-icon>
    </view>
  </view>
</template>

<script setup>
import { computed } from "vue";

const props = defineProps({
  text: {
    type: String,
    default: "开始录音",
  },
  modelValue: {
    type: String,
    default: "notStart",
  },
  containerStyle: {
    type: Object,
  },
});

const emit = defineEmits(["click"]);

const iconName = computed(() => {
  switch (props.modelValue) {
    case 'recording':
      return 'pause';
    case 'pause':
      return 'play-right-fill';
    default:
      return 'play-right-fill';
  }
});

const handleClick = (event) => {
  emit("click", event);
};

</script>
<style lang="scss" scoped>
.recording-button-container {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
}

.recording-button {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.recording-button-text {
  font-size: 28rpx;
  color: #fff;
}

.active {
  background-color: #fd5a5e;
}

.inactive {
  background-color: #367cff;
}
</style>
