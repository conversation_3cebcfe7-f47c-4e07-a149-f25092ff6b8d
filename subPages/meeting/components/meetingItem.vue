<template>
  <card @tap="goToMeetingDetail">
    <view class="meeting-item-container">
      <!-- 使用a.png作为默认图片去fallback展示 -->
      <view class="meeting-item-cover-container" :class="{ 'meeting-item-cover-border': hasCoverBorder }">
        <up-image :fade="false" :src="coverImg" mode="aspectFill" :width="imgSize" :height="imgSize" radius="14rpx"
          bgColor="transparent" style="display: flex;justify-content: center;align-items: center;">
          <template #error>
            <view class="meeting-item-default-cover" :style="{ width: imgSize, height: imgSize }">
              <up-icon name="play-right-fill" size="48rpx" color="#3F79FF"></up-icon>
            </view>
          </template>
        </up-image>
      </view>


      <view class="meeting-item-content">
        <view class="meeting-item-content-header">
          <text class="meeting-item-content-title">{{ meeting.meetingName }}</text>
          <text class="meeting-item-content-time">{{ calcMeetingTime(meeting.meetingBeginTime) }}</text>
        </view>

        <view class="meeting-item-content-info">
          <up-tag text="类别" size="mini" bgColor="#F3F7FF" color="#3F79FF" borderColor="transparent"></up-tag>
          <text class="meeting-item-content-text">{{ getMeetingTypeByValue(meeting.meetingType) }}</text>
          <up-tag text="主持人" size="mini" bgColor="rgba(240, 145, 77, 0.12)" color="#F0914D" borderColor="
transparent"></up-tag>
          <text class="meeting-item-content-text meeting-item-content-text-host">{{ hostName }}</text>
        </view>
      </view>
    </view>
  </card>
</template>
<script setup>
import dayjs from 'dayjs';
import Card from '@/components/card/card.vue';
import { useMeetingTypes } from '../hooks/useMeetingTypes';
import { computed } from 'vue';
const { getMeetingTypeByValue } = useMeetingTypes();
import { getArrayFieldsToString } from '../utils';

const hostName = computed(() => {
  return getArrayFieldsToString(props.meeting.partnerList, {
    enableFilter: true,
    filterValue: 1,
  });
});

const coverImg = computed(() => props.meeting.meetingNotePicList.at(0)?.uri ?? 'a.png')

const hasCoverBorder = computed(() => {
  return props.meeting.analyzeStatus === 0
})

const imgSize = computed(() => {
  return hasCoverBorder.value ? '84rpx' : '88rpx'
})

const calcMeetingTime = (time) => {
  const target = dayjs(time);
  if (!target.isValid()) return '';

  const now = dayjs();
  const diffDays = now.diff(target, 'day');

  // 3天内
  if (diffDays <= 3) {
    if (diffDays === 0) {
      return target.format('HH:mm');
    }
    return `${diffDays}天前 ${target.format('HH:mm')}`;
  }

  // 今年
  if (now.year() === target.year()) {
    return target.format('M月D日 HH:mm');
  }

  // 非今年
  return target.format('YYYY年M月D日 HH:mm');
}

const props = defineProps({
  meeting: Object
});

const goToMeetingDetail = () => {
  uni.navigateTo({
    url: `/subPages/meeting/meetingDetail/index?id=${props.meeting.meetingId}`,
    fail: (err) => {
      console.log(err);
      uni.navigateTo({
        url: '/subPages/meeting/index'
      });
    }
  });
}
</script>
<style lang="scss" scoped>
.meeting-item-container {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.meeting-item-content {
  flex: 1;
}

.meeting-item-content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.meeting-item-content-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.meeting-item-content-time {
  font-size: 24rpx;
  color: #808080;
}

.meeting-item-content-info {
  display: flex;
  align-items: center;
}

.meeting-item-content-text {
  font-size: 28rpx;
  color: #808080;
  margin-left: 16rpx;
  margin-right: 24rpx;

}

.meeting-item-content-text-host {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 0;
}

.meeting-item-default-cover {
  // width: 88rpx;
  // height: 88rpx;
  background-color: #F3F7FF;
  border-radius: 14rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.meeting-item-cover-container {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border-radius: 14rpx;
}

.meeting-item-cover-border {
  border-radius: 14rpx;
  overflow: hidden;
  width: 88rpx;
  height: 88rpx;
  background: #3F79FF;
  /* 底部部分的蓝色 */
  background-image:
    linear-gradient(to right, #3F79FF 70%, white 30%);
  /* 上半部分的蓝色和白色分割 */
  background-size: 100% 70%, 100% 30%;
  /* 设置上下区域的尺寸比例 */
  background-repeat: no-repeat;
}

.meeting-item-content {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
</style>