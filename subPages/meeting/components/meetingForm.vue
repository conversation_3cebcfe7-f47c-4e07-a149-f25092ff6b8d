<template>
  <up-form ref="formRef" :model="formData" :rules="rules" label-width="auto" labelPosition="top">
    <view class="container">
      <card :containerStyle="{
        padding: '0 20rpx 10rpx',
      }">
        <up-form-item label="会议名称" prop="meetingName" required>
          <up-input border="none" v-model="formData.meetingName" placeholder="请输入会议名称" />
        </up-form-item>
      </card>

      <card :containerStyle="{
        padding: '0 20rpx 10rpx',
      }">
        <up-form-item label="会议类别" prop="meetingType" required>
          <uni-data-select class="meeting-type-select" v-model="formData.meetingType"
            :localdata="computedMeetingTypeList" clear placeholder="请选择会议类别" />
        </up-form-item>
      </card>

      <card :containerStyle="{
        padding: '0 20rpx 10rpx',
      }">
        <up-form-item label="会议主持人" prop="host" required>
          <zxz-uni-data-select v-model="formData.host" :localdata="userList" clear multiple filterable />
        </up-form-item>
      </card>

      <card :containerStyle="{
        padding: '0 20rpx 10rpx',
      }">
        <up-form-item label="会议参与人" prop="attendees">
          <zxz-uni-data-select multiple v-model="formData.attendees" :localdata="userList" clear filterable />
        </up-form-item>
      </card>
      <card :containerStyle="{
        padding: '0 20rpx 10rpx',
      }">
        <up-form-item label="会议同步" prop="syncAttendees">
          <zxz-uni-data-select multiple v-model="formData.syncAttendees" :localdata="userListWithoutSelf" clear
            filterable />
        </up-form-item>
      </card>
      <card :containerStyle="{
        padding: '0 20rpx 10rpx',
      }">
        <up-form-item label="会议照片" prop="meetingNotePicList">
          <Upload type="image" :value="formData.meetingNotePicList" @callback="handleUploadCallback"
            @emitDelFile="delFile" :showDel="showDel" :gridStyle="{
              justifyItems: 'flex-start',
            }" />
        </up-form-item>
      </card>
    </view>
  </up-form>
</template>

<script setup>
import { ref, computed, nextTick, watch, onMounted } from "vue";
import { storeToRefs } from 'pinia'
import { useMeetingTypes } from "../hooks/useMeetingTypes.js";
import { useMeetingStore } from '../stores/meeting'
import { useUserStore } from '../stores/user'

const meetingStore = useMeetingStore()
const userStore = useUserStore()
const { formData } = storeToRefs(meetingStore)
const { userList } = storeToRefs(userStore)
const { setFormInstance } = meetingStore

const { meetingTypeList } = useMeetingTypes({ isShowAll: false });
const showDel = ref(true)

const computedMeetingTypeList = computed(() => {
  return meetingTypeList.map((item) => ({
    value: item.value,
    text: item.name,
  }))
})

const userListWithoutSelf = computed(() => {
  const { userId } = userStore.getMe();
  return userList.value.filter(item => item.value !== userId)
})

const formRef = ref(null)

// 当表单实例创建后，将其设置到 store 中
onMounted(() => {
  setFormInstance(formRef.value)
})

// 文件上传回调
const handleUploadCallback = (list) => {
  formData.value.meetingNotePicList.push(...list)
}

// 删除文件
const delFile = (_item, index) => {
  formData.value.meetingNotePicList = formData.value.meetingNotePicList.filter(
    (_, i) => i !== index
  )
}

// 表单验证规则
const rules = {
  meetingName: [{ required: true, message: "请输入会议名称", trigger: "blur" }],
  meetingType: [
    {
      type: "number",
      required: true,
      validator: (_rule, value, callback) => {
        if (!value) {
          callback(new Error("会议类别没有选择"))
        } else {
          callback()
        }
      },
    },
  ],
  host: [
    {
      required: true,
      type: "array",
      validator: (_rule, value, callback) => {
        if (!value || value.length === 0) {
          callback(new Error("至少需要选择一名主持人"));
        } else {
          callback();
        }
      },
    },
  ],
}

watch([() => formData.value.meetingType, () => formData.value.host], () => {
  nextTick(() => {
    formRef.value?.validateField('meetingType')
    formRef.value?.validateField('host')
  })
})

</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
</style>
<style lang="scss">
.form-item-select {
  :deep(.uni-select) {
    border: none !important;
    border-bottom: none !important;
  }
}
</style>
