<template>
  <up-overlay :show="showPopup" @click="showPopup = false" duration="50" @tap.stop></up-overlay>

  <view class="recording-button-container" :style="footerContainerStyle">
    <image v-if="showPopup" class="img" src="../../../static/ArcPopup.png" mode="widthFix"></image>
    <view class="overlay-title" v-if="showPopup">
      <text class="overlay-title-text">会议录制中</text>
      <view class="overlay-title-button" @tap.stop="handleConfirmModalOpen">完成
      </view>
    </view>

    <view class="overlay-countup" :style="{ display: showPopup ? 'flex' : 'none' }">
      <count-up ref="countUpRef" :initialValue="0" />
    </view>
    <up-modal showCancelButton @cancel="handleModalCancel" :show="showConfirmModal" content='确认后，会议纪要将即刻生成，请确保会议已结束。'
      asyncClose @confirm="handleFinish"></up-modal>
    <view class="default-view" @click="showPopup = true" @tap.stop>
      <recording-button v-model="recordingStatus" @tap.stop @click="handleRecordingButtonClick"
        :containerStyle="{ transform: 'translateY(-50%)' }" />
      <view class="footer-text" :class="{ 'footer-text-in-popup': showPopup, }">
        <text>注意事项：</text>
        <text>1、结束录音后，系统将总结会议纪要、会议待办</text>
        <text>2、为保证会议纪要准确性，请勿中途退出小程序</text>
        <text>3、请将话筒靠近讲话人，以保证声音清晰</text>
      </view>
    </view>
  </view>

</template>
<script setup>
import { ref, computed, onMounted } from "vue";
import { storeToRefs } from 'pinia';

import CountUp from "./countUp.vue";
import RecordingButton from "./recordingButton.vue";
import { useRecorderV2 } from "../hooks/useRecorderV2";
import { useMeetingStore } from '../stores/meeting'
import { uploadAudioFile, endMeeting } from '../api/meeting'
import { useAsrTokenStore } from '../stores/asrToken';
import { ASRFactory, ASRServiceType, DefaultASRServiceAudioConfig } from '../services/ASRFactory';

// 获取ASR
const asrTokenStore = useAsrTokenStore();
const { appid, uid, token, cluster } = storeToRefs(asrTokenStore)

// ASR 服务类型
const asrTypes = ASRServiceType;

// ASR 服务实例
const asrService = ref(null);
// const asrServiceCompleted = ref(false);

// 创建 ASR 服务实例
const initAsrService = async () => {
  console.log('initAsrService');
  console.log('appid', appid.value);
  console.log('uid', uid.value);
  console.log('token', token.value);
  console.log('cluster', cluster.value);
  try {
    // 初始化 ASR 服务
    if (appid.value && uid.value && token.value) {
      await asrService.value.initialize({
        appid: appid.value,
        uid: uid.value,
        token: token.value,
        cluster: cluster.value
      });
    }
  } catch (error) {
    console.error('初始化 ASR 服务失败:', error);
    uni.showToast({
      title: '初始化 ASR 服务失败: ' + error.message,
      icon: 'none'
    });
    return null;
  }
};

const countUpRef = ref(null);
const showConfirmModal = ref(false);
const meetingStore = useMeetingStore()
const { meetingId } = storeToRefs(meetingStore)

const handleRecordingFileUpload = async (index, tempFilePath) => {
  const res = await uploadAudioFile(tempFilePath, meetingStore.meetingId, index)
  if (index < 0 && asrService.value.status === 'disconnected') {
    handleRecordingFinish(asrService.value.resultText);
  }
  return res;
}

const handleRecordingFinish = async (text) => {
  try {
    await endMeeting({
      asrOnlineFlag: text.length > 0,
      meetingId: meetingId.value,
      meetingMinute: text || '',
    })

  } catch (error) {
    uni.showToast({
      title: error.message || '会议结束失败',
      icon: 'none'
    })
  } finally {
    showConfirmModal.value = false;
    uni.redirectTo({
      url: '/subPages/meeting/index',
    })
  }
}

const { start, pause, resume, stop } = useRecorderV2({
  handleRecordingStop: () => countUpRef.value.stop(),
  handleRecordingFileUpload,
  handleRecordingFinish,
  asrService,
});


const showPopup = ref(false);
const recordingStatus = ref("notStart");

const footerContainerStyle = computed(() => {
  return {
    zIndex: showPopup.value ? 999999 : 'unset',
    height: showPopup.value ? '700rpx' : 'inherit',
    background: showPopup.value ? '#FFFFFF' : 'transparent',
    borderRadius: showPopup.value ? '0' : '28rpx',
    margin: showPopup.value ? '0 -32rpx' : 'unset',
  }
})

const openPopup = () => {
  if (recordingStatus.value !== "notStart") {
    showPopup.value = true;
  }
};

const handleRecordingStart = () => {
  switch (recordingStatus.value) {
    case 'notStart':
      recordingStatus.value = 'recording';
      openPopup();
      countUpRef.value?.start();
      start();
      break;
    case 'recording':
      recordingStatus.value = 'pause';
      countUpRef.value.pause();
      pause();
      break;
    case 'pause':
      recordingStatus.value = 'recording';
      countUpRef.value.resume();
      resume();
      break;
  }
}

const handleRecordingButtonClick = async () => {
  if (meetingStore.meetingId !== null) {
    handleRecordingStart()
  } else {
    try {
      // 获取表单实例并验证
      const formInstance = meetingStore.getFormInstance()
      if (!formInstance) {
        throw new Error('表单实例未找到')
      }

      const isValid = await formInstance.validate()
      if (!isValid) {
        throw new Error('表单验证失败')
      }

      // 验证通过后提交表单
      const res = await meetingStore.submitForm()
      if (res) {
        handleRecordingStart()
      }
    } catch (error) {
      uni.showToast({
        title: error.message || '会议创建失败',
        icon: 'none'
      })
    }
  }
}

const handleConfirmModalOpen = () => {
  showConfirmModal.value = true;
}

const handleFinish = async () => {
  countUpRef.value.stop();
  recordingStatus.value = "notStart";
  stop();
};

const handleModalCancel = () => {
  showConfirmModal.value = false;
}

onMounted(async () => {
  // 保持屏幕常亮
  uni.setKeepScreenOn({
    keepScreenOn: true,
    success: () => {
      console.log('保持屏幕常亮成功')
    },
    fail: (err) => {
      console.log('保持屏幕常亮失败', err)
    }
  });

  asrService.value = ASRFactory.createASRService(asrTypes.BYTEDANCE, {
    audioConfig: DefaultASRServiceAudioConfig,
    onAudioStreamComplete: (result) => {
      console.log('语音识别完成:', result);
    }
  });

  if (!token.value && !appid.value) {
    await asrTokenStore.initTokenInfo();
  }

  if (asrService.value !== null) {
    await initAsrService();
  }
})

defineExpose({
  openPopup,
  handleButtonClick: handleRecordingButtonClick,
});
</script>
<style lang="scss" scoped>
.recording-button-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  position: relative;

  .img {
    transform: translateY(-100%);
    width: 100%;
    display: block;
    position: absolute;
    top: 0;
    left: 0
  }
}

.overlay-title {
  display: flex;
  box-sizing: border-box;
  justify-content: space-between;
  align-items: center;
  padding: 0 32rpx;
  width: 100%;
}

.overlay-title-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
}

.overlay-title-button {
  font-size: 28rpx;
  color: #3f79ff;
  padding: 12rpx;
}

.overlay-countup {
  display: flex;
  justify-content: center;
  align-items: center;
}

.default-view {
  display: flex;
  flex-direction: column;
  height: 360rpx;
  background: linear-gradient(to bottom, #FFF, transparent);
  border-radius: 28rpx;
  box-shadow: 4rpx 8rpx 16rpx rgba(0, 0, 0, 0.02);
  width: 100%;
  align-items: center;

}

.footer-text {
  font-size: 24rpx;
  font-weight: 400;
  color: #808080;
  line-height: 1.5;
  text-align: left;
  display: flex;
  flex-direction: column;
  padding-left: 32rpx;
  transform: translateY(-50%);
  align-self: flex-start;
}

.footer-text-in-popup {
  margin-left: 32rpx;
}
</style>
