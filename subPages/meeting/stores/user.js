import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { Base64 } from 'js-base64';
import { getUserList } from '../api/meeting'
import { TOKEN_NAME } from "../../../common/request.js";

export const useUserStore = defineStore('user', () => {
  const userList = ref([])
  const loading = ref(false)
  const loginUser = ref(null)

  // 使用 computed 创建用户 Map，避免重复创建
  const userMap = computed(() => new Map(
    userList.value.map(user => [user.value, user])
  ))

  // 获取用户列表
  const fetchUserList = async () => {
    loading.value = true
    try {
      const res = await getUserList({
        schoolId: uni.getStorageSync('USER_INFO').currentSchoolId,
        classId: uni.getStorageSync('USER_INFO').currentClassId || uni.getStorageSync('USER_INFO').classIds[0]
      })
      userList.value = res.data.map(({ id, name }) => ({
        value: id,
        text: name,
      }))
    } catch (error) {
      console.error('Error in fetching user list:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 重置状态
  const reset = () => {
    userList.value = []
    loading.value = false
  }

  // 解析JWT payload - 兼容uniapp
  const parseJWT = (jwt) => {
    if (!jwt) {
      return null; // 如果没有提供 JWT，则返回 null
    }

    const parts = jwt.split('.');

    // JWT 格式必须有三部分：Header、Payload、Signature
    if (parts.length !== 3) {
      console.error("Invalid JWT format");
      return null;
    }

    // 解码 Payload 部分
    const payloadBase64 = parts[1];

    try {
      // 使用 js-base64 库的 Base64 解码方法
      const decodedPayload = Base64.decode(payloadBase64);

      // 将解码后的 Payload 转换为 JSON 对象
      const payload = JSON.parse(decodedPayload);
      return payload;
    } catch (e) {
      console.error("Failed to parse JWT payload", e);
      return null;
    }
  }

  const getMe = () => {
    if (loginUser.value !== null) return loginUser.value
    const token = uni.getStorageSync(TOKEN_NAME)
    const payload = parseJWT(token)
    loginUser.value = payload
    return payload
  }

  // 根据ID获取用户信息 - O(1) 时间复杂度
  const getUserById = (id) => {
    return userMap.value.get(id)
  }

  // 根据ID列表获取用户信息列表 - O(n) 时间复杂度，n为ids长度
  const getUsersByIds = (ids) => {
    if (!ids?.length) return []
    return ids.map(id => userMap.value.get(id)).filter(Boolean)
  }

  return {
    userList,
    loading,
    fetchUserList,
    reset,
    getUserById,
    getUsersByIds,
    getMe
  }
})
