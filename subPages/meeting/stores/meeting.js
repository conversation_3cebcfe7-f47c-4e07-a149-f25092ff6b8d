import { defineStore, storeToRefs } from 'pinia'
import { ref } from 'vue'
import { createMeeting, updateMeeting, getMeetingDetail } from '../api/meeting'
import { generatePartnerList, removeEmptyFields } from '../utils/index'
import dayjs from 'dayjs'
import { useUserStore } from './user'

export const useMeetingStore = defineStore('meeting', () => {
  const userStore = useUserStore()
  const { getMe } = userStore
  const { userList } = storeToRefs(userStore)

  const tempSyncAttendees = ref([])

  // 状态管理
  const mode = ref('create') // 'create' | 'edit'
  const asrAnalysisState = ref(null)
  const meetingId = ref(null)
  const formData = ref({
    meetingName: "",
    host: [],
    attendees: [],
    meetingType: "",
    meetingNotePicList: [],
    syncAttendees: [],
    meetingMinute: ""
  })
  const formInstance = ref(null) // 存储表单实例的引用

  // 设置表单实例
  const setFormInstance = (instance) => {
    formInstance.value = instance
  }

  // 获取表单实例
  const getFormInstance = () => formInstance.value

  // 设置模式
  const setMode = (newMode) => {
    if (!['create', 'edit'].includes(newMode)) {
      console.warn('Invalid mode:', newMode)
      return
    }
    mode.value = newMode
  }

  // 提交表单
  const submitForm = async () => {
    try {
      const partnerList = generatePartnerList(userList.value, formData.value.host, formData.value.attendees)

      const params = removeEmptyFields({
        meetingName: formData.value.meetingName,
        meetingType: formData.value.meetingType,
        partnerList: partnerList,
        meetingBeginTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        meetingNotePicList: formData.value.meetingNotePicList.map(item => item.id),
        meetingPermlist: formData.value.syncAttendees?.map(item => ({
          permUserId: item,
          permUserName: userStore.getUserById(item).text
        })).concat(tempSyncAttendees.value)
      });

      console.log(params, 'params')

      // 根据模式调用不同的 API
      const res = mode.value === 'create'
        ? await createMeeting(params)
        : await updateMeeting({ ...params, meetingId: meetingId.value })

      console.log(`会议${mode.value === 'create' ? '创建' : '更新'}成功:`, res)
      if (mode.value === 'create') {
        meetingId.value = res.data
      }
      return res
      // return false;

    } catch (error) {
      console.error('表单提交失败:', error)
      throw error
    }
  }

  const resetForm = () => {
    meetingId.value = null
    formData.value.meetingName = ""
    formData.value.host = []
    formData.value.attendees = []
    formData.value.meetingType = ""
    formData.value.meetingNotePicList = []
    formData.value.syncAttendees = []
    tempSyncAttendees.value = []
  }

  const setFormData = (data) => {
    formData.value = {
      ...formData.value,
      ...data
    }
  }
  const setMeetingId = (id) => {
    meetingId.value = id
  }

  // 初始化编辑模式
  const initEditMode = async (id) => {
    try {
      setMode('edit')
      meetingId.value = id
      const meetingDetail = (await getMeetingDetail({ meetingId: id })).data;

      const { filteredList, self } = removeSelfFromList(meetingDetail.meetingPermList);

      setFormData({
        meetingName: meetingDetail.meetingName,
        meetingType: meetingDetail.meetingType,
        host: meetingDetail.partnerList.filter(item => item.masterFlag === 1).map(item => Number(item.partnerId)),
        attendees: meetingDetail.partnerList.filter(item => item.masterFlag === 0).map(item => Number(item.partnerId)),
        meetingNotePicList: meetingDetail.meetingNotePicList?.map(item => ({
          id: item.id,
          uri: item.uri,
          category: 1
        })),
        syncAttendees: filteredList?.map(item => item.permUserId),
        meetingMinute: meetingDetail.meetingMinute,
      })

      tempSyncAttendees.value = [self]
      if (meetingDetail.asrAnalysisState !== undefined) {
        asrAnalysisState.value = meetingDetail.asrAnalysisState
      }
      console.log(formData.value, 'formData')
    } catch (error) {
      console.error('初始化编辑模式失败:', error)
      throw error
    }
  }

  const removeSelfFromList = (list) => {
    const { userId } = getMe()
    const self = list.find(item => item.permUserId === userId)
    return {
      filteredList: list.filter(item => item.permUserId !== userId),
      self
    }
  }

  return {
    // 状态
    mode,
    meetingId,
    formData,
    asrAnalysisState,
    // 方法
    setMode,
    submitForm,
    resetForm,
    setFormInstance,
    getFormInstance,
    setFormData,
    setMeetingId,
    initEditMode
  }
})
