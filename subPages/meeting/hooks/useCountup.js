import { ref, computed } from 'vue'

export function useCountUp(initialSeconds = 0) {
  const seconds = ref(initialSeconds)
  let timer = null

  const formattedTime = computed(() => {
    const hours = Math.floor(seconds.value / 3600)
    const minutes = Math.floor((seconds.value % 3600) / 60)
    const secs = seconds.value % 60

    if (hours > 99) {
      return '99:59:59'
    }

    return `${hours.toString().padStart(2, '0')}:${minutes
      .toString()
      .padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  })

  const start = () => {
    if (!timer) {
      timer = setInterval(() => {
        seconds.value++
        if (formattedTime.value === '99:59:59') {
          stop()
        }
      }, 1000)
    }
  }

  const pause = () => {
    if (timer) {
      clearInterval(timer)
      timer = null
    }
  }

  const resume = () => {
    start() // 从当前秒数继续计时
  }

  const stop = () => {
    pause()
    seconds.value = 0
  }

  const reset = (value = 0) => {
    seconds.value = value
  }

  return {
    seconds,
    formattedTime,
    start,
    pause,
    resume,
    stop,
    reset
  }
}
