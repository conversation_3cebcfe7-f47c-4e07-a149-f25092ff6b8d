import { ref, computed, onUnmounted, onMounted, watch } from "vue";
import { useRecordAuthStore } from "../stores/recordAuth";

/**
 * 录音 Hook，支持可选的 ASR 服务
 * @param {Object} options - 配置选项
 * @returns {Object} - 录音相关方法和状态
 */
export const useRecorderV2 = (options = {}) => {
  const {
    audioConfig = {},
    handleRecordingFileUpload,
    handleRecordingStop,
    asrService, // 直接接收 ASR 服务实例的 ref
  } = options;

  const {
    MAX_DURATION = 60000,
    frameTime = 1000,
    sampleRate = 16000,
    numberOfChannels = 1,
    encodeBitRate = 48000,
    format = 'PCM', // 音频格式 PCM，mp3，aac，wav
  } = audioConfig;

  onMounted(() => {
    if (format.toLowerCase() === 'aac') {
      throw new Error('不支持AAC格式');
    }
  });

  const recorderManager = uni.getRecorderManager();
  const recordAuthStore = useRecordAuthStore();

  const isUploading = ref(false);
  // 录音文件索引, 用于区分不同的录音文件
  const recordIndex = ref(-1);
  const uploadFileArr = ref([]);
  const isRecording = ref(false);
  // 新增状态变量
  const isManualStop = ref(false);


  const frameSize = computed(() => {
    // frameTime(ms) * sampleRate(16kHz) * bitDepth(16bit) * channels(1) / 8 bits/byte / 1024 bytes/KB
    // frameTime需要转换为秒，所以除以1000
    return Math.floor((frameTime / 1000) * sampleRate * 16 * numberOfChannels / 8 / 1024);
  });

  // 处理文件上传队列
  const processUploadQueue = async () => {
    if (uploadFileArr.value.length === 0 || isUploading.value) return;

    isUploading.value = true;
    try {
      const fileInfo = uploadFileArr.value[0];
      const results = await handleRecordingFileUpload?.(fileInfo.index, fileInfo.tempFilePath);
      console.log('上传结果:', results);
      uploadFileArr.value.shift(); // 移除已处理的文件

      // 继续处理队列中的下一个文件
      if (uploadFileArr.value.length > 0) {
        processUploadQueue();
      }
    } catch (error) {
      console.error('上传失败:', error);
    } finally {
      isUploading.value = false;
    }
  };

  // 监听文件队列变化
  watch(uploadFileArr, (newArr) => {
    if (newArr.length > 0) {
      processUploadQueue();
    }
  }, { deep: true });

  // 录音开始事件
  recorderManager.onStart(async () => {
    if (recordIndex.value === -1) {
      console.log("录音已启动，启动参数为", {
        duration: MAX_DURATION,
        sampleRate,
        numberOfChannels,
        encodeBitRate,
        format,
        frameSize: frameSize.value,
      });
    } else {
      console.log('录音自动重启成功');
    }

    console.log('asrService in recorderManager.onStart', asrService?.value);

    // 如果提供了 ASR 服务且未连接，则启动 ASR 服务
    if (asrService?.value && asrService.value.status !== 'connected') {
      try {
        await asrService.value.start();
      } catch (error) {
        console.error('启动ASR服务失败:', error);
      }
    }

    recordIndex.value++;
    isRecording.value = true;
  });

  // 录音暂停事件
  recorderManager.onPause(() => {
    console.log("recorder pause");
    isRecording.value = false;
  });

  // 录音恢复事件
  recorderManager.onResume(() => {
    console.log("recorder resume");
    isRecording.value = true;
  });

  // 录音结束事件
  recorderManager.onStop((res) => {
    console.log("录音停止");
    isRecording.value = false;

    handleRecordFile(res);

    // 处理自动重启
    if (!isManualStop.value) {
      console.log('达到最大时长，自动重启录音');
      try {
        start();
      } catch (error) {
        console.error('自动重启录音失败:', error);
        handleRecordingStop?.();
      }
    }
  });

  // 录音实时数据回调
  recorderManager.onFrameRecorded((res) => {
    const { frameBuffer, isLastFrame } = res;

    console.log('收到录音数据:', frameBuffer.byteLength, '字节');
    console.debug('是否为最后一帧', isLastFrame);

    // 如果提供了 ASR 服务且已连接，发送音频数据
    if (asrService?.value && asrService.value.status === 'connected') {
      send(frameBuffer, isLastFrame);
    } else if (asrService?.value) {
      console.warn('ASR服务未连接，无法发送录音数据');
    }
  });

  // 录音错误事件
  recorderManager.onError((res) => {
    console.error("recorder error:", res.errMsg);
    isRecording.value = false;

    // 根据错误类型决定是否需要关闭连接
    if (res.errMsg?.includes('system error') || res.errMsg?.includes('abort')) {
      // 只有在系统错误或中断时才关闭连接
      if (asrService?.value) {
        asrService.value.stop();
      }
    }
  });

  recorderManager.onInterruptionEnd(() => {
    console.log('录音中断结束');
    if (asrService?.value && asrService.value.status === 'connected') {
      resume();
    }
  });

  // 处理完整录音文件
  const handleRecordFile = async (fileInfo) => {
    const { tempFilePath } = fileInfo;
    console.log("录音文件信息:", fileInfo, recordIndex.value);

    // 将文件信息添加到队列
    if (!!handleRecordingFileUpload) {
      uploadFileArr.value.push({
        index: recordIndex.value,
        tempFilePath
      });
    }
  };

  const start = async () => {
    try {
      console.log('开始录音前，先检查录音权限');
      // 检查录音权限
      if (!recordAuthStore.hasRecordAuth) {
        uni.showToast({
          title: "请先授权录音权限",
          icon: "none",
        });
        return false;
      }

      isManualStop.value = false; // 重置手动停止标记

      console.log('检查录音权限完成，开启录音');

      // 开始新录音
      recorderManager.start({
        duration: MAX_DURATION,
        sampleRate,
        numberOfChannels,
        encodeBitRate,
        format,
        frameSize: frameSize.value,
      });

      return true;
    } catch (error) {
      console.error("启动录音失败:", error);
      return false;
    }
  };

  const pause = () => {
    recorderManager.pause();
  };

  const resume = () => {
    recorderManager.resume();
  };

  const send = (buffer, isLast = false) => {
    try {
      if (!buffer || buffer.byteLength === 0) return;

      console.debug(`发送音频数据: ${buffer.byteLength}字节`, isLast ? '[最后一帧]' : '');

      // 发送音频数据到 ASR 服务
      if (asrService?.value) {
        asrService.value.sendAudio(buffer, isLast && isManualStop.value);
      }
    } catch (error) {
      console.error('音频发送失败:', error);
    }
  };

  const stop = async () => {
    try {
      isManualStop.value = true;
      // 停止当前录音
      recorderManager.stop();
      recordIndex.value = -1;
    } catch (error) {
      uni.showToast({
        title: '停止录音时出错: ' + error.message,
        icon: 'error'
      });
    }
  };

  // 组件卸载时清理资源
  onUnmounted(() => {
    // 确保关闭连接和停止录音
    if (asrService?.value) {
      asrService.value.stop();
    }
    stop();
  });

  return {
    isRecording,
    // asrResult,
    // asrConnectionStatus,
    start,
    pause,
    resume,
    stop,
  };
}; 