import { ref } from "vue";

export const useMeetingTypes = ({ isShowAll = true } = {}) => {
  // TODO: 从后端获取会议类型列表
  const meetingTypeList = ref([
    {
      name: "全部",
      value: 0,
    },
    {
      name: "园务",
      value: 1,
    },
    {
      name: "后勤",
      value: 2,
    },
    {
      name: "安全",
      value: 3,
    },
    {
      name: "卫生保健",
      value: 4,
    },
    {
      name: "教学",
      value: 5,
    },
    {
      name: "教研培训",
      value: 6,
    },
    {
      name: "家长",
      value: 7,
    },
    {
      name: "外出学习",
      value: 8,
    },
    {
      name: "其他",
      value: 9,
    },
  ]);

  const meetingTypeMap = new Map(meetingTypeList.value.map(item => [item.value, item]));

  const getMeetingTypeByValue = (value) => {
    return meetingTypeMap.get(value)?.name || '未知';
  };

  return {
    meetingTypeList: isShowAll ? meetingTypeList.value : meetingTypeList.value.slice(1),
    getMeetingTypeByValue,
  };
};
