import { ref, watch, onUnmounted } from 'vue';
import { get } from 'lodash-es';
import { onShow } from "@dcloudio/uni-app";

/**
 * 加载更多的Hook
 * @param {Function} loadFunction 加载数据的函数
 * @param {Object} options 配置项
 * @returns {Object} 返回加载状态和加载更多方法
 */
export default function useLoadingMore(loadFunction, options = {}) {
  // 加载状态 loadmore: 加载更多 nomore: 没有更多 loading: 加载中
  const loading = ref('loadmore');

  // 获取字段配置，设置默认值
  const fieldNames = {
    current: 'current',
    pageSize: 'pageSize',
    ...options.fieldNames
  };

  // 获取响应字段配置，设置默认值
  const responseFields = {
    list: 'data',
    total: 'total',
    ...options.responseFields
  };

  // 分页参数
  const pageParams = ref({
    [fieldNames.current]: 0,
    [fieldNames.pageSize]: options.pageSize || 10,
    ...options.params // 支持传入额外的固定参数
  });

  // 总数
  const total = ref(0);

  // 数据列表
  const list = ref([]);

  /**
   * 加载更多
   * @param {Object} extraParams 额外的请求参数
   */
  const loadMore = async (extraParams = {}) => {
    if (loading.value === 'loading' || loading.value === 'nomore') {
      return;
    }

    try {
      loading.value = 'loading';
      pageParams.value[fieldNames.current]++;

      // 合并固定参数和临时参数
      const requestParams = {
        ...pageParams.value,
        ...extraParams
      };

      const res = await loadFunction(requestParams);

      // 使用 lodash 的 get 方法安全地获取嵌套属性
      const newList = get(res, responseFields.list, []);
      const totalCount = get(res, responseFields.total, 0);

      // 追加数据
      list.value = [...list.value, ...newList];
      total.value = totalCount;

      // 判断是否还有更多数据
      if (list.value.length >= total.value) {
        loading.value = 'nomore';
      } else {
        loading.value = 'loadmore';
      }
    } catch (error) {
      console.error('Load more error:', error);
      loading.value = 'loadmore';
      // 加载失败时恢复页码
      pageParams.value[fieldNames.current]--;
    }
  };

  /**
   * 重置
   */
  const reset = () => {
    loading.value = 'loadmore';
    pageParams.value[fieldNames.current] = 0;
    total.value = 0;
    list.value = [];
  };

  // 监听 options.params 的变化
  if (options.params) {
    watch(
      () => options.params,
      () => {
        // 重置分页并重新加载数据
        reset();
        loadMore();
      },
      { deep: true } // 深度监听对象的变化
    );
  }

  onUnmounted(() => {
    reset();
  })

  onShow(() => {
    loadMore();
  })

  return {
    loading,
    pageParams,
    total,
    list,
    loadMore,
    reset
  };
}
