<template>
  <base-layout :autoBack="false" nav-title="会议详情" :footerStyle="{
    paddingBottom: `calc(20rpx + env(safe-area-inset-bottom));`,
    backgroundColor: '#fff',
  }" @leftClick="handleBack">
    <view class="meeting-detail">
      <card>
        <view class="meeting-detail-item">
          <text class="meeting-detail-item-title">会议名称</text>
          <text class="meeting-detail-item-content">{{
            meetingDetail.meetingName || '暂无会议名称' }}</text>
        </view>
      </card>
      <card>
        <view class="meeting-detail-item">
          <text class="meeting-detail-item-title">会议类别</text>
          <text class="meeting-detail-item-content">{{
            getMeetingTypeByValue(meetingDetail.meetingType) }}</text>
        </view>
      </card>
      <card>
        <view class="meeting-detail-item">
          <text class="meeting-detail-item-title">主持人</text>
          <text class="meeting-detail-item-content">{{
            hostsName }}</text>
        </view>
      </card>
      <card>
        <view class="meeting-detail-item">
          <text class="meeting-detail-item-title">参会人员</text>
          <text class="meeting-detail-item-content">{{
            attendeesName || '暂无参会人员' }}</text>
        </view>
      </card>
      <card>
        <view class="meeting-detail-item">
          <text class="meeting-detail-item-title">会议照片</text>
          <up-album :urls="imgUrls" v-if="imgUrls.length" autoWrap singleSize="160" unit="rpx"></up-album>
          <text v-else class="meeting-detail-item-content">暂无照片</text>
        </view>
      </card>
      <card>
        <view class="meeting-detail-item">
          <text class="meeting-detail-item-title">会议纪要</text>
          <up-read-more v-if="meetingDetail.analyzeStatus === 1" ref="uReadMoreRef" openText="展开" closeText="收起">
            <ua-markdown :source="meetingDetail.meetingSummary"></ua-markdown>
          </up-read-more>
          <text v-else-if="meetingDetail.analyzeStatus === 0" class="meeting-detail-item-content">会议纪要总结中</text>
          <text v-else-if="meetingDetail.analyzeStatus === 2" class="meeting-detail-item-content">会议纪要优化中</text>
          <text v-else class="meeting-detail-item-content">暂无会议纪要</text>
        </view>
      </card>
      <card>
        <view class="meeting-detail-item">
          <text class="meeting-detail-item-title">会议待办</text>
          <text class="meeting-detail-item-content" v-if="meetingDetail.meetingBacklog">
            {{ meetingDetail.meetingBacklog }}
          </text>
          <text v-else class="meeting-detail-item-content"
            :class="{ 'grey-text': meetingDetail.analyzeStatus === 0 }">暂无会议待办</text>
        </view>
      </card>
      <card>
        <view class="meeting-detail-item">
          <text class="meeting-detail-item-title">会议纪要同步人</text>
          <text class="meeting-detail-item-content" v-if="syncNames"
            :class="{ 'grey-text': meetingDetail.analyzeStatus === 0 }">{{
              syncNames }}</text>
          <text v-else class="meeting-detail-item-content"
            :class="{ 'grey-text': meetingDetail.analyzeStatus === 0 }">暂无会议纪要同步人</text>
        </view>
      </card>
    </view>
    <template #footer>
      <view class="footer">
        <up-button :plain="true" :hairline="true" text="导出Word" shape="circle" @tap="handleExportWord"></up-button>
        <up-button :plain="true" :hairline="true" text="编辑" shape="circle" @tap="editMeeting"></up-button>
      </view>
    </template>
  </base-layout>
</template>
<script setup>
import { onShow } from "@dcloudio/uni-app";
import UaMarkdown from "../components/ua-markdown/ua-markdown.vue";
import { ref, computed, nextTick } from 'vue';
import Card from '../../../components/card/card.vue';
import BaseLayout from '../../../components/base-layout/base-layout.vue';
import { getMeetingDetail, exportWord } from '../api/meeting.js';
import { useQueryParams } from '../../../common/hooks/useQueryParams.js';
import { useMeetingTypes } from '../hooks/useMeetingTypes.js';
import { getArrayFieldsToString } from '../utils';
import { onShareAppMessage } from "@dcloudio/uni-app";
import { sharePageObj, exportFile } from "@/utils";


const { params } = useQueryParams("id");
const { getMeetingTypeByValue } = useMeetingTypes();

const meetingDetail = ref({});
const hostsName = computed(() => getArrayFieldsToString(meetingDetail.value.partnerList, {
  enableFilter: true,
  filterValue: 1,
}));

const attendeesName = computed(() => getArrayFieldsToString(meetingDetail.value.partnerList, {
  enableFilter: true,
  filterValue: 0,
}));

const syncNames = computed(() => getArrayFieldsToString(meetingDetail.value.meetingPermList, {
  mapField: 'permUserName'
}));

const imgUrls = computed(() => meetingDetail.value?.meetingNotePicList?.map(item => item.uri) || []);

const handleBack = () => {
  uni.navigateTo({
    url: '/subPages/meeting/index',
  });
}


const uReadMoreRef = ref(null);

onShow(async () => {
  if (!params.value.id) return;
  const res = await getMeetingDetail({ meetingId: params.value.id });
  meetingDetail.value = res.data;

  await nextTick();

  if (uReadMoreRef.value && meetingDetail.value.meetingSummary) {
    uReadMoreRef.value.init();
  }
})

const handleExportWord = async () => {
  try {
    const result = await exportFile({
      getFileUrl: () => exportWord({ meetingId: params.value.id }),
    })
    console.log(result);
    return result.success;
  } catch (error) {
    console.error('导出Word文档失败:', error);
  }
}

const editMeeting = () => {
  uni.navigateTo({
    url: `/subPages/meeting/meetingManager/index?id=${params.value.id}&type=edit`
  });
};

onShareAppMessage(() => sharePageObj());
</script>
<style lang="scss" scoped>
.meeting-detail {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.meeting-detail-item {
  display: flex;
  flex-direction: column;
  gap: 28rpx;
}

.meeting-detail-item-title {
  font-size: 30rpx;
  color: #808080;
  font-weight: 400;
}

.meeting-detail-item-content {
  font-size: 30rpx;
  color: #333333;
  font-weight: 500;
}

.grey-text {
  // color: #B1B3B5;
}

.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20rpx;
}
</style>
