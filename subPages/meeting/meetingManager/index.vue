<template>
  <base-layout :nav-title="title" :footerStyle="footerStyle">
    <meeting-form />
    <template #footer>
      <recording-footer v-if="isCreate" />
      <view v-else class="footer-wrapper">
        <up-button text="取消" shape="circle" @tap="navigateToDetail" plain hairline></up-button>
        <up-button type="primary" @tap="hanldeEdit" text="提交" shape="circle" plain hairline></up-button>
      </view>
    </template>
  </base-layout>
</template>
<script setup>
import { onMounted, onUnmounted, computed, watchEffect } from "vue";
import MeetingForm from "../components/meetingForm.vue";
import BaseLayout from "../../../components/base-layout/base-layout.vue";
import RecordingFooter from "../components/recordingFooter.vue";
import { useRecordAuthStore } from "../stores/recordAuth";
import { useMeetingStore } from '../stores/meeting'
import { useAsrTokenStore } from '../stores/asrToken'
import { useUserStore } from '../stores/user'
import { useQueryParams } from '../../../common/hooks/useQueryParams.js';

const recordAuthStore = useRecordAuthStore();
const meetingStore = useMeetingStore()
const asrTokenStore = useAsrTokenStore()
const userStore = useUserStore()

const { submitForm } = meetingStore;

const hanldeEdit = async () => {
  await submitForm()
  navigateToDetail()
}

const navigateToDetail = () => {
  uni.navigateBack({
    delta: 1,
    fail: () => {
      uni.navigateTo({
        url: `/subPages/meeting/meetingDetail/index?id=${params.value.id}`
      })
    }
  })
}

const { params } = useQueryParams("type", "id");

const isCreate = computed(() => params.value.type === "create");

const footerStyle = computed(() => isCreate.value ? {
  position: 'relative',
  paddingBottom: '0',
} : {
  paddingBottom: `calc(20rpx + env(safe-area-inset-bottom));`,
  backgroundColor: '#fff'
})

const title = computed(() => isCreate.value ? "创建会议" : "编辑会议");

watchEffect(async () => {
  if (!isCreate.value && params.value.id !== undefined) {
    meetingStore.initEditMode(params.value.id)
  }
});

// 页面加载时直接初始化权限
onMounted(() => {
  if (isCreate.value) {
    recordAuthStore.initAuth();
  }
  userStore.fetchUserList();
});

// 如果需要可以在组件卸载时重置状态
onUnmounted(() => {
  meetingStore.resetForm()
})
</script>
<style lang="scss" scoped>
.footer-wrapper {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}
</style>
