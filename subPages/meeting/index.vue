<template>
  <BaseLayout nav-title="会议管理" :footerStyle="{
    paddingBottom: `calc(20rpx + env(safe-area-inset-bottom));`,
    backgroundColor: '#fff',
  }" @scrolltolower="loadMore">
    <view class="meeting-type-tabs">
      <u-tabs :list="meetingTypeList" lineColor="#3F79FF" @click="onMeetingTypeChange"></u-tabs>
    </view>
    <view class="meeting-list">
      <meeting-item v-for="item in list" :key="item.id" :meeting="item"></meeting-item>
    </view>

    <up-loadmore :status="loading" loadmore-text="上拉或点击加载更多" @loadmore="loadMore"></up-loadmore>
    <template #footer>
      <view class="footer">
        <!-- <icon-button type="secondary" icon="icon-history" text="AI学期总结" @tap="getConclusion">
          <up-icon name="file-text" color="#666666" size="18" class="icon-button-icon"></up-icon>
        </icon-button> -->
        <up-button color="#367CFF" shape="circle" @tap="createMeeting" type="primary" text="创建会议"></up-button>
      </view>
    </template>
  </BaseLayout>
</template>
<script setup>
import { ref } from "vue";
import BaseLayout from "../../components/base-layout/base-layout.vue";
// import IconButton from "../../components/icon-button/icon-button.vue";
import { useMeetingTypes } from "./hooks/useMeetingTypes.js";
import { getMeetingList } from "./api/meeting.js";
import MeetingItem from "./components/meetingItem.vue";
import useLoadingMore from "./hooks/useLoadingMore.js";
import { onShareAppMessage } from "@dcloudio/uni-app";
import { sharePageObj } from "@/utils";

const meetingReqInfo = ref({
  pageModel: {},
});

const { loadMore, loading, list } = useLoadingMore(getMeetingList, {
  fieldNames: {
    current: 'currentPage',
  },
  params: meetingReqInfo.value,
  responseFields: {
    total: 'metadata.count',
  }
});

const { meetingTypeList } = useMeetingTypes();

// const getConclusion = () => {
// };

const onMeetingTypeChange = (item) => {
  console.log(item);
  if (item.value === 0) {
    meetingReqInfo.value.pageModel.meetingType = undefined;
  } else {
    meetingReqInfo.value.pageModel.meetingType = item.value;
  }
};

const createMeeting = () => {
  uni.navigateTo({
    url: "/subPages/meeting/meetingManager/index?type=create",
  });
};

onShareAppMessage(() => sharePageObj());
</script>
<style lang="scss" scoped>
.meeting-type-tabs {
  position: sticky;
  top: 0;
  z-index: 10;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  mask:
    linear-gradient(to bottom,
      rgba(0, 0, 0, 0.8) 0%,
      rgba(0, 0, 0, 1) 25%,
      /* 延后开始完全不透明 */
      rgba(0, 0, 0, 1) 75%,
      /* 提前结束完全不透明 */
      rgba(0, 0, 0, 0.2) 100%);
}

.meeting-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12rpx;
  gap: 16rpx;
}

.icon-button-icon {
  background-color: #f5f5f5;
  padding: 10rpx;
  border-radius: 50%;
}
</style>
