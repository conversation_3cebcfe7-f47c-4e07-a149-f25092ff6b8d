/**
 * 生成会议参与者列表
 * @param {Array<{value: number|string, text: string}>} userList - 用户列表，包含用户ID和名称
 * @param {Array<number|string>} hostIds - 主持人ID列表
 * @param {Array<number|string>} attendeeIds - 参与者ID列表
 * @returns {Array<{partnerType: 1, partnerId: string, partnerName: string, masterFlag: 0|1}>} 返回统一格式的参与者列表，masterFlag为1表示主持人，0表示普通参与者
 */
export function generatePartnerList(userList, hostIds, attendeeIds) {
  // 创建用户Map，提高查找效率
  const userMap = new Map(userList.map(user => [String(user.value), user.text]))

  // 创建主持人Set，提高查找效率
  const hostSet = new Set(hostIds.map(id => String(id)))

  // 一次性去重合并ID,统一转为字符串
  const allParticipantIds = [...new Set([...hostIds, ...attendeeIds])].map(id => String(id))

  // 直接生成最终数组，减少map和filter的组合
  const result = []

  for (const id of allParticipantIds) {
    const name = userMap.get(id)
    if (name) {
      result.push({
        partnerType: 1,
        partnerId: id,
        partnerName: name,
        masterFlag: hostSet.has(id) ? 1 : 0
      })
    }
  }

  return result
}

/**
 * 移除对象中的空值字段
 * @param {Object} obj - 需要处理的对象
 * @returns {Object} 移除空值后的新对象
 * 
 * 会移除以下值:
 * - null
 * - undefined  
 * - 空数组 []
 */
export const removeEmptyFields = (obj) => {
  return Object.fromEntries(
    Object.entries(obj).filter(([_, value]) => {
      if (value === null || value === undefined) return false;
      if (Array.isArray(value) && value.length === 0) return false;
      return true;
    })
  );
};

/**
 * 从数组中获取并格式化参与者名称
 * @param {Array} list - 数据列表
 * @param {Object} options - 配置选项
 * @param {boolean} [options.enableFilter=false] - 是否启用过滤
 * @param {string} [options.filterField='masterFlag'] - 用于过滤的字段名
 * @param {any} [options.filterValue] - 用于过滤的值
 * @param {string} [options.mapField='partnerName'] - 用于映射的字段名
 * @param {string} [options.separator=','] - 连接符
 * @returns {string} 格式化后的字符串
 */
export const getArrayFieldsToString = (list = [], options = {}) => {
  const {
    enableFilter = false,
    filterField = 'masterFlag',
    filterValue,
    mapField = 'partnerName',
    separator = ','
  } = options;

  // 如果启用过滤且提供了过滤值，则进行过滤
  const filteredList = enableFilter && filterValue !== undefined
    ? list.filter(item => item[filterField] === filterValue)
    : list;

  // 映射并连接字符串
  return filteredList
    .map(item => item[mapField])
    .filter(Boolean) // 过滤掉空值
    .join(separator);
};