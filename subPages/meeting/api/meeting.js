import { request, generateUrl, TOKEN_NAME } from "../../../common/request.js";
import { exportFile } from "@/utils";

// 获取会议列表
export const getMeetingList = (params) => {
  return request({
    url: "/jsapi/business/meetingInfo/pageList",
    method: "POST",
    data: params,
  });
};


// 获取用户列表
export const getUserList = (params) => {
  return request({
    url: "/jsapi/global/user/userList",
    method: "POST",
    data: params,
  });
};


// 创建会议
export const createMeeting = (params) => {
  return request({
    url: "/jsapi/business/meetingInfo/beginMeeting",
    method: "POST",
    data: params,
  });
};


// 获取字节跳动api的token和appid
export const getByteDanceToken = () => {
  return request({
    url: "/jsapi//global/doubao/getAsrAppConfig",
    method: "POST",
  });
};


// 上传录音文件
export const uploadAudioFile = async (tempFilePath, meetingId, index) => {
  const url = generateUrl('/jsapi/business/meetingInfo/uploadMeetingVideoPcm');

  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url,
      filePath: tempFilePath,
      name: 'file',
      header: {
        'Authorization': uni.getStorageSync(TOKEN_NAME) || '',
      },
      formData: {
        meetingId,
        index,
      },
      success: (res) => {
        console.log('上传成功：', res)
        resolve(res);
      },
      fail: (error) => {
        reject(error);
      }
    });
  });
};

// 上传录音转写结果
export const uploadAudioTranscript = (params) => {
  return request({
    url: "/jsapi/business/meetingInfo/uploadMeetingAsr",
    method: "POST",
    data: params,
  });
};

// 结束会议
export const endMeeting = (params) => {
  return request({
    url: "/jsapi/business/meetingInfo/endMeeting",
    method: "POST",
    data: params,
  });
};

// 获取会议详情
export const getMeetingDetail = (params) => {
  return request({
    url: "/jsapi/business/meetingInfo/detailInfo",
    method: "GET",
    data: params,
  });
};

// 更新会议
export const updateMeeting = (params) => {
  return request({
    url: "/jsapi/business/meetingInfo/editMeeting",
    method: "POST",
    data: params,
  });
};

// 导出word
export const exportWord = async (params) => {
  return request({
    url: "/jsapi/business/meetingInfo/exportWord?meetingId=" + params.meetingId,
    method: "GET",
  });
};