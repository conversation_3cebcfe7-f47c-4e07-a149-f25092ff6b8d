<template>
  <base-layout
    nav-title="历史记录"
    :content-style="{ padding: '0 30rpx' }"
    :enable-load-more="true"
    @scrolltolower="handleScrollToLower"
  >
    <template #extra>
      <view class="time-label">
        <up-datetime-picker
          hasInput
          :show="showDatePicker"
          v-model="value1"
          mode="year-month"
          @cancel="showDatePicker = false"
          @confirm="handleDateConfirm"
          format="YYYY-MM"
          :closeOnClickOverlay="true"
          @close="showDatePicker = false"
        >
          <view class="date-display">
            {{ currentMonth }}
          </view>
        </up-datetime-picker>
        <uni-icons type="down" size="16" color="#333" @click="showDatePicker = true" />
      </view>
    </template>
    <template v-if="loading && pageNo === 1">
      <u-loading-page :loading="loading"></u-loading-page>
    </template>
    <template v-else>
      <view v-if="error" class="error">
        {{ error }}
      </view>
      <template v-else>
        <view class="records-container">
          <view v-if="chatRecords.length === 0" class="empty-state"> 暂无对话记录 </view>
          <template v-else>
            <template v-for="(group, groupLabel) in groupedRecords" :key="groupLabel">
              <chat-message
                class="chat-record"
                v-for="record in group"
                :key="record.templateId"
                :message="record.title || record.id"
                :timestamp="record.createdAt"
                :requestId="record.templateId"
                :conversationId="record.id"
                :title="record.title || '新对话'"
                :time="record.updatedAt"
                :delId="record.id"
              />
            </template>
          </template>

          <!-- 确保加载更多状态显示在底部 -->
          <view class="load-more-wrapper">
            <uni-load-more
              :status="loadMoreStatus"
              :content-text="{
                contentdown: '上拉加载更多',
                contentrefresh: '加载中...',
                contentnomore: '没有更多数据了'
              }"
            />
          </view>
        </view>
      </template>
    </template>
  </base-layout>
</template>

<script setup>
import { onShow, onUnload } from '@dcloudio/uni-app'
import { ref, computed } from 'vue'
import chatMessage from './components/chat-message.vue'
import { getConversationPageList } from '@/api/aiTemplate.js'
import { useQueryParams } from '../hooks/useQueryParams.js'
import dayjs from 'dayjs'

const currentMonth = ref(dayjs().format('YYYY-MM'))
const showDatePicker = ref(false)
const value1 = ref(Date.now())
const pageNo = ref(1)
const pageSize = ref(10)
const total = ref(0)
const loadMoreStatus = ref('more') // 'more', 'loading', 'noMore'

const handleDateConfirm = (e) => {
  showDatePicker.value = false
  const month = dayjs(e.value).format('YYYY-MM')

  currentMonth.value = month
  // 重置分页并重新加载
  pageNo.value = 1
  chatRecords.value = []
  loadMoreStatus.value = 'more'
  loadRecords(month)
}

const chatRecords = ref([])
const error = ref(null)
const loading = ref(false)

const { params } = useQueryParams('type', 'responseKey', 'id')

const loadRecords = async (month, isLoadMore = false) => {
  console.log('选择的日期', month)
  month = month.split('-')[1]
  try {
    if (!isLoadMore) {
      loading.value = true
    }

    const response = await (params.value.type === 'template'
      ? getConversationPageList({
          currentPage: pageNo.value,
          pageSize: pageSize.value,
          pageModel: {
            month: month,
            templateId: params.value.id
          }
        })
      : getConversationPageList({
          currentPage: pageNo.value,
          pageSize: pageSize.value,
          pageModel: {
            month: month,
            templateId: ''
          }
        }))

    if (response.status === 0) {
      chatRecords.value = response.data
      // 更新加载更多状态
      if (chatRecords.value.length >= total.value) {
        loadMoreStatus.value = 'noMore'
      } else {
        loadMoreStatus.value = 'more'
      }
    } else {
      throw new Error(response.message || '获取记录失败')
    }
  } catch (err) {
    error.value = err.message
    console.error('获取记录失败:', err)
    uni.showToast({
      title: err.message || '获取记录失败',
      icon: 'none',
      duration: 2000
    })
  } finally {
    loading.value = false
  }
}

// 处理滚动到底部
const handleScrollToLower = async () => {
  if (loadMoreStatus.value !== 'more') {
    return
  }

  loadMoreStatus.value = 'loading'
  pageNo.value++
  await loadRecords(currentMonth.value, true)
}

const groupedRecords = computed(() => {
  const groups = {}

  chatRecords.value.forEach((record) => {
    const recordDate = dayjs(record.createdAt)
    const label = recordDate.format('YYYY年MM月')

    if (!groups[label]) {
      groups[label] = []
    }
    groups[label].push(record)
  })

  return groups
})

// 修改刷新列表方法
const refreshList = () => {
  pageNo.value = 1
  chatRecords.value = []
  loadMoreStatus.value = 'more'
  loadRecords(currentMonth.value)
}

// 监听刷新事件
uni.$on('refresh-chat-list', refreshList)

// 组件卸载时移除事件监听
onUnload(() => {
  uni.$off('refresh-chat-list', refreshList)
})

onShow(() => {
  refreshList()
})
</script>

<style lang="scss" scoped>
.records-container {
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 88rpx - 40rpx);
  padding-bottom: 40rpx;
}

.time-label {
  width: 260rpx;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: #333;
  font-size: 28rpx;
  gap: 16rpx;
  background-color: #ffffff;
  box-shadow: 4rpx 8rpx 16rpx 0 rgba(0, 0, 0, 0.02);
  padding: 16rpx 28rpx;
  border-radius: 40rpx;
  font-weight: 600;
  margin-bottom: 54rpx;
  position: relative;
  z-index: 2;

  .date-display {
    font-size: 28rpx;
    color: #333;
    padding: 0 10rpx;
  }

  :deep(.u-input) {
    border: none;
    width: 130rpx;
  }
}

.chat-record {
  &:nth-last-child(2) {
    :deep(.message-tail) {
      display: none;
    }
  }
}

.empty-state {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 40rpx 0;
}

.load-more-wrapper {
  padding: 20rpx 0;
  width: 100%;
  text-align: center;
}
</style>
