<template>
  <view class="message-container" @tap="messageTap">
    <view class="message-tail"></view>
    <view class="message-icon">
      <uni-icons custom-prefix="iconfont" type="icon-message" size="20" color="#fff" />
    </view>
    <view class="message-content message-bubble">
      <view>
        <text class="message-text">{{ message }}</text>
        <view class="message-time">
          <text class="time-text">{{ time }}</text>
        </view>
      </view>

      <view class="message-actions">
        <uni-icons type="compose" size="20" color="#999999" @tap.stop="handleEdit" />
        <uni-icons type="trash" size="20" color="#999999" @tap.stop="handleDelete" />
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed, ref } from 'vue'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import { delConversation, updateConversation } from '@/api/aiTemplate'
dayjs.locale('zh-cn')

const props = defineProps({
  message: {
    type: [String, Number],
    required: true
  },
  timestamp: {
    type: String,
    required: false,
    default: () => dayjs().format()
  },
  requestId: {
    type: [String, Number],
    required: false,
    default: ''
  },
  conversationId: {
    type: [String, Number],
    required: false,
    default: ''
  },
  title: {
    type: String,
    required: false,
    default: ''
  },
  time: {
    type: String,
    required: false,
    default: ''
  },
  delId: {
    type: [String, Number],
    required: false,
    default: ''
  }
})

const formatTime = computed(() => {
  return props.timestamp ? dayjs(props.timestamp).format('MM月DD日 HH:mm') : ''
})

const handleEdit = () => {
  uni.showModal({
    title: '修改标题',
    content: props.title || '',
    editable: true,
    placeholderText: '请输入新的标题',
    success: async (res) => {
      if (res.confirm && res.content && res.content !== props.title) {
        try {
          const result = await updateConversation({
            conversationId: props.conversationId,
            title: res.content
          })

          if (result.status === 0) {
            // 显示修改成功提示
            uni.showToast({
              title: '修改成功',
              icon: 'success',
              duration: 2000
            })
            // 触发刷新列表事件
            uni.$emit('refresh-chat-list')
          } else {
            throw new Error(result.message || '修改失败')
          }
        } catch (error) {
          console.error('修改标题失败:', error)
          uni.showToast({
            title: error.message || '修改失败',
            icon: 'none',
            duration: 2000
          })
        }
      }
    }
  })
}

const handleDelete = () => {
  // 先弹出确认框
  uni.showModal({
    title: '提示',
    content: '确定要删除这条聊天记录吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          const result = await delConversation(props.conversationId)
          if (result.status === 0) {
            uni.showToast({
              title: '删除成功',
              icon: 'success',
              duration: 1000
            })
            // 触发刷新列表事件
            uni.$emit('refresh-chat-list')
          } else {
            throw new Error(result.message || '删除失败')
          }
        } catch (error) {
          uni.showToast({
            title: error.message || '删除失败',
            icon: 'none',
            duration: 1000
          })
        }
      }
    }
  })
}

const messageTap = () => {
  const id = String(props.conversationId)
  uni.navigateTo({
    url: `/subPages/aiAssistant/chat/index?recordId=${props.delId}&conversationId=${id}&requestId=${props.requestId}&type=chatRecord`
  })
}
</script>

<style scoped lang="scss">
.message-container {
  display: flex;
  align-items: flex-start;
  width: 100%;
  margin-bottom: 28rpx;
  gap: 20rpx;
  position: relative;
}

.message-tail {
  width: 1rpx;
  background-color: #367cff;
  height: calc(100% + 20rpx);
  position: absolute;
  left: 24rpx;
  top: 48rpx;
  opacity: 0.2;
  z-index: 1;
}

.message-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #367cff;
  border-radius: 50%;
  padding: 4rpx 8rpx;
  flex: 0;
  z-index: 1;
}

.message-content {
  flex: 1;
  display: flex;
  gap: 40rpx;
  justify-content: space-between;
  align-items: flex-start;
}

.message-bubble {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 20rpx;
  /* max-width: 80%; */
}

.message-text {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.4;
  font-weight: 500;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.message-time {
  display: flex;
  align-items: center;
  margin-top: 10rpx;
  gap: 8rpx;
}

.time-text {
  font-size: 24rpx;
  color: #999999;
}

.message-actions {
  display: flex;
  gap: 20rpx;
}
</style>
