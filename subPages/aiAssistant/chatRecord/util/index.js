export const jumpToChatRecord = (data) => {
  uni.navigateTo({
    url: `/subPages/aiAssistant/chatRecord/index${data ? '?' + Object.entries(data).map(([key, value]) => `${key}=${value}`).join('&') : ''}`,
    fail: (res) => {
      console.log(res);
      uni.navigateBack();
    },
  });
};

export function calculateDistance(selectorA, selectorB, contextA = null, contextB = null) {
  return new Promise((resolve, reject) => {

    // 创建查询对象
    const query = uni.createSelectorQuery()

    // 查询第一个元素（可能跨组件）
    const queryA = contextA ? query.in(contextA) : query
    queryA.select(selectorA).boundingClientRect(dataA => {
      if (!dataA) {
        reject(new Error(`元素 ${selectorA} 未找到`))
        return
      }

      // 查询第二个元素（可能跨组件）
      const queryB = contextB ? query.in(contextB) : query
      queryB.select(selectorB).boundingClientRect(dataB => {
        if (!dataB) {
          reject(new Error(`元素 ${selectorB} 未找到`))
          return
        }

        // 计算垂直距离（可根据需求修改计算方式）
        const distance = Math.abs(dataB.top - dataA.bottom)
        resolve({
          distance,
          positionA: dataA,
          positionB: dataB
        })
      }).exec()
    }).exec()
  })
}
