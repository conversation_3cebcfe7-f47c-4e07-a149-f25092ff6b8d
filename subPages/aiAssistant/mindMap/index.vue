<template>
  <base-layout nav-title="思维导图" :safe-area-inset-top="true">
    <view class="mindMap-box">
      <web-view :src="webviewPath" @message="getMessage"></web-view>
    </view>
  </base-layout>
</template>

<script setup>
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
// let webviewPath = 'https://ai.mypacelab.com/app/mindMap'
// let webviewPath = ref('http://localhost:5173/app/mindMap')
let webviewPath = ref('https://ai.mypacelab.com/app/mindMap')
let title = ref('')
let id = ref('')
onLoad((option) => {
  // console.log('option',option);
  title.value = option.title
  id.value = option.id
  let token = option.token
  webviewPath.value =
    webviewPath.value + `?title=${title.value}&type=ylf&id=${id.value}&token=${token}`
  // console.log('webviewPath',webviewPath.value)
})

// 接收回调参数
const getMessage = (e) => {
  // console.log('接受到', e.detail.data)
  saveBase64Image(e.detail.data[0])
}

// 保存 base64 图片
const saveBase64Image = (base64Data) => {
  // 去除两边的双引号
  const cleanedBase64 = base64Data.replace(/^"|"$/g, '')

  try {
    // 直接使用base64预览图片
    uni.previewImage({
      urls: [cleanedBase64],
      longPressActions: {
        itemList: ['保存图片'],
        success: function () {
          uni.showToast({
            title: '图片已保存',
            icon: 'success'
          })
        },
        fail: function () {
          uni.showToast({
            title: '保存失败',
            icon: 'none'
          })
        }
      }
    })
  } catch (error) {
    console.error('处理图片预览出错:', error)
    uni.showToast({
      title: '图片预览失败',
      icon: 'none'
    })
  }
}
</script>

<style lang="scss" scoped>
.mindMap-box {
  height: 100%;
  width: 100%;
  overflow: hidden;
}
</style>
