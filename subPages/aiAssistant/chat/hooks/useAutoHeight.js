import { ref } from "vue";

export const useAutoHeight = (maxLine = 2, maxHeight = 400) => {
    const autoHeight = ref(true);
    const textareaHeight = ref('auto');

    const textareaLineChange = (e) => {
      const lineCount = e.detail.lineCount;

      if (lineCount > maxLine) {
        autoHeight.value = false;
        // 设置最大高度限制，超过后启用滚动
        const calculatedHeight = Math.min(lineCount * 40, maxHeight); // 每行约40rpx
        textareaHeight.value = `${calculatedHeight}rpx`;
      } else {
        autoHeight.value = true;
        textareaHeight.value = 'auto';
      }
    };

    return {
        autoHeight,
        textareaHeight,
        handleTextareaLineChange: textareaLineChange
    };
};
