import { onShow, onUnload } from '@dcloudio/uni-app'
import { ref } from 'vue'

export function useKeyboard() {
  const keyboardHeight = ref(0)

  const initKeyboardListener = (callback) => {
    uni.onKeyboardHeightChange((res) => {
      keyboardHeight.value = res.height
      // 如果提供了回调函数，则执行
      callback?.(res.height)
    })
  }

  // 自动处理监听器的生命周期
  onShow(() => {
    initKeyboardListener()
  })

  onUnload(() => {
    // 清理键盘监听
    uni.offKeyboardHeightChange()
    // 重置状态
    keyboardHeight.value = 0
  })

  return {
    keyboardHeight,
    initKeyboardListener
  }
}