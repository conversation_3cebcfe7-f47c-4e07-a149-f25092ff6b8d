<template>
  <base-layout
    ref="baseLayoutRef"
    :nav-title="navTitle"
    :nav-class="isNavTransformed ? 'chat-navbar' : ''"
    navBgColor="transparent"
    containerClass="chat-container"
    :scroll-into-view="scrollToMessage"
    :scroll-with-animation="true"
    :keyboard-height="keyboardHeight"
    :scroll-distance="scrollDistance"
    :scroll-top="scrollTop"
  >
    <template #extra>
      <up-toast ref="uToast"></up-toast>
    </template>

    <view class="chat-page">
      <view class="message-item-wrap">
        <view
          v-for="(item, index) in messageList"
          :key="index"
          :id="'msg-' + index"
          class="message-item"
          :class="{
            'message-user': item.type === 'user',
            'message-ai': item.type === 'ai',
            'last-message': index === messageList.length - 1
          }"
        >
          <view class="message-content">
            <view
              v-if="item.type === 'ai'"
              class="ai-message"
              :class="{ 'error-message': item.isError }"
            >
              <view v-if="item.reasonContent" class="reasonBtn" @click="showReason(index)">
                <img class="icon1" src="/static/icon/reason.png" alt="" />
                已深度思考
                <img
                  class="icon2"
                  :src="reasonVisible[index] ? '/static/icon/down.png' : '/static/icon/top.png'"
                  alt=""
                />
              </view>
              <!-- <towxml
                v-if="item.reasonContent"
                v-show="!reasonVisible[index]"
                :nodes="getReasonHtml(item.reasonContent, index)"
              ></towxml>
              <towxml :nodes="getHtml(item.content)"></towxml> -->
              <ua-markdown :source="item.content"></ua-markdown>
              <view
                v-if="item.type === 'ai' && !isStreaming && !isWelcomeMessage(item, index)"
                class="action-btn-wrapper"
                :id="'msg-' + index"
              >
                <view v-if="item.hasMultipleAnswers" class="answer-navigation">
                  <text class="answer-count"
                    >{{ item.currentAnswerIndex + 1 }}/{{ item.answers.length }}</text
                  >
                  <icon-button
                    v-if="item.currentAnswerIndex > 0"
                    :size="18"
                    type="text"
                    icon="left"
                    text="上一个"
                    @tap="switchAnswer(item, -1)"
                  />
                  <icon-button
                    v-if="item.currentAnswerIndex < item.answers.length - 1"
                    :size="18"
                    type="text"
                    icon="right"
                    text="下一个"
                    @tap="switchAnswer(item, 1)"
                  />
                </view>
                <icon-button
                  v-if="index === messageList.length - 1"
                  :size="18"
                  type="text"
                  icon="icon-el-icon-refresh-left"
                  text="重来"
                  @tap="handleRegenerate(item.id)"
                />
                <view class="action-btn-group">
                  <icon-button
                    :size="18"
                    type="text"
                    icon="hand-down"
                    text="不好"
                    :customIcon="false"
                    @tap="handleFeedback(item.id)"
                  />
                  <icon-button
                    :size="18"
                    type="text"
                    icon="icon-copy"
                    text="复制"
                    @tap="copyMessage(item.content)"
                  />
                  <icon-button
                    :size="18"
                    type="text"
                    icon="icon-export"
                    text="导出"
                    @tap="exportWord(item.id)"
                  />
                </view>
              </view>
            </view>

            <text v-else class="message-text" :user-select="true">{{ item.content }}</text>
          </view>
        </view>

        <!-- Loading message -->
        <view v-if="isStreaming" class="message-item message-ai">
          <view class="message-content">
            <view class="ai-message">
              <view class="thinking-message">
                <up-loading-icon text="正在思考中" textSize="18"></up-loading-icon>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- Error retry message -->
    <view id="retry-message" class="retry-container" v-if="hasError" @tap="handleRetry">
      <uni-icons type="refreshempty" size="12" color="#ff4d4f"></uni-icons>
      <text class="retry-text">{{ errorMessage }}，请重试</text>
    </view>

    <template #footer>
      <!-- <view class="potato-cost">已消耗{{ potatoCost }}土豆</view> -->
      <!-- <up-alert
        v-if="potatoCost > 0"
        closable
        center
        type="warning"
        :description="description"
      ></up-alert> -->
      <view class="footerIndicator"></view>
      <chat-input
        v-model="inputMessage"
        :sendMessage="sendMessage"
        :exportWordAll="exportWordAll"
        :isStreaming="isStreaming"
        ref="chatInputRef"
        :keyboardHeight="keyboardHeight"
      />
    </template>

    <!-- Keyboard spacer -->
    <view :style="{ height: keyboardHeight + 'px' }"></view>
  </base-layout>
</template>
<script setup>
import { ref, nextTick, onMounted, watch, onUnmounted, getCurrentInstance } from 'vue'
import {
  getTemplateRecord,
  createTemplateRecord,
  createConversation,
  generateWordNew,
  generateWholeWordNew,
  getCurrentChatRecordNew,
  regenerateAnswerNew
} from '@/api/aiTemplate.js'
import { parseParams } from './utils/index.js'
import BaseLayout from '@/components/base-layout/base-layout2.vue'
import ChatInput from './components/chat-input.vue'
import IconButton from './components/icon-button.vue'
import { useKeyboard } from './hooks/useKeyboard'
import { calculateDistance } from './util2'
import { debounce } from 'lodash-es'
import { wsClient } from '../websocket'
import UaMarkdown from './components/ua-markdown/ua-markdown.vue'
const reasonVisible = ref({})
// 显示/隐藏理由的方法
const showReason = (index) => {
  reasonVisible.value[index] = !reasonVisible.value[index]
}
const uToast = ref(null)
const baseLayoutRef = ref(null)
const { proxy } = getCurrentInstance()
// 聊天相关状态
const chatInfo = ref({
  requestId: '',
  conversationId: '',
  type: ''
})
// 土豆消耗
const potatoCost = ref(0)
const description = ref('')
const messageList = ref([])
const inputMessage = ref('')
const isStreaming = ref(false)
const hasError = ref(false)
const errorMessage = ref('')
const errorType = ref('') // 保留 errorType 用于区分重试逻辑
// 滚动位置控制
const scrollToMessage = ref('')

const chatInputRef = ref(null)

const { keyboardHeight } = useKeyboard()

const scrollDistance = ref(0)

const isNavTransformed = ref(false)

// 添加 conversationId 的响应式引用
const conversationId = ref('')

const scrollTop = ref(0)

// 添加状态来保存最后一条消息的 questionId
const lastQuestionId = ref(null)

const isRegenerating = ref(false)

const getHtml = (markdown) => {
  return proxy.$towxml(markdown, 'markdown', {
    theme: 'light'
  })
}
const getReasonHtml = (markdown, index) => {
  return proxy.$towxml(markdown, 'markdown', {
    theme: `reason reason-${index}`
  })
}
const navTitle = ref('AI助手')

// 修改键盘监听
watch(
  () => keyboardHeight.value,
  async (height) => {
    if (height === 0) {
      isNavTransformed.value = false
      scrollToMessage.value = ''
      scrollDistance.value = 0
      return
    }

    // 先滚动到最后一条消息
    if (messageList.value.length > 0) {
      scrollToMessage.value = ''
      await nextTick()
      scrollToMessage.value = `msg-${messageList.value.length - 1}`
    }

    // 等待滚动完成
    await new Promise((resolve) => setTimeout(resolve, 100))

    const distanceObj = await calculateDistance('.last-message', '.footerIndicator')
    scrollDistance.value = distanceObj.distance

    // 更新导航栏状态
    isNavTransformed.value = distanceObj.distance <= height
  }
)

// 修改滚动方法，添加防抖
const scrollToLastMessage = async () => {
  // 防止重复执行
  if (scrollToLastMessage.isRunning) return
  scrollToLastMessage.isRunning = true

  try {
    // 等待更长时间确保 Markdown 内容完全渲染
    await new Promise((resolve) => setTimeout(resolve, 300))

    if (messageList.value.length > 0) {
      const lastIndex = messageList.value.length - 1
      scrollToMessage.value = `msg-${lastIndex}`

      await nextTick()

      // 获取所有需要的高度信息
      const [messageContentData, inputAreaData, scrollViewData, contentData] = await Promise.all([
        // 获取消息内容高度
        new Promise((resolve) => {
          uni
            .createSelectorQuery()
            .select(`#msg-${lastIndex} .message-content`)
            .boundingClientRect((data) => resolve(data))
            .exec()
        }),
        // 获取输入区域高度
        new Promise((resolve) => {
          uni
            .createSelectorQuery()
            .select('.input-area')
            .boundingClientRect((data) => resolve(data))
            .exec()
        }),
        // 获取 scroll-view 高度
        new Promise((resolve) => {
          uni
            .createSelectorQuery()
            .select('.content-scroll-view')
            .boundingClientRect((data) => resolve(data))
            .exec()
        }),
        // 获取内容总高度
        new Promise((resolve) => {
          uni
            .createSelectorQuery()
            .select('.message-item-wrap')
            .boundingClientRect((data) => resolve(data))
            .exec()
        })
      ])

      // 只打印一次日志
      console.log('高度信息:', {
        messageContent: messageContentData?.height || 0,
        inputArea: inputAreaData?.height || 0,
        scrollView: scrollViewData?.height || 0,
        content: contentData?.height || 0
      })

      // 计算需要滚动的距离
      const scrollDistance = Math.max(0, (contentData?.height || 0) - (scrollViewData?.height || 0))

      // 设置滚动位置
      if (scrollDistance > 0) {
        scrollTop.value = scrollDistance
      }
    }
  } catch (error) {
    console.error('滚动计算错误:', error)
  } finally {
    // 重置运行状态
    setTimeout(() => {
      scrollToLastMessage.isRunning = false
    }, 100)
  }
}

// 修改错误处理函数
const handleError = (error, message, type) => {
  hasError.value = true
  errorMessage.value = message
  errorType.value = type
  nextTick(() => {
    // 设置滚动到错误提示
    scrollToMessage.value = 'retry-message'
  })
}

// 修改 WebSocket 消息处理函数
const handleWsMessage = async (data) => {
  // 获取
  // let conversationIdArr = JSON.parse(uni.getStorageSync('conversationIdArr'))
  try {
    if (data.key === 'chat' && data.conversationId === conversationId.value) {
      if (data.error) {
        handleError(new Error(data.value || '请求失败'), data.value, 'send')
        isStreaming.value = false
        isRegenerating.value = false
        return
      }
      // 如果是结束消息，保存 questionId
      if (data.end) {
        lastQuestionId.value = data.questionId
        isStreaming.value = false
        if (isRegenerating.value) {
          try {
            const recordRes = await getCurrentChatRecord(chatInfo.value.conversationId)
            if (recordRes.status === 0 && recordRes.data.length > 0) {
              messageList.value = formatChatRecords(recordRes.data)
              // 滚动到最新消息
              nextTick(() => {
                scrollToMessage.value = 'msg-' + (messageList.value.length - 1)
              })
            }
          } catch (error) {
            console.error('获取聊天记录失败:', error)
          } finally {
            isRegenerating.value = false
          }
          return
        }
        // 更新最后一条AI消息的id
        const lastMessage = messageList.value[messageList.value.length - 1]
        if (lastMessage && lastMessage.type === 'ai') {
          lastMessage.id = data.questionId
        }
        // 等待DOM更新完成后再滚动
        nextTick(() => {
          setTimeout(() => {
            // 确保所有消息都在滚动前被正确渲染
            const allMessages = [...messageList.value]
            messageList.value = allMessages
            scrollToLastMessage()
          }, 500)
        })
        return
      }
      // 处理普通聊天消息
      const content = data.value || ''

      if (isStreaming.value && !isRegenerating.value) {
        // 区分是重新生成还是普通对话
        if (isRegenerating.value) {
          // 重新生成时,添加新的AI回复
          if (data.reason) {
            messageList.value.push({
              type: 'ai',
              reasonContent: data.reason ? content : ''
            })
          } else {
            messageList.value.push({
              type: 'ai',
              content: '',
              reasonContent: data.reason ? content : ''
            })
          }
        } else {
          // 普通对话时的逻辑保持不变
          isStreaming.value = false
          messageList.value.push({
            type: 'ai',
            content: '',
            reasonContent: data.reason ? content : ''
          })
        }
      } else {
        // 追加内容到最后一条消息
        const lastMessage = messageList.value[messageList.value.length - 1]
        if (lastMessage && lastMessage.type === 'ai') {
          if (data.reason) {
            // 如果是 reason 内容，追加到 reasonContent
            lastMessage.reasonContent = (lastMessage.reasonContent || '') + content
          } else {
            // 否则追加到普通 content
            lastMessage.content = (lastMessage.content || '') + content
          }
        } else {
          // 如果没有最后一条消息或不是AI消息，创建新消息
          messageList.value.push({
            type: 'ai',
            content: data.reason ? '' : content,
            reasonContent: data.reason ? content : ''
          })
        }
      }
    }
    if (data.key === 'error') {
      // handleError(new Error(data.value || '请求失败'), '请求失败', 'send')
      isStreaming.value = false
    }
  } catch (error) {
    console.error('处理 WebSocket 消息失败:', error)
    handleError(error, '消息处理失败', 'send')
    isRegenerating.value = false
  }
}

// 发送消息
const sendMessage = async (isRetry = false) => {
  if (isStreaming.value) {
    return
  }
  // 重试时不校验发送按钮是否可用
  if (!isRetry && (!chatInputRef.value || chatInputRef.value?.isSendDisabled)) {
    console.log('发送按钮不可用')
    return
  }

  let userMessage = inputMessage.value?.trim()
  if (!userMessage && isRetry) {
    const lastUserMessage = [...messageList.value].reverse().find((item) => item.type === 'user')
    if (lastUserMessage) {
      userMessage = lastUserMessage.content
    }
  }

  if (!userMessage) {
    return
  }
  messageList.value.push({ type: 'user', content: userMessage })
  inputMessage.value = ''
  try {
    setTimeout(() => {
      scrollToLastMessage()
    }, 100)

    isStreaming.value = true

    // 确保有会话 ID
    if (!conversationId.value) {
      console.log('发送聊天', chatInfo.value)

      let initRes = null
      if (chatInfo.value.type == 'directDialogue') {
        initRes = await createConversation({
          templateId: '1',
          message: userMessage,
          // conversationId: conversationId.value,
          classId:
            uni.getStorageSync('USER_INFO')?.currentClassId ||
            uni.getStorageSync('USER_INFO')?.classIds[0],
          schoolId: uni.getStorageSync('USER_INFO')?.currentSchoolId
        })
      } else {
        initRes = await createConversation({
          conversationId: '',
          message: userMessage,
          // conversationId: conversationId.value,
          classId:
            uni.getStorageSync('USER_INFO')?.currentClassId ||
            uni.getStorageSync('USER_INFO')?.classIds[0],
          schoolId: uni.getStorageSync('USER_INFO')?.currentSchoolId
        })
      }
      const id = initRes?.data?.conversationId || initRes?.conversationId
      // 获取土豆消耗
      // potatoCost.value = initRes?.data?.potatoCost || 0
      // description.value = `已消耗${potatoCost.value}土豆`
      if (id) {
        conversationId.value = id
        chatInfo.value.conversationId = id
      } else {
        throw new Error('获取会话ID失败')
      }
    }

    // 发送消息
    wsClient.send({
      key: 'chat',
      value: JSON.stringify({
        conversationId: conversationId.value,
        message: userMessage
      })
    })
  } catch (error) {
    console.error('发送消息失败:', error)
    isStreaming.value = false
    handleError(error, '发送失败，请重试', 'send')
  }
}

// 重试功能
const handleRetry = async () => {
  if (!chatInfo.value.conversationId) {
    uni.showToast({
      title: '会话已失效，请重新开始',
      icon: 'none'
    })
    return
  }
  hasError.value = false
  try {
    switch (errorType.value) {
      case 'init':
        await sendMessage(true)
        break

      case 'record':
        // 重试获取聊天记录，传递正确的 conversationId
        const id = chatInfo.value.conversationId?.data || chatInfo.value.conversationId
        const recordRes = await getCurrentChatRecord(id)
        if (recordRes.code === 20000 && recordRes.data) {
          messageList.value = recordRes.data
            .map((item) => [
              { type: 'user', content: item.question || item.content },
              { type: 'ai', content: item.answer || item.contentRes }
            ])
            .flat()
        } else {
          throw new Error(recordRes.message || '获取聊天记录失败')
        }
        break

      case 'template':
        // 重试模板创建
        await onPageInit()
        break

      case 'send':
        // 重试发送消息
        await sendMessage(true)
        break
    }
  } catch (error) {
    console.error('重试失败:', error)
    handleError(error, `重试${errorType.value}失败`, errorType.value)
  }
}

// 复制消息
const copyMessage = (content) => {
  // 提取纯文本内容
  // const plainText = content.replace(/<\/?[^>]+(>|$)/g, '').replace(/(\r\n|\n|\r)/gm, ' ')

  uni.setClipboardData({
    data: content,
    success: () => {
      uni.showToast({
        title: '复制成功',
        icon: 'success',
        duration: 2000
      })
    }
  })
}

// 使用 debounce 创建防抖函数
const handleFeedback = debounce(
  async (questionId) => {
    try {
      await submitFeedback({ questionId, content: '测试' })
      uToast.value.show({
        message: '反馈成功',
        type: 'success'
      })
    } catch (error) {
      uni.showToast({
        title: '反馈失败',
        icon: 'none'
      })
    }
  },
  2000,
  { leading: true, trailing: false }
)

// 修改重来按钮的处理函数
const handleRegenerate = async (id) => {
  if (!id) {
    uni.showToast({
      title: '无法重新生成，请重试',
      icon: 'none'
    })
    return
  }
  try {
    // 显示加载状态
    isStreaming.value = true
    // 设置重新生成状态
    isRegenerating.value = true
    // 删除最后一条 AI 回复
    const lastMessage = messageList.value[messageList.value.length - 1]
    if (lastMessage && lastMessage.type === 'ai') {
      messageList.value.pop()
    }
    // 调用重新生成接口
    const res = await regenerateAnswerNew(id)
    if (res.status !== 0) {
      throw new Error(res.message || '重新生成失败')
    }
  } catch (error) {
    console.error('重新生成失败:', error)
    uni.showToast({
      title: error.message || '重新生成失败',
      icon: 'none'
    })
    isRegenerating.value = false
    isStreaming.value = false
  }
}

// 修改页面初始化逻辑
const onPageInit = async () => {
  try {
    const [recordId, requestId, id, type, templateId, templateValue, title] = parseParams(
      'recordId',
      'requestId',
      'conversationId',
      'type',
      'templateId',
      'templateValue',
      'title'
    )
    navTitle.value = title || 'AI助手'
    if (type === 'template' && templateId) {
      // 解析并处理模板值
      const templateData = JSON.parse(decodeURIComponent(templateValue))
      // 调用模板生成API
      const res = await createTemplateRecord({
        templateId: Number(templateId),
        templateValue: JSON.stringify(templateData),
        classId:
          uni.getStorageSync('USER_INFO')?.currentClassId ||
          uni.getStorageSync('USER_INFO')?.classIds[0],
        schoolId: uni.getStorageSync('USER_INFO')?.currentSchoolId
      })

      if (res.status === 0 && res.data) {
        const { conversationId: chatId, message } = res.data
        if (!chatId) {
          throw new Error('获取会话ID失败')
        }
        // 获取土豆消耗
        // potatoCost.value = res.data?.potatoCost || 0
        // description.value = `已消耗${potatoCost.value}土豆`
        wsClient.send({
          key: 'start',
          value: JSON.stringify(res.data)
        })

        // let storedConversationArr = uni.getStorageSync('conversationIdArr')
        // let conversationIdArr = storedConversationArr ? JSON.parse(storedConversationArr) : []
        // conversationIdArr.unshift(chatId)
        // uni.setStorageSync('conversationIdArr', JSON.stringify(conversationIdArr))
        // 设置会话ID
        conversationId.value = chatId
        chatInfo.value = {
          conversationId: chatId,
          type: 'template',
          templateId
        }
        // 格式化模板返回的消息作为用户消息
        if (res.data.messageType === 'multiText') {
          try {
            const messages = JSON.parse(res.data.message)
            messages.forEach((msg) => {
              messageList.value.push({
                type: 'user',
                content: msg.text
              })
            })
          } catch (e) {
            console.error('解析多行文本失败:', e)
            messageList.value.push({
              type: 'user',
              content: res.data.message
            })
          }
        } else {
          messageList.value.push({
            type: 'user',
            content: res.data.message
          })
        }

        // 设置流式响应状态为 true（在发送消息之前）
        isStreaming.value = true
        // uni.$on('ws-message', handleWsMessage)
        // 调用一下聊天记录接口（不处理返回结果）
        // getCurrentChatRecord(res.data.conversationId)
      } else {
        throw new Error(res.message || '创建模板对话失败')
      }
    } else if (type === 'directDialogue') {
      // 处理深度思考类型
      // 调用模板生成API
      // const res = await createByAiTemplate({
      //   data: {
      //     templateId: Number(templateId)
      //   }
      // })
      // if (res.code === 20000) {

      //   console.log('深度思考', chatInfo.value)
      // }
      // conversationId.value = res.data.conversationId
      chatInfo.value = {
        type: 'directDialogue',
        templateId
      }
      messageList.value = [
        {
          type: 'ai',
          content: `老师您好，我是基于Deepseek R1的教研AI助手，无论是主题课程背景知识、课程设计、教学实施，还是日常工作的相关问题，我都会努力满足您的需求。请告诉我具体问题，让我们一起开启智能教学研之旅！`
        }
      ]
    } else if (type === 'chatRecord') {
      let res = null
      conversationId.value = id
      try {
        res = await getCurrentChatRecordNew({
          currentPage: 1,
          pageSize: 10,
          pageModel: {
            conversationId: id
          }
        })
        if (res.status === 0) {
          if (res.data?.length > 0) {
            messageList.value = formatChatRecords(res.data)
            // 设置会话信息
            chatInfo.value = {
              conversationId: id,
              type: 'chatRecord'
            }
            nextTick(() => {
              scrollToMessage.value = 'msg-' + (messageList.value.length - 1)
            })
          } else {
            messageList.value = [{ type: 'ai', content: '你好！我是青禾AI，有什么可以帮你的吗？' }]
          }
        } else {
          throw new Error(res.message || '获取聊天记录失败')
        }
      } catch (error) {
        console.error('获取聊天记录失败:', error)
        handleError(error, '获取聊天记录失败', 'record')
        messageList.value = [{ type: 'ai', content: '你好！我是青禾AI，有什么可以帮你的吗？' }]
      }
    } else {
      messageList.value = [{ type: 'ai', content: '你好！我是青禾AI，有什么可以帮你的吗？' }]
    }
  } catch (error) {
    console.error('页面初始化失败:', error)
    handleError(error, '初始化失败', 'init')
  }
}

// 添加判断是否是欢迎消息的函数
const isWelcomeMessage = (item, index) => {
  return (
    index === 0 &&
    (item.content === '你好！我是青禾AI，有什么可以帮你的吗？' ||
      item.content.includes('老师您好，我是基于Deepseek R1的教研AI助手'))
  )
}

// 修改聊天记录处理逻辑
const formatChatRecords = (records) => {
  // 添加日期格式化辅助函数
  const formatDate = (dateStr) => {
    if (!dateStr) return new Date()
    // 将日期字符串转换为 iOS 兼容格式
    return new Date(dateStr.replace(/\s/, 'T'))
  }

  const formattedMessages = []
  const answerGroups = new Map() // 用于存储同一个问题的多个回答
  let lastAiMessage = null // 用于跟踪最后一条AI消息
  records.forEach((record) => {
    // 处理用户消息
    if (record.role === 'user') {
      // 处理多行文本
      if (record.messageType === 'multiText') {
        try {
          const messages = JSON.parse(record.message)
          messages.forEach((msg) => {
            formattedMessages.push({
              type: 'user',
              content: msg.text,
              id: record.id,
              timestamp: record.createTime
            })
          })
        } catch (e) {
          console.error('解析多行文本失败:', e)
          const content = record.message
          formattedMessages.push({
            type: 'user',
            content: content,
            id: record.id,
            timestamp: record.createTime
          })
        }
      } else {
        const content = record.message
        formattedMessages.push({
          type: 'user',
          content: content,
          id: record.id,
          timestamp: record.createTime
        })
      }
    }
    // 处理AI回复
    else if (record.role === 'assistant') {
      lastAiMessage = record // 记录最后一条AI消息
      // 如果有answerId，说明是对同一问题的回复
      if (record.answerId) {
        if (!answerGroups.has(record.answerId)) {
          answerGroups.set(record.answerId, [])
        }
        answerGroups.get(record.answerId).push({
          reasonContent: record.reason || record.reasonContent,
          content: record.message,
          timestamp: record.createTime,
          id: record.id
        })
      } else {
        // 普通回复
        formattedMessages.push({
          type: 'ai',
          reasonContent: record.reason || record.reasonContent,
          content: record.message,
          id: record.id,
          timestamp: record.createTime
        })
      }
    }
  })

  // 修改处理分组回复的逻辑
  answerGroups.forEach((answers, answerId) => {
    if (answers.length > 1) {
      // 按时间排序（降序 - 最新的在前面）
      answers.sort((a, b) => formatDate(b.timestamp) - formatDate(a.timestamp))

      formattedMessages.push({
        type: 'ai',
        reasonContent: answers[0].reasonContent,
        content: answers[0].content, // 默认显示最新的回复
        id: answers[0].id,
        timestamp: answers[0].timestamp,
        hasMultipleAnswers: true,
        answers: answers, // 保存所有回复
        currentAnswerIndex: 0, // 当前显示的回复索引
        totalAnswers: answers.length // 添加总回复数
      })
    } else if (answers.length === 1) {
      // 只有一条回复时，作为普通回复处理
      formattedMessages.push({
        type: 'ai',
        reasonContent: answers[0].reasonContent,
        content: answers[0].content,
        id: answers[0].id,
        timestamp: answers[0].timestamp
      })
    }
  })

  // 更新 lastQuestionId
  if (lastAiMessage) {
    lastQuestionId.value = lastAiMessage.id
  }
  // console.log('formattedMessages', formattedMessages)

  // 修改最终排序逻辑
  return formattedMessages.sort((a, b) => formatDate(a.timestamp) - formatDate(b.timestamp))
}

// 添加切换回复的方法
const switchAnswer = (item, direction) => {
  if (!item.hasMultipleAnswers) return

  const newIndex = item.currentAnswerIndex + direction
  if (newIndex >= 0 && newIndex < item.answers.length) {
    item.currentAnswerIndex = newIndex
    item.content = item.answers[newIndex].content
    item.id = item.answers[newIndex].id
  }
}

// 导出单个对话
const exportWord = async (id) => {
  // loading
  uni.showLoading({
    title: '正在导出...',
    mask: true
  })
  try {
    const res = await generateWordNew(id)
    if (res.status === 0) {
      const url = res.data.url
      const filename = decodeURIComponent(url.split('/').pop().split('?')[0])
      // 在小程序中下载文件
      uni.downloadFile({
        url: url,
        success: (dlRes) => {
          if (dlRes.statusCode === 200) {
            // 重命名临时文件
            const fs = wx.getFileSystemManager()
            const newPath = `${wx.env.USER_DATA_PATH}/${filename}`
            // 打开文档
            fs.copyFileSync(dlRes.tempFilePath, newPath) // 重命名文件
            uni.openDocument({
              filePath: newPath,
              showMenu: true,
              success: () => {
                uni.hideLoading()
                console.log('打开文档成功')
              },
              fail: (error) => {
                uni.hideLoading()
                console.error('打开文档失败:', error)
                uni.showToast({
                  title: '打开文档失败',
                  icon: 'none'
                })
              }
            })
          } else {
            uni.showToast({
              title: '下载失败',
              icon: 'none'
            })
          }
        },
        fail: (error) => {
          uni.hideLoading()
          console.error('下载失败:', error)
          uni.showToast({
            title: '下载失败',
            icon: 'none'
          })
        }
      })
    }
  } catch (error) {
    uni.hideLoading()
  }
}

//  导出完整对话
const exportWordAll = async () => {
  uni.showLoading({
    title: '正在导出...',
    mask: true
  })
  if (!conversationId.value) {
    uni.showToast({
      title: '没有对话记录',
      icon: 'none'
    })
    return
  }
  const res = await generateWholeWordNew(conversationId.value)

  if (res.status === 0) {
    const url = res.data.url
    const filename = decodeURIComponent(url.split('/').pop().split('?')[0])
    // 在小程序中下载文件
    uni.downloadFile({
      url: url,
      success: (dlRes) => {
        if (dlRes.statusCode === 200) {
          // 打开文档
          // 重命名临时文件
          const fs = wx.getFileSystemManager()
          const newPath = `${wx.env.USER_DATA_PATH}/${filename}`
          // 打开文档
          fs.copyFileSync(dlRes.tempFilePath, newPath) // 重命名文件
          uni.openDocument({
            filePath: newPath,
            showMenu: true,
            success: () => {
              uni.hideLoading()
              console.log('打开文档成功')
            },
            fail: (error) => {
              uni.hideLoading()
              console.error('打开文档失败:', error)
              uni.showToast({
                title: '打开文档失败',
                icon: 'none'
              })
            }
          })
        } else {
          uni.showToast({
            title: '下载失败',
            icon: 'none'
          })
        }
      },
      fail: (error) => {
        uni.hideLoading()
        console.error('下载失败:', error)
        uni.showToast({
          title: '下载失败',
          icon: 'none'
        })
      }
    })
  }
}
onMounted(async () => {
  try {
    await wsClient.connect()
    console.log('WebSocket 连接已建立')
  } catch (error) {
    console.error('WebSocket 连接失败:', error)
  }
  // 注册 ws 消息监听器
  uni.$on('ws-message', handleWsMessage)
  // 再执行页面初始化逻辑
  await onPageInit()
  scrollToLastMessage()
})

// 在组件卸载时移除监听和关闭连接
onUnmounted(() => {
  wsClient.close()
  conversationId.value = ''
  uni.$off('ws-message', handleWsMessage)
})
</script>

<style lang="scss" scoped>
.chat-navbar {
  z-index: 1000;
  :deep(.u-status-bar) {
    background: rgba(255, 255, 255, 0.65) !important;
    backdrop-filter: blur(8px) !important;
    -webkit-backdrop-filter: blur(8px) !important;
  }

  :deep(.u-navbar__content) {
    background: rgba(255, 255, 255, 0.65) !important;
    backdrop-filter: blur(8px) !important;
    -webkit-backdrop-filter: blur(8px) !important;
  }
}
.reasonBtn {
  font-size: 24rpx;
  width: 200rpx;
  padding: 10rpx 15rpx;
  background: #e2e8f0;
  border-radius: 12rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 15rpx;
  .icon1 {
    width: 23rpx;
    height: 23rpx;
    margin-right: 10rpx;
  }
  .icon2 {
    width: 50rpx;
    height: 50rpx;
    // margin-left: 5rpx;
  }
}
:deep(.chat-container) {
  background: linear-gradient(180deg, #f2fafc 0%, #e4f4ff 100%), /* 渐变背景 */ #e4f4ff !important; /* 纯色背景 */
  background-size: 100% 50%, 100% 50% !important; /* 各占一半 */
  background-position: top, bottom !important; /* 渐变在上，纯色在下 */
  background-repeat: no-repeat !important; /* 禁止背景重复 */
}

.chat-page {
  min-height: 100%;
  display: flex;
  flex-direction: column;
  background-color: transparent;
}

.message-item-wrap {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding: 20rpx;
  flex: 1;
}

.message-item {
  display: flex;
  margin-bottom: 24rpx;
  position: relative;
  z-index: 1;
  width: 100%;

  .message-content {
    max-width: 80%;
    padding: 20rpx 30rpx;
    border-radius: 10rpx;
    background-color: #fff;
    word-break: break-all;

    // 确保内容正确展示
    .ai-message {
      width: 100%;

      :deep(p) {
        margin: 0;
        line-height: 1.5;
      }
    }

    .action-btn-wrapper {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-wrap: wrap; // 允许按钮换行
      gap: 8rpx;

      .answer-navigation {
        display: flex;
        align-items: center;
        gap: 8rpx;

        .answer-count {
          font-size: 24rpx;
          color: #999;
          margin: 0 8rpx;
        }
      }

      .action-btn-group {
        display: inline-flex;
        align-items: center;
        justify-content: flex-end;
        gap: 16rpx;
        margin-left: auto;
      }
    }
  }

  &.message-user {
    flex-direction: row-reverse;

    .message-content {
      border-radius: 40rpx 0 40rpx 40rpx;
      background-color: #367cff;
      color: #fff;
      font-size: 28rpx;
    }
  }

  &.message-ai {
    .message-content {
      background-color: #fff;
      border-radius: 0 40rpx 40rpx 40rpx;

      // 错误消息样式
      &.error-message {
        background-color: #fff2f0;
        border: 1px solid #ffccc7;

        :deep(p) {
          color: #ff4d4f;
          margin: 0;
        }
      }
    }
  }
}

.popup-container {
  flex: 0 !important;
}

.popup-content {
  width: 90vw;
  height: calc(100vh - env(safe-area-inset-top) - env(safe-area-inset-bottom));
  display: flex;
}

.list-icon {
  width: 60rpx;
  height: 60rpx;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
}

.retry-container {
  position: absolute;
  bottom: 10rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  .retry-text {
    color: #ff4d4f;
    font-size: 20rpx;
    margin-left: 10rpx;
  }
}

.error-message {
  color: #ff4d4f !important;

  :deep(p) {
    color: #ff4d4f !important;
  }
}

:deep(.base-layout-content) {
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; // 增加 iOS 滚动惯性
  display: flex;
  flex-direction: column;
  position: relative;
}

.message-item {
  scroll-margin-top: 20rpx;
}

.thinking-message {
  padding: 10rpx 0;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.input-area {
  position: relative;
  z-index: 100;
}

.answer-navigation {
  display: flex;
  align-items: center;
  gap: 8rpx;

  .answer-count {
    font-size: 24rpx;
    color: #999;
    margin: 0 8rpx;
  }
}

.potato-cost {
  padding: 10rpx 20rpx;
}
</style>
