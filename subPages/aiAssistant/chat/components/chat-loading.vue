<template>
  <view class="container">
    <view class="circle left"></view>
    <view class="circle middle"></view>
    <view class="circle right"></view>
  </view>
</template>

<script setup>
defineProps({
  size: {
    type: String,
    default: '20rpx'
  },
})
</script>
<style lang="css" scoped>
.container {
  display: inline-flex;
  justify-items: flex-start;
  align-items: center;
  gap: 10rpx;
  height: v-bind(size);
}

.circle {
  width: v-bind(size);
  height: v-bind(size);
  border-radius: 50%;
  animation: scaleAnimation 1.5s infinite ease-in-out;
}

.left {
  background-color: rgb(189, 212, 255);
  /* <PERSON><PERSON> JSON 颜色 */
  animation-delay: 0s;
}

.middle {
  background-color: rgb(123, 169, 255);
  animation-delay: 0.2s;
}

.right {
  background-color: rgb(54, 124, 255);
  animation-delay: 0.4s;
}

@keyframes scaleAnimation {
  0% {
    transform: scale(1);
  }

  33% {
    transform: scale(0.4);
  }

  66% {
    transform: scale(1.1);
  }

  100% {
    transform: scale(1);
  }
}
</style>