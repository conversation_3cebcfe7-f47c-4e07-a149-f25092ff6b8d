<template>
  <view class="input-area" :style="inputAreaStyle">
    <view class="icon-button-wrapper">
      <icon-button
        class="icon-button"
        :size="14"
        icon="icon-message"
        text="新建会话"
        @tap="openNewChat"
      />
      <icon-button
        class="icon-button"
        :size="14"
        icon="icon-export"
        text="导出完整对话"
        @tap="exportWordAll"
      />
      <icon-button
        class="icon-button"
        :size="14"
        icon="icon-history"
        text="历史记录"
        @tap="jumpToChatRecord({ responseKey: 'conversations' })"
      />
    </view>
    <view class="text-area-wrapper">
      <up-textarea
        ref="textareaRef"
        class="text-area"
        v-model="inputValue"
        :adjust-position="false"
        @linechange="handleTextareaLineChange"
        maxlength="-1"
        @confirm="sendMessage"
        placeholder="有问题，就告诉我吧～"
        confirm-type="none"
        :auto-height="autoHeight"
        :height="textareaHeight"
        border="none"
        disableDefaultPadding
        :cursor-spacing="10"
        :showConfirmBar="false"
        :is-style="false"
      />
      <view class="message-input">
        <uni-icons
          color="#fff"
          class="send-icon"
          :class="{ disabled: isSendDisabled }"
          type="arrow-up"
          size="20"
          @tap="!isSendDisabled && sendMessage()"
        ></uni-icons>
      </view>
    </view>
  </view>
</template>
<script setup>
import { ref, computed, watch } from 'vue'
import IconButton from './icon-button.vue'
import { useAutoHeight } from '../hooks/useAutoHeight.js'
import { jumpToChatRecord } from '../util2'
const props = defineProps({
  isStreaming: {
    type: Boolean,
    default: false
  },
  modelValue: {
    type: String,
    default: ''
  },
  sendMessage: {
    type: Function
  },
  exportWordAll: {
    type: Function
  },
  keyboardHeight: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['update:modelValue'])

// 添加本地状态
const inputValue = ref('')

// 监听 props.modelValue 的变化，更新本地状态
watch(
  () => props.modelValue,
  (newVal) => {
    inputValue.value = newVal
  },
  { immediate: true }
)

// 监听本地状态的变化，触发更新
watch(
  () => inputValue.value,
  (newVal) => {
    emit('update:modelValue', newVal)
  }
)

const { autoHeight, textareaHeight, handleTextareaLineChange } = useAutoHeight(2, 500)

// 修改 inputAreaStyle 计算属性
const inputAreaStyle = computed(() => {
  if (props.keyboardHeight > 0) {
    return {
      transform: `translateY(-${props.keyboardHeight}px)`,

      paddingBottom: '20rpx' // 键盘弹起时使用固定padding
    }
  }

  return {
    paddingBottom: 'calc(20rpx + env(safe-area-inset-bottom))' // 键盘收起时考虑安全区域
  }
})

// 计算属性：判断发送按钮是否禁用
const isSendDisabled = computed(() => {
  return !props.modelValue.trim() || props.isStreaming
})

const openNewChat = () => {
  uni.redirectTo({ url: '/subPages/aiAssistant/chat/index' })
}

// 添加方法获取输入框高度
const getInputAreaHeight = () => {
  return new Promise((resolve) => {
    uni
      .createSelectorQuery()
      .select('.input-area')
      .boundingClientRect((data) => {
        resolve(data?.height || 0)
      })
      .exec()
  })
}

defineExpose({
  isSendDisabled,
  getInputAreaHeight
})
</script>
<style lang="scss" scoped>
.input-area {
  transition: 'all 0.3s';
  padding: 20rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  max-height: 60vh; /* 限制最大高度为视口高度的60% */
  overflow-y: auto; /* 超出时启用滚动 */

  z-index: 99;
  will-change: transform;

  .icon-button-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16rpx;
    margin-bottom: 20rpx;

    .icon-button {
      flex-grow: 1;
    }
  }

  .text-area-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    padding: 0 10rpx 0 20rpx;
    border-radius: 40rpx;
    flex: 1;

    background: #ffffff;

    .text-area {
      flex: 1;
      margin: 10rpx 0;
      max-height: 500rpx;
      overflow-y: auto;
    }

    .message-input {
      display: flex;
      align-items: center;
      align-self: flex-end;
      padding: 14rpx 0;
    }
  }

  // 添加硬件加速
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
}

.plus-icon {
  color: #666;
  margin-right: 16rpx;
}

.send-icon {
  background-color: #367cff;
  border-radius: 50%;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;

  &.disabled {
    background-color: #ccc;
    opacity: 0.6;
    pointer-events: none;
  }
}
</style>
