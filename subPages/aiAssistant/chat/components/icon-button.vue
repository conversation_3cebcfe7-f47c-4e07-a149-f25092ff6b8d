<template>
  <view
    @tap="onTap"
    class="container"
    :class="{
      'text-button': type === 'text',
      default: type === 'default',
      'secondary-text': type === 'secondary',
    }"
  >
    <uni-icons
      :style="{background: type === 'secondary' ? '#F5F5F5' : 'transparent', padding: type === 'secondary' ? '10rpx' : '0', borderRadius: type === 'secondary' ? '50%' : '0'}"
      :color="color"
      :custom-prefix="customIcon ? 'iconfont' : ''"
      :size="size"
      :type="icon"
    />
    <text>{{ text }}</text>
  </view>
</template>
<script setup>
const props = defineProps({
  icon: {
    type: String,
    required: true,
  },
  onTap: {
    type: Function,
    default: () => {},
  },
  size: {
    type: Number,
    default: 14,
  },
  color: {
    type: String,
    default: "#333333",
  },
  text: {
    type: String,
    required: true,
  },
  type: {
    type: String,
    default: "default",
  },
  customIcon: {
    type: Boolean,
    default: true,
  },
});
</script>
<style lang="scss" scoped>
.container {
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.default {
  display: flex;
  height: 40rpx;
  border-radius: 30rpx;
  border: 1px solid #fff;
  padding: 10rpx 20rpx;
  background-color: #f7fbff;
  font-size: 28rpx;
}

.text-button {
  display: inline-flex;
  color: #808080;
  font-size: 24rpx;
}

.secondary-text{
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: #808080;
  white-space: nowrap;
}
</style>
