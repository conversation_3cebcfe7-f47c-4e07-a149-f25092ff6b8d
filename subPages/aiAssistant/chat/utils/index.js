import { Base64 } from "js-base64";
import { generateWord, exportWordByTemplate } from "@/api/aiTemplate";

// 辅助函数：将 Uint8Array 转换为字符串
export const uint8ArrayToString = (array) => {
  const arrayBuffer = new Uint8Array(array);
  const base64 = uni.arrayBufferToBase64(arrayBuffer);
  return Base64.decode(base64);
};

// 转换为对象数组的函数
export const parseDataToArray = (dataString) => {
  const dataRegex = /^data: ({.*})$/gm;
  const matches = [...dataString.matchAll(dataRegex)];

  return matches.map((match) => JSON.parse(match[1]));
};

export const parseParams = (...params) => {
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.options || {};
  return params.map((param) => options[param]);
};

export const exportWord = async (questionId, templateRecordId) => {
  let wordUrl;

  try {
    const res =
      templateRecordId === undefined
        ? await generateWord(questionId)
        : await exportWordByTemplate({ templateRecordId, questionId });
    wordUrl = res.data.wordUrl;

    const result = await uni.downloadFile({
      url: wordUrl,
    });

    let savedFilePath;

    // 保存文件到本地
    if (result.statusCode === 200) {
      uni.saveFile({
        tempFilePath: result.tempFilePath,
        success: (res) => {
          console.log("result", result, 'saveRes', res);
          savedFilePath = res.savedFilePath;
          uni.showToast({
            title: "下载成功",
            icon: "success",
          });
          uni.openDocument({
            filePath: savedFilePath,
            showMenu: true, // 显示右上角菜单可用其他应用打开
          });
        },
        fail: (err) => {
          console.log("err", err);
        },
      });
    }


  } catch (error) {
    uni.showToast({
      title: `文件下载失败，请重试`,
      icon: "fail",
    });
    return false;
  }

  return true;
};
