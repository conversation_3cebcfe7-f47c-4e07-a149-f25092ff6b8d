<template>
  <base-layout
    nav-title="青禾AI"
    :footerStyle="{
      paddingBottom: `calc(20rpx + env(safe-area-inset-bottom));`,
      backgroundColor: '#fff'
    }"
    @scrolltolower="loadMore"
  >
    <text class="tips">如未找到所需模板，可前往「我的」联系客服，提交需求</text>
    <!-- 搜索框 -->
    <view class="search-wrapper">
      <input
        type="text"
        v-model="pageParams.pageModel.keyword"
        placeholder="搜索应用..."
        confirm-type="search"
        @input="handleInput"
      />
      <image class="search-icon" src="/static/icon/search.png" alt="" />
    </view>
    <view class="template-wrapper">
      <view class="template-item" v-for="(item, index) in templateList" :key="index">
        <ai-template-item v-bind="item" />
      </view>
    </view>
    <up-loadmore
      :status="loading"
      loadmore-text="上拉或点击加载更多"
      @loadmore="loadMore"
    ></up-loadmore>
    <template #footer>
      <view class="footer-indicator"></view>
      <up-button color="#367CFF" text="立即聊天" @tap="goToChat" shape="circle"></up-button>
    </template>
  </base-layout>
</template>
<script setup>
import { onShow, onShareAppMessage } from '@dcloudio/uni-app'
import { sharePageObj } from '@/utils'
import { reactive, ref } from 'vue'
import aiTemplateItem from './components/aiTemplateItem.vue'
import { getAiTemplateList, getAiTemplateListNew } from '../../../api/aiTemplate.js'
import BaseLayout from '../../../components/base-layout/base-layout2.vue'

const templateList = ref([])

const loading = ref('loadmore') // loading状态 loadmore: 加载更多 nomore: 没有更多 loading: 加载中

const pageParams = reactive({
  pageSize: 100,
  currentPage: 0,
  pageModel: {
    keyword: ''
  }
})

const total = ref(0)

const loadMore = () => {
  pageParams.currentPage++
  getTemplateList()
}

const handleInput = () => {
  pageParams.currentPage = 0
  // pageParams.pageModel.keyword = ''
  templateList.value = []
  getTemplateList()
}

const getTemplateList = async () => {
  loading.value = 'loading'
  const res = await getAiTemplateListNew(pageParams)
  templateList.value.push(...res.data)
  total.value = res.total
  loading.value = 'loadmore'

  if (templateList.value.length >= total.value) {
    loading.value = 'nomore'
  } else {
    loading.value = 'loadmore'
  }
}

onShow(() => {
  loadMore()
})

const goToChat = () => {
  uni.navigateTo({
    url: '/subPages/aiAssistant/chat/index',
    fail: (err) => {
      console.log(err)
      uni.switchTab({
        url: '/pages/aiAssistant/index'
      })
    }
  })
}
onShareAppMessage(() => sharePageObj())
</script>
<style lang="scss" scoped>
.layout {
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #f5f5f5 0%, #f5f5f5 100%);
}

.tips {
  color: #333;
  font-size: 24rpx;
  display: flex;
  justify-content: center;
}

.footer {
  padding: 40rpx;
  display: flex;
  gap: 20rpx;
}

.scroll-container {
  flex: 1;
  background: linear-gradient(180deg, #f5f5f5 0%, #f5f5f5 100%);
  height: calc(100% - 80rpx);
  overflow-y: auto;
}

.template-wrapper {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  padding: 20rpx;
  justify-content: space-between;
}

.template-item {
  width: 50%;
  padding: 14rpx;
  box-sizing: border-box;
}

.search-wrapper {
  margin: 10rpx 20rpx 15rpx;
  height: 64rpx;
  border: 1rpx solid rgba(238, 238, 238, 1);
  box-shadow: 4rpx 8rpx 16rpx rgba(0, 0, 0, 0.02);
  border-radius: 24rpx;
  background: #fff;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  ::v-deep input {
    width: 100%;
    height: 64rpx;
    font-size: 13px;
  }
  .search-icon {
    width: 40rpx;
    height: 40rpx !important;
  }
}
</style>
