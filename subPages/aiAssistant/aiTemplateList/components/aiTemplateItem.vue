<template>
  <card @tap="openAiTemplatePage(id, name)"
        :containerStyle="{ display: 'flex', alignItems: 'center', minHeight: '80rpx' }">
    <image class="template-logo" :src="templateImageUrl" mode="widthFix" v-if="!!templateImageUrl"></image>
    <image class="template-logo" src="@/static/icon/defaultTemplateIcon.svg" mode="widthFix" v-else />
    <text class="template-name">{{ name }}</text>
  </card>
</template>

<script setup>
import { openAiTemplatePage } from "@/utils";
import Card from "@/components/card/card.vue";
const props = defineProps({
  templateImageUrl: {
    type: String,
    default: "",
  },
  name: {
    type: String,
    default: "",
  },
  id: {
    type: [String, Number],
    default: null,
  },
});
</script>

<style lang="scss" scoped>
.template-logo {
  width: 60rpx;
  // border-radius: 20rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.template-name {
  font-weight: 500;
  font-size: 28rpx;
  color: #333333;
}
</style>
