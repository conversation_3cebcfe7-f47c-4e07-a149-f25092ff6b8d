import { request } from "../../common/request.js";

// 获取ai模版详情
export const getAiTemplateItem = (id) => {
  return request({
    url: `/business/ai-assistant-template/detail/${id}`,
    method: "POST",
  });
};

// 上传文件到ai
export const uploadFileToAi = (data) => {
  return request({
    url: `/business/assistant_conversation/upload`,
    method: "POST",
    data,
  });
};

// 创建流式对话
export const initialChat = () => {
  return request({
    url: "/business/assistant_conversation/start",
    method: "POST",
  });
};

// 获取ai模版详情
export const getAiTemplateItemNew = (templateId) => {
  return request({
    url: `/jsapi/template/chat/getTemplateDetail/${templateId}`,
    method: "GET",
  });
};
