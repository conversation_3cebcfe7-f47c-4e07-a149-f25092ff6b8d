import { ref } from 'vue';
import { initialChat } from '../api';
import config from '../../../common/config';
import { TOKEN_NAME } from '../../../common/request';

export const useUploadToAI = () => {
  const fileList = ref([]);
  const chatInfo = ref({
    conversationId: '',
    requestId: ''
  });
  const url = `${config[config.DEFINE_ENV].TRUE_API}${config[config.DEFINE_ENV].BASE_API}/business/assistant_conversation/upload`;

  // 初始化对话，获取 conversationId
  const initConversation = async () => {
    try {
      const res = (await initialChat()).data;
      chatInfo.value.conversationId = res.conversationId;
      chatInfo.value.requestId = res.requestId;
      return res;
    } catch (error) {
      console.error('初始化对话失败:', error);
      throw error;
    }
  };

  // 上传单个文件
  const uploadFile = async (tempFilePath) => {
    // 如果没有 conversationId，先初始化对话
    if (!chatInfo.value.conversationId) {
      await initConversation();
    }

    return new Promise((resolve, reject) => {
      uni.uploadFile({
        url,
        filePath: tempFilePath,
        name: 'file',
        header: {
          'Authorization': uni.getStorageSync(TOKEN_NAME) || '',
        },
        formData: {
          conversationId: chatInfo.value.conversationId,
        },
        success: (res) => {
          if (res.statusCode === 200) {
            const data = JSON.parse(res.data);
            resolve(data);
          } else {
            reject(new Error('上传失败'));
          }
        },
        fail: (error) => {
          reject(error);
        }
      });
    });
  };

  // 添加图片（处理 up-upload 的 afterRead 事件）
  const addImages = async (event) => {
    console.log('addImages', event);
    // 统一处理为数组，兼容 multiple 模式
    let lists = [].concat(event.file);
    let fileListLen = fileList.value.length;

    // 先将所有文件添加到列表，状态为 uploading
    lists.forEach(item => {
      fileList.value.push({
        ...item,
        status: 'uploading',
        message: '上传中',
      });
    });

    // 逐个上传文件
    for (let i = 0; i < lists.length; i++) {
      try {
        const result = await uploadFile(lists[i].url);
        let item = fileList.value[fileListLen];
        fileList.value.splice(fileListLen, 1, {
          ...item,
          status: 'success',
          message: '',
          ...result
        });
        fileListLen++;
      } catch (error) {
        let item = fileList.value[fileListLen];
        fileList.value.splice(fileListLen, 1, {
          ...item,
          status: 'failed',
          message: '上传失败'
        });
        fileListLen++;
        console.error('上传失败:', error);
      }
    }
  };

  // 删除图片
  const deleteImage = (index) => {
    fileList.value.splice(index, 1);
  };

  return {
    fileList,
    chatInfo,
    addImages,
    deleteImage,
    initConversation
  };
};

