import {onShow} from "@dcloudio/uni-app";
import { ref } from 'vue'

export function useQueryParams(...params) {
  const getPageOptions = () => {
    try {
      const pages = getCurrentPages();
      return pages[pages.length - 1].options ?? {}
    } catch (e) {
      console.warn('获取页面参数失败:', e)
      return {}
    }
  }

  // 创建响应式对象
  const paramsObj = ref({})

  // 初始化参数
  const initParams = () => {
    const current = getPageOptions()
    params.forEach(param => {
      paramsObj.value[param] = current[param]
    })
  }
  initParams()

  // 更新方法
  const updateParams = () => initParams()

  onShow(() => {
    updateParams()
  });

  return {
    params: paramsObj,
    updateParams
  }
}
