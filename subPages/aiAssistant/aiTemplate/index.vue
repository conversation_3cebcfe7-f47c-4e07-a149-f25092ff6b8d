<template>
  <base-layout2
    :nav-title="title"
    containerClass="template-container"
    navBgColor="transparent"
    :footerStyle="{
      paddingBottom: `calc(20rpx + env(safe-area-inset-bottom));`,
      backgroundColor: '#fff',
      display: footerVisible ? 'block' : 'none'
    }"
  >
    <view class="template-page">
      <view class="form-container">
        <view v-if="shouldShowExampleButton" class="example-btn-container">
          <view class="example-btn" @click="fillExample">填入示例</view>
        </view>
        <ai-dynamic-form
          ref="dynamicFormRef"
          :title="title"
          :templateId="id"
          @update-footer-visible="updateFooterVisible"
        />
      </view>
    </view>
    <template #footer>
      <view class="footer">
        <icon-button
          type="secondary"
          icon="icon-history"
          text="历史记录"
          @tap="jumpToChatRecord({ type: 'template', id })"
        />
        <up-button
          color="#367CFF"
          shape="circle"
          @tap="submitForm"
          type="primary"
          text="一键生成"
        ></up-button>
      </view>
    </template>
  </base-layout2>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import aiDynamicForm from './components/aiDynamicForm.vue'
import { pickBy, isEmpty } from 'lodash-es'
// import IconButton from '@/subPages/chat/components/icon-button.vue'
import { parseParams } from '../chat/utils'
import { jumpToChatRecord } from '../chatRecord/util'
import BaseLayout2 from '@/components/base-layout/base-layout2.vue'
const dynamicFormRef = ref(null)
const id = ref(undefined)
const title = ref(undefined)
const footerVisible = ref(true)
const shouldShowExampleButton = computed(() => {
  return ['观察记录', '项目制课程设计', '教研计划'].includes(title.value)
})

// 页面每次显示时重新获取参数
onShow(() => {
  const [pageId, pageTitle] = parseParams('id', 'title')
  id.value = pageId
  title.value = pageTitle
})

const updateFooterVisible = (visible) => {
  footerVisible.value = visible
}

const buildSubmitData = (validateData, formDataWithDefaultValue) => {
  return {
    ...formDataWithDefaultValue,
    ...pickBy(validateData, (value) => !isEmpty(value))
  }
}

const submitForm = async () => {
  try {
    const validateData = await dynamicFormRef.value.validate()
    let templateValue = []
    const submitData = buildSubmitData(validateData, dynamicFormRef.value.formDataWithDefaultValue)
    for (const [key, value] of Object.entries(submitData)) {
      if (Array.isArray(value)) {
        templateValue.push({
          name: key,
          value: value.join(',')
        })
      } else {
        templateValue.push({
          name: key,
          value: value
        })
      }
    }
    uni.navigateTo({
      url: `/subPages/aiAssistant/chat/index?templateId=${id.value}&type=template&templateValue=${JSON.stringify(
        templateValue
      )}&title=${title.value}`
    })
  } catch (error) {
    console.error('提交失败：', error)
  }
}

const fillExample = () => {
  if (dynamicFormRef.value) {
    dynamicFormRef.value.fillExampleData()
  }
}
</script>

<style lang="scss" scoped>
.form-container {
  padding: 20rpx;
}
.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12rpx;
  gap: 16rpx;
}

.popup-content {
  width: 90vw;
  display: flex;
}
::v-deep .u-button__text {
  font-size: 28rpx !important;
}

.example-btn-container {
  display: flex;
  justify-content: center;
  margin-bottom: 20rpx;
}

.example-btn {
  padding: 12rpx 24rpx;
  background-color: #367CFF;
  color: #fff;
  border-radius: 8rpx;
  cursor: pointer;
}
</style>
