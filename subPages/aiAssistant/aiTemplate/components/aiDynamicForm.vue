<template>
  <up-form
    :model="formData"
    ref="formRef"
    labelPosition="top"
    labelWidth="auto"
    :labelStyle="labelStyle"
  >
    <view
      class="form-item-container"
      :class="{ 'form-item-container-select': item.type === 'select' }"
      :key="index"
      v-for="(item, index) in formConfig"
    >
      <view class="form-item-header">
        <up-form-item :label="item.keyName" :prop="item.keyName" :required="item.required == 1">
          <!-- 移除示例按钮 -->
          <template #label>
            <view class="label-container">
              <text>{{ item.keyName }}</text>
              <text v-if="item.required == 1" class="required-star">*</text>
            </view>
          </template>

          <!-- Input类型 -->
          <up-textarea
            placeholder-style="color:#B1B3B5;"
            type="textarea"
            maxlength="-1"
            border="none"
            confirm-type="none"
            v-if="item.type === 'input'"
            v-model="formData[item.keyName]"
            :placeholder="item.tip"
            class="form-item-textarea"
            :class="{ 'isRequired': item.required === 1 }"
            :style="{ padding: '10rpx 0 0 0' }"
            :height="60"
          />

          <view class="form-item-tip" v-if="item.type === 'select' || item.type === 'multiSelect'">{{
            item.tip
          }}</view>
          <!-- Select类型 -->
          <uni-data-checkbox
            v-if="item.type === 'select'"
            v-model="formData[item.keyName]"
            :localdata="parseMultiSelectOptions(item.extend)"
            :multiple="false"
            mode="button"
            wrap
            :style="{
              marginTop: '20rpx'
            }"
          />

          <up-picker
            v-if="item.type === 'select'"
            :show="pickers[item.keyName]?.show"
            :columns="[parseOptions(item.extend)]"
            @confirm="handlePickerConfirm($event, item)"
            @cancel="closePicker(item)"
          />

          <!-- MultiSelect类型 -->
          <uni-data-checkbox
            v-if="item.type === 'multiSelect'"
            v-model="formData[item.keyName]"
            :localdata="parseMultiSelectOptions(item.extend)"
            :multiple="true"
            mode="button"
            wrap
            :style="{
              marginTop: '20rpx'
            }"
          />

          <!-- Uploader类型 -->
          <up-upload
            v-if="item.type === 'uploader'"
            v-model="formData[item.keyName]"
            :placeholder="item.tip"
          />
        </up-form-item>
      </view>
    </view>
  </up-form>
</template>

<script setup>
import { ref, onMounted, nextTick, reactive, watch } from 'vue'
import { getAiTemplateItemNew } from '../../api'

const emit = defineEmits(['update-footer-visible'])

const props = defineProps({
  templateId: {
    required: true
  },
  title: {
    required: false,
    default: ''
  }
})

const formRef = ref(null)
const formConfig = ref([])
const formData = ref({})
const formDataWithDefaultValue = ref({})
const templateScenarios = ref('')

// 右侧插入红色*号
const labelStyle = {
  fontSize: '15px',
  fontWeight: '600',
  color: 'rgba(51, 51, 51, 1)'
}

// 替换 actionSheets 为 pickers
const pickers = reactive({})

// 修改 parseOptions 方法
const parseOptions = (extend) => {
  let options = []
  if (typeof extend === 'string') {
    try {
      options = extend.split(',')
    } catch (e) {
      options = []
    }
  } else if (Array.isArray(extend)) {
    options = extend
  }
  return options
}

// 添加 parseMultiSelectOptions 方法
const parseMultiSelectOptions = (extend) => {
  let options = []
  if (typeof extend === 'string') {
    try {
      options = extend.split(',')
    } catch (e) {
      options = []
    }
  } else if (Array.isArray(extend)) {
    options = extend
  }

  // 转换为 uni-data-checkbox 需要的格式
  return options.map((item) => ({
    text: item,
    value: item
  }))
}

// 初始化表单数据
const initFormData = (config) => {
  const data = {}
  config.forEach((item) => {
    if (item.type === 'multiSelect') {
      data[item.keyName] = [] // 确保初始化为空数组
      formDataWithDefaultValue.value[item.keyName] = item.defaultValue
        ? item.defaultValue.split(',')
        : []
    } else {
      data[item.keyName] = ''
      formDataWithDefaultValue.value[item.keyName] = item.defaultValue ?? ''
    }
  })
  formData.value = data
  // 加载保存的数据
  loadFormDataFromStorage()
}

// 获取表单配置
const getFormConfig = async () => {
  try {
    const res = await getAiTemplateItemNew(props.templateId)
    formConfig.value = res.data || []
    initFormData(formConfig.value)
    templateScenarios.value = res.data.applicationScenarios
    // 确保 formRef 已经挂载后再设置规则
    nextTick(() => {
      generateRules(formConfig.value)
    })
  } catch (error) {
    console.error('获取表单配置失败：', error)
  }
}

// 生成验证规则
const generateRules = (config) => {
  const rulesObj = {}

  config.forEach((item) => {
    const ruleArray = []

    switch (item.type) {
      case 'input':
        if (item.required === 1) {
          ruleArray.push({
            required: true,
            message: `请输入${item.keyName}`,
            trigger: ['change', 'blur']
          })
        }
        break

      case 'select':
        if (item.required === 1) {
          ruleArray.push({
            required: true,
            message: `请选择${item.keyName}`,
            trigger: 'change'
          })
        }
        break

      case 'multiSelect':
        if (item.required === 1) {
          ruleArray.push({
            type: 'array',
            min: 1,
            message: `请至少选择一个${item.keyName}`,
            trigger: 'change'
          })
        }
        break

      case 'uploader':
        if (item.required === 1) {
          ruleArray.push({
            validator: (rule, value, callback) => {
              if (!value || (Array.isArray(value) && value.length === 0)) {
                callback(new Error(`请上传${item.keyName}`))
              } else {
                callback()
              }
            },
            trigger: 'change'
          })
        }
        break
    }

    if (ruleArray.length > 0) {
      rulesObj[item.keyName] = ruleArray
    }
  })

  // 使用 setRules 方法设置规则
  formRef.value?.setRules(rulesObj)
}

// 显示 Picker
const showPicker = (item) => {
  if (!pickers[item.keyName]) {
    pickers[item.keyName] = { show: false }
  }
  pickers[item.keyName].show = true
  emit('update-footer-visible', false)
}

// 关闭 Picker
const closePicker = (item) => {
  pickers[item.keyName].show = false
  emit('update-footer-visible', true)
}

// 处理选择确认
const handlePickerConfirm = (event, item) => {
  formData.value[item.keyName] = event.value[0]
  closePicker(item)
}

// 保存表单数据到本地存储
const saveFormDataToStorage = () => {
  const storageKey = `form_data_${props.templateId}`
  uni.setStorageSync(storageKey, formData.value)
}

// 从本地存储加载表单数据
const loadFormDataFromStorage = () => {
  const storageKey = `form_data_${props.templateId}`
  const savedData = uni.getStorageSync(storageKey)
  if (savedData) {
    formData.value = { ...formData.value, ...savedData }
  }
}

// 监听表单数据变化，自动保存
watch(
  () => formData.value,
  (newVal) => {
    saveFormDataToStorage()
  },
  { deep: true }
)

onMounted(() => {
  getFormConfig()
})

// 定义验证方法
const validate = () => {
  return new Promise(async (resolve, reject) => {
    if (!formRef.value) {
      console.error('formRef is null')
      reject(new Error('表单实例不存在'))
      return
    }

    try {
      const valid = await formRef.value?.validate()
      if (valid) {
        resolve(formData.value)
      } else {
        reject(new Error('表单验证失败'))
      }
    } catch (error) {
      reject(error)
    }
  })
}

// 示例数据
const exampleData = {
  '观察记录': {
    '观察对象': '七七、朵朵',
    '观察年龄段': '中班',
    '观察类型': '游戏观察',
    '观察地点': '角色区',
    '活动名称': '旅行前的准备',
    '活动梗概': '七七扮演爸爸、朵朵扮演妈妈，他们在收拾物品，准备他们自己和宝宝的生活用品、衣服、玩具等。一遍想象旅行的地方和旅行的过程，一遍准备相应的物品。'
  },
  '项目制课程设计': {
    '年级': '大班',
    '项目课程名称': '研发种植区滴水器',
    '驱动性问题': '如何做一个好用的滴水器？',
    '过程产物': '灌溉方法调研清单、滴水器设计图、滴水器制作材料清单、滴水器制作人员分配和职责、滴水器失败版本、产品发布会宣传物品',
    '最终的产品或成果': '成功的滴水器',
    '结题活动': '种植区滴水器产品发布会',
    '项目周期': '一学期'
  },
  '教研计划': {
    '教研主题': '户外混龄自主游戏实施',
    '教研目标': '1.规划幼儿园户外场地和游戏区\n2.研讨各区材料清单和玩法\n3.各班轮流到各区开展自主游戏，熟悉材料和玩法\n4.根据实际问题调整材料和环境\n5.逐步开展全园混龄自主游戏',
    '教研重点': '各区材料清单和玩法的反复改进、幼儿游戏记录与材料更新、全园混龄自主游戏安排',
    '教研关联内容': '材料、户外自主游戏',
    '教研次数': '10次'
  }
}

// 填充示例数据
const fillExample = () => {
  const templateTitle = props.title
  if (templateTitle && exampleData[templateTitle]) {
    const templateExampleData = exampleData[templateTitle]
    // 遍历表单配置，找到对应的字段并填充数据
    formConfig.value.forEach(item => {
      if (templateExampleData[item.keyName] !== undefined) {
        // 对于多选类型，将字符串转换为数组
        if (item.type === 'multiSelect') {
          formData.value[item.keyName] = templateExampleData[item.keyName].split('、')
        } else {
          formData.value[item.keyName] = templateExampleData[item.keyName]
        }
      }
    })
  }
}

// 暴露方法给父组件
defineExpose({
  formRef,
  formData,
  validate,
  formDataWithDefaultValue,
  fillExampleData: fillExample
})

// 判断是否显示示例按钮
const shouldShowExampleButton = (keyName) => {
  return ['观察记录', '项目制课程设计', '教研计划'].includes(keyName)
}
</script>

<style lang="scss" scoped>
.form-item-container {
  background: #ffffff;
  padding: 5rpx 24rpx;
  margin-bottom: 24rpx;
  border-radius: 28rpx;
  box-shadow: 4rpx 8rpx 16rpx rgba(0, 0, 0, 0.02);
}

.select-container {
  display: flex;
  align-items: center;
  padding: 10rpx 0px;
  justify-content: space-between;
}

.select-value {
  flex: 1;
  font-size: 15px;
  font-weight: 400;
  letter-spacing: 0px;
  line-height: 30rpx;
  color: rgba(177, 179, 181, 1);
  &:empty::before {
    content: attr(placeholder);
    color: #999;
  }
}

.select-arrow {
  margin-left: 20rpx;
  color: #999;
  display: flex;
  align-items: center;
}

:deep(.uni-data-checklist) {
  .checklist-group {
    .checklist-box {
      margin-right: 16rpx;
      margin-bottom: 16rpx;

      // 按钮样式
      .checklist-content {
        padding: 12rpx 24rpx;
        background: #f5f7fa;
        border-radius: 8rpx;
        border: none;

        // 选中状态
        &.is-checked {
          background: #e6f7ff;
          color: #367cff;
        }
      }
    }
  }
}
.form-item-tip {
  font-size: 15px;
  color: #B1B3B5;
  margin: 10rpx 0rpx;
}

.placeholderClass {
  color: red;
}

.label-container {
  display: flex;
  // justify-content: space-between;
  align-items: center;
  width: 100%;
  position: relative;
}

.required-star {
  color: #f56c6c;
  margin-left: 4rpx;
  font-size: 16px;
}

.example-btn {
  font-size: 24rpx;
  color: #367CFF;
  padding: 4rpx 12rpx;
  border: 1px solid #367CFF;
  border-radius: 8rpx;
  margin-left: 20rpx;
}

.form-item-header {
  position: relative;
  width: 100%;
}
</style>

<style>
.placeholderClass {
  color: red;
}
</style>
