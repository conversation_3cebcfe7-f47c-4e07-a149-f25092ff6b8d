import config from '@/common/config'
class WebSocketClient {
  constructor() {
    this.ws = null
    this.userId = null
    this.isConnecting = false
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectTimeout = 3000 // 3秒后重连
    this.heartbeatTimer = null
    this.heartbeatInterval = 30000 // 30秒发送一次心跳
    this.lastHeartbeatResponse = Date.now()
    this.heartbeatTimeout = 35000 // 35秒没有响应就认为断开
    this.connected = false
    this.manualClosed = false // 添加标志，表示是否是手动关闭
  }

  isConnected() {
    return this.connected && this.ws && this.ws.readyState === 1
  }
  // 修改 wsClient.connect，返回 Promise
  connect() {
    return new Promise((resolve, reject) => {
      if (this.isConnecting || this.isConnected()) {
        resolve()
        return
      }

      // 重置手动关闭标志
      this.manualClosed = false
      const userId = uni.getStorageSync('USER_INFO')?.id
      if (!userId) {
        reject(new Error('未获取到用户ID'))
        return
      }

      // const TRUE_API = 'https://jsapi.mypacelab.com/api'
      const TRUE_API = config[config.DEFINE_ENV].JAVA_API_URL + '/ws'
      const wsUrl = `${TRUE_API.replace('https', 'wss')}/websocket/${userId}`

      this.ws = uni.connectSocket({
        url: wsUrl,
        success: () => {
          console.log('WebSocket 连接创建成功')
        },
        fail: (error) => {
          console.error('WebSocket 连接创建失败:', error)
          reject(error)
        }
      })

      this.ws.onOpen(() => {
        console.log('WebSocket 连接已打开')
        this.isConnecting = false
        this.reconnectAttempts = 0
        this.startHeartbeat()
        this.connected = true
        resolve()
      })

      this.ws.onClose(() => {
        console.log('WebSocket 连接已关闭')
        this.handleClose()
      })

      this.ws.onError((error) => {
        console.error('WebSocket 错误:', error)
        this.handleError(error)
        reject(error)
      })

      this.ws.onMessage((res) => {
        try {
          if (res.data === 'heartcheck') {
            this.lastHeartbeatResponse = Date.now()
            return
          }
          const data = JSON.parse(res.data)
          uni.$emit('ws-message', data)
        } catch (error) {
          console.error('解析 WebSocket 消息失败:', error)
        }
      })
    })
  }

  handleClose() {
    this.isConnecting = false
    this.ws = null
    this.stopHeartbeat()
    this.connected = false

    // 只有在非手动关闭的情况下才重连
    if (!this.manualClosed) {
      this.reconnect()
    }
  }

  handleError(error) {
    this.isConnecting = false
    this.ws = null
    this.connected = false
    this.stopHeartbeat()
    uni.$emit('ws-error', error)

    // 只有在非手动关闭的情况下才重连
    if (!this.manualClosed) {
      this.reconnect()
    }
  }

  reconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts || this.manualClosed) {
      console.log('达到最大重连次数或已手动关闭，不再重连')
      return
    }

    this.reconnectAttempts++
    console.log(`${this.reconnectTimeout / 1000}秒后尝试第${this.reconnectAttempts}次重连`)

    setTimeout(() => {
      if (this.manualClosed) {
        console.log('已手动关闭，取消重连')
        return
      }
      this.connect(this.userId)
    }, this.reconnectTimeout)
  }

  send(data) {
    if (!this.isConnected()) {
      console.error('WebSocket 未连接')
      if (!this.manualClosed) {
        this.reconnect()
      }
      return
    }

    try {
      this.ws.send({
        data: typeof data === 'string' ? data : JSON.stringify(data),
        fail: (error) => {
          console.error('发送消息失败:', error)
          if (!this.manualClosed) {
            this.reconnect()
          }
        }
      })
    } catch (error) {
      console.error('发送消息失败:', error)
      if (!this.manualClosed) {
        this.reconnect()
      }
    }
  }

  startHeartbeat() {
    this.stopHeartbeat()
    this.lastHeartbeatResponse = Date.now()

    this.heartbeatTimer = setInterval(() => {
      if (!this.isConnected()) {
        this.stopHeartbeat()
        return
      }

      const now = Date.now()
      if (now - this.lastHeartbeatResponse > this.heartbeatTimeout) {
        console.log('心跳超时，开始重连')
        if (!this.manualClosed) {
          this.reconnect()
        }
        return
      }

      this.send('heartcheck')
    }, this.heartbeatInterval)
  }

  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  close() {
    // 设置手动关闭标志
    this.manualClosed = true
    this.stopHeartbeat()

    if (this.ws) {
      try {
        this.ws.close({
          success: () => {
            console.log('WebSocket 连接已主动关闭')
          },
          fail: (error) => {
            console.error('关闭 WebSocket 连接失败:', error)
          },
          complete: () => {
            this.isConnecting = false
            this.userId = null
            this.ws = null
            this.reconnectAttempts = 0
            this.connected = false
          }
        })
      } catch (error) {
        console.error('关闭 WebSocket 连接出错:', error)
        this.isConnecting = false
        this.userId = null
        this.ws = null
        this.reconnectAttempts = 0
        this.connected = false
      }
    }
  }
}

export const wsClient = new WebSocketClient()
