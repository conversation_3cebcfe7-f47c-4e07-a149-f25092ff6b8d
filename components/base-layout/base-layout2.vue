<template>
  <view
    class="base-layout-container"
    :class="containerClass"
  >
    <!-- Navbar 区域 -->
    <up-navbar
      :title="navTitle"
      :safeAreaInsetTop="safeAreaInsetTop"
      :fixed="navFixed"
      :placeholder="navPlaceholder"
      :bgColor="navBgColor"
      :autoBack="autoBack"
      @leftClick="handleNavBack"
      :class="navClass"
      :titleStyle="titleStyle"
    >
      <!-- 添加左侧插槽，当 autoBack 为 false 时显示空内容 -->
      <template #left>
        <slot name="navleft">
          <view class="nav-left-slot">
            <up-icon
              name="arrow-left"
              size="19"
            ></up-icon>
          </view>
        </slot>
      </template>
    </up-navbar>

    <slot name="extra"></slot>
    <!-- 内容滚动区域 -->
    <scroll-view
      class="content-scroll-view"
      :class="scrollClass"
      :scroll-y="scrollEnabled"
      :scroll-top="scrollTop"
      :scroll-into-view="scrollIntoView"
      :enable-flex="true"
      :scroll-with-animation="scrollWithAnimation"
      :style="[
        contentStyle,
        {
          transform: scrollViewTransform,
          marginBottom: scrollViewMargin,
          transition: 'all 0.3s',
        },
      ]"
      @scrolltolower="handleScrollToLower"
      :refresher-enabled="refresherEnabled"
      :refresher-triggered="refresherTriggered"
      @refresherrefresh="handleRefresherRefresh"
    >
      <slot></slot>
    </scroll-view>

    <!-- Footer 底部区域 -->
    <view
      class="footer-container"
      :style="footerStyle"
      ref="footerRef"
    >
      <slot name="footer"></slot>
    </view>
  </view>
</template>

<script setup>
import { getCurrentInstance, ref, onMounted, computed } from "vue";
const props = defineProps({
  // container
  containerClass: {
    type: String,
    default: "",
  },
  // Navbar 相关 Props
  navTitle: String,
  safeAreaInsetTop: {
    type: Boolean,
    default: true,
  },
  navFixed: {
    type: Boolean,
    default: true,
  },
  navPlaceholder: {
    type: Boolean,
    default: true,
  },
  navBgColor: {
    type: String,
    default: "transparent",
  },
  autoBack: {
    type: Boolean,
    default: true,
  },
  navClass: String,

  // ScrollView 相关 Props
  scrollEnabled: {
    type: Boolean,
    default: true,
  },
  scrollTop: [Number, String],
  scrollIntoView: String,
  scrollWithAnimation: {
    type: Boolean,
    default: true,
  },
  contentStyle: [Object, String],
  scrollClass: String,

  // Footer 相关 Props
  footerStyle: [Object, String],

  // 新增键盘相关的 props
  keyboardHeight: {
    type: Number,
    default: 0,
  },
  scrollDistance: {
    type: Number,
    default: 0,
  },

  // 新增启用加载更多的 prop
  enableLoadMore: {
    type: Boolean,
    default: false,
  },
  // 下拉刷新相关 Props
  refresherEnabled: {
    type: Boolean,
    default: false,
  },
  refresherTriggered: {
    type: Boolean,
    default: false,
  },
});

const titleStyle = {
  fontSize: "17px",
  fontWeight: "500",
  color: "rgba(51, 51, 51, 1)",
};

const emit = defineEmits(["leftClick", "scrolltolower", "refresherrefresh"]);

const handleNavBack = () => {
  emit("leftClick");
};

const handleScrollToLower = () => {
  if (props.enableLoadMore) {
    emit("scrolltolower");
  }
};

const handleRefresherRefresh = () => emit("refresherrefresh");

const footerRef = ref(null);
const navbarHeight = ref(0);
const footerHeight = ref(0);

onMounted(() => {
  // 获取导航栏高度
  const query = uni.createSelectorQuery();
  query
    .select(".u-navbar")
    .boundingClientRect((data) => {
      if (data) {
        navbarHeight.value = data.height;
      }
    })
    .exec();

  // 获取底部区域高度
  query
    .select(".footer-container")
    .boundingClientRect((data) => {
      if (data) {
        footerHeight.value = data.height;
      }
    })
    .exec();
});

// 计算滚动视图的变换样式
const scrollViewTransform = computed(() => {
  if (props.keyboardHeight === 0) return "translateY(0)";

  if (props.scrollDistance <= 20) {
    // 已经滚动到底部
    return `translateY(-${props.keyboardHeight}px)`;
  }

  if (props.scrollDistance < props.keyboardHeight) {
    // 距离底部的距离小于键盘高度
    const translateY = props.keyboardHeight - props.scrollDistance;
    return `translateY(-${translateY}px)`;
  }

  return "translateY(0)";
});

// 计算滚动视图的底部边距
const scrollViewMargin = computed(() => {
  if (props.keyboardHeight === 0) return "0px";

  if (props.scrollDistance <= 20) {
    return "0px";
  }

  if (props.scrollDistance < props.keyboardHeight) {
    return `${props.scrollDistance}px`;
  }

  return `${props.keyboardHeight}px`;
});

defineExpose({
  $scope: getCurrentInstance(),
});
</script>

<style lang="scss" scoped>
.base-layout-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f5f5
    url("https://c.mypacelab.com/vxmp/img/history_background_3x.png") no-repeat
    center top;
  background-size: contain;
  overflow: hidden;

  .content-scroll-view {
    flex: 1;
    min-height: 0; // 修复 flex 滚动问题
    // padding: 20rpx;
    box-sizing: border-box;
    will-change: transform, margin;
  }

  .footer-container {
    padding: 20rpx;
    position: relative;
    z-index: 1;
  }

  // 添加左侧插槽样式
  .nav-left-slot {
    width: 87rpx; // 与默认返回按钮宽度一致
    height: 32rpx;
  }
}
</style>

<style>
.u-navbar__content__title {
  font-size: 17px !important;
  font-weight: 500;
  letter-spacing: 0px;
  line-height: 18px;
  color: rgba(51, 51, 51, 1);
  text-align: center;
  vertical-align: top;
}
</style>
