<template>
  <view class="content">
    <!-- 已上传文件展示区域 -->
    <view class="uploaded-files-container" v-if="fileList.length > 0">
      <view class="grid-container" :style="gridStyle">
        <view class="file-item" v-for="(item, index) in fileList" :index="index" :key="index">
          <view class="file-item-content" v-if="item.category === 1">
            <image
              class="file-item-image"
              @click="previewImage(item.uri)"
              :src="item.uri"
              mode="aspectFit"
              style="max-height: 180rpx; min-height: 100rpx; max-width: 200rpx; min-width: 100rpx"
            >
            </image>
          </view>
          <view class="file-item-content" v-if="item.category === 5">
            <video
              class="file-item-video"
              :src="item.uri"
              id="myVideo1"
              muted
              loop
              controls
              show-mute-btn
              auto-pause-if-open-native
              auto-pause-if-navigate
            ></video>
          </view>
          <view
            class="file-item-content file-attachment"
            v-if="item.category === 10 || item.category === 9"
          >
            <image
              class="file-icon"
              src="https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/dynamics/file.svg"
              mode="aspectFit"
            ></image>
            <view class="file-name">{{ item.filename }}</view>
          </view>
          <view class="file-item-icon" v-if="showDel">
            <up-icon @click="delFile(item, index)" name="close" color="#ffffff" size="26rpx" />
          </view>
        </view>
      </view>
    </view>

    <!-- 上传按钮区域 -->
    <view class="upload-button-container" v-if="showDel">
      <view class="upload-button" @click="onUpdata">
        <image
          class="upload-icon"
          src="https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/dynamics/upload.svg"
          mode="aspectFit"
        ></image>
        <view class="upload-text">上传图片/音频</view>
      </view>
    </view>
    <!-- <view class="">
			<uni-grid :column="3" :highlight="true">
				<uni-grid-item v-for="(item, index) in fileList" :index="index" :key="index">
					<view class='file-item' >
						<view class='file-item-content' v-if='item.category === 1'>
							<image class='file-item-image' @click='previewImage' :src='item.uri'
								style='max-height: 180rpx;min-height: 100rpx;max-width:200rpx;min-width: 100rpx;'>
							</image>

						</view>
						<view class='file-item-content' v-if='item.category === 5'>
							<video class='file-item-video' :src="item.uri" id="myVideo1" muted loop controls
								show-mute-btn auto-pause-if-open-native auto-pause-if-navigate>
							</video>
						</view>
						<view class="file-item-name">
							<view class="files-name">{{ item.filename }}</view>
							<view class='del-icon' @click='delFile(item, index)'>删除</view>
						</view>
					</view>
				</uni-grid-item>
			</uni-grid>
		</view> -->
    <Popup :show="isPopup" @click="isPopup = false" @close="isPopup = false" :style="{ width: 0 }">
      <view class="text-area" @click="selectImage" style="height: 100rpx">
        <text class="title">选择上传的图片</text>
      </view>
      <view class="text-area" @click="selectVideo" style="height: 100rpx">
        <text class="title">选择上传的视频</text>
      </view>
      <view class="text-area" @click="selectFile" style="height: 100rpx">
        <text class="title">选择上传的文件</text>
      </view>
    </Popup>
  </view>
</template>

<script>
/**
 * Upload组件
 *
 * 这是一个自定义组件，用于上传文件 视频 图片
 * @prop {Array} value - 用于接收父组件需要展示内容的值
 * @prop {String} type - 用于接收父组件需要上传的文件类型，默认为all，可选值为image、video、file
 * @prop {Boolean} showDel - 是否显示删除按钮，默认为true
 * @prop {number} count - 上传时可以选的数量大小，默认为9
 * @prop {number} maxCount - 最大可上传数量限制，默认为-1 ，-1为不限制数量
 * @emit callback - 当用户上传成功时，会触发该事件，并返回上传成功后的文件信息
 * @emit delFile - 当用户删除时，会触发该事件，并返回删除的文件信息和下标 如 {item,index}
 * @returns {Array} - 返回上传成功后的文件信息
 */
// TODO：video 上传大小 50M 文件 10M (包括图片)
import { fetchClient, handleUpload } from '@/common/utils/fileUpload'

export default {
  emits: ['callback', 'emitDelFile', 'updata'],
  props: {
    maxCount: {
      type: Number,
      default: -1 // -1为不限制数量 最大可上传数量限制
    },
    type: {
      type: String,
      default: 'all' // 可选值为 all image video file
    },
    value: {
      type: Array,
      default: () => []
    },
    showDel: {
      type: Boolean,
      default: true
    },
    gridStyle: {
      type: [Object, String]
    },
    count: {
      type: Number,
      default: 9
    },
    fileCategory: {
      type: Number,
      default: 0,
      required: true // 必传
    }
  },
  watch: {
    value(newVal, oldVal) {
      console.log('Prop变化了:', oldVal, '=>', newVal)
      // 在此进行相应的处理
      if (newVal) {
        console.log('newVal', ...newVal)

        this.fileList = [...newVal]
      }
    }
  },
  data() {
    return {
      client: [],
      fileList: [],
      readonly: false,
      borderStyle: {
        width: 1,
        color: 'blue',
        style: 'dashed',
        radius: 2
      },
      isPopup: false
    }
  },
  created() {
    const setClient = (value) => {
      this.client = value
    }

    fetchClient(setClient)
  },

  methods: {
    // 选择什么类型上传
    onUpdata() {
      let _type = this.type
      let o = {
        image: '图片',
        video: '视频',
        file: '文件'
      }
      let fileType = o[_type] || '文件'
      // 最大长度限制
      if (this.maxCount > 0 && this.fileList?.length >= this.maxCount) {
        return uni.$u.toast('最多上传' + this.maxCount + '个' + fileType)
      }
      if (_type != 'all') {
        if (_type === 'image') this.selectImage()
        if (_type === 'video') this.selectVideo()
        if (_type === 'file') this.selectFile()
      } else {
        // 唤起弹框
        this.isPopup = true
      }
    },
    delFile(item, index) {
      const delInfo = this.fileList?.filter((item, idx) => {
        return index == idx
      })
      this.fileList = this.fileList?.filter((item, idx) => {
        return index !== idx
      })
      this.$emit('emitDelFile', delInfo, index)
    },
    previewImage: function (uri) {
      // console.log('previewImage----', e)
      console.log('previewImage----', this.fileList, this.fileList[0])
      const that = this
      const imgarr = this.fileList.map((item) => {
        if (item.category == 1) {
          return item.uri
        }
      })
      wx.previewImage({
        urls: imgarr, // 需要预览的图片路径列表
        current: uri // 当前显示的图片路径（可选）
      })
    },
    selectVideo() {
      const that = this
      this.isPopup = false
      uni.chooseVideo({
        sourceType: ['album', 'camera'],
        compressed: false,
        maxDuration: 60,
        camera: 'back',
        success: async function (res) {
          // 判断视频大小 最大限制 视频 50M
          const maxSize = 50 * 1024 * 1024
          if (res.size > maxSize) {
            uni.showToast({
              title: '视频大小不能超过50M',
              icon: 'error',
              duration: 2500
            })
            return
          }
          const tempFilePath = res.tempFilePath
          console.log('1------selectVideo', res, that.fileList)
          const result = await that.fileUpload({
            ...res,
            path: tempFilePath
          })
          console.log('%c Line:97 🥑 result', 'color:#93c0a4', result)

          if (result) {
            that.fileList = [
              ...(that.fileList || []),
              {
                ...result
              }
            ]
            that.$emit('callback', [result])
          }
        },
        fail: function (err) {
          console.log('2------selectVideo', err)
        }
      })
    },
    selectFile() {
      let that = this
      this.isPopup = false
      uni.chooseMessageFile({
        count: this.count, // 默认最多一次选择9张图
        type: 'file',
        // sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
        success: async function (res) {
          console.log('1-------selectFile', res)
          const maxSize = 50 * 1024 * 1024 // 50M
          let tempFiles = res.tempFiles //本地临时路径
          if (tempFiles.some((item) => item.size > maxSize)) {
            uni.showToast({
              title: '文件大小不能超过50M',
              icon: 'error'
            })
            return
          }
          const list = tempFiles?.map((item) => {
            return {
              ...item,
              category: item.type === 'image' ? 1 : item.type === 'video' ? 5 : 9,
              uri: item.path,
              filename: item.name || ''
            }
          })

          if (list.length) {
            const res = await Promise.all(list.map((item) => that.fileUpload(item)))
            that.fileList = [...(that.fileList || []), ...(list.filter((item) => item) || [])]
            console.log('%c Line:131 🍬 res', 'color:#b03734', res)
            that.$emit('callback', res)
          }
        }
      })
    },

    selectImage() {
      let that = this
      this.isPopup = false
      uni.chooseImage({
        count: this.count, // 默认最多一次选择9张图
        // sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
        success: async function (res) {
          const maxSize = 10 * 1024 * 1024 // 10M
          console.log('%c Line:145 🥥 res', 'color:#e41a6a', res)
          let tempFiles = res.tempFiles //本地临时路径
          if (tempFiles.some((item) => item.size > maxSize)) {
            uni.showToast({
              title: '图片大小不能超过10M',
              icon: 'error',
              duration: 2500
            })
            return
          }
          const list = tempFiles?.map((item) => {
            return {
              ...item,
              category: 1,
              uri: item.path,
              filename: item.name || ''
            }
          })
          if (list.length) {
            const res = await Promise.all(list.map((item) => that.fileUpload(item)))
            that.fileList = [...(that.fileList || []), ...(list.filter((item) => item) || [])]
            console.log('%c Line:132 🍬 res', 'color:#b03734', res)
            const ids = res.map((item) => item.id)
            that.$emit('callback', res)
          }
        }
      })
    },

    /* 上传函数 */
    async fileUpload(file) {
      console.log('开始上传', file)
      uni.showLoading({
        title: '上传中'
      })
      if (!file?.fileCategory) file.fileCategory = this.fileCategory
      const resource = await handleUpload(this.client, file)
      console.log('%c Line:168 🍺 resource', 'color:#f5ce50', file, resource)
      if (resource.uri) {
        uni.showToast({
          title: '上传成功',
          icon: 'success',
          duration: 1000
        })
        uni.hideLoading()
        return {
          ...resource,
          ...file,
          serviceUri: resource.uri
        }
      } else {
        console.log('上传失败', resource)
        uni.showModal({
          content: '上传失败',
          showCancel: false
        })
        uni.hideLoading()
        return undefined
      }
    }
  }
}
</script>

<style scoped lang="scss">
.content {
  display: flex;
  width: 100%;
  flex-direction: column;

  .uploaded-files-container {
    margin-bottom: 20rpx;

    .grid-container {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(148rpx, 1fr));
      gap: 10rpx;
      justify-items: start;
      width: 100%;
    }
  }

  .upload-button-container {
    display: flex;
    justify-content: center;
    padding: 20rpx 0;

    .upload-button {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 260rpx;
      border: 3rpx dashed rgba(217, 217, 217, 1);
      border-radius: 8rpx;
      background: rgba(250, 250, 250, 0.7);

      .upload-icon {
        width: 48rpx;
        height: 48rpx;
        margin-bottom: 8rpx;
      }

      .upload-text {
        font-size: 24rpx;
        color: #999;
      }
    }
  }
}

.logo {
  height: 200rpx;
  width: 200rpx;
  margin-top: 200rpx;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 50rpx;
}

.text-area {
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid #d9d9d9;
}

.text-area:last-child {
  border-bottom: none;
}

.title {
  font-size: 30rpx;
  font-weight: 600;
}

.file-item {
  display: flex;
  align-items: center;
  min-width: 148rpx;
  min-height: 148rpx;
  // border: 2px dashed #D9D9D9;
  border-radius: 8rpx;
  box-sizing: border-box;
  position: relative;
  margin-bottom: 16rpx;
  border: 1px solid #d9d9d9;
}

.file-item-content {
  width: 100%;
  position: relative;

  &.file-attachment {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 148rpx;
    padding: 10rpx;
    box-sizing: border-box;

    .file-icon {
      width: 48rpx;
      height: 48rpx;
      margin-bottom: 8rpx;
    }

    .file-name {
      font-size: 22rpx;
      color: #666;
      text-align: center;
      word-break: break-all;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
  }
}

.file-item-image,
.file-item-video {
  // height: 180rpx;
  width: 150rpx;
  height: 150rpx;
  // margin: 0 auto;
  display: block;
  border-radius: 8.46rpx;
  // margin-bottom: 10rpx;
}

.file-item-name {
  width: 100%;
}

.file-item-icon {
  position: absolute;
  top: 0;
  right: 0;
  width: 30rpx;
  height: 30rpx;
  border-radius: 0rpx 8rpx 0rpx 8rpx;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}

.files-name {
  padding: 0 10rpx;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  white-space: normal;
  font-size: 32rpx;
  word-wrap: break-word;
}
</style>
