<!-- 导出word -->
<template>
  <view
    class="f-s-30 f-w-400"
    @tap="healeExportWord"
  >
    <slot></slot>
  </view>
  <!-- <up-picker
    :show="show"
    :columns="columns"
    @confirm="confirm"
    keyName="name"
    @cancel="show = false"
  /> -->
  <export-word-popup
    :show="show"
    :list="columns"
    keyName="name"
    @change="confirm"
    @close="show = false"
  />
</template>

<script setup>
/**
 * 不提供点击样式、文字等。提供多模板选择时弹出框。
 * @param {String} documentTemplateCategory 文档模板类型 枚举值: SubjectActivity ObservationRecord MaterialDetail 必传
 * @param {String} subjectType 模板类型 School
 * @param {Number} subjectId 学校id 必传
 * @param {String} type 模板类型 活动或者观察记录
 * ***/
import { onMounted, reactive, ref } from "vue";
import { exportWord, getTemplateList } from "@/api";
import exportWordPopup from "./popup-export.vue";
const emit = defineEmits(["error"]);
const props = defineProps({
  // 文档模板类型 枚举值: SubjectActivity ObservationRecord MaterialDetail
  documentTemplateCategory: {
    type: String,
    default: "SubjectActivity",
    required: true,
  },
  // 模板列表
  subjectType: {
    type: String,
    default: "School",
  },
  // 学校id
  subjectId: {
    type: Number,
    default: null,
    required: true,
  },
  // 模板类型
  type: {
    type: String,
    default: "",
  },
  isGenerateAsync: {
    type: Boolean,
    default: false,
  },
});
const columns = ref([]);
let show = ref(false);
let wordId = ref(null); // 模板Id
let actId = ref(null); // id
let map = {
  活动: "subjectActivityIds",
  观察记录: "observationRecordId",
  AI主题书: "subjectId", // 课程ID
};

const confirm = (e) => {
  console.log(e);
  // const { value } = e;
  const { id } = e;
  let params = { id };
  params[map[props.type]] =
    map[props.type] == "subjectActivityIds"
      ? [actId.value]
      : map[props.type] == "subjectId"
      ? Number(actId.value)
      : actId.value;
  useExportWord(params);
  show.value = false;
};

// 请求学校模板
const getSchoolWid = async () => {
  const { documentTemplateCategory, subjectType, subjectId } = props;
  // 判断是不是数字 是就转化为数字型

  const from = {
    documentTemplateCategory,
    subjectType,
    subjectId,
  };
  // 目前只有一个模板，有多个模板可能需要新加内容
  let res = await getTemplateList(from);
  if (res.status == 0) {
    /**
     * 1、如果只有一个模板，直接导出
     * 2、如果有多个模板，弹出选择框
     * 3、如果获取模板失败，提示获取模板失败
     * 4、如果获取模板成功，但是没有模板，提示没有模板
     * **/
    if (res.data.length == 1) {
      // 直接导出
      wordId.value = res.data[0]?.id;
    } else if (res.data.length > 1) {
      columns.value = res.data;
    } else {
      emit("error", false);
    }
    // wordId.value = res.data[0]?.id;
  } else {
    emit("error", false);
    // uni.$u.toast("获取模板失败!");
  }
};
// 暴露出的给别的组件使用的方法
const healeExportWord = async (id) => {
  console.log(columns.value);
  if (!wordId.value && columns.value.length == 0) {
    uni.$u.toast("暂无模板");
    return;
  }
  actId.value = id;
  try {
    if (wordId.value) {
      // let params = { id: wordId.value };
      // params[map[props.type]] =
      //   map[props.type] == "subjectActivityIds"
      //     ? [id]
      //     : map[props.type] == "subjectId"
      //     ? Number(id)
      //     : id;
      // if (props.isGenerateAsync) params.isGenerateAsync = true;
      let params = formatParams(id);
      useExportWord(params);
    } else {
      show.value = true;
    }
  } catch (error) {
    console.log(error);
    uni.$u.toast(error);
  }
};

// 格式化请求参数
const formatParams = (fid) => {
  let params = { id: wordId.value };
  params[map[props.type]] =
    map[props.type] == "subjectActivityIds"
      ? [fid]
      : map[props.type] == "subjectId"
      ? Number(fid)
      : fid;
  if (props.isGenerateAsync) params.isGenerateAsync = true;
  return params;
};

// 导出文档
/**obj
 * @dos https://app.apifox.com/project/5100144
 * @param {Number} id 模板id 必传
 * @param {Array} subjectActivityIds 活动id，多个用逗号隔开
 * @param {String} observationRecordId 观察记录id，多个用逗号隔开
 * **/
const useExportWord = async (obj) => {
  // loading
  uni.showLoading({
    title: "正在导出...",
    mask: true,
  });
  try {
    const res = await exportWord(obj);
    if (res.status === 0) {
      // ai主题书导出需要时间
      if (res.data == null) {
        return uni.$u.toast("AI主题书导出中，大约需要5-10分钟，请稍候...");
      }

      const { uri, filename } = res.data;
      const url = uri;
      console.log(filename);
      // 在小程序中下载文件
      uni.downloadFile({
        url: url,
        success: (dlRes) => {
          if (dlRes.statusCode === 200) {
            // 重命名临时文件
            const fs = wx.getFileSystemManager(); // 获取文件系统管理器
            const newPath = `${wx.env.USER_DATA_PATH}/${filename}`;
            // 打开文档
            fs.copyFileSync(dlRes.tempFilePath, newPath); // 重命名文件
            uni.openDocument({
              filePath: newPath,
              showMenu: true,
              success: () => {
                uni.hideLoading();
                console.log("打开文档成功");
              },
              fail: (error) => {
                uni.hideLoading();
                console.error("打开文档失败:", error);
                uni.$u.toast("打开文档失败!");
              },
            }); // 打开文档
          } else {
            uni.$u.toast("下载Word失败!");
          }
        },
        fail: (error) => {
          uni.hideLoading();
          console.error("下载失败:", error);
          uni.showToast({
            title: "下载失败",
            icon: "none",
          });
        },
      });
    }
    if(res.status === 12003) {
      uni.hideLoading();
      uni.$u.toast(res.message);
    }
  } catch (error) {
    console.log(error);
    uni.hideLoading();
    return
  }
};

defineExpose({
  healeExportWord,
});

onMounted(() => {
  getSchoolWid();
});
</script>

<style lang="scss" scoped></style>
