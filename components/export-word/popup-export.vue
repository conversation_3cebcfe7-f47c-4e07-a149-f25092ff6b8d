导出word弹窗
<template>
  <Popup
    :show="show"
    @close="close"
  >
  <view class="title">请选择模板</view>
    <popup-item
      v-for="(item, index) in list"
      :key="index"
      :text="item[keyName]"
      icon="/static/common/leaving.png"
      @tap="change(item)"
    />
  </Popup>
</template>

<script setup>
import {} from "vue";
import Popup from "@/components/Popup/Popup.vue";
import popupItem from "@/components/Popup-item/Popup-item.vue";
defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  list: {
    type: Array,
    default: () => [],
  },
  keyName: {
    type: String,
    default: "text",
  },
});
const emit = defineEmits(["close", "change"]);
const close = () => {
  emit("close");
};
const change = (item) => {
  emit("change", item);
};
</script>

<style lang="scss" scoped>
.title{
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  text-align: center;
}
</style>
