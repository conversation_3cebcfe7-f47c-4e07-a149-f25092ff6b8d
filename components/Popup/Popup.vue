<template>
	<view style="width: 750rpx;">
		<up-popup :show="show" @close="emit('close');" :safeAreaInsetBottom="safeAreaInsetBottom">
			<image class="img" src="@/static/ArcPopup.png" mode="aspectFill"></image>
			<view class="popup-layout" :style="{ height: height}">
				<scroll-view :scroll-y="true" style="height: 100%;">
					<slot></slot>
				</scroll-view>
			</view>
		</up-popup>
	</view>
</template>

<script setup>
/**
 * 弹窗组件
 * @param {Boolean} show 是否显示弹窗
 * @param {Boolean} safeAreaInsetBottom 是否显示安全区域
 * @event close 关闭弹窗
 */

const props = defineProps({
	show: {
		type: Boolean,
		default: false,
		required: true
	},
	safeAreaInsetBottom: {
		type: Boolean,
		default: true
	},
	height: {
		type: String,
		default: 'auto'
	}
})

const emit = defineEmits(['close'])
</script>

<style lang="scss" scoped>
.popup-layout {
	width: 100vw;
	padding: 0 32rpx;
	max-height: 80vh;
	background: #fff;
	box-sizing: border-box;
	overflow-y: auto;
	border-top: none;
}

.img {
	width: 100%;
	box-sizing: border-box;
	height: 76rpx;
	position: absolute;
	top: -75rpx;
	left: 0;
}
</style>
