<template>
  <view class="Popup-item">
    <view v-if="icon">
      <image v-if="isPath" :src="icon" mode="aspectFit" :style="iconStyle" />
      <up-icon v-else :name="icon" :size="size" :style="iconStyle" />
    </view>
    <view class="slot-content" :style="contentStyle">
      <slot>{{ text }}</slot>
    </view>
  </view>
</template>

<script setup>
import { computed } from "vue";

const props = defineProps({
  icon: {
    type: String,
    default: ''
  },
  size: {
    type: String,
    default: '40rpx'
  },
  text: [String],
  contentStyle: [String, Object]
})

const isPath = computed(() => {
  return props.icon.includes('/') || props.icon.includes('.')
})

const iconStyle = computed(() => {
  return {
    width: props.size,
    height: props.size,
    marginRight: '28rpx'
  }
})
</script>

<style lang="scss" scoped>
.Popup-item {
  height: 88rpx;
  line-height: 88rpx;
  color: #333333;
  display: flex;
  align-items: center;

  &:active {
    background: #f5f5f5;
  }

  image,
  .icon {
    flex-shrink: 0;
    display: block;
  }

  .slot-content {
    flex: 1;
    overflow: hidden;
    font-size: 30rpx;
    font-weight: 400;
  }
}
</style>