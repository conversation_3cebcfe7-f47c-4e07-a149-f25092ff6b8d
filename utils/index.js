import {
  getCurrentInstance,
  nextTick
} from 'vue';
import {
  enumList,
  changeClassId
} from '@/api/index.js';
import config from "@/common/config.js";
import Tracker from "@/common/utils/Tracker.js";
import dayjs from "dayjs";
import { setToken } from "@/utils/token.js";
import { wxLogin } from "@/api/login.js";

const instance = getCurrentInstance();
const APP_ID = config[config.DEFINE_ENV].APPID; // appid
// 权限检查
export const checks = () => {
  const TOKEN_NAME = config[config.DEFINE_ENV].TOKEN_NAME; // token名称

  if (!uni.getStorageSync(TOKEN_NAME)) {
    uni.showToast({
      title: "请登录！",
      icon: "none"
    })
    setTimeout(() => {
      uni.reLaunch({
        url: '/pages/login/login'
      })
    }, 1000)
    return
  }
}

// 获取顶部安全区填充
export const menuFill = () => {
  let SYSTEM_INFO = uni.getSystemInfoSync()
  return SYSTEM_INFO.statusBarHeight
}

// 获取底部安全区高度
export const getBottonHeight = () => {
  const {
    safeAreaInsets
  } = uni.getSystemInfoSync()
  return safeAreaInsets.bottom
}

/**
 * @param calssName 节点名称 如 .name / #name
 ***/

// 获取节点信息高度
export function getNodeHeight(calssName) {

  let h = null
  const instance = getCurrentInstance();
  const query = uni.createSelectorQuery().in(instance.proxy);
  // const query = uni.createSelectorQuery()
  // uni.createSelectorQuery().select('.box')
  query
    .select(calssName)
    .boundingClientRect(data => {
      console.log(data);

      h = data.height
      console.log(data.height);
    }).exec();

  return h
}

export function timeRange(item) {
  if (item) {
    let string = item.split(' ')[0]
    return string.replace(/-/g, '.')
  }

}


/**
 * @description 获取对应字典的值
 * @param {String} dictName 字典名称
 * @return promise
 */

export const getDICT = async (dictName = 'all') => {
  let res = await enumList()
  if (!uni.getStorageSync("ENUM")) uni.setStorageSync("ENUM", res.data);
  if (dictName == 'all') {
    return res.data
  }
  return res.data[dictName]
};

export const openAiTemplatePage = (id, title) => {
  uni.navigateTo({
    url: `/subPages/aiAssistant/aiTemplate/index?id=${id}&title=${title}`,
    fail: () => {
      uni.switchTab({
        url: "/subPages/aiAssistant/aiTemplateList/index",
      });
    }
  });
};
/**
 * @description 分享
 * @param {String} path 当前页面路径名字称
 * @param {String} title 分享标题
 * @param {String} imageUrl 分享图片路径
 * @return Object<string>
 */
export const sharePageObj = (option) => {
  let {
    path,
    title,
    imageUrl
  } = option || {};
  let paths = getCurrentPagePath();
  return {
    title: title || '邀请你使用幼立方',
    path: path || paths,
    imageUrl: imageUrl || 'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/share_app.jpg'
  };
};
// 获取当前页面路径的函数
const getCurrentPagePath = () => {
  const pages = getCurrentPages(); // 获取当前页面栈
  const currentPage = pages[pages.length - 1]; // 获取栈顶页面，也就是当前页面
  const currentPath = currentPage.route; // 获取当前页面的路径
  console.log('当前页面路径:', currentPage);
  return '/' + currentPath;
};

/**
 * 通用文件导出函数
 * @param {Object} options - 导出配置项
 * @param {string} options.url - 文件下载地址
 * @param {Function} options.getFileUrl - 获取文件URL的异步函数
 * @param {Object} options.params - 获取文件URL的参数
 * @param {Object} options.downloadOptions - 下载配置项
 * @param {boolean} options.autoOpen - 是否自动打开文件
 * @returns {Promise<{success: boolean, filePath?: string, error?: Error}>}
 */
export const exportFile = async ({
  url,
  getFileUrl,
  params = {},
  downloadOptions = {},
  autoOpen = true
} = {}) => {
  try {
    // 获取下载地址
    let fileUrl = url;
    if (!fileUrl && getFileUrl) {
      const res = await getFileUrl(params);
      fileUrl = res?.data?.wordUrl || res?.data?.url || res?.data;
    }

    if (!fileUrl) {
      throw new Error('无效的文件地址');
    }

    // 下载文件
    const downloadResult = await uni.downloadFile({
      url: fileUrl,
      ...downloadOptions
    });

    console.log(downloadResult);

    if (downloadResult.statusCode !== 200) {
      throw new Error(`下载失败: ${downloadResult.statusCode}`);
    }

    // 保存文件
    return new Promise((resolve, reject) => {
      console.log(downloadResult.tempFilePath);
      uni.saveFile({
        tempFilePath: downloadResult.tempFilePath,
        success: (saveRes) => {
          const savedFilePath = saveRes.savedFilePath;

          uni.showToast({
            title: "下载成功",
            icon: "success"
          });

          if (autoOpen) {
            uni.openDocument({
              filePath: savedFilePath,
              showMenu: true,
              success: () => {
                resolve({
                  success: true,
                  filePath: savedFilePath
                });
              },
              fail: (err) => {
                console.error('打开文件失败:', err);
                resolve({
                  success: true,
                  filePath: savedFilePath,
                  error: err
                });
              }
            });
          } else {
            resolve({
              success: true,
              filePath: savedFilePath
            });
          }
        },
        fail: (err) => {
          console.error('保存文件失败:', err);
          reject(new Error('保存文件失败'));
        }
      });
    });

  } catch (error) {
    console.error('文件导出错误:', error);
    uni.showToast({
      title: `文件导出失败，请重试`,
      icon: "error"
    });
    return {
      success: false,
      error
    };
  }
};

// 全局获取班级ID，并改变当前classID
/**
 * 导出一个异步函数 useCurrentClassId，用于切换当前班级
 * @param {Number} ccId - 当前班级ID
 * @param {Number} csId - 当前学校ID
 * @param {Number} ct - 当前学期
 */
export const useCurrentClassId = async (ccId = null, csId, ct = null) => {
  // 从本地存储中获取用户信息
  const user = uni.getStorageSync("USER_INFO");
  // 修改当前的班级并更新本地缓存
  // 如果ccid有就更新currentClassId
  let data = {}
  if (ccId) data.currentClassId = ccId
  if (csId) data.currentSchoolId = csId
  if (ct) data.currentTerm = ct
  uni.setStorageSync("USER_INFO", {
    ...user,
    ...data
  })

  // 如果切换了学校，同步更新CURRENT_SCHOOL_ID
  if (csId) {
    uni.setStorageSync('CURRENT_SCHOOL_ID', csId)
  }

  // 更新当前班级
  const from = {
    currentClassId: ccId,
    currentSchoolId: csId || user.currentSchoolId,
    currentTerm: ct || user.currentTerm
  }
  let res = await changeClassId(from)
  if (res.status == 0) {
    if (csId) return;
    uni.$u.toast('切换成功')
  }
};

// 格式化日期全局函数
export const formatDate = (date = "", format = "YYYY-MM-DD HH:mm:ss") => {
  return dayjs(date).format(format)
}


//js判断一个链接是图片还是视频
export const checkMediaType = (url = '') => {
  var extension = url.split('.').pop().toLowerCase();
  var imageExtensions = ['jpg', 'jpeg', 'gif', 'png'];
  var videoExtensions = ['mp4', 'wmv', 'avi', 'mov'];
  if (imageExtensions.includes(extension)) {
    return 'image';
  }
  if (videoExtensions.includes(extension)) {
    return 'video';
  }
  return null;
}

/**
 * 处理 API 请求的响应
 * @param {Function} [successCallback] - 请求成功时的自定义回调函数
 * @param {Object} res - API 请求的响应数据
 * @returns {*|null} - 请求成功时返回回调函数的结果或响应数据，失败时返回 null
 */
export const handleApiResponse = (successCallback, res) => {
  if (res.status === 0) {
    if (typeof successCallback === 'function') {
      return successCallback(res.data);
    }
    return res.data; // 若未提供回调函数，则直接返回响应数据
  } else {
    const errorMessage = res.message || '接口错误，请联系管理人员';
    uni.showToast({
      title: errorMessage,
      icon: 'error'
    });
    return null;
  }
};

/**
 * 判断url后缀是什么类型的文件, 如果是图片 {uri: url, category: 1}，如果是视频 {uri: url, category: 5}
 * @param {string} url - 文件地址
 * 
 * **/
export const getFileType = (url) => {
  const fileExtension = url.split('.').pop().toLowerCase();
  const imageExtensions = ['jpg', 'jpeg', 'gif', 'png', 'bmp', 'webp'];
  const videoExtensions = ['mp4', 'mov', 'avi', 'wmv', 'flv', 'mkv'];
  if (imageExtensions.includes(fileExtension)) {
    return {
      uri: url,
      category: 1
    };
  }
  if (videoExtensions.includes(fileExtension)) {
    return {
      uri: url,
      category: 5
    };
  }
}

// 静默登录
export const wxLoginApi = async () => {
  const data = await uni.login();
  const res = await wxLogin({
    appId: APP_ID,
    code: data?.code,
  });
  const { user, token } = res.data;
  console.log(user);

  if (res.status == 0) {
    if (token?.token) {
      console.log(token);
      setUserInfo(token, user);
      return;
    }
    // 如果token不存在，则提示用户重新登录
    uni.$u.toast("手机号未绑定, 请授权！");
    // clearToken();
  } else {
    uni.$u.toast(res?.error?.stack);
  }
};

const setUserInfo = async (o, u = {}) => {
  const user = u;
  setToken(o.token);
  uni.setStorageSync("USER_INFO", user);
  uni.removeStorageSync("USER_CLEAR");
  uni.reLaunch({
    url: "/pages/index/index",
  });
};

/**
 * 用户日志记录 数据埋点
 * @param {string} title - 页面标题
 * 
 * **/
export const headleLogger = (title = "") => {
  const tracker = new Tracker();
  let { path, options } = getCurPagePathOp()
  let param = Object.keys(options)
    .map(key => `${key}=${options[key]}`)
    .join('&')
  tracker.trigger({
    page: path == '/' ? '/pages/index/index' : path,
    params: param ? param : '',
    title,
  })
}

// 获取当前页面路径的函数和参数
const getCurPagePathOp = () => {
  const pages = getCurrentPages(); // 获取当前页面栈
  const currentPage = pages[pages.length - 1]; // 获取栈顶页面，也就是当前页面
  const currentPath = currentPage.route; // 获取当前页面的路径
  return {
    path: '/' + currentPath,
    options: currentPage.options
  };
};

