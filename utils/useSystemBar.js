import {ref} from 'vue';
 
export default function() {
	// 获取系统信息
	let systemInfo = '';
	// #ifdef MP-WEIXIN
		systemInfo = uni.getWindowInfo(); 
	// #endif
	// #ifndef MP-WEIXIN
		systemInfo = uni.getSystemInfoSync();
	// #endif
	
	// 获取刘海-状态栏高度
	let StatusBarHeight = systemInfo.statusBarHeight || 15;
	
	
	// 获取title-自定义内容高度
	let TitleBarHeight = '';
	if(uni.getMenuButtonBoundingClientRect){
		let {top,height} = uni.getMenuButtonBoundingClientRect();
		TitleBarHeight = height + (top - StatusBarHeight)*2		
	}else{
		TitleBarHeight = 40;
	}
	
	// 占位高度-胶囊离下面内容高度
	let NavBarHeight = StatusBarHeight + TitleBarHeight;
	
	// 判断是否是头条
	let ttLeftIconLeft = '';		// 头条胶囊左侧有一个固定的图标,所以要获取图标加图标左侧间距的距离
	// #ifdef MP-TOUTIAO
		let {leftIcon:{left,width}}  = tt.getCustomButtonBoundingClientRect();
		ttLeftIconLeft = left+ parseInt(width);
	// #endif
	// #ifndef MP-TOUTIAO
		ttLeftIconLeft = 0;
	// #endif
	
	
	return {
		StatusBarHeight,
		TitleBarHeight,
		NavBarHeight,
		ttLeftIconLeft
	}
}