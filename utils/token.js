import config from "@/common/config.js";
//获取token
const TOKEN_NAME = config[config.DEFINE_ENV].TOKEN_NAME; // token名称
export const getToken = () => {
	return uni.getStorageSync(TOKEN_NAME) || false
}
//设置token
export const setToken = (token, expiration = 30) => {
	uni.setStorageSync(TOKEN_NAME, token)
	// 设置过期时间
	//expiration 单位为毫秒 默认30天
	let h = (expiration * 24) * 60 * 60 * 1000;
	const expireTime = Date.now() + h;
	uni.setStorageSync("EXPIRE_TIME", expireTime)
}
// 导出一个名为 setUserInfo 的常量，它是一个函数，接受一个参数 token
export const setUserInfo = (userinfo) => {
	uni.setStorageSync('USER_INFO', userinfo)
}
/**
 * @see 清除token和清楚用户信息并导航至登录页面
 ***/
export const clearToken = (key) => {
	uni.clearStorageSync()
	// uni.removeStorageSync('TOKEN_KEY')
	// uni.removeStorageSync('USER_INFO')
	if (key) uni.setStorageSync('USER_CLEAR', true)
	uni.reLaunch({
		url: '/pages/login/login'
	})
}