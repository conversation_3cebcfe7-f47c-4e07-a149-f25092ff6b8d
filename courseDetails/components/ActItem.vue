<!-- 活动列表 -->
<template>
  <view :class="{ 'act-item': true, active: actData.isExplad }">
    <view class="act-item-tit" @click="actData.isExplad = !actData.isExplad">
      <text>{{ actData.subjectStage.title }}</text>
      <up-icon
        class="arrow"
        name="arrow-up"
        color="rgba(102, 102, 102, 1)"
        size="16"
      />
    </view>

    <template v-if="actData.isExplad">
      <view class="act-item-desc">
        带 <text class="blue">*</text> 活动为未完成教案或评价的活动，{{ tipText }}
      </view>

      <view class="act-item-wrap">
        <view class="act-item-wrap-tr">
          <view class="act-item-wrap-th"></view>
          <view class="act-item-wrap-th">活动名称</view>
          <view class="act-item-wrap-th">日期</view>
          <view class="act-item-wrap-th">形式</view>
          <view class="act-item-wrap-th"></view>
        </view>
        <up-radio-group v-model="_actId" placement="row" @change="groupChange" style="gap: none">
          <view
            class="act-item-wrap-tr"
            v-for="(item, _idx) in actData.subjectActivities"
            :key="_idx"
            @click="groupChange(item.id)"
          >
            <view class="act-item-wrap-th">
              {{_idx+1>9?_idx+1:`0${_idx+1}`}}
            </view>
            <view class="act-item-wrap-th black">
              {{item.name}}<text class="blue">*</text>
            </view>
            <view class="act-item-wrap-th black">
              {{ item.implementedAt && formatDate(item.implementedAt,"MM-DD") }}
            </view>
            <view class="act-item-wrap-th">
              <view :class="`tag-${map[item.organizationForm]}`">{{item.organizationFormStr}}</view>
            </view>
            <view class="act-item-wrap-th">
              <slot>
                <up-radio shape="circle" :name="item.id" />                
              </slot>
            </view>
          </view>
        </up-radio-group>
      </view>
      <view class="text-Empty" v-if="!actData.subjectActivities.length">暂无活动，请添加！</view>
    </template>
  </view>
</template>
<script setup>
  import { reactive, ref, computed, toRaw, watch } from "vue";
  import { formatDate } from "@/utils/index.js";

  const emit = defineEmits(["change"]);
  const props = defineProps({
    // 班级
    actData: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    actId: {
      type: [String, Number],
      default: "",
    },
    tipText: {
      type: String,
      default: "请选择活动",
    }
  });
  const map = {
    Collective: "blue",
    Family: "red",
    Group: "green",
    Talk: "orange",
    Team: "violet"
  };

  const idx = ref(0);
  const isExplad = ref(false); // 是否展开
  const _actId = ref(toRaw(props.actId));
  let changeVal = ref(null);

  watch(
    () => props.actId,
    (val) => {
      _actId.value = val;
    }
  );

  
  const groupChange = (val) => {
    _actId.value = val;
    emit("change", val);
  };
</script>

<style lang="scss" scoped>
  .modlCont {
    text-align: center;
  }
  .blue {
    color: rgba(63, 121, 255, 1);
  }

  .black {
    color: rgba(51, 51, 51, 1) !important;
  }

  @each $var in "blue", "orange", "red", "green", "violet" {
    .tag-#{$var} {
      display: inline-block;
      border-radius: 8upx;
      font-size: 24upx;
      font-weight: 500;
      line-height: 33upx;
      padding: 4upx 14upx;

      @if $var == "blue" {
        color: rgba(82, 131, 247, 1);
        background-color: rgba(82, 131, 247, 0.12);
      }

      @if $var == "orange" {
        color: rgba(240, 145, 77, 1);
        background-color: rgba(240, 145, 77, 0.12);
      }

      @if $var == "red" {
        color: rgba(237, 111, 114, 1);
        background-color: rgba(237, 111, 114, 0.12);
      }

      @if $var == "green" {
        color: rgba(84, 186, 106, 1);
        background-color: rgba(84, 186, 106, 0.12);
      }

      @if $var == "violet" {
        color: rgba(110, 116, 230, 1);
        background-color: rgba(110, 116, 230, 0.12);
      }
    }
  }

  .act-item {
    @include selfshaow;
    padding: 32upx;
    margin-top: 24upx;
    background-color: #fff;
    border-radius: 28upx;

    &-tit {
      @include selflex(x, between, start);
      font-size: 30upx;
      font-weight: 600;
      letter-spacing: 0px;
      line-height: 36upx;
      color: rgba(51, 51, 51, 1);
      text-align: left;
    }

    &-desc {
      font-size: 22upx;
      line-height: 30upx;
      color: rgba(128, 128, 128, 1);
      padding: 12upx 0 24upx 0;
      border-bottom: 1upx solid #f3f3f3;
    }

    &-wrap {
      &-tr {
        padding: 16upx 0;
        border-bottom: 1upx solid #f3f3f3;
        @include selflex(x, start, center);

        &:last-child {
          border-width: 0;
        }
      }
      display: flex;
      flex-wrap: wrap;

      &-th {
        font-size: 28upx;
        line-height: 40upx;
        color: rgba(128, 128, 128, 1);
        display: flex;
        justify-content: flex-start;
        
        &:nth-child(1) {
          width: 50upx;
        }

        &:nth-child(2) {
          width: 300upx;
        }

        &:nth-child(3) {
          width: 100upx;
        }

        &:nth-child(4) {
          width: 100upx;
        }

        &:nth-child(5) {
          flex: 1;
          ::v-deep .u-radio-group--row {
            width: 50upx;
          }
        }
      }
    }

    &.active {
      .arrow {
        transform: rotate(180deg);
      }

      .act-item-desc {
        display: block;
      }
    }
  }
</style>
