<template>
	<view class="l-dragSort" :style="{ height: initListPosition[0]?.height * listArray.length + 'px' }">
		<!-- @touchmove.stop.prevent 禁止滑动 -->
		<view class="listItem" 
			v-for="(item, index) in listArray" :key="index" :style="{
				'height': lineHeight + 'rpx',
				'top': listPosition[index]?.top + 'px',
				'transition': curretnItemIndex === index ? 'initial' : '.3s'
			}" :class="{ 'activeClass': index == curretnItemIndex }">
			<view class="left">
				<slot name="leftIco">
					<image class="leftIco" v-if="item.leftIcon" :src="item.leftIcon"></image>
					<view v-if="item.orderNum" style="margin-right: 30rpx;">{{ item.orderNum }}</view>
				</slot>
				<view class="name" :style="styleName">
					{{ item.name }}
				</view>
			</view>
			<image class="rightIco" src="@/static/icon/dragsort.png" @touchstart="onTouchstart($event, index)" @touchmove.stop.prevent="onTouchmove" @touchend="onTouchend" ></image>
		</view>
	</view>
</template>

<script>
export default {
	name: "lDragSort",
	props: {
		//列表
		list: {
			type: Array,
			default: []
		},
		// 列表每行高度
		lineHeight: {
			type: Number,
			default: 80
		},
		styleName: {
			type: Object,
			default: {}
		}
	},
	data() {
		return {
			listArray: [],
			// 所有元素定位位置
			listPosition: [],
			// 记录拖动前元素定位位置
			initListPosition: [],
			// 记录当前拖动元素的下标
			curretnItemIndex: -1,
			// 记录拖动前的位置
			recordPosition: {
				y: 0
			},
			// 记录拖动前的定位位置
			recordCurrentPositionItem: {
				top: 0
			},
			// 是否正在交换位置
			isChange: false
		}
	},
	created() {
		// this.init()
		this.listArray = [...this.list]
	},
	mounted() {
		this.init()
	},
	methods: {
		init() {
			const query = uni.createSelectorQuery().in(this);
			query.selectAll('.listItem').fields({
				rect: true,
				size: true
			}, data => {
				data.forEach((item, index) => {
					this.listPosition.push({
						height: item.height,
						top: item.height * index,
					});
				});
				this.initListPosition = [...this.listPosition]
			});
			query.exec(); //执行所有请求
		},
		onTouchstart(event, index) {
			const {
				pageY
			} = event.touches[0];

			// 记录当前拖动元素的下标
			this.curretnItemIndex = index;
			// 记录拖动前的位置
			this.recordPosition = {
				y: pageY
			};
			// 记录拖动前的定位位置
			this.recordCurrentPositionItem = this.listPosition[index];
		},
		onTouchmove(event) {
			const {
				pageY
			} = event.touches[0];

			// 获取移动的差
			this.$set(this.listPosition, this.curretnItemIndex, {
				top: this.listPosition[this.curretnItemIndex].top + (pageY - this.recordPosition
					.y),
			});
			// 记录位置
			this.recordPosition = {
				y: pageY
			};
			// 向下
			if (this.listPosition[this.curretnItemIndex].top >= this.listPosition[this.curretnItemIndex + 1]?.top -
				this.initListPosition[0].height / 2) {
				if (this.isChange) return
				this.isChange = true
				let temp = this.listArray[this.curretnItemIndex]
				// console.log(temp)
				this.listArray[this.curretnItemIndex] = this.listArray[this.curretnItemIndex + 1]
				this.listArray[this.curretnItemIndex + 1] = temp
				this.listPosition[this.curretnItemIndex + 1] = this.listPosition[this.curretnItemIndex]
				this.listPosition[this.curretnItemIndex] = this.recordCurrentPositionItem
				this.curretnItemIndex = this.curretnItemIndex + 1
				this.recordCurrentPositionItem = this.initListPosition[this.curretnItemIndex]
				this.isChange = false
			}
			// 向上
			if (this.listPosition[this.curretnItemIndex].top <= this.listPosition[this.curretnItemIndex - 1]?.top +
				this.initListPosition[0].height / 2) {
				if (this.isChange) return
				this.isChange = true
				let temp = this.listArray[this.curretnItemIndex]
				// console.log(temp)
				this.listArray[this.curretnItemIndex] = this.listArray[this.curretnItemIndex - 1]
				this.listArray[this.curretnItemIndex - 1] = temp
				this.listPosition[this.curretnItemIndex - 1] = this.listPosition[this.curretnItemIndex]
				this.listPosition[this.curretnItemIndex] = this.recordCurrentPositionItem
				this.curretnItemIndex = this.curretnItemIndex - 1
				this.recordCurrentPositionItem = this.initListPosition[this.curretnItemIndex]
				this.isChange = false
			}
		},

		onTouchend(event) {
			// 拖动元素归位
			this.listPosition[this.curretnItemIndex] = this.initListPosition[this
				.curretnItemIndex];
			this.curretnItemIndex = -1;
			this.$emit('change', [...this.listArray])
		},
	}
}
</script>

<style scoped lang="scss">
.l-dragSort {
	width: 100%;
	height: 100%;
	position: relative;
	box-sizing: border-box;

	.listItem {
		width: 100%;
		position: absolute;
		display: flex;
		justify-content: space-between;
		align-items: center;
		box-sizing: border-box;
		background-color: #fff;
		overflow-y: auto;

		.name{
			text-overflow: ellipsis;
			overflow: hidden;
			white-space: nowrap;
			width: 75vw;
		}

		.left {
			height: 100%;
			display: flex;
			align-items: center;
			// padding-left: 30rpx;

			.leftIco {
				margin-right: 20rpx;
				width: 50rpx;
				height: 50rpx;
			}

		}

		.rightIco {
			width: 36rpx;
			height: 36rpx;
		}
	}
}

.activeClass {
	box-shadow: 0 0px 50rpx #cfcfcf;
	z-index: 999;
}
</style>
