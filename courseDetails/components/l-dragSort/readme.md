# l-dragSort
目前仅微信小程序能用
# ## 使用方法
配置easycom规则后，自动按需引入，无需`import`组件，直接引用即可。

```html
<template>
	<l-dragSort :list="list" @change="change"></l-dragSort>
</template>

<script>
	export default {
		data(){
			return{
				list: [{
						leftIcon: '/static/money.png',
						name: '账户余额',
						rightIcon: '/static/drag.png'
					},
					{
						leftIcon: '/static/ny.png',
						name: '农业银行',
						rightIcon: '/static/drag.png'
					},
					{
						leftIcon: '/static/gs.png',
						name: '工商银行',
						rightIcon: '/static/drag.png'
					},
					{
						leftIcon: '/static/js.png',
						name: '建设银行',
						rightIcon: '/static/drag.png'
					},
				]
			}
		},
		methods:{
			change(e){
				console.log(e)
			}
		}
	};
</script>
```

## 组件属性

| 属性 | 类型 | 默认值 | 说明 |
|:---:|:---:|:---:|---|
| list | Array | [] | 列表 |
| lineHeight | Number | 80 | 列表每行高度 |

## 组件事件

| 名称 | 触发时机 |
|:---:|---|
| change | 拖动结束 返回排序后的数组 |