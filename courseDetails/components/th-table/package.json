{"id": "th-table", "displayName": "th-table 表格 支持自定义按钮插槽 排序 固定多列", "version": "1.0.9", "description": "th-table表格组件，支持app,支持自定义插槽，排序，表头，单元格事件，基于uni-table封装", "keywords": ["table", "表格"], "repository": "", "engines": {"HBuilderX": "^3.96"}, "directories": {"example": ""}, "dcloudext": {"sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "type": "component-vue"}, "uni_modules": {"dependencies": ["uni-scss", "uni-datetime-picker", "uni-table"], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"App": {"app-vue": "y", "app-nvue": "n"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "y", "百度": "y", "字节跳动": "n", "QQ": "y"}, "快应用": {"华为": "n", "联盟": "n"}, "Vue": {"vue2": "n", "vue3": "y"}}}}}