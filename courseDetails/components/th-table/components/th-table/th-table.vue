<template>
  <view>
    <uni-table :loading="loading" :border="border" :stripe="stripe" emptyText="" :type="type">
      <uni-tr>
        <uni-th v-for="item in column" :align="item.align || 'center'" :key="item.index" :style="{
          position: item.isFixed ? 'sticky' : '',
          right: item.fixedW || 0,
          background: '#fff',
          zIndex: item.isFixed ? '1' : '',
          minHeight: '70rpx',
          fontSize: thsize ? thsize + 'rpx' : '26rpx',
        }" :width="item.width ? item.width : ''" @tap="
            item.isSort
              ? checkSort(item.key, item.key === st ? (sr === -1 ? 1 : -1) : -1)
              : ''
            ">
          <view class="span1" :class="item.isSort ? 'span' : ''">{{ item.title }}</view>
          <!-- :class="[item.isSort ? ' ' : '', item.key == st ? (sr == -1 ? 'sortBottom' : 'sortTop') : '',]" -->
        </uni-th>
      </uni-tr>
      <uni-tr v-for="(item, index) in listData" :key="index" style="position: relative">
        <uni-td v-for="(item1, index1) in column" :key="index1" :style="{
          verticalAlign: 'middle',
          position: item1.isFixed ? 'sticky' : 'relative',
          right: item1.fixedW || 0,
          background: '#fff',
          zIndex: item1.isFixed ? 1 : '',
          height: height ? `${height * 100}rpx` : 'auto',
          minHeight: '80rpx',
          fontSize: tdsize ? tdsize + 'rpx' : '',
        }" :class="[item1.isFixed ? 'th-table-fixed-right' : '']" :align="item1.align || 'center'"
          @tap="tdClick && item1.slot != 'action' ? tdClick(item) : ''">
          <template v-if="item1.slot && item1.slot == 'action'">
            <slot :name="item1.slot" :item="item" :index="index">
              <view @tap="actionTap(item, index, listData)">.&nbsp;.&nbsp;.</view>
            </slot>
          </template>

          <!-- 小程序多个同名具名组件显示不正常只能单独写 之后用的地方多了需要换个slot没有问题的组件 -->
          <template v-if="item1.slot && item1.slot == 'activities'">
            <slot :name="item1.slot" :item="item" :index="index">
              <view v-if="item.activities && item.activities.length > 0">
                <view class="activities-title">
                  {{
                    item.activities[0].stageTitle
                      ? item.activities[0].stageTitle
                      : "-"
                  }}</view>
                <view class="activities-id" v-for="(activItem, activIndex) in item.activities" :key="activIndex">
                  {{ activItem.id }}.&nbsp;&nbsp;{{ activItem.title }}</view>
              </view>
              <text v-else>-</text>
            </slot>
          </template>
          <template v-else>
            {{
              item1.formatter
                ? item1.formatter(item[item1.key], index)
                : item[item1.key]
            }}
          </template>
          <view class="mask" @click.stop.prevent="onMask(item)" v-if="state && item.state == 2" />
        </uni-td>
      </uni-tr>
    </uni-table>
    <view class="empty" v-if="!listData || listData.length == 0">
      <image :src="icon
          ? icon
          : 'https://u.southmoney.com/fed/images/msouthmoney/listNull.png'
        " mode="scaleToFill" style="width: 432rpx; height: 302rpx" v-if="icon" />
      <text>{{ emptyText ? emptyText : "暂无数据" }}</text>
    </view>
  </view>
</template>

<script setup>
const props = defineProps({
  stripe: Boolean,
  border: Boolean,
  column: Array,
  listData: Array,
  checkSort: Function,
  st: String,
  sr: Number,
  loading: Boolean,
  height: String,
  type: String,
  tdsize: Number,
  thsize: Number,
  icon: String,
  emptyText: String,
  state: Boolean,
});
const emit = defineEmits(["tdClick", "actionTap"]);
function tdClick(item) {
  emit("tdClick", item);
}
function actionTap(item, index, allData) {
  emit("actionTap", item, index, allData);
}
function onMask(val) {
  console.log(val);
  uni.$u.toast("教案正在生成中！");
}
</script>

<script>
export default {
  options: { styleIsolation: "shared", multipleSlots: true },
};
</script>

<style lang="scss" scoped>
:deep(.uni-table-loading) {
  display: none;
}

.mask {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 28rpx;
  z-index: 10;
  overflow-y: none;
}

.sortDefault {
  .span1 {
    position: relative;
  }

  .span1::after {
    position: absolute;
    /* display: inline-block; */
    content: "";
    right: -16rpx;
    bottom: 0;
    border-bottom: 10rpx solid #999;
    border-left: 10rpx solid transparent;
  }
}

.sortBottom,
.sortTop {
  .span {
    position: relative;
    color: #ff9900;
  }

  .span::after {
    right: -20rpx !important;
    top: 50%;
    bottom: initial;
    transform: translateY(-50%);
    border: none;
  }
}

.sortBottom {
  .span::after {
    content: "↓";
  }
}

.sortTop {
  .span::after {
    content: "↑";
  }
}

.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 32rpx;
  color: #999;
  padding-bottom: 80rpx;
}

.th-table-fixed-right {
  box-shadow: -2px 0 0px 0px #ccc !important;
  // border: 1px solid red;
}
</style>
