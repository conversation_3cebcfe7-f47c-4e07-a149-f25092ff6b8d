<!-- ai帮写 -->
<template>
  <base-layout :nav-title="navTitle" :footerStyle="{
    paddingBottom: `calc(20rpx + env(safe-area-inset-bottom))`,
    backgroundColor: '#fff',
  }">
    <view class="template-wrapper" v-if="!isAddForm">
      <view class="template-wrapper-title">请选择课程类型</view>
      <view class="template-item" :class="{ 'active-template-item': isActiveId == item.id }"
        v-for="(item, index) in templateList" :key="index" @click="goToAitemplate(item.id)">
        {{ item.value }}
      </view>
    </view>
    <add-form :style="{ display: isAddForm ? 'block' : 'none' }" ref="addFormRef" @Calendar="isFooter = !isFooter" />
    <template #footer>
      <view class="footer-btn" :style="{ display: isFooter ? 'flex' : 'none' }">
        <up-button :text="sdtext" v-if="isBtn" :color="color" @tap="goToChat('sd')" shape="circle"></up-button>
        <up-button text="提交" style="width: 100vw;" v-else :color="color" @click="addFormRef.sendForm(isActiveId)"
          shape="circle"></up-button>
        <up-button color="#367CFF" v-if="isAi" text="AI生成" @click="goToChat('ai')" shape="circle"></up-button>
      </view>
    </template>
  </base-layout>
</template>
<script setup>
import { onShareAppMessage } from "@dcloudio/uni-app";
import { sharePageObj } from "@/utils";
import { reactive, ref } from "vue";
import BaseLayout from "@/components/base-layout/base-layout.vue";
import addForm from "./components/addForm.vue";
import { getThemeList } from "../api/aiwrite.js";
let isFooter = ref(true); // 是否显示底部
let isBtn = ref(true); // 是否显示按钮
let isAi = ref(true); // 是否显示AI按钮
let color = ref("");
let addFormRef = ref(null);
let isActiveId = ref(0); // 主题选中
let sdtext = ref("手动生成");
let navTitle = ref("创建课程");
const templateList = ref([]);

let isAddForm = ref(false);

const getTemplateList = async () => {
  const res = await getThemeList(31);
  templateList.value = res.data;
};
if (process.env.NODE_ENV == "production") {
  getTemplateList();
} else {
  templateList.value = [
    { value: "主题课程设计", id: 3524 },
    { value: "项目课程设计", id: 3832 },
    { value: "领域/特色课程设计", id: 3834 },
    { value: "生活课程设计", id: 3835 },
    { value: "STEM课程设计", id: 3833 },
    { value: "体育课程设计", id: 3836 }
  ]
}
const goToAitemplate = (item) => {
  isActiveId.value = item;
};

const goToChat = (type = "") => {
  if (isActiveId.value == 0) {
    uni.$u.toast("请选择课程类型");
    return;
  }
  if (type == "sd") {
    navTitle.value = "创建课程";
  }
  if (type == "ai") {
    navTitle.value = "AI生成课程";
    uni.navigateTo({
      url: `/courseDetails/aiWriteTemplatel/aiWriteTemplatel?id=${isActiveId.value}`,
    });
    return;
  }
  isAi.value = false;
  isAddForm.value = true;
  isBtn.value = false;
  color.value = "#367CFF";
  sdtext.value = "提交";
};
onShareAppMessage(() => sharePageObj());
</script>
<script>
export default {
  options: { styleIsolation: "shared" }, // 解除样式隔离
};
</script>
<style lang="scss" scoped>
.layout {
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #f5f5f5 0%, #f5f5f5 100%);
}

.footer-btn {
  display: flex;
  align-items: center;

  .u-button {
    width: 332rpx;
  }

  :deep(.u-button__text) {
    font-size: 30rpx !important;
    font-weight: 600;
  }
}

.tips {
  color: #333;
  font-size: 24rpx;
  display: flex;
  justify-content: center;
}

.footer {
  padding: 40rpx;
  display: flex;
  gap: 20rpx;
}

.scroll-container {
  flex: 1;
  background: linear-gradient(180deg, #f5f5f5 0%, #f5f5f5 100%);
  height: calc(100% - 80rpx);
  overflow-y: auto;
}

.template-wrapper {
  justify-content: space-between;
  background: url("https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/courseDetails_ai_bg.png") no-repeat;
  background-size: 170rpx 175rpx;

  .template-wrapper-title {
    text-align: center;
    height: 175rpx;
    line-height: 175rpx;
    font-size: 34rpx;
    font-weight: 500;
    color: #333333;
  }
}

.template-item {
  height: 144rpx;
  line-height: 144rpx;
  text-align: center;
  border-radius: 28rpx;
  background: white;
  font-size: 30rpx;
  font-weight: 600;
  margin-bottom: 24rpx;
  box-sizing: border-box;

  &:last-child {
    margin-bottom: 0;
  }
}

.active-template-item {
  border: 1px solid #367cff;
  color: #367cff;
}
</style>
