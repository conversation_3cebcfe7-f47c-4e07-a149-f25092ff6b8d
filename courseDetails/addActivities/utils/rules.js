import { reactive } from "vue"
export default () => {
    const basicRules = {
        name: [{
            required: true,
            message: '请输入活动名称',
            trigger: ['blur', 'change'],
        }],
        plannedGeneration: [{
            required: true,
            message: '请选择预设生成',
            trigger: ['blur', 'change'],
        }],
        author: [{
            required: true,
            message: '请输入作者',
            trigger: ['blur'],
        }],
        organizationForm: [{
            required: true,
            message: '请输入组织形式',
            trigger: ['blur', 'change'],
        }],
        durationInMinutes: [{
            required: true,
            type: 'number',
            message: '请输入活动时长',
            trigger: ['blur','change'],
        }]
    }
    const hxjyRules = {
        matrix1Id: [{
            required: true,
            type: 'string',
            message: '请选择领域',
            trigger: ['blur', 'change'],
        }],
        matrix2Id: [{
            required: true,
            message: '请选择维度',
            trigger: ['blur', 'change'],
        }],
        matrix3Id: [{
            required: true,
            message: '请选择子维度',
            trigger: ['blur', 'change'],
        }],
        targetId: [{
            required: true,
            message: '请选择指标',
            trigger: ['blur', 'change'],

        }]
    }
    return {
        hxjyRules,
        basicRules
    }
}