<!-- 新增活动 -->
<template>
  <view
    class="add_activities_layout layout"
    :style="{ paddingBottom: heightBottom + 76 + 'px' }"
  >
    <view class="col">
      <view class="title">活动基本信息</view>
      <view>
        <up-form
          ref="uFormRef"
          labelPosition="top"
          :model="formData"
          labelWidth="70"
          :labelStyle="labelStyle"
          :rules="basicRules"
        >
          <up-form-item
            label="活动名称"
            prop="name"
            required
          >
            <up-input
              v-model="formData.name"
              placeholder="请输入内容"
              :customStyle="customStyle"
              fontSize="24rpx"
            />
          </up-form-item>
          <view class="flex-jsb-ac">
            <up-form-item
              style="width: 292rpx"
              label="预设生成"
              prop="plannedGeneration"
              required
            >
              <uni-data-select
                v-model="formData.plannedGeneration"
                :localdata="yssc"
                clear
              />
              <!-- 用于触发验证 -->
              <up-input
                style="display: none"
                v-model="formData.plannedGeneration"
              />
            </up-form-item>
            <up-form-item
              style="width: 292rpx"
              label="作者"
              prop="author"
              required
            >
              <up-input
                v-model="formData.author"
                disabledColor="#fff"
                fontSize="24rpx"
                :customStyle="customStyle"
                placeholder="请输入内容"
              />
            </up-form-item>
          </view>
          <view class="flex-jsb-ac">
            <up-form-item
              style="width: 292rpx"
              label="组织形式"
              prop="organizationForm"
              required
            >
              <uni-data-select
                v-model="formData.organizationForm"
                :localdata="zzxs"
                clear
              />
              <!-- 用于触发验证 -->
              <up-input
                style="display: none"
                v-model="formData.organizationForm"
              />
            </up-form-item>
            <up-form-item
              style="width: 292rpx"
              label="活动时长"
              prop="durationInMinutes"
              required
            >
              <up-input
                :customStyle="customStyle"
                v-model="formData.durationInMinutes"
                fontSize="24rpx"
                type="number"
                placeholder="请输入内容"
              >
                <template #suffix>
                  <view style="font-size: 24rpx; font-weight: 400">分钟</view>
                </template>
              </up-input>
            </up-form-item>
          </view>
        </up-form>
      </view>
      <!-- 核心经验 :rules="hxjyRules" -->
      <up-form
        ref="coreFormRef"
        labelPosition="left"
        labelWidth="70"
        :model="coreFormData"
        :labelStyle="labelStyle"
      >
        <view
          class="hxjy"
          v-for="(item, index) in coreFormData"
          :key="index"
        >
          <view class="hxjy_title flex-ac">
            <view class="line"></view>
            <text class="hxjy_title_text">核心经验</text>
            <image
              src="@/static/common/delete.png"
              @click="coreFormData.splice(index, 1)"
            />
          </view>
          <up-form-item
            label="领域"
            prop="matrix1Id"
          >
            <uni-data-select
              v-model="item.matrix1Id"
              :localdata="domainData"
              style="width: 460rpx"
              @change="coreFormChange(item.matrix1Id, item, 'DimensionData')"
              clear
            />
            <!-- 用于触发验证 -->
            <up-input
              v-model="item.matrix1Id"
              style="display: none"
            />
          </up-form-item>
          <up-form-item
            label="维度"
            prop="matrix2Id"
          >
            <uni-data-select
              v-model="item.matrix2Id"
              :localdata="item.DimensionData"
              style="width: 460rpx"
              @change="coreFormChange(item.matrix2Id, item, 'subDimensionData')"
              clear
            />
            <!-- 用于触发验证 -->
            <up-input
              style="display: none"
              v-model="item.matrix2Id"
            />
          </up-form-item>
          <up-form-item
            label="子维度"
            prop="matrix3Id"
          >
            <uni-data-select
              v-model="item.matrix3Id"
              :localdata="item.subDimensionData"
              style="width: 460rpx"
              @change="coreFormChange(item.matrix3Id, item, 'indexData')"
              clear
            />
            <!-- 用于触发验证 -->
            <up-input
              style="display: none"
              v-model="item.matrix3Id"
            />
          </up-form-item>
          <up-form-item
            label="指标"
            prop="targetId"
          >
            <uni-data-select
              v-model="item.targetId"
              :localdata="item.indexData"
              style="width: 300rpx"
              @change="coreFormChange(item.targetId)"
              clear
              placement="top"
            />
            <!-- 用于触发验证 -->
            <up-input
              style="display: none"
              v-model="item.targetId"
            />
          </up-form-item>
        </view>
      </up-form>

      <view
        class="add_hxjy"
        @click="addCoreFormItem"
        >新增核心经验</view
      >
    </view>
    <view
      class="action-btn"
      :style="{ height: heightBottom + 52 + 'px' }"
      v-if="isBtn"
    >
      <up-button
        type="primary"
        text="保存"
        round
        shape="circle"
        @click="sendSave"
        color="#367CFF"
      ></up-button>
    </view>
  </view>
</template>

<script setup>
import { reactive, ref } from "vue";
import { enumList, getMatrixList, getTargetList } from "@/api/index.js";
import { addActivity } from "../api/subjeckActivity.js";
import { onLoad } from "@dcloudio/uni-app";
import Rules from "./utils/rules.js";
const { hxjyRules, basicRules } = Rules();
const formData = reactive({
  author: "",
  name: "",
  organizationForm: "",
  plannedGeneration: "",
  durationInMinutes: "",
});
const customStyle = { height: "70rpx", padding: "0 24rpx" };
const labelStyle = { fontSize: "28rpx", fontWeight: "600" };
// 组织形式的下拉选择数据
let zzxs = reactive([]); // { value: 0, text: "预设" },
let coreFormData = ref([
  // {
  // 	matrix1Id: "",
  // 	matrix2Id: "",
  // 	matrix3Id: "",
  // 	targetId: "",
  // 	DimensionData: [], // 维度数据
  // 	subDimensionData: [], // 子维度数据
  // 	indexData: [], // 指标数据
  // }
]); // 核心经验表单数据
const { safeAreaInsets } = uni.getSystemInfoSync();
let heightBottom = ref(safeAreaInsets.bottom);
const domainData = ref([]); // 领域数据
// 预设生产的下拉选择数据
let yssc = reactive([
  { value: "Planned", text: "预设" },
  { value: "Generated", text: "生成" },
]);
let _gradeId = ref(0);
let _subjectId = ref(0);
let _subjectStageId = ref(0);
let isBtn = ref(true); //保存后按钮消失

onLoad((options) => {
  _gradeId.value = options.gradeId;
  _subjectId.value = options.subjectId;
  _subjectStageId.value = options.subjectStageId;
  formData.author = options.creator || "";
  uni.setStorageSync("addForm", true);
});

const getenumList = async () => {
  let res = await enumList();
  const obj1 = res.data.SubjectActivityOrganizationFormEnumDesc;
  for (let key in obj1) {
    zzxs.push({
      value: key,
      text: obj1[key],
    });
  }
};

const removeArray = (data) => {
  return data.map((item) => {
    let newItem = {};
    for (let key in item) {
      if (!Array.isArray(item[key])) {
        newItem[key] = item[key];
      }
    }
    return newItem;
  });
};
let sendData = ref([]);
const sendSave = () => {
  formData.durationInMinutes = Number(formData.durationInMinutes)
    ? Number(formData.durationInMinutes)
    : "";
  sendData.value = {
    ...formData,
    subjectId: _subjectId.value,
    subjectStageId: _subjectStageId.value,
    matrices: [],
  };
  //剔除掉值为数组的属性
  sendData.matrices = removeArray(coreFormData.value);
  basicValiDateForm();
};
const uFormRef = ref(null);
const coreFormRef = ref(null);
const validateForm = async () => {
  coreFormRef.value
    .validate()
    .then((valid) => {
      // 使用了其他组件，验证到不了这里
    })
    .catch((e) => {
      // 处理验证错误
      let data = [];
      // 判断核心经验是否全部填写
      data = removeArray(coreFormData.value);
      let result = data.map((item) => {
        // 检查每个对象的属性是否为空
        return Object.values(item).every((value) => value != "");
      });
      const falg = result.every((item) => item == true);
      if (falg) {
        basicValiDateForm();
        coreFormRef.value.clearValidate();
      } else {
        uni.$u.toast("核心经验校验失败，请重新填写");
      }
    });
};

const basicValiDateForm = () => {
  uFormRef.value
    .validate()
    .then(async (valid) => {
      if (valid) {
        uni.$u.toast("校验通过, 1s后返回");
        isBtn.value = false;
        sendData.value.matrices = removeArray(coreFormData.value);
        console.log(sendData.value);
        const res = await addActivity(sendData.value);
        console.log(res);
        if (res.status == 0) {
          setTimeout(() => {
            uni.navigateBack({ delta: 1 });
          }, 1000);
        }
      } else {
        uni.$u.toast("基本信息校验失败");
      }
    })
    .catch((e) => {
      uni.$u.toast("活动基本信息校验失败");
    });
};

// 添加核心经验
function addCoreFormItem() {
  coreFormData.value.push({
    matrix1Id: "",
    matrix2Id: "",
    matrix3Id: "",
    targetId: "",
    DimensionData: [], // 维度数据
    subDimensionData: [], // 子维度数据
    indexData: [], // 指标数据
  });
}

// 获取下拉数据
const getMatrix = async (pid, arr, flag = true) => {
  let res;
  if (flag) {
    res = await getMatrixList({
      pid,
      current: 1,
      pageSize: 1000,
    });
  } else {
    res = await getTargetList({
      matrix3Id: pid,
      current: 1,
      pageSize: 1000,
      gradeId: _gradeId.value,
    });
  }
  res.data.forEach((el) => {
    arr.push({
      value: el.id,
      text: el.title,
    });
  });
};

// 选中一项后
const coreFormChange = (id, data, type) => {
  id = id;
  if (!data) {
    return;
  }
  if (type != "indexData") {
    console.log("触发");
    data[type] = [];
    getMatrix(id, data[type]);
  } else {
    data[type] = [];
    getMatrix(id, data[type], false);
  }
};

getenumList();
getMatrixList({
  pid: 0,
  current: 1,
  pageSize: 1000,
}).then((res) => {
  res.data.forEach((el) => {
    domainData.value.push({
      value: el.id,
      text: el.title,
    });
  });
});
</script>
<script>
export default {
  options: { styleIsolation: "shared" }, // 解除样式隔离
};
</script>

<style lang="scss" scoped>
.add_activities_layout {
  height: 100%;
  box-sizing: border-box;
  padding: 0 32rpx;
  overflow-y: auto;

  :deep(.u-form-item__body__left__content) {
    flex-direction: row-reverse;
    flex: initial;

    .u-form-item__body__left__content__required {
      right: -16rpx;
      left: initial;
    }
  }

  .hxjy {
    border-top: 1px solid #eee;
    padding-top: 45rpx;
    margin-top: 21rpx;

    ::v-deep .uni-select__input-text {
      width: 400rpx;
    }

    .hxjy_title {
      image {
        width: 36rpx;
        height: 36rpx;
        margin-left: auto;
      }

      .hxjy_title_text {
        font-size: 30rpx;
        font-weight: 600;
        color: rgba(51, 51, 51, 1);
      }

      .line {
        width: 4rpx;
        height: 30rpx;
        border-radius: 0rpx 685rpx, 685rpx, 0rpx;
        background: #3f79ff;
        margin-right: 16rpx;
      }
    }
  }

  .add_hxjy {
    text-align: center;
    font-size: 28rpx;
    font-weight: 500;
    color: rgba(63, 121, 255, 1);
    margin-top: 20rpx;
  }

  .col {
    width: 100%;
    border-radius: 28rpx;
    background: rgba(255, 255, 255, 1);
    box-shadow: 4rpx 8rpx 16rpx rgba(0, 0, 0, 0.02);
    box-sizing: border-box;
    padding: 32rpx;
    margin-top: 8rpx;

    .title {
      font-size: 30rpx;
      font-weight: 600;
      color: rgba(51, 51, 51, 1);
      padding-bottom: 32rpx;
      border-bottom: 1px solid #eee;
    }
  }

  .action-btn {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    box-sizing: border-box;
    padding: 16rpx 32rpx;
    background: #fff;
    opacity: 1;
    z-index: 999;
  }
}
</style>
