<template>
  <view class="layout">
    <view class="class_title">
      <view class="title"
        >{{ classDe.title }}
        <view
          class="category"
          v-if="categoryTitle"
          :style="categorystyle"
        >
          {{ categoryTitle }}
        </view>
      </view>
      <view class="text">{{ classDe?.term }} | {{ classDe.creator }}</view>
      <view class="text bottomT"
        >{{ timeRange(classDe.startAt) }} - {{ timeRange(classDe.endAt) }}</view
      >
    </view>
    <view>
      <view class="tabs flex-ac">
        <!-- <up-tabs :itemStyle="{ height: '46rpx', padding: '0', marginRight: '16rpx' }" :list="list"
				@click="tabSwitcher" :current="activeIndex" :activeStyle="activeStyle" lineHeight='0'
				lineWidth="0"></up-tabs> -->

        <view
          v-for="(item, index) in list"
          :key="item.name"
          class="tabs_item"
          :class="{ active_tabs_item: activeIndex == index }"
          @click="tabSwitcher(index)"
          >{{ item.name }}
        </view>
      </view>

      <view class="tabs_content">
        <!-- 活动列表 -->
        <activity-list
          v-if="activeIndex == 0 && classDe.schoolId"
          :subjectId="subjectId"
          :creator="classDe.creator"
          :themeName="classDe.title"
          :schoolId="classDe.schoolId"
          :subjectStageId="classDe.subjectStageId"
        ></activity-list>
        <!-- 课程文本 -->
        <calss v-if="activeIndex == 1"></calss>
        <!-- 阶段列表 -->
        <stage-list
          :subjectId="subjectId"
          v-if="activeIndex == 3"
        ></stage-list>
        <!-- 核心经验统计 -->
        <core-stat v-if="activeIndex == 4"></core-stat>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from "vue";
import { onLoad, onShareAppMessage } from "@dcloudio/uni-app";
import activityList from "./components/activityList/activityList.vue";
import calss from "./components/calss/calss.vue";
import coreStat from "./components/coreStat/coreStat.vue";
import stageList from "./components/stageList/stageList.vue";
import classDatail from "./utils/index.js";
import { timeRange, sharePageObj } from "@/utils/index.js";
import config from "@/common/config";
const { getDetail, classDe, categoryTitle, categorystyle } = classDatail();
const list = reactive([
  { name: "活动列表" },
  { name: "课程文本" },
  { name: "思维导图" },
  { name: "阶段列表" },
  { name: "核心经验统计" },
]);

let subjectId = ref("");
let activeIndex = ref(0);
let _id = "";
let _title = "";
let _gradeId = "";
let _aiFlag = "";
async function tabSwitcher(index) {
  if (index == 2) {
    const TOKEN_NAME = config[config.DEFINE_ENV].TOKEN_NAME;
    let token = uni.getStorageSync(TOKEN_NAME);
    uni.navigateTo({
      url:
        "/subPages/aiAssistant/mindMap/index?title=" +
        _title +
        "&id=" +
        subjectId.value +
        "&token=" +
        token,
    });
  } else {
    activeIndex.value = Number(index);
  }
}

onShareAppMessage(() =>
  sharePageObj({
    path: `/courseDetails/classHome/classHome?id=${_id}&title=${_title}&gradeId=${_gradeId}`,
  })
);

onLoad((option) => {
  const { id, title, gradeId, aiFlag, classId } = option;
  _id = id;
  _title = title;
  _gradeId = gradeId;
  _aiFlag = aiFlag;
  subjectId.value = option.id;
  uni.setStorageSync("subjectId", option.id);
  uni.setStorageSync("classId", Number(classId));
});
onMounted(() => {
  if (subjectId.value) getDetail(subjectId.value);
});
onUnmounted(() => {
  uni.removeStorageSync("subjectId");
  uni.removeStorageSync("themeName");
  uni.removeStorageSync("classId");
});
</script>

<style lang="scss" scoped>
@import "@/common/css/index.scss";

.layout {
  background: url("https://c.mypacelab.com/vxmp/img/classDeatils_bg_3x.png")
    no-repeat 100%;
  background-size: cover;
  background-position: top left;
  padding: 0 32rpx;
  height: 100%;
  overflow-y: auto;

  .category {
    all: initial;
    box-sizing: border-box;
    display: inline-block;
    height: 48rpx;
    line-height: 48rpx;
    font-size: 24rpx;
    font-weight: 600;
    padding: 0 16rpx;
    border-radius: 8rpx;
    margin-left: 16rpx;
    white-space: nowrap;
  }

  .tabs {
    margin: 32rpx 0 28rpx 0;
    overflow-x: auto;

    .tabs_item {
      // min-width: 136rpx;
      text-align: center;
      font-size: 24rpx;
      font-weight: 400;
      margin-right: 16rpx;
      padding: 8rpx 20rpx;
      box-sizing: border-box;
      white-space: nowrap;
      background: #ffffff;
      border-radius: 52.8rpx;
      color: #525252;
    }

    .active_tabs_item {
      border-radius: 52.8rpx;
      background: #367cff;
      align-items: center;
      color: #fff;
      padding: 8rpx 20rpx;
    }
  }

  .class_title {
    box-sizing: border-box;
    padding-top: 15rpx;

    .title {
      display: flex;
      align-items: center;
      font-size: 44rpx;
      font-weight: 600;
      color: rgba(51, 51, 51, 1);
      margin-bottom: 16rpx;
    }

    .text {
      font-size: 24rpx;
      font-weight: 400;
      color: rgba(128, 128, 128, 1);
      margin-top: 15rpx;
    }
  }
}
</style>
