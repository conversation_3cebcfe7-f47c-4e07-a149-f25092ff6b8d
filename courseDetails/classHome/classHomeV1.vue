<template>
  <view class="layout">
    <view class="class_title">
      <view class="title"
        >{{ classDe.title }}
        <view
          class="category"
          v-if="categoryTitle"
          :style="categorystyle"
        >
          {{ categoryTitle }}
        </view>
      </view>
      <view class="text">{{ classDe?.term }} | {{ classDe.creator }}</view>
      <view class="text bottomT"
        >{{ timeRange(classDe.startAt) }} - {{ timeRange(classDe.endAt) }}</view
      >
    </view>
    <view class="container">
      <Row title="课程状态">
        <template #right>
          <text
            v-if="isAICheck"
            class="statusText color1"
            >待采用</text
          >
          <view
            class="flex-ac"
            v-else
          >
            <text
              class="statusText color1"
              v-if="!pageData.status"
              >草稿</text
            >
            <up-switch
              v-model="pageData.status"
              @change="updataState"
              asyncChange
              size="20"
              :disabled="pageData.disabled"
            />
          </view>
        </template>
      </Row>
      <view class="par-row">
        <view
          v-for="(item, index) in pageData.list"
          :key="index"
          class="par-row-item"
          @tap="item.fn"
        >
          <view>{{ item.title }}</view>
          <up-icon
            :name="item.icon"
            size="30rpx"
          />
        </view>
      </view>
      <Row
        title="核心经验统计"
        icon="arrow-right"
        @tap="gotoPage('/courseDetails/classHome/coreStat/coreStat')"
      />
      <Row title="AI主题书" @tap="goToBook">
        <template #right>
          <view class="flex-ac" >
            <!-- <text class="statusText color2">待生成</text> -->
            <up-icon
              name="arrow-right"
              size="30rpx"
            />
          </view>
        </template>
      </Row>
      <!-- 课程大纲 -->
      <view class="par-row">
        <view
          class="flex-jsb-ac"
          style="
            height: 100rpx;
            box-shadow: 0px 4px 10px rgba(255, 255, 255, 0.2);
          "
        >
          <view style="flex: 1">课程大纲</view>
          <view class="flex-ac">
            <image
              v-if="!pageData.disabled"
              style="width: 30rpx; height: 30rpx"
              src="/static/common/editor.png"
              @tap="editActiveitem"
            />
            <!-- <up-icon
              :name="isShowList ? 'arrow-up' : 'arrow-down'"
              size="36rpx"
              @click="isShowList = !isShowList"
            /> -->
          </view>
        </view>
        <!-- 活动列表 -->
        <activity-list-v1
          v-if="pageData.actList.length > 0 && pageData.actList"
          :list="pageData.actList"
          :gradeId="_gradeId"
          :creator="classDe.creator"
          @gotoPage="gotoPage"
          @edit="editItem"
          @onClick="onClick"
          @sort="getActivityListData"
          :disabled="pageData.disabled"
          :isAICheck="isAICheck"
        />
        <u-loading-icon v-if="pageData.listLoading" />
        <view
          class="text-Empty"
          v-if="pageData.actList.length == 0 && !pageData.listLoading"
          >暂无数据</view
        >
      </view>
    </view>

    <!-- 单独活动的编辑 -->
    <Popup
      :show="isShowActivityItem"
      @close="isShowActivityItem = false"
    >
      <view class="iconAction">
        <view
          class="editorIcon"
          @click="gotoPageAct('AI')"
        >
          <image src="@/static/common/courseDetails_AICreate.png"></image>
          <view>AI生成教案</view>
        </view>
        <view @click="gotoPageAct('editActivity')">
          <image src="@/static/common/editor.png"></image>
          <view>编辑教案</view>
        </view>
        <view @click="gotoPageAct('export')">
          <icon-button
            :size="20"
            type="text"
            icon="icon-export"
            text=""
            style="margin-right: 19rpx; margin-top: 0"
          />
          <view>导出word</view>
        </view>
        <view @click="isModal = true">
          <image src="@/static/common/delete.png"></image>
          <view>删除</view>
        </view>
      </view>
    </Popup>

    <!-- 编辑课程大纲 -->
    <Popup
      :show="isPopShow"
      @close="closePop"
    >
      <view
        class="action"
        v-if="activeIndex == 0"
      >
        <view @tap="changeIndex(2)">编辑阶段</view>
        <view @tap="changeIndex(1)">编辑活动</view>
      </view>
      <!-- 编辑阶段 -->
      <view
        class="pop-container"
        v-if="activeIndex == 2"
      >
        <StageList :subjectId="_id" />
      </view>
      <!-- 编辑活动 -->
      <view
        class="pop-container"
        v-if="activeIndex == 1"
      >
        <PopTabs
          v-model="activeKey"
          :tabsList="pageData.tabsList"
        />
        <EditDelete
          v-if="activeKey == '编辑与删除'"
          :stageData="pageData.actList"
          @gotoPage="gotoPageED"
          @deleteItem="deleteItem"
          :gradeId="_gradeId"
        />

        <Sort
          v-if="activeKey == '调整顺序'"
          :stageData="pageData.actList"
          @updata="updata"
        />
        <!-- <stage-list
          :subjectId="_id"
          v-if="activeKey == '调整阶段'"
        ></stage-list> -->
      </view>
    </Popup>
    <!-- 弹出确认框 -->
    <up-modal
      :show="isModal"
      @confirm="deleteActivityItem"
      @cancel="isModal = false"
      :asyncClose="true"
      :content="deleteTitle"
      contentTextAlign="center"
      showCancelButton
    />

    <export-word
      ref="exportWordRef"
      type="活动"
      documentTemplateCategory="SubjectActivity"
      :subjectId="schoolId"
    />
    <!-- ai弹出确认框 -->
    <up-modal
      :show="isAiModal"
      @confirm="aiConfirm"
      @cancel="isAiModal = false"
      :asyncClose="true"
      :content="
        aiType == 0 ? '确定要生成所有教案吗？' : '确定要重新生成课程框架吗？'
      "
      contentTextAlign="center"
      showCancelButton
    />

    <!-- 底部AI二次确定 -->
    <view
      class="action-bottom"
      v-if="isAICheck"
    >
      <view class="action-bottom-btn">
        <up-button
          plain
          color="#FF9A3B"
          shape="circle"
          text="重新生成大纲"
          @click="addAllFormdata(1)"
        />
      </view>

      <view class="action-bottom-btn">
        <up-button
          color="#FF9A3B"
          shape="circle"
          text="采用以上大纲"
          @click="addAllFormdata(0)"
        />
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from "vue";
import { onLoad, onShareAppMessage, onShow } from "@dcloudio/uni-app";
import classDatail from "./utils/index.js";
import { timeRange, sharePageObj } from "@/utils/index.js";
import Row from "./components/row/row.vue";
import config from "@/common/config";
import activityListV1 from "./components/activityList/activityListV1.vue";
// import stageList from "./components/stageList/stageList.vue";
import PopTabs from "./components/popTabs/popTabs.vue";
import EditDelete from "./components/editDelete/editDelete.vue";
import Sort from "./components/sort/sort.vue";
import StageList from "./components/stageList/stageList.vue";
import ExportWord from "@/components/export-word/export-word.vue";

import {
  getActivityList,
  deleteActivity,
  getTemplateList,
  exportWord,
  updataActState,
  aiGenerate,
  aiOnceMoreGenerate,
} from "@/courseDetails/api/subjeckActivity.js";
import { getClassText } from "@/courseDetails/api/class.js"; // 是否需要二次确定

let _id = ref("");
let _gradeId = "";
let exportWordRef = ref(null);
let schoolId = uni.getStorageSync("USER_INFO").currentSchoolId;
let isPopShow = ref(false); // 是否显示弹窗
let isShowActivityItem = ref(false); // 是否显示弹窗
let activityItem = ref(null); // 当前选择活动
let activeKey = ref("编辑与删除");
let activeIndex = ref(0); // 显示编辑活动/阶段 0 无 1 编辑活动 2 编辑阶段
let wordId = ref(0); // 模板id 根据学校查出来的
let isModal = ref(false); // 是否显示删除单个活动弹窗
let deleteTitle = ref("确定要删除吗？"); // 删除单个活动弹窗标题文本
let isAICheck = ref(false); // 是否需要AI二次确定
let isAiModal = ref(false); // 是否显示AI二次确定弹窗
let aiType = ref(0); // 0 采用 1 重新生成
const { getDetail, classDe, categoryTitle, categorystyle } = classDatail();

const pageData = reactive({
  status: false, // 是否为草稿 默认为 true  不是草稿 false 是草稿
  list: [
    {
      title: "课程文本",
      key: "text",
      icon: "arrow-right",
      fn: () => {
        if (isAICheck.value) return uni.$u.toast("请先确认大纲");
        let url =
          "/courseDetails/classHome/classText/classText?id=" + _id.value;
        if (pageData.disabled) url += "&disabled=true";
        uni.navigateTo({ url });
      },
    },
    {
      title: "思维导图",
      key: "mindMap",
      icon: "arrow-right",
      fn: () => {
        if (isAICheck.value) return uni.$u.toast("请先确认大纲");
        const TOKEN_NAME = config[config.DEFINE_ENV].TOKEN_NAME;
        const title = uni.getStorageSync("themeName");
        let token = uni.getStorageSync(TOKEN_NAME);
        let id = uni.getStorageSync("subjectId");
        uni.navigateTo({
          url:
            "/subPages/aiAssistant/mindMap/index?title=" +
            title +
            "&id=" +
            id +
            "&token=" +
            token,
        });
      },
    },
  ],
  actList: [],
  tabsList: [
    { name: "编辑与删除" },
    { name: "调整顺序" },
    // { name: "调整阶段" },
  ],
  listLoading: false,
  disabled: false, // 是否只读
});

onLoad(async (option) => {
  const { id, gradeId, isPublic } = option;
  _id.value = id;
  _gradeId = gradeId;
  if (isPublic) pageData.disabled = true;
  if (id) {
    uni.setStorageSync("subjectId", id);
    await getDetail(id);
    if (classDe.value.state != 4) pageData.status = true;
  }
});

// 主题书
function goToBook() {
  uni.navigateTo({
    url: `/courseDetails/AIThemeBook/AIThemeBook?id=${_id.value}`,
  })
}

// 检查是否需要AI二次确定
const needsAICheck = async () => {
  const res = await getClassText(_id.value);
  if (res.status == 0) {
    const { aiFlag, submitAiFlag } = res.data;
    console.log(aiFlag == 1 && submitAiFlag == 0);

    isAICheck.value = aiFlag == 1 && submitAiFlag == 0;
  }
};

const addAllFormdata = async (type = "") => {
  isAiModal.value = true;
  aiType.value = type;
};

// ai弹框确认事件
const aiConfirm = async () => {
  let id = uni.getStorageSync("subjectId");
  let res;
  try {
    res = await (aiType.value == 0
      ? aiGenerate(id)
      : aiOnceMoreGenerate({ subjectId: id }));
    if (res.status === 0) {
      isAiModal.value = false;
      if (aiType.value == 0) {
        isAICheck.value = 0;
        // 刷新页面
        updataAct();
        // getList(uni.getStorageSync("subjectId"));
        uni.$u.toast("确认成功");
      } else {
        let sid = JSON.parse(res.data).categoryId;
        uni.navigateTo({
          url: `/courseDetails/aiWriteTemplatel/aiWriteTemplatel?subjectId=${id}&id=${sid}`,
        });
      }
    } else {
      throw new Error(res.message || "接口错误，请联系管理员！");
    }
  } catch (error) {
    console.log(error);
    uni.showToast({
      title: error.message,
      icon: "error",
    });
  }
};

// 点击活动跳转详情
const onClick = (item) => {
  if (isAICheck.value) return uni.$u.toast("请先确认大纲");
  const { id } = item;
  let url = `/courseDetails/activityDetails/activityDetails?id=${id}&theme${classDe.title}=&gradeId=${_gradeId}`;
  if (pageData.disabled) url += "&disabled=true";
  uni.navigateTo({
    url,
  });
};
// 评价跳转
const gotoPage = (url) => {
  if (isAICheck.value) return uni.$u.toast("请先确认大纲");
  // 判断是不是字符串
  if (typeof url == "string") {
    uni.navigateTo({
      url,
    });
    return;
  }
  // /courseDetails/evaluate/evaluate?id=4458
  uni.navigateTo({
    url: `/courseDetails/evaluate/evaluate?id=${url.id}`,
  });
};

// 修改课程状态
const updataState = async (val) => {
  console.log(val);

  let n = val ? 1 : 4;
  uni.showModal({
    content: val ? "确定要发布吗？" : "确定要放入草稿箱吗？",
    success: (res) => {
      if (res.confirm) {
        updataAct(n);
      }
    },
  });
};

// 修改状态
// 1 发布 2 ai生成中 3 ai生成失败 4 草稿
const updataAct = async (state) => {
  const parmas = {
    id: Number(_id.value),
    state,
  };
  try {
    const res = await updataActState(parmas);
    if (res.status == 0) {
      uni.$u.toast("修改成功");
      state == 1 ? (pageData.status = true) : (pageData.status = false);
      getActivityListData();
    }
  } catch (e) {
    console.log(e);
    uni.showToast({
      title: "修改失败",
      icon: "none",
    });
  }
};

// 重置状态
const resetStatus = () => {
  isPopShow.value = false;
  setTimeout(() => {
    activeKey.value = "编辑与删除";
    activeIndex.value = 0;
  }, 300);
};

// 编辑一项活动
const editActiveitem = (val) => {
  if (isAICheck.value) return uni.$u.toast("请先确认大纲");
  isPopShow.value = true;
};

// 切换编辑活动/阶段
const changeIndex = (index) => {
  isPopShow.value = false;
  setTimeout(() => {
    activeIndex.value = index;
    isPopShow.value = true;
  }, 300);
};

// 获取课程大纲
const getActivityListData = async () => {
  pageData.listLoading = true;
  try {
    const res = await getActivityList(_id.value);
    if (res.status == 0) {
      pageData.actList = res.data;
      pageData.actList.forEach((item, index) => {
        if (index == 0) {
          item.isOpen = true;
        } else {
          item.isOpen = false;
        }
      });
      pageData.listLoading = false;
    }
  } catch (e) {
    console.log(e);
    uni.showToast({
      title: "获取活动大纲失败",
      icon: "none",
    });
    pageData.listLoading = false;
  }
};

/**单独活动的编辑与删除 相关**/
// 编辑
const editItem = (item) => {
  if (isAICheck.value) return uni.$u.toast("请先确认大纲");
  activityItem.value = item;
  deleteTitle.value = `确定要删除 ${item.name} 吗？`;
  isShowActivityItem.value = true;
};
// 删除
async function deleteActivityItem() {
  await deleteItem(activityItem.value);
  isModal.value = false;
  isShowActivityItem.value = false;
}
// 跳转
async function gotoPageAct(path) {
  const { subjectStageId, subjectId, author, id } = activityItem.value;
  let url = "";
  switch (path) {
    case "AI":
      url = `/courseDetails/aiCreateDetails/aiCreateDetails?&id=${id}&subjectId=${subjectId}`;
      break;
    case "editActivity":
      url = `/courseDetails/${path}/${path}?subjectStageId=${subjectStageId}&subjectId=${subjectId}&gradeId=${_gradeId}&id=${id}&author=${author}`;
      break;
    case "export":
      if (exportWordRef.value) exportWordRef.value.healeExportWord(id);

      return; // 直接返回，不需要设置url
  }
  if (url) uni.navigateTo({ url });

  isShowActivityItem.value = false;
}

// 编辑与删除 的页面跳转
const gotoPageED = (item) => {
  console.log(item);
  if (item == "add") {
    isPopShow.value = false;
    return;
  }
  const { subjectStageId, subjectId, id, author } = item;
  uni.navigateTo({
    url: `/courseDetails/editActivity/editActivity?subjectStageId=${subjectStageId}&subjectId=${subjectId}&gradeId=${_gradeId}&id=${id}&author=${author}`,
  });
  isPopShow.value = false;
};
// 删除活动
const deleteItem = async (item) => {
  console.log(item.id);
  try {
    const res = await deleteActivity(item.id);
    if (res.status == 0) {
      uni.showToast({
        title: "删除成功",
        icon: "none",
      });
      isPopShow.value = false;
      getActivityListData();
    }
  } catch (e) {
    console.log(e);
    uni.showToast({
      title: "删除失败",
      icon: "none",
    });
  }
};
// 调整排序
const updata = () => {
  // 更新页面
  resetStatus();
  getActivityListData();
};

// 关闭弹框
const closePop = () => {
  isPopShow.value = false;
  setTimeout(() => {
    resetStatus();
    getActivityListData();
  }, 400);
};

onShow(() => {
  resetStatus();
  getActivityListData();
});

onMounted(() => needsAICheck());

onUnmounted(() => {
  uni.removeStorageSync("themeName");
  uni.removeStorageSync("subjectId");
  uni.removeStorageSync("classId");
});
onShareAppMessage(() =>
  sharePageObj({
    path: `/courseDetails/classHome/classHomeV1?id=${_id}`,
  })
);
</script>

<style lang="scss" scoped>
@import "@/common/css/index.scss";

.action-bottom {
  width: 100%;
  height: 104rpx;
  position: fixed;
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 99;
  right: 0rpx;
  bottom: 0rpx;
  background-color: #fff;
  display: flex;
  justify-content: space-evenly;
  align-items: center;

  &-btn {
    width: 332rpx;
  }
}

.iconAction {
  & > view {
    height: 88rpx;
    display: flex;
    align-items: center;
    font-size: 30rpx;
    font-weight: 400;
    letter-spacing: 0rpx;
    line-height: 48rpx;
    color: rgba(51, 51, 51, 1);
    text-align: left;
    vertical-align: middle;

    image {
      width: 40rpx;
      height: 40rpx;
      margin-right: 28rpx;
    }
  }
}

.flex-ac {
  display: flex;
  align-items: center;
}

.flex-jsb-ac {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action {
  & > view {
    height: 88rpx;
    line-height: 88rpx;
    text-align: center;

    &:active {
      opacity: 0.8;
      background: #f5f5f5;
    }
  }
}

.pop-container {
  height: 80vh;
  overflow-y: auto;
}

.container {
  margin-top: 32rpx;

  .statusText {
    font-size: 28rpx;
    font-weight: 400;
    margin-right: 12rpx;
  }

  .color1 {
    color: #fd5a5e;
  }

  .color2 {
    color: #808080;
  }

  .par-row {
    padding: 0 32rpx;
    box-sizing: border-box;
    background: #ffffff;
    border-radius: 28rpx;
    margin-bottom: 24rpx;
    ::v-deep .activity-list {
      border-bottom: 1px solid #eeeeee;
    }
    ::v-deep .activity-list:nth-last-child(2) {
      border-bottom: none;
    }
    &-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      min-height: 100rpx;
      border-bottom: 1px solid #eeeeee;

      &:last-child {
        border-bottom: none;
      }
    }
  }
}

.layout {
  background: url("https://c.mypacelab.com/vxmp/img/classDeatils_bg_3x.png")
    no-repeat 100%;
  background-size: cover;
  background-position: top left;
  padding: 0 32rpx;
  padding-bottom: 124rpx;
  height: 100%;
  overflow-y: auto;

  .category {
    all: initial;
    box-sizing: border-box;
    display: inline-block;
    height: 48rpx;
    line-height: 48rpx;
    font-size: 24rpx;
    font-weight: 600;
    padding: 0 16rpx;
    border-radius: 8rpx;
    margin-left: 16rpx;
    white-space: nowrap;
  }

  .class_title {
    box-sizing: border-box;
    padding-top: 15rpx;

    .title {
      display: flex;
      align-items: center;
      font-size: 44rpx;
      font-weight: 600;
      color: rgba(51, 51, 51, 1);
      margin-bottom: 16rpx;
    }

    .text {
      font-size: 24rpx;
      font-weight: 400;
      color: rgba(128, 128, 128, 1);
      margin-top: 15rpx;
    }
  }
}
</style>
