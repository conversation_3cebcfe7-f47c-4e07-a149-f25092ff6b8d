import { reactive, ref } from "vue"
export default (isShow, popupData, popupTitle, DeleteName) => {
    // 使用 reactive 创建响应式对象  
    const options = reactive([{
        text: '编辑',
        style: {
            backgroundColor: '#3c9cff',
            fontSize: '24rpx'
        },
    },
    {
        text: '删除',
        style: {
            backgroundColor: '#fa3534',
            fontSize: '24rpx'
        }
    }
    ]);

    function swipeBtn(item) {
        const value = JSON.parse(item.name)
        const { title, id, index } = value
        if (item.index == 0) {
            popupTitle.value = '编辑阶段'
            popupData.title = title
            popupData.id = id
            isShow.value = true
        } else if (item.index == 1) {
            DeleteName(value, index)
        }
    }

    return {
        options,
        swipeBtn
    }
}