import { reactive, ref } from "vue"
import { UstageName, AstageName, DstageName } from "@/courseDetails/api/classDetalis.js"
let subData = ref('')
let isEdit = ref(false)
let curItem = ref('');//当前选中的项
let curIndex = ref(-1);//当前选中的项的索引
export default (isShow, subjectId, getList, stage, _aiFlag) => {
	let isModal = ref(false) // 确认框状态
	let ModelData = {}
	const uFormRef = ref(null);
	let popupTitle = ref("新增阶段");
	const popupData = reactive({
		title: "",
		id: "",
	})
	// 校验规则
	const rules = {
		title: [{
			required: true,
			message: '请输入阶段名称',
			trigger: ['blur']
		}]
	}

	function itemFn(item, index) {
		if (_aiFlag.value) {
			uni.$u.toast("请先生成所有教案，再编辑");
			return
		}
		isEdit.value = true
		isShow.value = true
		curItem.value = null;
		curItem.value = item;
		curIndex.value = index;
	}


	function open(isEdits, item) {
		if (_aiFlag.value) {
			uni.$u.toast("请先生成所有教案，再编辑");
			return
		}
		isShow.value = true
		isEdit.value = false
		subData.value = item

		if (isEdits) {
			popupTitle.value = "编辑阶段"
			popupData.title = item.title
			popupData.id = item.id
		} else {
			popupTitle.value = "新增阶段"
			popupData.title = ""
		}
	}

	function close() {
		isShow.value = false
		popupTitle.title = "新增阶段"
		subData.value = null
		popupData.title = ''
		popupData.id = ''
		popupData.subjectId = ''
	}


	async function UpdataName(data) {
		let res = await UstageName(data)
		console.log(res);
	}

	async function AddName(data) {
		let res = await AstageName(data)
		getList({ subjectId })
	}

	function DeleteName(data, i) {
		console.log(data, i);
		isModal.value = true
		ModelData.data = data
		ModelData.index = i

	}
	// 点击确认删除后执行
	async function confirm() {
		let { data, index } = ModelData
		const obj = {
			id: data.id,
			forceFlag: true
		}

		let res = await DstageName(obj)
		ModelData = {}
		isModal.value = false
		setTimeout(() => stage.data.splice(index, 1), 300)
	}



	// 提交方法  
	function submit() {
		uFormRef.value.validate().then(valid => {
			if (valid) {
				// uni.$u.toast('校验通过')
				if (popupTitle.value == "新增阶段") {
					AddName({
						title: popupData.title,
						subjectId
					})
				} else if (popupTitle.value == "编辑阶段") {
					UpdataName({
						id: popupData.id,
						title: popupData.title
					})
					// subData.value.title = popupData.title
					setTimeout(() => getList({ subjectId }), 100)
				}

			}
			close()
		})
	}

	return {
		popupData,
		uFormRef,
		rules,
		popupTitle,
		open,
		close,
		submit,
		DeleteName,
		isModal,
		confirm,
		itemFn,
		isEdit,
		curItem,
		curIndex
	}
}