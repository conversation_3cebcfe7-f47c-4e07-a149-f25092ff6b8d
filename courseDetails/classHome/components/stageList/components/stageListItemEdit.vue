<template>
  <view class="iconAction">
    <view @click="onFn('edit')">
      <image src="@/static/common/editor.png"></image>
      <view>编辑</view>
    </view>
    <view @click="onFn('delete')">
      <image src="@/static/common/delete.png"></image>
      <view>删除</view>
    </view>
  </view>
</template>

<script setup>
const emit = defineEmits(["edit", "delete"]);
const onFn = (type) => {
  if (type === "edit") emit("edit", type); // 编辑
  if (type === "delete") emit("delete", type); // 删除
};
</script>

<style lang="scss" scoped>
.iconAction {
  & > view {
    height: 88rpx;
    display: flex;
    align-items: center;
    font-size: 30rpx;
    font-weight: 400;
    letter-spacing: 0rpx;
    line-height: 48rpx;
    color: rgba(51, 51, 51, 1);
    text-align: left;
    vertical-align: middle;

    image {
      width: 40rpx;
      height: 40rpx;
      margin-right: 28rpx;
    }
  }
}
</style>
