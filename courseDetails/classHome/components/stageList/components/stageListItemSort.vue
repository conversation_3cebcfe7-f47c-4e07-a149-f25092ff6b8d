<template>
  <view class="sort-layout">
    <view class="sort-list-title">
      <view>阶段排序</view>
      <view @click="sendSort">完成</view>
    </view>
    <l-dragSort
      @change="change"
      :list="list"
      :lineHeight="100"
      :styleName="{ fontSize: '30rpx', fontWeight: '400' }"
    ></l-dragSort>
  </view>
</template>

<script setup>
import { ref, computed, watch } from "vue";
import { stageListSort } from "@/courseDetails/api/classDetalis.js";
import lDragSort from "@/courseDetails/components/l-dragSort/components/l-dragSort/l-dragSort.vue";
const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
});
let slist = ref([]);
watch(
  () => props.list,
  (nv, ov) => {
    console.log(nv);
    if (nv) slist.value = nv;
  },
  { deep: true }
);

let ids = ref([]);
function change(val) {
  let array = val;
  ids.value = array.map((item) => {
    return item.id;
  });
}

async function sendSort() {
  let form = {
    ids: ids.value,
    subjectId: uni.getStorageSync("subjectId"),
  };
  if (ids.value.length == 0) {
    uni.showToast({
      title: "请先拖动排序",
      icon: "error",
    });
    return;
  }
  if (!uni.getStorageSync("subjectId")) {
    uni.showToast({
      title: "错误！请重新进入课程",
      icon: "error",
    });
    return;
  }
  let res = await stageListSort(form);
  if (res.status == 0) {
    uni.$u.toast("成功！");
    emit("updata");
  }
  console.log(res);
}

const emit = defineEmits(["updata", "close"]);
// 示例
// let list = ref([
//   {
//     name: "账户余额",
//     rightIcon: "/static/icon/dragsort.png",
//   },
//   {
//     name: "农业银行",
//     rightIcon: "/static/icon/dragsort.png",
//   },
//   {
//     name: "工商银行",
//     rightIcon: "/static/icon/dragsort.png",
//   },
//   {
//     name: "建设银行",
//     rightIcon: "/static/icon/dragsort.png",
//   },
// ]);
</script>

<style lang="scss" scoped>
.sort-layout {
  // width: 100%;
  // height: 80%;
  box-sizing: border-box;

  .sort-list-title {
    display: flex;
    align-items: center;
    justify-content: space-between;

    view:first-child {
      font-size: 34rpx;
      font-weight: 600;
    }

    view:last-child {
      font-size: 30rpx;
      font-weight: 500;
      color: rgba(63, 121, 255, 1);
    }
  }
}
</style>
