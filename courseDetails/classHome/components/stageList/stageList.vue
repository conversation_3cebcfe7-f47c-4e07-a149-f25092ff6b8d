<template>
  <view class="stageListlayout">
    <view class="table">
      <view class="head">
        <text class="head_title1 ths">序号</text>
        <text class="head_title2 ths">阶段名</text>
        <image
          src="@/static/icon/sort.png"
          @click="onSort"
        />
        <up-icon
          name="plus-circle"
          size="36rpx"
          @click="open(false)"
        />
      </view>

      <view v-if="stage.data && stage.data.length != 0">
        <view
          v-for="(item, index) in stage.data"
          :key="item.id"
        >
          <!-- <up-swipe-action :autoClose="true">
            <up-swipe-action-item
              :show="item.isShow"
              :options="options"
              :name="JSON.stringify(item)"
              @click="swipeBtn"
            >
              <view class="list">
                <view class="head_title1">{{ index + 1 }}</view>
                <view class="head_title2">{{ item.title }}</view>
              </view>
            </up-swipe-action-item>
          </up-swipe-action> -->
          <view class="list">
            <view class="head_title1">{{ index + 1 }}</view>
            <view class="head_title2">{{ item.title }}</view>
            <up-icon
              name="more-dot-fill"
              @click="itemFn(item, index)"
              size="36rpx"
            />
          </view>
        </view>
      </view>
      <up-empty
        style="margin-top: 100rpx"
        v-else
        mode="data"
      />
    </view>

    <!-- <view class="btn">
      <up-button
        type="primary"
        shape="circle"
        icon="plus-circle"
        text="新建"
        @click="open(false)"
      />
    </view> -->

    <Popup
      :show="isShow"
      @close="close"
    >
      <view
        class="popup"
        v-if="!isEdit"
      >
        <text>{{ popupTitle }}</text>
        <view>
          <up-form
            :rules="rules"
            ref="uFormRef"
            :model="popupData"
            labelPosition="left"
            labelWidth="75px"
          >
            <up-form-item
              prop="title"
              required
              label="阶段名称:"
            >
              <up-input
                v-model="popupData.title"
                placeholder="请输入阶段名称"
              ></up-input>
            </up-form-item>
          </up-form>
        </view>
        <up-button
          style="margin-top: 30rpx"
          type="primary"
          shape="circle"
          text="确认"
          @click="submit"
        />
      </view>
      <StageListItemEdit
        v-if="isEdit"
        @edit="stageItem"
        @delete="stageItem"
      />
    </Popup>
    <!-- 弹出确认框 -->
    <up-modal
      :show="isModal"
      @confirm="confirm"
      @cancel="isModal = false"
      ref="uModal"
      :asyncClose="true"
      content="请注意，确认删除后，会删除该阶段下所有的活动都会删除，您确认删除吗？"
      contentTextAlign="center"
      showCancelButton
    />
    <Popup
      :show="show"
      @close="show = false"
    >
      <stage-list-item-sort
        v-if="isShowSort"
        :list="stage.list"
        @updata="StageListSortoFn"
      />
    </Popup>
  </view>
</template>

<script setup>
import { stageList } from "@/courseDetails/api/classDetalis.js";
import { onLoad } from "@dcloudio/uni-app";
import { onMounted, reactive, ref } from "vue";
import popups from "./utils/popup.js";
import stageUtils from "./utils/index.js";
import StageListItemEdit from "./components/stageListItemEdit.vue";
import StageListItemSort from "./components/stageListItemSort.vue";
import Popup from "@/components/Popup/Popup.vue";
import { getClassText } from "@/courseDetails/api/class.js";
let isShow = ref(false);
let show = ref(false);
let isShowSort = ref(false);
let subId = ref(""); // 在微信中第一次onload取不到值
const stage = reactive({
  data: [],
  list: [],
});

let _aiFlag = ref(false);

const { subjectId } = defineProps(["subjectId"]);

const {
  open,
  close,
  popupData,
  submit,
  uFormRef,
  rules,
  popupTitle,
  DeleteName,
  isModal,
  confirm,
  itemFn,
  isEdit,
  curItem,
  curIndex,
} = popups(isShow, subjectId, getList, stage, _aiFlag);

const { swipeBtn, options } = stageUtils(
  isShow,
  popupData,
  popupTitle,
  DeleteName
);
// 编辑和删除
function stageItem(type) {
  if (type == "edit") {
    isShow.value = false;
    console.log(curItem.value);
    setTimeout(() => {
      open(true, curItem.value);
    }, 300);
  }
  if (type == "delete") {
    // isModal = true;
    DeleteName(curItem.value, curIndex.value);
    isShow.value = false;
    isEdit.value = false;
  }
}

const onSort = () => {
  if(_aiFlag.value) return uni.showToast({ title: "请先生成所有教案，再编辑", icon: "none" });
  show.value = !show.value;
  isShowSort.value = true;
};
function StageListSortoFn() {
  show.value = false;
  getList({ subjectId: subjectId || subId.value });
  setTimeout(() => {
    isShowSort.value = false;
  }, 300);
}

async function getList(data) {
  stage.list = [];
  let res = await stageList(data);
  stage.data = res.data;
  stage.list = res.data;
  stage.list.forEach((item, index) => {
    item.name = item.title;
    item.orderNum =  index + 1;
    item.rightIcon = "/static/icon/dragsort.png";
  });
}
// 查询是否确认ai生成
const getDateils = async () => {
  const res = await getClassText(uni.getStorageSync("subjectId"));
  if (res.status == 0) {
    const { aiFlag, submitAiFlag } = res.data;
    _aiFlag.value = aiFlag == 1 && submitAiFlag == 0;
  }
};

onLoad((option) => {
  subId.value = option.id;
});
onMounted(() => {
  getDateils();
  getList({ subjectId: subjectId || subId.value });
});
</script>

<style lang="scss" scoped>
.stageListlayout {
  height: 100%;
}

.popup {
  height: 40%;
  text {
    text-align: center;
    font-weight: bold;
  }
}

.table {
  box-sizing: border-box;
  padding: 32rpx;
  background: white;
  border-radius: 32rpx;
  border: 1px solid #e4e7ed;
  margin: 24rpx 0;

  .head {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    height: 60rpx;
    font-size: 24rpx;
    font-weight: 400;
    color: rgba(128, 128, 128, 1);

    image {
      width: 36rpx;
      height: 36rpx;
      margin-right: 32rpx;
    }

    .ths {
      border-right: 1px solid #e4e7ed;
      text-align: center;
      font-size: 28rpx;
      font-weight: 600;
      color: #000;
    }

    .ths:last-of-type {
      border-right: none;
    }
  }

  .list {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid #e4e7ed;
    padding: 26rpx 0;
    font-size: 28rpx;
    font-weight: 400;
    color: rgba(51, 51, 51, 1);

    .head_title1 {
      text-align: center;
    }

    .list_btn {
      display: flex;
      justify-content: space-around;
    }
  }

  .head_title1 {
    width: calc(100% / 6);
    // border: 1px solid red;
  }

  .head_title2 {
    flex: 1;
  }

  .head_title3 {
    width: calc(100% / 3);
  }
}

// .btn {
//   width: 100%;
//   position: fixed;
//   bottom: 30rpx;
//   left: 0;
//   padding: 0 30rpx;
//   box-sizing: border-box;
// }
</style>
