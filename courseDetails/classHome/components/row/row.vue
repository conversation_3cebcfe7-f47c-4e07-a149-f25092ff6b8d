<template>
    <view class="row">
        <template v-if="!cust">
            <view>
                <slot name="left">{{ title }}</slot>
            </view>
            <view>
                <up-icon v-if="icon" :name="icon" size="30rpx" />
                <slot v-else name="right" />
            </view>
        </template>
        <slot v-else></slot>
    </view>
</template>

<script setup>
import { } from "vue";
defineProps({
    title: {
        type: String,
        default: "",
    },
    icon: {
        type: String,
        default: "",
    },
    cust: {
        type: Boolean,
        default: false,
    },
});
</script>

<style lang="scss" scoped>
.row {
    padding: 0 32rpx;
    box-sizing: border-box;
    background: #ffffff;
    border-radius: 28rpx;
    margin-bottom: 24rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 100rpx;

    &:last-child {
        border-bottom: none;
    }
}
</style>
