<!-- 编辑与删除 -->
<template>
  <view>
    <uni-data-select
      v-model="activeId"
      placeholder="请选择阶段"
      :localdata="formatData(stageData)"
      @change="selectItem"
      style="background: #f5f5f5; margin-bottom: 24rpx"
    />
    <view
      class="editem"
      v-for="item in cateList?.actList"
      :key="item.id"
    >
      <text style="flex: 1">{{ item.name }}</text>
      <view class="editem-icon">
        <image
          style="width: 40rpx"
          src="@/static/common/delete.png"
          @tap="onDelete(item)"
        />
        <image
          src="@/static/common/editor.png"
          @tap="emit('gotoPage', item)"
        />
      </view>
    </view>
    <view class="editem">
      <text
        class="add"
        @tap="gotoPage"
        >{{
          JSON.stringify(cateList) == "{}" ? "请先选择阶段" : "+ 添加活动"
        }}</text
      >
    </view>
    <up-modal
      :show="isModal"
      @confirm="aiConfirm"
      @cancel="isModal = false"
      :asyncClose="true"
      :content="content"
      contentTextAlign="center"
      showCancelButton
    />
  </view>
</template>

<script setup>
import { ref } from "vue";
import utils from "../../utils";
const { formatData } = utils();
const emit = defineEmits(["gotoPage", "deleteItem"]);
const props = defineProps({
  stageData: {
    type: Object,
    default: () => [],
  },
  gradeId: {
    type: Number || String,
    default: "",
  },
});

// 响应式数据
const cateList = ref({});
let isModal = ref(false);
let activeItem = ref(null);
let activeId = ref(0); // 当前选中的id
let content = ref("确定删除该活动吗？");

const onDelete = (item) => {
  isModal.value = true;
  activeItem.value = item;
  console.log(item);
  content.value = `确定删除 ${item.name} 吗?`;
};
const gotoPage = () => {
  if (activeId.value == 0) {
    return;
  }
  const val = cateList.value;
  const creator = uni.getStorageSync("USER_INFO").name;
  uni.navigateTo({
    url: `/courseDetails/addActivities/addActivities?subjectId=${val.subjectId}&subjectStageId=${val.id}&gradeId=${props.gradeId}&creator=${creator}`,
  });
  emit("gotoPage", "add");
};
const aiConfirm = () => {
  isModal.value = false;
  emit("deleteItem", activeItem.value);
  console.log("确定");
};

// 方法
const selectItem = (id) => {
  console.log(id);
  cateList.value = props.stageData.find((item) => item.id == id) || {};
  console.log(cateList.value);
};
</script>

<style lang="scss" scoped>
.editem {
  box-sizing: border-box;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .add {
    color: #3f79ff;
    font-size: 30rpx;
    font-weight: 500;
  }

  &-icon {
    display: flex;
    justify-content: space-between;
    width: 112rpx;

    image {
      width: 36rpx;
      height: 36rpx;
    }
  }
}
</style>
