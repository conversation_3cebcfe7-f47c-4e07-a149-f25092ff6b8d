<template>
  <view class="sort-layout">
    <view class="sort-list-title">
      <view>活动排序</view>
      <view @click="sendSort">完成</view>
    </view>
    <l-dragSort
      @change="change"
      :list="data.list"
      :lineHeight="100"
      :styleName="{ fontSize: '30rpx', fontWeight: '400' }"
    />
  </view>
</template>

<script setup>
import { activitySort } from "@/courseDetails/api/classDetalis.js";
import lDragSort from "@/courseDetails/components/l-dragSort/components/l-dragSort/l-dragSort.vue";
import { ref } from "vue";

const { data } = defineProps({
  data: {
    type: Object,
    default: () => {},
  },
});

let ids = ref([]);
function change(val) {
  let array = val;
  ids.value = array.map((item) => {
    return item.id;
  });
}

async function sendSort() {
  if (ids.length == 0) {
    return;
  }
  const { subjectId, subjectStageId } = data;
  let res = await activitySort({
    subjectId: Number(subjectId),
    subjectStageId,
    ids: ids.value,
  });
  ids.value = [];
  emit("updata");
}

const emit = defineEmits(["updata"]);
// 示例
// let list = ref([
//   {
//     name: '账户余额',
//     rightIcon: '/static/icon/dragsort.png'
//   },
//   {
//     name: '农业银行',
//     rightIcon: '/static/icon/dragsort.png'
//   },
//   {
//     name: '工商银行',
//     rightIcon: '/static/icon/dragsort.png'
//   },
//   {
//     name: '建设银行',
//     rightIcon: '/static/icon/dragsort.png'
//   },
// ])
</script>

<style lang="scss" scoped>
.sort-layout {
  // width: 100%;
  // height: 80%;
  box-sizing: border-box;

  .sort-list-title {
    display: flex;
    align-items: center;
    justify-content: space-between;

    view:first-child {
      font-size: 34rpx;
      font-weight: 600;
    }

    view:last-child {
      font-size: 30rpx;
      font-weight: 500;
      color: rgba(63, 121, 255, 1);
    }
  }
}
</style>
