<template>
  <view class="">
    <uni-data-select
      v-model="activeId"
      :localdata="formatData(stageData)"
      @change="selectItem"
      style="background: #f5f5f5; margin-bottom: 24rpx"
    />
    <ActivitySortList
      v-if="cateList.list.length > 0"
      :data="cateList"
      @updata="emit('updata')"
    />
    <text class="add" v-else>请先选择阶段</text>
  </view>
</template>

<script setup>
import { reactive, ref, nextTick } from "vue";
import utils from "../../utils";
import ActivitySortList from "./components/activitySortList.vue";
const { formatData } = utils();
let isShow = ref(true);
const emit = defineEmits(["updata"]);
let activeId = ref(""); // 当前选中的id
const cateList = reactive({
  list: [], // 排序列表
  subjectId: "", // subjectId
  subjectStageId: "", // id
});
const props = defineProps({
  stageData: {
    type: Object,
    default: () => [],
  },
  gradeId: {
    type: Number || String,
    default: "",
  },
});
// 方法
const selectItem = async (id) => {
  cateList.list.length = 0;
  await nextTick();
  const data = props.stageData.find((item) => item.id == id);
  cateList.list = data.actList;
  cateList.subjectId = data.subjectId;
  cateList.subjectStageId = data.id;
};
</script>

<style lang="scss" scoped>
.add {
  color: #3f79ff;
  font-size: 30rpx;
  font-weight: 500;
}
</style>
