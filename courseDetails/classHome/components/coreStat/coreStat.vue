<!-- 核心经验统计 -->
<template>
	<view style="height: 100%;">
		<view class="col" v-if="tableData.data || tableData.data.length > 0">
			<view class="header">
				<view class="flex-jsb-ac">
					<text class="f-s-30 f-w-500">核心经验统计</text>
					<view class="flex-ac">
						<image src="@/static/common/refresh.png" @click="getTableData(subjectId)" />
						<image v-if="sr == -1" src="@/static/icon/aOrder.png" @click="checkSort" />
						<image v-if="sr == 1" src="@/static/icon/dOrder.png" @click="checkSort" />	
						<image src="@/static/icon/edit.png" @click="editFormItem" />
					</view>
				</view>
				<view class="f-s-24 f-w-400 header-tips">统计数据有延迟，每天凌晨统计前一日数据<br />如需查看对应活动等信息，请点击设置按钮进行设置</view>
			</view>
			<view class="table">
				<th-table :column="tableData.columns" :listData="tableData.data" :loading="isShowTable" height="0.5" 
					emptyText="暂无数据" :sr="sr" st="count">
					<template #activities="{ item }"></template>
				</th-table>
			</view>
		</view>
		<Popup :show="isPopup" @click="isPopup = false" @close="closePopup">
			<row-select :ParentList="tableData.columns" :List="columns" label="title" name="key"
				@bylChange="bylChange"></row-select>
		</Popup>
	</view>
</template>

<script setup>
import { getSubStatistics } from '@/courseDetails/api/classDetalis.js'
import { onMounted, ref } from 'vue';
import RowSelect from '@/courseDetails/components/row-select.vue';
import tools from './tools.js';
import ThTable from "@/courseDetails/components/th-table/components/th-table/th-table.vue";
let subjectId = uni.getStorageSync('subjectId')
let isShowTable = ref(false) // 是否显示表格
let isPopup = ref(false) // 是否显示弹窗
let sr = ref(-1) // 排序  1降序 -1升序


const { tableData, columns } = tools()


const bylChange = (list) => {
	console.log("%c list: ", "color:#86a9d4;", list);
	const commonElements = columns.filter(element => list.includes(element.key));
	tableData.columns = commonElements
	isPopup.value = false

}

function closePopup() {
	isPopup.value = false
}
// 数组排序
const checkSort = () => {
	// console.log(type);
	// sr.value 1降序 -1升序
	console.log(sr.value);
	let name = 'count'
	if (sr.value == 1) {
		sr.value = -1
		tableData.data = tableData.data.sort((a, b) => a[name] - b[name])
		return
	}
	if (sr.value == -1) {
		sr.value = 1
		tableData.data = tableData.data.sort((a, b) => b[name] - a[name])
		return
	}
}

const editFormItem = (item, creator) => {
	isPopup.value = true

}

const getTableData = async (id) => {
	isShowTable.value = true
	tableData.data = []
	let res = await getSubStatistics(id)
	console.log("%c res: ", "color:#86a9d4;", res);
	if (res.status == 0) {
		tableData.data = res.data
	}
	isShowTable.value = false

}
onMounted(() => {
	getTableData(subjectId)
})
// getTableData(subjectId)

</script>
<script>
export default {
	options: { styleIsolation: 'shared', multipleSlots: true },
}
</script>

<style scoped lang="scss">
.col {
	border-radius: 28rpx;
	background: rgba(255, 255, 255, 1);
	box-shadow: 4rpx 8rpx 16rpx rgba(0, 0, 0, 0.02);
	box-sizing: border-box;
	padding: 32rpx;
	margin-bottom: 24rpx;
	height: auto;
}

.header {
	.header-tips {
		color: #808080;
		line-height: 33rpx;
		margin-top: 12rpx;
	}

	image {
		width: 36rpx;
		height: 36rpx;
		margin-left: 28rpx;
	}

	image:first-child {
		margin-left: 0;
	}
}

::v-deep .uni-table-td:first-child {
	padding-left: 0;
}

::v-deep .uni-table-td:last-child {
	padding-right: 0;
}

::v-deep .uni-table-td {
	font-size: 22rpx;
	padding: 16rpx 18rpx;
	margin: 0 18rpx !important;
}

::v-deep .uni-table-th {
	padding: 24rpx 18rpx;
}

::v-deep .uni-table-th:first-child {
	padding-left: 0;
}

::v-deep .uni-table-th:last-child {
	padding-right: 0;
}
</style>
