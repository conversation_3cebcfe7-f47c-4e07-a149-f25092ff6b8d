import { reactive } from 'vue';
export default () => {
    let tableData = reactive({
        data: [],
        columns: [
            {
                key: 'matrix1Title',
                title: '领域',
                width: 56,
                align: 'left',
            },
            {
                key: 'matrix2Title',
                title: '维度',
                width: 56,
                align: 'left',
            },
            {
                key: 'matrix3Title',
                title: '子维度',
                width: 56,
                align: 'left',
            },
            {
                key: 'count',
                title: '出现次数',
                width: 46,
                isSort: true,
                align: 'left',
                formatter: (row) => {
                    return row || 0
                }

            },
            // 默认不出现
            // {
            //     key: 'activities',
            //     title: '对应活动',
            //     width: 150,
            //     slot: 'activities',
            //     align: 'left',
            // }
        ]
    })
    const columns = [
        {
            key: 'matrix1Title',
            title: '领域',
            width: 56,
        },
        {
            key: 'matrix2Title',
            title: '维度',
            width: 56,
        },
        {
            key: 'matrix3Title',
            title: '子维度',
            width: 56,
        },
        {
            key: 'count',
            title: '出现次数',
            width: 46,
            formatter: (row) => {
                return row || 0
            }

        },
        {
            key: 'activities',
            title: '对应活动',
            width: 150,
            slot: 'activities',
            align: 'left',
        }
    ]

    return {
        tableData,
        columns
    }
}