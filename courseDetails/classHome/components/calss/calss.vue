<!-- 课程文本 -->
<template>
  <view class="calss_layout">
    <up-loading-icon
      style="margin-top: 100rpx"
      v-if="isShowLoading"
    />
    <template v-else>
      <!-- 缘起 -->
      <view class="col">
        <view class="title flex-jsb-ac">
          <text class="title_text">缘起</text>
          <image
            src="/static/common/editor.png"
            @click="gotoPage"
          />
        </view>
        <view class="col-content">
          <up-read-more
            ref="uReadMoreRef"
            :showHeight="200"
            :toggle="true"
            color="#3F79FF"
            closeText="展开"
            openText="收起"
            textIndent="0"
          >
            <rich-text
              style="white-space: pre-line"
              :nodes="content"
            ></rich-text>
          </up-read-more>
          <view v-if="!content">-</view>
        </view>
      </view>
      <!-- 教师先行 -->
      <view class="col">
        <view class="title flex-jsb-ac">
          <text class="title_text">教师先行</text>
        </view>
        <view
          style="padding-bottom: 32rpx"
          v-if="allData?.prospect && allData?.prospect != '[]'"
        >
          <text
            class="text-s28-w400"
            style="color: #808080"
            >教师的困惑</text
          >
          <up-read-more
            ref="uReadMoreRef"
            :showHeight="200"
            :toggle="true"
            color="#3F79FF"
            closeText="展开"
            openText="收起"
            textIndent="0"
          >
            <view
              class="jsxx"
              v-for="(item, index) in JSON.parse(allData.prospect)"
              :key="index"
            >
              <!-- 困惑 -->
              <view class="col-read-more-content">
                <view class="flex-fs">
                  <up-tag
                    :text="`困惑${index + 1}`"
                    type="success"
                    plain
                    plainFill
                    color="#54BA6A"
                    bgColor="#edf8f0"
                    borderColor="#edf8f0"
                  ></up-tag>
                  <view class="flex-f1 content-title text-s28-w500">{{
                    item.question || "-"
                  }}</view>
                </view>
              </view>
              <!-- 自学 -->
              <view class="col-read-more-content">
                <view class="flex-fs">
                  <up-tag
                    :text="`自学${index + 1}`"
                    type="success"
                    plain
                    plainFill
                    color="#F0914D"
                    bgColor="#FDF4ED"
                    borderColor="#FDF4ED"
                  ></up-tag>
                  <view
                    class="flex-f1 content-title text-s26-w500"
                    style="color: #808080"
                    >{{ item.answer || "-" }}
                  </view>
                </view>
              </view>
            </view>
          </up-read-more>
        </view>
        <view v-else>-</view>
      </view>
      <!-- 学情分析 -->
      <view class="col col-yyxq">
        <view class="title flex-jsb-ac">
          <text class="title_text">学情分析</text>
        </view>
        <view
          class="col-yyxq-centent text-s28-w400"
          style="white-space: pre-line"
          v-if="allData.studyAnalyze"
          >{{ strToArr(allData.studyAnalyze) }}</view
        >
        <view
          class="col-yyxq-centent text-s28-w400"
          style="white-space: pre-line"
          v-else
          >-</view
        >
      </view>
      <!-- 幼儿兴趣 -->
      <view class="col col-yyxq">
        <view class="title flex-jsb-ac">
          <text class="title_text">幼儿兴趣</text>
        </view>
        <view
          class="col-yyxq-centent text-s28-w400"
          v-if="allData.interest"
          >{{ allData.interest }}</view
        >
        <view
          class="col-yyxq-centent text-s28-w400"
          v-else
          >-</view
        >
      </view>
      <!-- 课程目标 -->
      <view class="col col-yyxq">
        <view class="title flex-jsb-ac">
          <text class="title_text">课程目标</text>
        </view>
        <view
          class="col-yyxq-centent text-s28-w400"
          style="white-space: pre-line"
          v-if="allData.objectives"
          >{{ strToArr(allData.objectives) }}</view
        >
        <view
          class="col-yyxq-centent text-s28-w400"
          style="white-space: pre-line"
          v-else
          >-</view
        >
      </view>
      <!-- 课程重点 -->
      <view class="col col-yyxq">
        <view class="title flex-jsb-ac">
          <text class="title_text">课程重点</text>
        </view>
        <view
          class="col-yyxq-centent text-s28-w400"
          style="white-space: pre-line"
          v-if="allData.keynote"
          >{{ strToArr(allData.keynote) }}</view
        >
        <view
          class="col-yyxq-centent text-s28-w400"
          style="white-space: pre-line"
          v-else
          >-</view
        >
      </view>
      <!-- 课程难点 -->
      <view class="col col-yyxq">
        <view class="title flex-jsb-ac">
          <text class="title_text">课程难点</text>
        </view>
        <view
          class="col-yyxq-centent text-s28-w400"
          style="white-space: pre-line"
          v-if="allData.difficulties"
          >{{ strToArr(allData.difficulties) }}</view
        >
        <view
          class="col-yyxq-centent text-s28-w400"
          style="white-space: pre-line"
          v-else
          >-</view
        >
      </view>
      <!-- 资源使用 -->
      <view class="col col-yyxq">
        <view class="title flex-jsb-ac">
          <text class="title_text">资源使用</text>
        </view>
        <view
          class="col-yyxq-centent text-s28-w400"
          style="white-space: pre-line"
          v-if="allData.resourceUtilization"
          >{{ strToArr(allData.resourceUtilization) }}</view
        >
        <view
          class="col-yyxq-centent text-s28-w400"
          style="white-space: pre-line"
          v-else
          >-</view
        >
      </view>
      <!-- 材料投放 -->
      <view class="col col-yyxq">
        <view class="title flex-jsb-ac">
          <text class="title_text">材料投放</text>
        </view>
        <view
          class="col-yyxq-centent text-s28-w400"
          style="white-space: pre-line"
          v-if="allData.materialPlacement"
          >{{ strToArr(allData.materialPlacement) }}</view
        >
        <view
          class="col-yyxq-centent text-s28-w400"
          style="white-space: pre-line"
          v-else
          >-</view
        >
      </view>
      <!-- 环创建议 -->
      <view class="col col-yyxq">
        <view class="title flex-jsb-ac">
          <text class="title_text">环创建议</text>
        </view>
        <view
          class="col-yyxq-centent text-s28-w400"
          style="white-space: pre-line"
          v-if="allData.environmentSuggest"
          >{{ strToArr(allData.environmentSuggest) }}</view
        >
        <view
          class="col-yyxq-centent text-s28-w400"
          style="white-space: pre-line"
          v-else
          >-</view
        >
      </view>
      <!-- 结题活动 -->
      <view class="col col-yyxq">
        <view class="title flex-jsb-ac">
          <text class="title_text">结题活动</text>
        </view>
        <view
          class="col-yyxq-centent text-s28-w400"
          style="white-space: pre-line"
          v-if="allData.completionActivity"
          >{{ strToArr(allData.completionActivity) }}</view
        >
        <view
          class="col-yyxq-centent text-s28-w400"
          style="white-space: pre-line"
          v-else
          >-</view
        >
      </view>
      <!-- 课程总结 -->
      <!-- <view class="col col-yyxq">
        <view class="title flex-jsb-ac">
          <text class="title_text">课程总结</text>
        </view>
        <view class="col-yyxq-centent text-s28-w400" style="white-space: pre-line">{{ allData?.summary || '-' }}</view>
      </view> -->
      <!-- 主题目标 -->
      <!-- <view class="col col-yyxq">
        <view class="title flex-jsb-ac">
          <text class="title_text">主题目标</text>
        </view>
        <view class="ztmb">
          <view class="content-hxjy-box flex-ac" v-for="(item, index) in allData.matrices" :key="item.id">
            <view class="icon">目标{{ index + 1 }}</view>
            <view class="flex-f1">
              <view class="content no-bb-content flex-ac">
                <up-tag style="margin-right: 40rpx" text="领域" plain plainFill size="mini" color="#608BF0"
                  bgColor="#eff3fd" borderColor="#eff3fd"></up-tag>
                <view class="flex-f1 no-bb-content text-s28-w400">{{
                  item.matrix1Title
                }}</view>
              </view>
              <view class="content no-bb-content flex-ac">
                <up-tag style="margin-right: 40rpx" text="维度" type="warning" plain plainFill size="mini" color="#F0914D"
                  bgColor="#fdf4ed" borderColor="#fdf4ed"></up-tag>
                <view class="flex-f1 no-bb-content text-s28-w400">{{
                  item.matrix2Title
                }}</view>
              </view>
              <view class="content no-bb-content flex-ac">
                <up-tag style="margin-right: 18rpx" text="子维度" type="success" plain plainFill size="mini"
                  color="#54BA6A" bgColor="#edf8f0" borderColor="#edf8f0"></up-tag>
                <view class="flex-f1 no-bb-content text-s28-w400">{{
                  item.matrix3Title
                }}</view>
              </view>
            </view>
          </view>
        </view>
      </view> -->
    </template>
  </view>
</template>

<script setup>
import { ref, onMounted, nextTick } from "vue";
import { getClassText } from "@/courseDetails/api/class.js";
import { getMatrixList } from "@/api";
import { onShow } from "@dcloudio/uni-app";
const content = ref(`-`);
let allData = ref("");
let uReadMoreRef = ref(null);
let isShowLoading = ref(false);
let _aiFlag = ref(false);
const gotoPage = () => {
  console.log(111);
  if (_aiFlag.value) {
    uni.$u.toast("请先生成所有教案，再编辑");
    return;
  }
  uni.navigateTo({
    url: "/courseDetails/editClassText/editClassText",
  });
};

// 把数组转化为字符串
const strToArr = (val) => {
  //判断是否可以被JSON.parse解析，如果可以则说明是数组，否则是字符串
  try {
    let arr = JSON.parse(val);
    if (Array.isArray(arr)) {
      return arr.join("\n"); // 将数组转化为字符串
    } else {
      return val;
    }
  } catch (e) {
    return val;
  }

  // 通过JSON.parse将字符串转化为数组，如果不是数组则返回原数据
};

const getDateils = async (id) => {
  isShowLoading.value = true;
  const res = await getClassText(id);
  if (res.status == 0) {
    const { origin, aiFlag, submitAiFlag } = res.data;
    content.value = origin;
    allData.value = res.data;
    _aiFlag.value = aiFlag == 1 && submitAiFlag == 0;
    await nextTick();
    if (uReadMoreRef.value) {
      uReadMoreRef.value.init();
    }
  }
  isShowLoading.value = false;
};

// #ifdef MP-WEIXIN
onMounted(() => {
  let id = uni.getStorageSync("subjectId");
  getDateils(id);
});
// #endif

onShow(() => {
  let id = uni.getStorageSync("subjectId");
  getDateils(id);
});
// 获取矩阵列表
const getMatrix = async (pid, data) => {
  let form = {
    pid,
    current: 1,
    pageSize: 1000,
  };
  const res = await getMatrixList(form);
  if (res.status == 0) return res.data;
};
</script>

<script>
export default {
  options: { styleIsolation: "shared" }, // 解除样式隔离
};
</script>

<style lang="scss" scoped>
.text-s28-w400 {
  font-size: 28rpx;
  font-weight: 400;
}

.text-s28-w500 {
  font-size: 28rpx;
  font-weight: 500;
}

.text-s26-w500 {
  font-size: 28rpx;
  font-weight: 500;
}

.calss_layout {
  height: 100%;
  overflow-y: auto;
  box-sizing: border-box;

  .ztmb {
    padding: 16rpx 0;

    .content-hxjy-box {
      margin-top: 20rpx;
      background: linear-gradient(
        90deg,
        rgba(228, 244, 255, 0.4) 0%,
        #ffffff 22%
      );
      border-radius: 23.78rpx 0rpx 0rpx 23.78rpx;
      padding: 26rpx 0 26rpx 24rpx;
      margin-bottom: 20rpx;

      .content {
        align-items: flex-start;
        padding-bottom: 20rpx;
      }

      .content:last-of-type {
        padding-bottom: 0;
      }

      .icon {
        text-align: center;
        font-size: 24rpx;
        font-weight: 500;
        color: rgba(51, 51, 51, 1);
        margin-right: 54rpx;
      }

      &:last-of-type {
        margin-bottom: 0;
      }
    }
  }

  .col-yyxq-centent {
    margin-top: 32rpx;
    border-radius: 12rpx;
    background: #f9f9f9;
    border: 1rpx solid #eeeeee;
    padding: 24rpx;
    line-height: 44rpx;
    color: rgba(51, 51, 51, 1);
    white-space: pre-line;
  }

  .jsxx {
    box-sizing: border-box;
    padding: 28rpx 0;
    border-bottom: 1px solid #eee;

    .col-read-more-content {
      margin-bottom: 16rpx;
      align-items: flex-start !important;

      .content-title {
        padding-left: 10rpx;
      }

      ::v-deep .u-tag {
        width: 80rpx;
        height: 24rpx;
        padding: 4rpx 8rpx;
        justify-content: center;
        margin-right: 10rpx;
      }
    }

    &:last-child {
      border-bottom: none;
    }
  }

  .col-read-more-bg {
    background-image: linear-gradient(
      -180deg,
      rgba(255, 255, 255, 0) 0%,
      rgb(255, 255, 255) 80%
    );
    padding-top: 100px;
    margin-top: -100px;
    color: #3f79ff !important;
  }

  .col-read-more-height {
    height: 100% !important;
  }

  .col-read-more {
    position: relative;
    z-index: 1;
    color: #808080;
  }

  ::v-deep .user-class {
    .u-text__value {
      color: #808080 !important;
    }
  }

  .u-read-more__content {
    height: auto !important; // 可能在异步得时候会有问题
  }

  uni-rich-text {
    font-size: 28rpx;
    font-weight: 400;
    color: rgba(51, 51, 51, 1);
  }

  ::v-deep .uicon-arrow-up {
    color: #808080 !important;
  }

  .col {
    border-radius: 28rpx;
    background: rgba(255, 255, 255, 1);
    box-shadow: 4rpx 8rpx 16rpx rgba(0, 0, 0, 0.02);
    box-sizing: border-box;
    padding: 32rpx;
    margin-bottom: 24rpx;
    height: auto;

    .col-content {
      padding-top: 24rpx;
      height: 100%;
    }

    .title {
      .title_text {
        font-size: 30rpx;
        font-weight: 500;
        color: rgba(51, 51, 51, 1);
      }

      image {
        width: 30rpx;
        height: 30rpx;
      }
    }
  }
}
</style>
