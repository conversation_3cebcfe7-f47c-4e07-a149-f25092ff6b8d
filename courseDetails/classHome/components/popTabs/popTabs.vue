<template>
    <view class="popContent">
        <view v-for="item in tabsList" :key="item.name" class="popContent-item"
            :class="{ 'active': mode === item.name }" @click="mode = item.name">{{ item.name
            }}</view>
    </view>
</template>

<script setup>
import { reactive } from "vue"
const mode = defineModel()
defineProps({
    tabsList: {
        type: Array,
        default: () => []
    }
})
</script>

<style lang="scss" scoped>
.popContent {
    display: flex;
    align-items: center;
    gap: 24rpx;
    margin-bottom: 58rpx;
}

.popContent-item {
    height: 54rpx;
    line-height: 56rpx;
    font-size: 26rpx;
    font-weight: 400;
    padding: 0 20rpx;
    border-radius: 12rpx;
    box-sizing: border-box;
}

.active {
    color: white;
    background-color: #3F79FF
}
</style>
