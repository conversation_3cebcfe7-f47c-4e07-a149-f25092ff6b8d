import { getActivityList, deleteActivity } from "@/courseDetails/api/subjeckActivity.js"
import { reactive, ref, nextTick } from "vue"
import { handleApiResponse } from "@/utils"

export default (subId, _gradeId, creator, _aiFlag) => {
	const subjectActivities = ref([])
	const subjectStage = ref([])
	let isPopup = ref(false) // 添加活动弹窗
	let isComponent = ref(false) // 显示谁的组件
	let activityItemId = ref('') // 单独活动id
	let activityItem = reactive({}) // 单独活动
	let isModal = ref(false) // 删除弹窗
	let deleteTitle = ref('') // 删除弹框标题
	let isShowLoading = ref(false) // 是否显示加载
	let sortList = reactive({
		list: [], // 排序列表
		subjectId: '', // subjectId
		subjectStageId: '', // id
	}) // 排序列表

	// 完成排序后发送请求
	function sort() {
		isPopup.value = false
		isComponent.value = ''
		let id = uni.getStorageSync('subjectId')
		setTimeout(() => { getList(id) }, 300)
	}

	// 打开表格
	function openTable(item) {
		item.isOpenTable = !item.isOpenTable

	}
	// 获取活动列表
	async function getList(id) {
		isShowLoading.value = true
		subjectStage.value = []
		subjectActivities.value = []
		const res = await getActivityList(id)
		handleApiResponse((data) => {
			data.forEach((item, i) => {
				if (item?.actList) item.actList.forEach((it, ii) => it.index = ii + 1)
				item.isOpenTable = i == 0 ? true : false
				subjectStage.value.push(item)
				subjectActivities.value.push(item.actList)
			})
			isShowLoading.value = false
		}, res)

	}
	function closePopup() {
		isPopup.value = false
		setTimeout(() => {
			isComponent.value = ''
		}, 390)
	}
	// 编辑表格一项
	async function editTableCol(item, index, allData) {
		const { name, id } = item
		let isShowLoading = await isShowLoadingFn()
		if (isShowLoading) {
			isPopup.value = true
			isComponent.value = '操作'
			activityItemId.value = id
			activityItem.data = item
			activityItem.index = index
			activityItem.allData = allData
			deleteTitle.value = `您确定删除“${name}”`
		}
	}


	// 删除活动
	async function deleteActivityItem() {
		const res = await deleteActivity(activityItemId.value)
		isModal.value = false
		isPopup.value = false
		const { index, allData } = activityItem
		allData.splice(index, 1)
	}
	// 新建表单一项
	async function addFormItem(val, creator) {
		let isShowLoading = await isShowLoadingFn()
		if (isShowLoading) uni.navigateTo({
			url: `/courseDetails/addActivities/addActivities?subjectId=${val.subjectId}&subjectStageId=${val.id}&gradeId=${_gradeId}&creator=${creator}`
		})

	}

	// 排序表单
	async function sortFormItem(el, info) {
		let isShowLoading = await isShowLoadingFn()
		if (isShowLoading) {
			isComponent.value = ''
			isPopup.value = true
			sortList.list = el
			sortList.subjectId = info.subjectId + ''
			sortList.subjectStageId = info.id
			isComponent.value = '排序'
		}

	}

	// 判断是否ai生产
	function isShowLoadingFn() {
		return new Promise((resolve, reject) => {
			if (_aiFlag.value) {
				uni.$u.toast("请先生成所有教案，再编辑");
				resolve(false)
			} else {
				resolve(true)
			}
		})
	}


	return {
		deleteTitle,
		isModal,
		addFormItem,
		isPopup,
		closePopup,
		openTable,
		subjectStage,
		subjectActivities,
		getList,
		editTableCol,
		deleteActivityItem,
		sortFormItem,
		isComponent,
		sortList,
		sort,
		activityItemId,
		activityItem,
		isShowLoading
	}
}