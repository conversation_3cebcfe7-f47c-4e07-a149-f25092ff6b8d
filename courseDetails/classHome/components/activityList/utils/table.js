import { reactive, ref } from "vue"
import { getDICT } from '@/utils/index.js';
import dayjs from "dayjs";
export default () => {
	function removeDuplicates(arr) {
		return Array.from(new Set(arr))
	}
	let zzxs = ref({})
	getDICT('SubjectActivityOrganizationFormEnumDesc').then(res => {
		zzxs.value = res
	})

	const state = reactive({
		columns: [
			{
				key: 'index',
				title: '',
				width: 18,
			},
			{
				key: 'name',
				title: '活动名称',
				width: 150,
			},
			{
				key: 'implementedAt',
				title: '日期',
				width: 64,
				formatter: (row) => {
					return row ? dayjs(row).format("MM-DD") : "-"
				}
			},
			{
				key: 'organizationForm',
				title: '形式',
				width: 58,
				formatter: (row) => {
					return zzxs.value[row] || '-'
				}

			},
			{
				key: 'matrices',
				title: '领域',
				width: 160,
				formatter: (row) => {
					if (row) {
						let str = row.map(item => item?.matrix1Title).join('、')
						return str || '-'
					}
					return '-'
				}
			},
			{
				key: 'action',
				title: '操作',
				slot: 'action',
				width: 60,
				isFixed: true, // 是否固定
			},
		],
	})

	return {
		state,
	}
}