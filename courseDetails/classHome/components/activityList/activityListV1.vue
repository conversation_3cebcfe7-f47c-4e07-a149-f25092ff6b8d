<!-- 课程大纲 -->
<template>
  <view
    class="activity-list"
    v-for="(item, index) in list"
    :key="index"
  >
    <!-- 阶段名 -->
    <view class="title">
      <view
        class="title-left"
        :class="{ omit: !item.isOpen }"
        @click="item.isOpen = !item.isOpen"
      >
        {{ item.title }}
      </view>
      <view
        class="title-right"
        :style="{ alignItems: !item.isOpen ? 'center' : 'flex-start' }"
      >
        <image
          v-if="!disabled"
          src="@/static/icon/sort.png"
          @click="headlestageAction('sort', item)"
        />
        <image
          v-if="!disabled"
          src="@/static/icon/add.png"
          @click="headlestageAction('add', item)"
        />
        <up-icon
          :name="item.isOpen ? 'arrow-up' : 'arrow-down'"
          size="36rpx"
          :style="{ marginLeft: disabled ? 'auto' : 'none' }"
          @click="item.isOpen = !item.isOpen"
        />
      </view>
    </view>
    <!-- 列表内容 -->
    <template v-if="item.isOpen">
      <view class="act-item-desc">
        带 <text class="blue">*</text> 活动为未完成教案或评价的活动
      </view>
      <view
        class="list"
        v-for="(itm, inx) in item.actList"
        :key="itm.id + inx"
      >
        <view class="list-top">
          <text class="list-top-left">{{ inx + 1 }}、{{ itm.name }}</text>
          <up-icon
            v-if="!disabled"
            name="more-dot-fill"
            size="36rpx"
            @tap="emit('edit', itm)"
          />
        </view>
        <view
          class="list-bottom"
          @tap.prevent="emit('onClick', itm)"
        >
          <view class="list-bottom-left">
            <view
              class="flex-ac"
              style="margin-bottom: 14rpx"
            >
              <!-- <text class="list-bottom-left-icon icon-1">形式</text> -->
              <text :class="`tag-${map[itm.organizationForm]}`">{{
                changeFormat(itm.organizationForm, "enum")
              }}</text
              >&nbsp;&nbsp;
              <text class="list-bottom-left-icon icon-2">领域</text>
              <text style="flex: 1">{{
                changeFormat(itm.matrices, "area")
              }}</text>
            </view>
            <view
              >日期：{{
                changeFormat(itm.implementedAt, "date") || "未选择"
              }}</view
            >
          </view>
          <view
            v-if="!disabled"
            class="list-bottom-right"
            @tap.stop="emit('gotoPage', itm)"
            >评价</view
          >
        </view>
      </view>
    </template>
  </view>
  <Popup
    :show="isPopShow"
    @close="isPopShow = false"
  >
    <activity-sort-list
      v-if="sortList"
      :data="sortList"
      @updata="sort"
    />
  </Popup>
</template>

<script setup>
import { ref } from "vue";
import activitySortList from "./components/activitySortList.vue";
import Popup from "@/components/Popup/Popup.vue";
const mapData1 =
  uni.getStorageSync("ENUM").SubjectActivityOrganizationFormEnumDesc;
const mapData2 = uni.getStorageSync("ENUM").ObservationRecordSceneEnumDesc;
const isPopShow = ref(false);
let sortList = ref(null);
const emit = defineEmits(["gotoPage", "edit", "onClick", "sort"]);
const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  gradeId: [String],
  creator: [String],
  isAICheck: [Boolean],
});

const map = {
  Collective: "blue",
  Family: "red",
  Group: "green",
  Talk: "orange",
  Team: "violet",
};

//
const sort = (data) => {
  isPopShow.value = false;
  emit("sort", data);
};

// 点击阶段操作
const headlestageAction = async (type, item) => {
  if (props.isAICheck) return uni.$u.toast("请先确认大纲");
  console.log(item);
  if (type == "add") {
    uni.navigateTo({
      url: `/courseDetails/addActivities/addActivities?subjectId=${item.subjectId}&subjectStageId=${item.id}&gradeId=${props.gradeId}&creator=${props.creator}`,
    });
  }
  if (type == "sort") {
    item.isOpen = true;
    isPopShow.value = true;
    sortList.value = null;
    sortList.value = {
      list: item.actList,
      subjectId: item.subjectId,
      subjectStageId: item.id,
    };
  }
};

// 转换格式
const changeFormat = (str, type) => {
  if (!str) return "未选择";
  switch (type) {
    case "date":
      return str.split(" ")[0];
    case "enum":
      return mapData1[str];
    case "area":
      if (Array.isArray(str))
        return str.map((item) => item.matrix1Title).join("、");
      return mapData2[str];
    default:
      return str;
  }
};
</script>

<style lang="scss" scoped>
@each $var in "blue", "orange", "red", "green", "violet" {
  .tag-#{$var} {
    display: inline-block;
    border-radius: 8upx;
    font-size: 20upx;
    font-weight: 500;
    line-height: 33upx;
    padding: 0 6rpx;
    box-sizing: border-box;

    @if $var == "blue" {
      color: rgba(82, 131, 247, 1);
      background-color: rgba(82, 131, 247, 0.12);
    }

    @if $var == "orange" {
      color: rgba(240, 145, 77, 1);
      background-color: rgba(240, 145, 77, 0.12);
    }

    @if $var == "red" {
      color: rgba(237, 111, 114, 1);
      background-color: rgba(237, 111, 114, 0.12);
    }

    @if $var == "green" {
      color: rgba(84, 186, 106, 1);
      background-color: rgba(84, 186, 106, 0.12);
    }

    @if $var == "violet" {
      color: rgba(110, 116, 230, 1);
      background-color: rgba(110, 116, 230, 0.12);
    }
  }
}

.flex-ac {
  display: flex;
  align-items: center;
}
.blue {
  color: rgba(82, 131, 247, 1);
}

.list {
  color: #333333;
  font-weight: 400;
  min-height: 176rpx;
  
  &-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 14rpx;
    image {
      width: 36rpx;
      height: 36rpx;
    }
  }

  &-bottom {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    padding-left: 28rpx;

    &-left {
      font-size: 24rpx;
      color: #808080;
      flex: 1;

      &-icon {
        display: inline-block;
        box-sizing: border-box;
        width: 52rpx;
        height: 28rpx;
        line-height: 28rpx;
        text-align: center;
        font-size: 20rpx;
        font-weight: 500;
        border-radius: 5.84rpx;
        padding: 0 6rpx;
        margin-right: 8rpx;
      }

      .icon-1 {
        background: rgba(84, 186, 106, 0.12);
        color: #54ba6a;
      }

      .icon-2 {
        background: rgba(240, 145, 77, 0.12);
        color: rgba(240, 145, 77, 1);
      }
    }

    &-right {
      font-size: 24rpx;
      font-weight: 600;
      width: 88rpx;
      height: 48rpx;
      line-height: 48rpx;
      text-align: center;
      color: #3f79ff;
      background: #eef2f9;
      border-radius: 52.8rpx;
    }
  }
}
.act-item-desc {
  font-size: 22rpx;
  font-weight: 500;
  color: rgba(128, 128, 128, 1);
  margin-bottom: 20rpx;
  margin-top: -16rpx;
  position: relative;;
  // border-bottom: 1px solid #e5e5e5;
  // padding-bottom: 32rpx;
}

.title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  padding: 32rpx 0rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &-left {
    width: 440rpx;
    margin-right: 20rpx;
  }
  //
  .omit {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  &-right {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex: 1;
    margin-bottom: auto;
    image {
      width: 36rpx;
      height: 36rpx;
    }
  }
}
</style>
