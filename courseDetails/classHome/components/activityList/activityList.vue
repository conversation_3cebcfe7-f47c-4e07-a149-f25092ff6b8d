<!-- 活动列表 -->
<template>
  <view class="al-layout">
    <up-loading-icon
      style="margin-top: 100rpx"
      v-if="isShowLoading"
    />
    <view v-if="subjectStage.length != 0">
      <view
        class="activityList"
        v-for="(item, subIndex) in subjectStage"
        :key="item.id"
      >
        <view class="title">
          <view
            class="t-text"
            @click="openTable(item)"
            >{{ item.title }}</view
          >
          <view class="t-iconList">
            <image
              src="@/static/icon/sort.png"
              @click="sortFormItem(subjectActivities[subIndex], item)"
            />
            <image
              src="@/static/icon/add.png"
              @click="addFormItem(item, creator)"
            />
            <up-icon
              class="t-icon"
              :name="item.isOpenTable ? 'arrow-up' : 'arrow-down'"
              size="36rpx"
              @click="openTable(item)"
            />
          </view>
        </view>
        <view
          class="activity-table"
          :class="{ 'activity-table-open': item.isOpenTable }"
        >
          <th-table
            v-if="
              subjectActivities[subIndex] &&
              subjectActivities[subIndex].length != 0
            "
            :column="state.columns"
            :listData="subjectActivities[subIndex]"
            @tdClick="tapRow"
            @actionTap="editTableCol"
            height="0.5"
            state
          >
            <template #action>
              <!-- <view @click="editTableCol(item, index, subjectActivities[subIndex])"></view> -->
            </template>
          </th-table>

          <up-empty
            v-else
            style="margin-top: 100rpx"
            mode="data"
          />
        </view>
      </view>
    </view>
    <view
      v-if="subjectStage.length == 0 && !isShowLoading"
      style="
        background: #f5f5f5;
        color: #3f79ff;
        font-size: 28rpx;
        padding: 32rpx;
      "
      >请先添加阶段!!!</view
    >

    <Popup
      :show="isPopup"
      @click="isPopup = false"
      @close="closePopup"
    >
      <view
        class="iconAction"
        v-if="isComponent == '操作'"
      >
        <view
          class="editorIcon"
          @click="gotoPage('AI')"
        >
          <image src="@/static/common/courseDetails_AICreate.png"></image>
          <view>AI生成教案</view>
        </view>
        <view
          class="editorIcon"
          @click="gotoPage('evaluate')"
        >
          <image src="@/static/common/comment.png"></image>
          <view>评价</view>
        </view>
        <view
          class="deleteIcon"
          @click="gotoPage('editActivity')"
        >
          <image src="@/static/common/editor.png"></image>
          <view>编辑</view>
        </view>
        <view
          class="deleteIcon"
          @click="gotoPage('export')"
        >
          <icon-button
            :size="20"
            type="text"
            icon="icon-export"
            text=""
            style="margin-right: 19rpx"
          />
          <view>导出word</view>
        </view>
        <view
          class="deleteIcon"
          @click="isModal = true"
        >
          <image src="@/static/common/delete.png"></image>
          <view>删除</view>
        </view>
      </view>
      <!-- 切换对应组件 -->
      <!-- <activity-add-form v-if="isComponent == '新增'" :creator="creator" :gradeId="_gradeId"
				@confirm="activityConfirm" /> -->
      <activity-sort-list
        v-if="isComponent == '排序'"
        :data="sortList"
        @updata="sort"
      />
    </Popup>
    <!-- 弹出确认框 -->
    <up-modal
      :show="isModal"
      @confirm="deleteActivityItem"
      @cancel="isModal = false"
      :asyncClose="true"
      :content="deleteTitle"
      contentTextAlign="center"
      showCancelButton
    />
    <!-- ai弹出确认框 -->
    <up-modal
      :show="isAiModal"
      @confirm="aiConfirm"
      @cancel="isAiModal = false"
      :asyncClose="true"
      :content="aiContent"
      contentTextAlign="center"
      showCancelButton
    />

    <view
      class="addClass"
      v-if="_aiFlag"
    >
      <view class="addClass-btn">
        <up-button
          plain
          color="#FF9A3B"
          shape="circle"
          text="再次生成课程"
          @click="addAllFormdata(1)"
        ></up-button>
      </view>

      <view class="addClass-btn">
        <up-button
          color="#FF9A3B"
          shape="circle"
          text="生成所有教案"
          @click="addAllFormdata(0)"
        ></up-button>
      </view>
    </view>
    <!-- 模板导出 -->
    <PopupExport
      :show="isShowTemplate"
      :list="templateList"
      keyName="name"
      @change="onConfirm"
      @close="isShowTemplate = false"
    />
  </view>
</template>

<script setup>
import { ref, onMounted, computed, nextTick } from "vue";
import tools from "./utils/index.js";
import table from "./utils/table.js";
import { onLoad, onShow } from "@dcloudio/uni-app";
import activitySortList from "./components/activitySortList.vue";
import Popup from "@/components/Popup/Popup.vue";
import IconButton from "@/components/icon-button/icon-button.vue";
import ThTable from "@/courseDetails/components/th-table/components/th-table/th-table.vue";
import {
  aiGenerate,
  aiOnceMoreGenerate,
  exportWord,
  getTemplateList,
} from "@/courseDetails/api/subjeckActivity.js";
import { getClassText } from "@/courseDetails/api/class.js";
import PopupExport from "./components/popup-export.vue";

const { subjectId, creator, schoolId } = defineProps({
  subjectId: String,
  creator: {
    type: String,
    default: "",
  }, // 作者名
  schoolId: String || Number,
});
let _gradeId = ref("");
let _creator = computed(() => creator);
let _aiFlag = ref(false);
let isAiModal = ref(false);
let aiContent = ref("如果已有教案，将会被覆盖，确定要生成所有教案吗？");
let aiType = ref(-1); // 0 生成所有教案 1 重新生成课程框架
let wordId = ref(0); // 模板id 根据学校查出来的
let isShowTemplate = ref(false); // 模板选择
let templateList = ref([]);
onLoad((options) => {
  _gradeId.value = options.gradeId;
});

// 多模板选择
const onConfirm = async (e) => {
  const { id } = e;
  await useExportWord({
    id,
    subjectActivityIds: [activityItem?.data?.id],
  });
  isPopup.value = false;
  isShowTemplate.value = false;
};

const addAllFormdata = async (type = "") => {
  isAiModal.value = true;
  aiType.value = type;
  aiContent.value =
    aiType.value == 0 ? "确定要生成所有教案吗？" : "确定要重新生成课程框架吗？";
};
// 查询是否确认ai生成
const getDateils = async () => {
  const res = await getClassText(uni.getStorageSync("subjectId"));
  if (res.status == 0) {
    const { aiFlag, submitAiFlag } = res.data;
    _aiFlag.value = aiFlag == 1 && submitAiFlag == 0;
  }
};
// ai弹框确认事件
const aiConfirm = async () => {
  let id = uni.getStorageSync("subjectId");
  let res;
  try {
    res = await (aiType.value == 0
      ? aiGenerate(id)
      : aiOnceMoreGenerate({ subjectId: id }));
    if (res.status === 0) {
      isAiModal.value = false;
      if (aiType.value == 0) {
        _aiFlag.value = 0;
        getList(uni.getStorageSync("subjectId"));
        uni.$u.toast("确认成功");
      } else {
        let sid = JSON.parse(res.data).categoryId;
        uni.navigateTo({
          url: `/courseDetails/aiWriteTemplatel/aiWriteTemplatel?subjectId=${id}&id=${sid}`,
        });
      }
    } else {
      throw new Error(res.message || "接口错误，请联系管理员！");
    }
  } catch (error) {
    console.log(error);

    uni.showToast({
      title: error.message,
      icon: "error",
    });
  }
};

const {
  deleteTitle,
  isModal,
  isComponent,
  addFormItem,
  sortFormItem,
  getList,
  openTable,
  subjectStage,
  subjectActivities,
  closePopup,
  activityConfirm,
  editTableCol,
  deleteActivityItem,
  isPopup,
  sortList,
  sort,
  activityItemId,
  activityItem,
  isShowLoading,
} = tools(subjectId, _gradeId.value, _creator.value, _aiFlag);
const { state } = table();

// 刷新页面
const refreshPage = (str) => {
  getList(uni.getStorageSync("subjectId") || subjectId);
  uni.removeStorageSync(str);
};

onShow(() => {
  if (uni.getStorageSync("addForm")) refreshPage(`addForm`);
  // 判断是否是从评价返回来的
  if (uni.getStorageSync("evaluate")) refreshPage(`evaluate`);
  // 判断是否是从编辑活动返回来的
  if (uni.getStorageSync("editActivity")) refreshPage(`editActivity`);
});

function tapRow(row) {
  if (_aiFlag.value) {
    uni.$u.toast("请先生成所有教案，再编辑");
    return;
  }
  const result = subjectStage.value.find(
    (item) => item.id == row.subjectStageId
  );
  const themeTitle = uni.getStorageSync("themeName");
  console.log(result.title);
  uni.navigateTo({
    url: `/courseDetails/activityDetails/activityDetails?id=${row.id}&theme=${themeTitle}&stage=${result.title}&gradeId=${_gradeId.value}`,
  });
}

async function gotoPage(path) {
  const { data } = activityItem;
  const { subjectStageId, subjectId, author, id } = data;
  console.log(data);

  let url;
  switch (path) {
    case "AI":
      url = `/courseDetails/aiCreateDetails/aiCreateDetails?&id=${activityItemId.value}&subjectId=${subjectId}`;
      break;
    case "editActivity":
      url = `/courseDetails/${path}/${path}?subjectStageId=${subjectStageId}&subjectId=${subjectId}&gradeId=${_gradeId.value}&id=${activityItemId.value}&author=${author}`;
      break;
    case "evaluate":
      url = `/courseDetails/${path}/${path}?id=${activityItemId.value}`;
      break;
    case "export":
      isPopup.value = false;
      if (!wordId.value && templateList.value.length == 0) {
        uni.$u.toast("暂无教案word模版，请联系系统客服添加！");
        isPopup.value = false;
        return;
      }
      if (templateList.value.length > 1) {
        isShowTemplate.value = true;
        return;
      }
      useExportWord({
        id: wordId.value,
        subjectActivityIds: [Number(id)],
      });

      return; // 直接返回，不需要设置url
  }

  if (url) {
    uni.navigateTo({ url });
  }

  isPopup.value = false;
}

// 导出文档
const useExportWord = async (obj) => {
  // loading
  uni.showLoading({
    title: "正在导出...",
    mask: true,
  });
  try {
    const res = await exportWord(obj);
    if (res.status === 0) {
      const { uri, filename } = res.data;
      const url = uri;
      // const filename = decodeURIComponent(url.split("/").pop().split("?")[0]);
      // let filenames = filename.split("-")[0];
      console.log(filename);

      // 在小程序中下载文件
      uni.downloadFile({
        url: url,
        success: (dlRes) => {
          if (dlRes.statusCode === 200) {
            // 重命名临时文件
            const fs = wx.getFileSystemManager(); // 获取文件系统管理器
            const newPath = `${wx.env.USER_DATA_PATH}/${filename}`;
            // 打开文档
            fs.copyFileSync(dlRes.tempFilePath, newPath); // 重命名文件
            // 兼容电脑端下载
            const userAgent = uni.getSystemInfoSync().platform;
            console.log(userAgent, "检测设备");

            if (userAgent === "windows" || userAgent === "mac") {
              wx.saveFileToDisk({
                filePath: dlRes.tempFilePath,
                success: (saveRes) => {
                  uni.hideLoading();
                  console.log("保存文件成功", saveRes);
                },
                fail: (error) => {
                  uni.hideLoading();
                  console.error("保存文件失败:", error);
                  uni.$u.toast("保存文件失败");
                },
              });
              return;
            }

            uni.openDocument({
              filePath: newPath,
              showMenu: true,
              success: () => {
                uni.hideLoading();
                console.log("打开文档成功");
              },
              fail: (error) => {
                uni.hideLoading();
                console.error("打开文档失败:", error);
                uni.$u.toast("打开文档失败");
              },
            }); // 打开文档
          } else {
            uni.$u.toast("下载失败");
          }
        },
        fail: (error) => {
          uni.hideLoading();
          console.error("下载失败:", error);
          uni.$u.toast("下载失败");
        },
      });
    }
  } catch (error) {
    uni.hideLoading();
  }
};

// 根据学校请求id
const getSchoolTid = async () => {
  const from = {
    documentTemplateCategory: "SubjectActivity",
    subjectType: "School",
    subjectId: schoolId,
  };
  // 目前只有一个模板，有多个模板可能需要新加内容
  let res = await getTemplateList(from);

  if (res.status == 0) {
    switch (res.data.length) {
      case 0:
        wordId.value = false;
        break;
      case 1:
        wordId.value = res.data[0]?.id;
        break;
      default:
        await nextTick();
        templateList.value = res.data;
        break;
    }
  } else {
    wordId.value = false; // 没有模板
    templateList.value = [];
  }
};

onMounted(() => {
  getDateils();
  getList(uni.getStorageSync("subjectId") || subjectId); // 重复切换id为空
  getSchoolTid();
});
</script>

<script>
export default {
  options: { styleIsolation: "shared", multipleSlots: true },
};
</script>

<style lang="scss" scoped>
.al-layout {
  padding-bottom: calc(100rpx + env(safe-area-inset-bottom));
  .addClass {
    width: 100%;
    height: 104rpx;
    position: fixed;
    padding-bottom: env(safe-area-inset-bottom);
    z-index: 99;
    right: 0rpx;
    bottom: 0rpx;
    background-color: #fff;
    display: flex;
    justify-content: space-evenly;
    align-items: center;

    &-btn {
      width: 332rpx;
    }
  }

  .iconAction {
    & > view {
      height: 88rpx;
      display: flex;
      align-items: center;
      font-size: 30rpx;
      font-weight: 400;
      letter-spacing: 0rpx;
      line-height: 48rpx;
      color: rgba(51, 51, 51, 1);
      text-align: left;
      vertical-align: middle;

      image {
        width: 40rpx;
        height: 40rpx;
        margin-right: 28rpx;
      }
    }
  }
}

.activityList {
  box-sizing: border-box;
  padding: 32rpx;
  background: white;
  border-radius: 32rpx;
  margin: 24rpx 0;

  .activity-table {
    height: 0;
    overflow: hidden;
  }

  .activity-table-open {
    height: 100%;
    margin-top: 14rpx;
  }

  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .t-text {
      font-size: 30rpx;
      font-weight: 500;
      color: rgba(51, 51, 51, 1);
      width: 440rpx;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .t-iconList {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;

      image {
        width: 36rpx;
        height: 36rpx;
      }
    }
  }
}
</style>
