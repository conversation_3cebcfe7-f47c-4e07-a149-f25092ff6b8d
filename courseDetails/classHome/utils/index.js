import { getClassDetail } from "@/courseDetails/api/classDetalis.js";
import { ref } from "vue";
export default () => {
  const classDe = ref({});
  let categoryTitle = ref("");
  const term = {
    11: "小小班上",
    12: "小小班下",
    13: "小班上",
    14: "小班下",
    15: "中班上",
    16: "中班下",
    17: "大班上",
    18: "大班下",
    19: "大大班上",
    20: "大大班下",
  };
  const categoryList = uni.getStorageSync("CATEGORY_LIST") || [];
  const categorystyle = ref({});
  const categoryStyleMap = [
    {
      value: "主题课程",
      itemStyle: {
        background: "#FDF2EA",
        color: "#F0A14D",
      },
    },
    {
      value: "项目课程",
      itemStyle: {
        background: "#EDF2FE",
        color: "#5283F7",
      },
    },
    {
      value: "领域/特色课程",
      itemStyle: {
        background: "#FCEDEE",
        color: "#ED6F72",
      },
    },
    {
      value: "生活课程",
      itemStyle: {
        background: "#F0F1FC",
        color: "#6E74E6",
      },
    },
    {
      value: "STEM课程",
      itemStyle: {
        background: "#EDF8F0",
        color: "#54BA6A",
      },
    },
    {
      value: "体育课程",
      itemStyle: {
        background: "#F0F1F6",
        color: "#7278A6",
      },
    },
    {
      value: "学习活动",
      itemStyle: {
        background: "#FDF2EA",
        color: "#F0A14D",
      },
    },
  ];
  const getDetail = async (id) => {
    let res = await getClassDetail(id);
    classDe.value = res.data;
    uni.setStorageSync("themeName", classDe.value.title);
    classDe.value.term = term[classDe.value.term];
    categoryTitle.value = categoryList.find(
      (item) => item.id == classDe.value.categoryId
    )?.value;
    categoryStyleMap.forEach((item) => {
      if (item.value == categoryTitle.value) {
        categorystyle.value = item.itemStyle;
      }
    });
  };

  // 转换格式
  const formatData = (data) => {
    return data.map((item) => {
      return {
        text: item.title,
        value: item.id,
      };
    });
  };

  return {
    classDe,
    categoryTitle,
    categorystyle,
    getDetail,
    formatData
  };
};
