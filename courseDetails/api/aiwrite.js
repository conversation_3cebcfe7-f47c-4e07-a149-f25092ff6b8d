import { request } from "@/common/request.js"

// AI帮写主题课程
export function setAIthemeForm(data) {
	return request({
		url: `/jsapi/business/subject/submitAiTopicSubject`,
		method: 'POST',
        data
	})
}

// AI帮写项目课程
export function setAiProjectForm(data) {
	return request({
		url: `/jsapi/business/subject/submitAiProjectSubject`,
		method: 'POST',
        data
	})
}

// AI帮写领域课程
export function setAiDomainForm(data) {
	return request({
		url: `/jsapi/business/subject/submitAiDomainSubject`,
		method: 'POST',
        data
	})
}
// AI帮写生活课程
export function setAiLifeForm(data) {
	return request({
		url: `/jsapi/business/subject/submitAiLifeSubject`,
		method: 'POST',
        data
	})
}
// AI帮写STEM课程
export function setAiStemForm(data) {
	return request({
		url: `/jsapi/business/subject/submitAiStemSubject`,
		method: 'POST',
        data
	})
}
// AI帮写体育课程
export function setAiGymForm(data) {
	return request({
		url: `/jsapi/business/subject/submitAiGymSubject`,
		method: 'POST',
        data
	})
}
// 获取主题课程
export function getThemeList(data) {
	return request({
		url: `/business/dictionary/list?category=${data}`,
		method: 'GET',
	})
}

// 预提交AI帮写活动// 获取上一个是否评价填写
export function getAiPrepare(data) {
	return request({
		url: `/jsapi/business/subject/prepareAiTeachingPlan`,
		method: 'POST',
		data
	})
}

// AI帮写活动
export function getAiSubmit(data) {
	return request({
		url: `/jsapi/business/subject/submitAiTeachingPlan`,
		method: 'POST',
		data
	})
}


