import { request } from "@/common/request.js"

// 查询课程文本
export function getClassText(id) {
	return request({
		url: `/jsapi/business/subject/detail/${id}`,
		method: 'POST'
	})
}

//Business - 矩阵管理  矩阵 - 查询列表
export function getMatrixList(data) {
	return request({
		url: `/business/matrix/list`,
		method: 'GET',
		data
	})
}

// /pts/business/subject/u
// 	课程文本 - 更新信息
// export function updataMatrix(data) {
// 	return request({
// 		url: `/business/subject/u`,
// 		method: 'PUT',
// 		data
// 	})
// }

// 	课程文本 - 更新信息
export function updataMatrix(data) {
	return request({
		url: `/jsapi/business/subject/updateSubject`,
		method: 'POST',
		data
	})
}
