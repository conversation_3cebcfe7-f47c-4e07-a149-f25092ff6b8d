import { request } from "@/common/request.js"
const basicUrl = '/business'
// 获取阶段列表
export function getStageList(data) {
    return request({
        url: `${basicUrl}/subject-stage/list?subjectId=${data}`,
        method: 'GET'
    })
}

// 编辑活动信息
export function editActivity(data) {
    return request({
        url: `${basicUrl}/subject-activity/u`,
        method: 'PUT',
        data
    })
}

// 获取编辑活动详情
export function getActivityDetail(data) {
    return request({
        url: `${basicUrl}/subject-activity/detail/${data}`,
        method: 'GET'
    })
}

// 获取活动 - 上下文活动
export function getActivityDetailContent(data) {
    return request({
        url: `${basicUrl}/subject-activity/context?id=${data.id}&subjectId=${data.subjectId}`,
        method: 'GET'
    })
}
/***
 * @param {number} relId
 * @param {string} matchType observation 观察记录 subject 教案 onetoone 一对一 
 * **/
export function getCoreExp(data) {
    return request({
        url: `/jsapi/business/coreExp/getCoreExpById`,
        method: "GET",
        data,
    });
}
export function updateCoreExpList(data) {
    return request({
        url: `/jsapi/business/coreExp/updateCoreExpBatch/${data.matchType}/${data.relId}`,
        method: "POST",
        data: data.list,
    })
}

// 字典Java
export function getJavaDictList(data) {
    return request({
        url: `/jsapi/global/sysDictItem/page`,
        method: "POST",
        data
    })
}
