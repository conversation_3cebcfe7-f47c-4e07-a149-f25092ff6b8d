import { request } from "@/common/request.js"

// 获取评价详情
export function getevaDetails(id) {
	return request({
		url: `/business/subject-activity/detail/${id}`,
		method: 'GET'
	})
}

// 提交评价详情
export function setevaDetails(data) {
	return request({
		url: `/business/subject-activity/u-additional-questions`,
		method: 'PUT',
        data
	})
}

// 获取儿童列表
export function getChildrenList(data) {
    return request({
        url: `/business/child/list`,
        method: 'GET',
        data
    })
}

// 获取老师列表
export function getTeacherList(data) {
	return request({
        url: `/jsapi/business/class/moments/teacher/listUser`,
        method: 'GET',
        data
    })
}

// ai润色
export function getAiPolishCont(data) {
	return request({
        url: `/jsapi/business/subject/polishInteractionText`,
        method: 'POST',
        data
    })
}