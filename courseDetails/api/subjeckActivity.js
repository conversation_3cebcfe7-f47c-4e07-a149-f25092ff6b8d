// 活动管理相关接口
import { request } from "@/common/request.js"

export function getActivityList(id) {
	return request({
		// url: `/business/subject-activity/list?subjectId=${id}`,
		url: `/jsapi/business/subject/stagePageList?subjectId=${id}`,
		method: "GET"
	})
}
// 新建活动
export function addActivity(data) {
	return request({
		url: `/business/subject-activity`,
		method: "POST",
		data
	})
}

// 删除
export function deleteActivity(id) {
	return request({
		url: `/jsapi/business/subject/actDelete/${id}`,
		method: "POST"
	})
}

// ai生成全部活动
export function aiGenerate(data) {
	return request({
		url: `/jsapi/business/subject/continueAiSubjectStage?subjectId=${data}`,
		method: "POST",
	})
}
// ai再次生成全部活动
export function aiOnceMoreGenerate(data) {
	return request({
		url: `/jsapi/business/subject/prepareResetAiSubject`,
		method: "GET",
		data
	})
}

// 导出word
export function exportWord(data) {
	return request({
		url: `/business/document-template/export-document`,
		method: "POST",
		data
	})
}

// 请求模板
export function getTemplateList(data) {
	return request({
		url: `/business/document-template/list`,
		method: "GET",
		data
	})
}

// 修改状态为已发布
export function updataActState(data) {
	return request({
		url: `/business/subject/s`,
		method: "PUT",
		data
	})
}

// 修主题书任务状态
export function getTaskList(data) {
	return request({
		url: `/business/task/list`,
		method: "GET",
		data
	})
}