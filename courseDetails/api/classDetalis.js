import { request } from "@/common/request.js"

// 获取阶段列表
export function stageList(data) {
	return request({
		url: '/business/subject-stage/list',
		method: 'get',
		data
	})
}

// 阶段 - 更新名称
export function UstageName(data) {
	return request({
		url: '/business/subject-stage/u',
		method: 'put',
		data
	})
}

// 阶段 - 新增阶段名称
export function AstageName(data) {
	return request({
		url: '/business/subject-stage',
		method: 'post',
		data
	})
}

// 阶段 - 删除阶段名称
export function DstageName(date) {
	return request({
		url: `/jsapi/business/subject/stageDelete/${date.id}?forceFlag=${date.forceFlag}`,
		method: 'POST'
	})
}

// 课程详情
export function getClassDetail(id) {
	return request({
		url: `/business/subject/detail/${id}`,
		method: 'GET',
	})
}

// 排序活动
export function activitySort(data) {
	return request({
		url: `/business/subject-activity/u-sort`,
		method: 'PUT',
		data
	})
}

// 排序阶段列表
export function stageListSort(data) {
	return request({
		url: `/business/subject-stage/u-sort`,
		method: 'PUT',
		data
	})
}

// 核心经验统计 - /pts/business/subject/statistics/{id}
export function getSubStatistics(id) {
    return request({
        url: `/business/subject/statistics/${id}`,
        method: 'GET'
    })
}
