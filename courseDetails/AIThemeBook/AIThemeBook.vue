<template>
  <view>
    <BaseLayout
      nav-title="AI主题书"
      :content-style="{ padding: '0' }"
      :footerStyle="{
        background: '#fff',
        paddingBottom: `calc(20rpx + env(safe-area-inset-bottom))`,
      }"
      :enableLoadMore="true"
      :refresherEnabled="true"
      :refresherTriggered="refreshing"
      @scrolltolower="scrolltolower"
      @refresherrefresh="handleRefresherRefresh"
    >
      <view class="container">
        <!-- 历史报告列表  -->
        <view class="report-list">
          <!-- 空状态占位符 -->
          <view
            v-if="reportList.length === 0"
            class="empty-state"
          >
            <view class="empty-icon">📄</view>
            <view class="empty-text">暂无AI主题书</view>
          </view>

          <view
            v-for="(item, index) in reportList"
            :key="index"
            class="report-item"
            :class="{
              'report-item-clickable': item.state === 0,
              'report-item-error': item.state === -1,
            }"
            @click="onDownloadFileDirectly(deconstruction(item?.result))"
          >
            <view class="report-item-header">
              <view class="title">
                {{
                  deconstruction(item?.result).filename || "AI主题书生成中..."
                }}</view
              >
              <view class="time">{{
                deconstruction(item?.result).createdAt
              }}</view>
              <view class="time">{{
                formatFileSize(deconstruction(item?.result).size)
              }}</view>
              <view class="status-wrapper">
                <view
                  class="status"
                  :class="{
                    'status-pending': item.state === 2 || item.state === 1,
                    'status-success': item.state === 0,
                  }"
                >
                  {{ getStatusText(item.state) }}
                </view>
              </view>
            </view>
            <view
              class="item-header-center"
              @click.capture.stop="onOpenPop(item)"
            >
              <!-- <up-icon
              class="card-icon"
              size="42rpx"
              name="more-dot-fill"
            /> -->
            </view>
          </view>
        </view>
      </view>
      <template #footer>
        <!-- 生成报告按钮 -->
        <view>
          <button
            class="add-btn"
            @tap="healedExportWord"
          >
            生成最新主题书
          </button>
        </view>
      </template>
    </BaseLayout>
    <!-- popup弹出有问题需要放在baselayout外面 -->
    <export-word
      ref="exportWordRef"
      type="AI主题书"
      documentTemplateCategory="SubjectAIStory"
      :subjectId="schoolId"
      :isGenerateAsync="true"
    />
  </view>
</template>

<script setup>
import { onShow, onLoad } from "@dcloudio/uni-app";
import { onMounted, onUnmounted, reactive, ref } from "vue";
import BaseLayout from "@/components/base-layout/base-layout.vue";
import ExportWord from "@/components/export-word/export-word.vue";

import { getTaskList } from "@/courseDetails/api/subjeckActivity.js";
// import dayjs from "dayjs";

let reportList = ref([]);
let schoolId = uni.getStorageSync("USER_INFO").currentSchoolId;
let id = ref(0);
let exportWordRef = ref(null);
let timer = ref(null);
let refreshing = ref(false); // 是否正在刷新
let isScrolltolower = ref(true); // 是否触底加载
let initPage = reactive({
  current: 1,
  pageSize: 10,
  // 101 - 儿童 - 生成 - AI报告
  // 102 - 儿童 - 生成 - 合并AI报告结果
  // 120 - 课程 - 生成 - AI 主题书
  category: 120,
});
onLoad((options) => {
  id.value = options.id;
});

onMounted(() => {
  getBookList(id.value);
  timer.value = setInterval(() => {
    getBookList(id.value);
  }, 15000);
});

onUnmounted(() => {
  clearInterval(timer.value);
});

// 触底加载
const scrolltolower = async () => {
  if (!isScrolltolower.value) return;
  initPage.current++;
  await getBookList(id.value);
};

// 下拉刷新
const handleRefresherRefresh = async () => {
  // if (refreshing.value) return;
  refreshing.value = true;
  await getBookList(id.value);
  refreshing.value = false;
};

// 获取文件类型
function getFileType(fileUrl) {
  if (!fileUrl) return "pdf";
  const extension = fileUrl.split(".").pop().toLowerCase();
  const typeMap = {
    pdf: "pdf",
    doc: "doc",
    docx: "docx",
    xls: "xls",
    xlsx: "xlsx",
    ppt: "ppt",
    pptx: "pptx",
    txt: "txt",
  };
  return typeMap[extension] || "pdf";
}

// 换算文件大小为MB
function formatFileSize(size) {
  if (!size) return "0KB";
  if (size < 1024) {
    return size + "B";
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + "KB";
  } else {
    return (size / 1024 / 1024).toFixed(2) + "MB";
  }
}

// 解构
const deconstruction = (data) => {
  if (data) {
    return JSON.parse(data);
  }
  return "";
};

// 直接下载文件
function onDownloadFileDirectly(item) {
  if (!item.uri) {
    uni.showToast({
      title: "文件不存在",
      icon: "none",
    });
    return;
  }

  uni.showLoading({
    title: "下载中...",
    mask: true,
  });

  // 微信小程序端使用 wx.downloadFile 以获得更好的体验
  // #ifdef MP-WEIXIN
  wx.downloadFile({
    url: item.uri,
    success: (res) => {
      if (res.statusCode === 200) {
        uni.showToast({
          title: "下载成功",
          icon: "success",
        });
        // 打开文件
        uni.openDocument({
          filePath: res.tempFilePath,
          showMenu: true,
          fileType: getFileType(item.uri),
          success: function () {
            console.log("打开文档成功");
          },
          fail: function (err) {
            console.error("打开文档失败:", err);
            uni.showToast({
              title: "打开文件失败",
              icon: "none",
            });
          },
        });
      } else {
        uni.showToast({
          title: "下载失败",
          icon: "none",
        });
      }
    },
    fail: (err) => {
      console.error("下载失败:", err);
      uni.showToast({
        title: "下载失败",
        icon: "none",
      });
    },
    complete: () => {
      uni.hideLoading();
    },
  });
  // #endif

  // 其他平台使用 uni.downloadFile
  // #ifndef MP-WEIXIN
  uni.downloadFile({
    url: item.uri,
    success: (res) => {
      if (res.statusCode === 200) {
        uni.showToast({
          title: "下载成功",
          icon: "success",
        });
        // 打开文件
        uni.openDocument({
          filePath: res.tempFilePath,
          showMenu: true,
          fileType: getFileType(item.uri),
          success: function () {
            console.log("打开文档成功");
          },
          fail: function (err) {
            console.error("打开文档失败:", err);
            uni.showToast({
              title: "打开文件失败",
              icon: "none",
            });
          },
        });
      } else {
        uni.showToast({
          title: "下载失败",
          icon: "none",
        });
      }
    },
    fail: (err) => {
      console.error("下载失败:", err);
      uni.showToast({
        title: "下载失败",
        icon: "none",
      });
    },
    complete: () => {
      uni.hideLoading();
    },
  });
  // #endif
}

// 生成最新主题书
const healedExportWord = () => {
  if (exportWordRef.value) {
    exportWordRef.value.healeExportWord(id.value);
    getBookList(id.value);
  }
};

const getBookList = async (id) => {
  let params = { ...initPage, subjectId: id };
  let res = await getTaskList(params);
  if (res.status == 0) {
    reportList.value = res.data;
    if (initPage.current >= Math.ceil(res.metadata.count / initPage.pageSize))
      isScrolltolower.value = false;
  } else {
    uni.$u.toast("主题书获取失败");
  }
};
// 获取状态文本
function getStatusText(status) {
  switch (status) {
    case 0:
      return "完成";
    case 1:
      return "待生成";
    case 2:
      return "生成中";
    default:
      return "未知";
  }
}
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
  position: relative;
  padding: 20rpx;

  .report-list {
    padding-bottom: 140rpx;

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 120rpx 40rpx;
      text-align: center;

      .empty-icon {
        font-size: 120rpx;
        margin-bottom: 40rpx;
        opacity: 0.6;
      }

      .empty-text {
        font-size: 32rpx;
        color: #333333;
        font-weight: 600;
        margin-bottom: 20rpx;
      }

      .empty-desc {
        font-size: 26rpx;
        color: #808080;
        line-height: 1.4;
      }
    }

    .report-item {
      background: #fff;
      margin-bottom: 20rpx;
      border-radius: 12rpx;
      box-shadow: 4rpx 8rpx 16rpx #eee;
      padding: 30rpx;
      border-radius: 28rpx;
      position: relative;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      transition: all 0.3s ease;

      &.report-item-clickable {
        cursor: pointer;

        &:hover {
          transform: translateY(-2rpx);
          box-shadow: 4rpx 12rpx 24rpx rgba(0, 0, 0, 0.15);
        }

        &:active {
          background: #f5f7fa;
        }
      }

      &.report-item-error {
        border-left: 8rpx solid #d32f2f;
        background: #fff5f5;
      }
    }

    .report-item-header {
      flex: 1;

      .title {
        font-size: 28rpx;
        color: #333333;
        font-weight: 600;
        margin-bottom: 10rpx;
        width: 600rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .time {
        font-size: 24rpx;
        color: #808080;
        margin-bottom: 10rpx;
      }

      .status-wrapper {
        .status {
          display: inline-flex;
          align-items: center;
          padding: 4rpx 12rpx;
          border-radius: 8rpx;
          font-size: 20rpx;
          font-weight: 500;
          margin-bottom: 8rpx;

          &.status-error {
            background: #ffebee;
            color: #d32f2f;

            .u-icon {
              cursor: pointer;
              transition: transform 0.3s ease;

              &:hover {
                transform: rotate(180deg);
              }
            }
          }

          &.status-pending {
            background: #fff3e0;
            color: #f57c00;
          }

          &.status-success {
            background: #e8f5e8;
            color: #388e3c;
          }
        }

        .download-hint {
          font-size: 20rpx;
          color: #3f79ff;
          font-weight: 500;
        }
      }
    }
  }
}
.add-btn {
  height: 80rpx;
  line-height: 80rpx;
  background: #3f79ff;
  color: #fff;
  border-radius: 44rpx;
  font-size: 30rpx;
  font-weight: 600;
  border: none;
  margin: 0 !important;
}

.iconAction {
  & > view {
    height: 88rpx;
    display: flex;
    align-items: center;
    font-size: 30rpx;
    font-weight: 400;
    letter-spacing: 0rpx;
    line-height: 48rpx;
    color: rgba(51, 51, 51, 1);
    text-align: left;
    vertical-align: middle;

    image {
      width: 40rpx;
      height: 40rpx;
      margin-right: 28rpx;
    }

    .u-icon {
      margin-right: 28rpx;
    }
  }
}
</style>
