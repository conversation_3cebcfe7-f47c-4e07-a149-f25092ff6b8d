<!-- 功能列表 ai教案-导出wrod-活动评价 -->
<template>
  <view class="layout">
    <up-navbar
      :title="pageData.title"
      bgColor="transparent"
      placeholder
      fixed
      safeAreaInsetTop
      autoBack
    />
    <view class="title-text">选择/切换课程</view>
    <uni-data-select
      v-model="courseId"
      :localdata="pageData.localdata"
      @change="change"
      placeholder="请选择课程"
      :disabled="selectDisabled"
    />
    <view class="box">
      <template v-if="!selectDisabled">
        <ActItem
          v-for="(item, idx) in pageData.actArr"
          :key="idx"
          :actData="item"
          :actId="item.id"
          @change="actIdChange"
        />
      </template>
      <view
        class="text-Empty"
        v-if="selectDisabled"
        >word模版支持园所定制。<br />
        暂无教案word模版，请联系系统客服添加！
      </view>
      <view
        class="text-Empty"
        v-if="!pageData.data.length"
        >暂无课程,请先选择添加课程！</view
      >
      <view
        class="text-Empty"
        v-if="!pageData.actArr.length"
        >暂无阶段,请先选择添加阶段！</view
      >
    </view>
    <export-word
      v-if="pageData.title == '导出Word'"
      ref="exportWordRef"
      type="活动"
      documentTemplateCategory="SubjectActivity"
      :subjectId="schoolId"
      @error="headleError"
    />
  </view>
</template>

<script setup>
import { onLoad } from "@dcloudio/uni-app";
import { onMounted, ref, reactive, computed } from "vue";
import { getAiclassList } from "@/api";
import { getActList } from "@/api/classApi.js";
import ActItem from "@/courseDetails/components/ActItem.vue";
import ExportWord from "@/components/export-word/export-word.vue";

let courseId = ref(null);
let exportWordRef = ref(null);
let actTypeArr =
  uni.getStorageSync("ENUM").SubjectActivityOrganizationFormEnumDesc;
const paging = reactive({
  currentPage: 1,
  pageSize: 99,
});
let schoolId = uni.getStorageSync("USER_INFO").currentSchoolId;
let selectDisabled = ref(false);
// 页面数据
const pageData = reactive({
  localdata: [], // { text: '显示文本', value: '选中后的值 any', disable: '是否禁用 boolean' },  //数组内每项是对象，需要严格遵循如下格式
  data: [], // 当前班级课程
  actArr: [], // 活动列表
  title: "", // 标题
});

const actIdChange = (id) => {
  switch (pageData.title) {
    case "AI教案":
      uni.navigateTo({
        url: `/courseDetails/aiCreateDetails/aiCreateDetails?id=${id}&subjectId=${courseId.value}`,
      });
      break;
    case "导出Word":
      if (exportWordRef.value && !selectDisabled.value)
        exportWordRef.value.healeExportWord(id);

      break;
    case "活动评价":
      uni.navigateTo({
        url: `/courseDetails/evaluate/evaluate?id=${id}`,
      });
      break;
    default:
      break;
  }
};
// 选择课程
let olde = "";
const change = (e) => {
  if (olde == e) {
    return;
  }
  olde = e;
  getSelectActListFn(e);
};

// 获取选中的活动列表
const getSelectActListFn = async (subjectId) => {
  try {
    const res = await getActList({ subjectId });
    if (res.status == 0) {
      pageData.actArr = res.data.map((item, index) => {
        item.isExplad = index === 0 ? true : false;
        item.subjectActivities = item.subjectActivities.map((_item) => {
          return {
            organizationFormStr: actTypeArr[_item.organizationForm],
            ..._item,
          };
        });
        return { ...item };
      });
    }
  } catch (er) {
    console.log(er);
    uni.$u.toast("获取活动列表失败");
  }
};

// 当前班级的课程列表
const getCurClassList = async () => {
  const userInfo = uni.getStorageSync("USER_INFO");
  const params = {
    currentPage: paging.currentPage,
    pageSize: paging.pageSize,
    pageModel: {
      schoolId: userInfo.currentSchoolId,
      classId: userInfo.currentClassId,
      statusList: [1],
    },
  };
  try {
    let res = await getAiclassList(params);
    if (res.status == 0) {
      pageData.data = res.data;
      // 过滤 是ai生成的教案
      const filData = filterActList(res.data);
      pageData.localdata = filData.map((item) => {
        return {
          text: item.title,
          value: item.id,
        };
      });
    }
  } catch (error) {
    console.log(error);
    uni.$u.toast("获取班级课程列表失败");
  }
};

// 处理活动列表
const filterActList = (arr) => {
  let actArr = arr;
  // 根据 arrde 的 updatedAt 来从大到小排序
  actArr.sort((a, b) => {
    return new Date(b.updatedAt) - new Date(a.updatedAt);
  });
  let category_list = uni.getStorageSync("CATEGORY_LIST");
  console.log(category_list, "actArr");
  actArr.forEach((item) => {
    let ctitle = category_list.find((ite) => ite.id == item.categoryId)?.value;
    if (ctitle) item.title = `${item.title}-${ctitle}`;
  });
  switch (pageData.title) {
    case "AI教案":
      return actArr;
    default:
      return actArr;
  }
};

//处理没有模板的情况
const headleError = (e) => {
  selectDisabled.value = true;
};

onLoad((options) => (pageData.title = options.type));
onMounted(async () => {
  await getCurClassList();
  courseId.value = pageData.data[0].id;
  console.log(courseId.value, "courseId.value");
  // if (pageData.title == "导出Word") {
  //   // if (selectDisabled.value) pageData.actArr = []
  //   return;
  // }
  await getSelectActListFn(courseId.value);
});
</script>
<script>
export default {
  options: { styleIsolation: "shared", multipleSlots: true },
};
</script>

<style lang="scss" scoped>
.layout {
  background: url("https://c.mypacelab.com/vxmp/img/classDeatils_bg_3x.png")
    no-repeat center top;
  background-size: 100% 100%;
  //   background-size: cover;
  // background-position: top left;
  padding: 0 32rpx;
  // min-height: 100vh;
  .box {
    height: 100%;
    padding-bottom: calc(320rpx + env(safe-area-inset-bottom)); 
    // #ifdef MP-WEIXIN
    min-height: calc(100vh - 366rpx);
    padding-bottom: calc(380rpx + env(safe-area-inset-bottom));
    // #endif
    overflow-y: auto;
    box-sizing: border-box;
    
  }

  .actitembtn {
    background-color: #3f79ff;
    color: #fff;
    font-size: 22rpx;
    border-radius: 10rpx;
    padding: 4rpx;
    box-sizing: border-box;
  }

  :deep(.uni-select) {
    height: 88rpx;
    border: 1px solid #3f79ff;
    background-color: #ffffff;
    padding: 0 32rpx;
    border-radius: 20rpx;
    margin-bottom: 32rpx;

    .uni-select__input-text {
      color: #3f79ff;
      font-size: 30rpx;
      font-weight: 500;
    }
  }

  .title-text {
    font-size: 30rpx;
    font-weight: 500;
    color: #333333;
    margin-top: 16rpx;
    margin-bottom: 24rpx;
  }
}
</style>
