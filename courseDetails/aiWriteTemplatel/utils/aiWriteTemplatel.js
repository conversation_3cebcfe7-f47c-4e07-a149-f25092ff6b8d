import { reactive, onMounted } from "vue";

const generalConfig = reactive([
    {
        label: '学期',
        type: 'multiSelect',
        multiple: false,
        keyName: 'term',
        required: 1,
        DICT_NAME: 'termList',
        extend: []
    },
    {
        label: '年级',
        type: 'multiSelect',
        multiple: false,
        keyName: 'gradeId',
        required: 1,
        DICT_NAME: 'gradeList',
        extend: []
    },
    { label: '实施时间', type: 'pickerTime', keyName: 'grade', required: 1, tip: '请选择实施时间' },
    { label: '创作者', type: 'input', keyName: 'creator', required: 1 },
])
export default () => {

    const extend1 = [
        {
            value: 'Collective',
            text: '集体',
        },
        {
            value: 'Family',
            text: '家庭',
        },
        {
            value: 'Group',
            text: '分组',
        },
        {
            value: 'Talk',
            text: '谈话',
        },
        {
            value: 'Team',
            text: '小组',
        }
    ]
    const extendNum1 = [
        {
            value: '2',
            text: '2',
        },
        {
            value: '3',
            text: '3',
        },
        {
            value: '4',
            text: '4',
        },
        {
            value: '5',
            text: '5',
        },
        {
            value: '6',
            text: '6',
        },
        {
            value: '7',
            text: '7',
        },
        {
            value: '8',
            text: '8',
        }
    ]
    const extendNum2 = [
        {
            value: 3,
            text: '3',
        },
        {
            value: 4,
            text: '4',
        },
        {
            value: 5,
            text: '5',
        },
        {
            value: 6,
            text: '6',
        },
        {
            value: 7,
            text: '7',
        },
        {
            value: 8,
            text: '8',
        },
        {
            value: 9,
            text: '9',
        },
        {
            value: 10,
            text: '10',
        }
    ]
    // multiple: true,  true 多选 false 单选
    const fromList = [
        //主题课程
        {
            title: '主题课程',
            id: 3524,
            config: [
                ...generalConfig,
                { label: '主题课程名称', type: 'input', keyName: 'themeName', keyName: 'title', required: 1, tip: "请输入主题课程名称" },
                { label: '主题课程目标', type: 'textarea', tip: '（重要信息）请输入主题课程目标，请详尽列出', keyName: 'objectives', required: 1, tip: '（重要信息）请列举主题课程目标，例：\n了解过年是家人团聚的时光;\n了解新年的美食;\n了解新年要做的事情了解新年游园会' },
                {
                    label: '主题脉络中需包含的活动形式',
                    type: 'multiSelect',
                    multiple: true, // true 多选 false 单选
                    tip: '请多选活动形式，如无对应选项，请联系客服定制',
                    keyName: 'activityOrganizationFormList',
                    required: 1,
                    extend: []
                },
                {
                    label: '子主题数量',
                    type: 'multiSelect',
                    required: 1,
                    multiple: false,
                    keyName: 'zly', // 不参与接口，仅用于前端展示
                    extend: extendNum1,
                },
                {
                    label: '每个子主题活动数量',
                    type: 'multiSelect',
                    required: 1,
                    multiple: false,
                    keyName: 'activityNum',
                    extend: extendNum2,
                },
                // { label: '各子主题名称', type: 'textarea', tip: "（重要信息）请列举主题课程目标，例：\n子主题一：过年要团圆\n子主题二：体验新年味\n子主题三：新年我准备好了\n子主题四：新年游园会", keyName: 'stageList', required: 1 },
            ]
        },
        //项⽬课程
        {
            title: '项目课程',
            id: 3832,
            config: [
                ...generalConfig,
                { label: '项目课程名称', type: 'input', tip: '请输入项目课程名称', keyName: "title", required: 1 },
                { label: '最终的产品或成果', type: 'textarea', tip: "（重要信息）请输入最终的产品或成果，比如，波洛克艺术展、滴水器产品发布会、奶茶店开业仪式", keyName: "finalProduct", required: 1 },
                { label: '过程产物', type: 'textarea', tip: '（重要信息）请输入项目过程的重点产物，比如，奶茶配方、奶茶logo设计、奶茶店装饰规划等', keyName: "processProduct", required: 1 },
            ]
        },
        // 领域/特⾊课程
        {
            title: '领域/特色课程',
            id: 3834,
            config: [
                ...generalConfig,
                {
                    label: '领域',
                    keyName: 'domain',
                    type: 'multiSelect',
                    required: 1,
                    multiple: false,
                    extend: [
                        {
                            value: '科学',
                            text: '科学',
                        },
                        {
                            value: '数学',
                            text: '数学',
                        },
                        {
                            value: '语言',
                            text: '语言',
                        },
                        {
                            value: '社会',
                            text: '社会',
                        },
                        {
                            value: '食育',
                            text: '食育',
                        },
                        {
                            value: '美术',
                            text: '美术',
                        },
                        {
                            value: '音乐',
                            text: '音乐',
                        },
                        {
                            value: '舞蹈',
                            text: '舞蹈',
                        },
                        {
                            value: '戏剧',
                            text: '戏剧',
                        },
                    ],
                    tip: '请选择生活领域，如无对应选项，请联系客服定制。健康或安全课程请移步到“生活课程设计”'
                },
                { label: '领域课程名称', type: 'input', keyName: 'title', required: 1, tip: "请输入领域课程名称，如小小科学家" },
                { label: '领域课程目标', type: 'textarea', keyName: 'objectives', required: 1 },
                {
                    label: '活动中需包含的活动形式',
                    type: 'multiSelect',
                    keyName: 'activityOrganizationFormList',
                    required: 1,
                    multiple: true,
                    extend: []
                },
                {
                    label: '⼦领域数量',
                    type: 'multiSelect',
                    required: 1,
                    multiple: false,
                    keyName: 'zly', // 不参与接口，仅用于前端展示
                    extend: extendNum1
                },
                {
                    label: '每个⼦领域活动数量',
                    type: 'multiSelect',
                    keyName: 'activityNum',
                    required: 1,
                    multiple: false,
                    extend: extendNum2
                },
                // { label: '⼦领域名称', type: 'textarea', keyName: "stageList", required: 1, tip: '用换行分隔，（重要信息）请输入子领域名称，请对应子领域数量，如“子领域一：XX”“子领域二：XX”' },
            ]
        },
        //生活课程
        {
            title: '生活课程',
            id: 3835,
            config: [
                ...generalConfig,
                {
                    label: '生活领域',
                    type: 'multiSelect',
                    required: 1,
                    multiple: false,
                    tip: '请选择生活领域，如无对应选项，请联系客服定制',
                    keyName: 'domain',
                    extend: [
                        {
                            value: '常规习惯',
                            text: '常规习惯',
                        },
                        {
                            value: '安全',
                            text: '安全',
                        },
                        {
                            value: '身体健康',
                            text: '身体健康',
                        },
                        {
                            value: '心理健康',
                            text: '心理健康',
                        },
                        {
                            value: "身心健康",
                            text: '身心健康',
                        }
                    ]
                },
                { label: '生活课程名称', type: 'input', required: 1, keyName: 'title' },
                { label: '生活课程目标', type: 'textarea', required: 1, keyName: 'objectives' },
                {
                    label: '活动中需包含的活动形式',
                    type: 'multiSelect',
                    required: 1,
                    multiple: true,
                    tip: '请选择活动形式，如无对应选项，请联系客服定制',
                    extend: [],
                    keyName: 'activityOrganizationFormList'
                },
                {
                    label: '子主题数量',
                    type: 'multiSelect',
                    required: 1,
                    multiple: false,
                    extend: extendNum1,
                    keyName: 'zly', // 不参与接口，仅用于前端展示
                },
                {
                    label: '每个子主题活动数量',
                    type: 'multiSelect',
                    required: 1,
                    multiple: false,
                    extend: extendNum2,
                    keyName: 'activityNum'
                },
                // {label: '子主题名称', type: 'textarea', required: 1, keyName: 'stageList',tip: "（重要信息）请输入子主题名称，请对应子主题数量，如\n“子主题一：XX”\n“子主题二：XX”"},
            ]
        },
        // STEM课程
        {
            title: 'STEM课程',
            id: 3833,
            config: [
                ...generalConfig,
                { label: 'STEM课程名称', type: 'input', required: 1, keyName: 'title', tip: '请输入STEM课程名称' },
                // { label: 'STEM课程目标', type: 'textarea', required: 1, keyName: 'objectives', tip: "请输入STEM课程目标，比如，\n了解小猪的习性、\n了解房屋的特征、\n了解房屋的构架、\n了解房屋的材料、\n知道搭建的方法等" },
                { label: '过程产物', type: 'textarea', required: 1, keyName: 'processProduct', tip: '（重要信息）请输入过程产物，比如，\n房屋设计图、\n搭建的房屋模型、\n房屋PS示意图等' },
                { label: '最终的产品或成果', type: 'textarea', required: 1, keyName: 'finalProduct', tip: '（重要信息）请输入最终的产品或成果，比如，\n幼儿园的猪圈，\n种植区滴水器等' },
                // { label: '课程各阶段实施步骤', type: 'textarea', required: 1, keyName: 'stageList', tip: '请输入课程各阶段实施步骤，比如，\n阶段一：调查猪圈的形状、材料、搭建方法；\n阶段二：制作设计图、工程计划表等' },
                // { label: '结题活动', type: 'input', required: 1, keyName: 'closeActivity', tip: '请输入结题活动名称，比如，小猪的乔迁仪式' },
            ]
        },
        // 体育课程
        {
            title: '体育课程',
            id: 3836,
            config: [
                ...generalConfig,
                {
                    label: '体育领域',
                    type: 'multiSelect',
                    required: 1,
                    multiple: false,
                    keyName: 'domain',
                    extend: [
                        { text: '篮球', value: '篮球' },
                        { text: '跳绳', value: '跳绳' },
                        { text: '足球', value: '足球' },
                    ],
                    tip: '请选择体育领域，如无对应选项，请联系客服定制'
                },
                { label: '体育课程名称', type: 'input', required: 1, keyName: 'title', tip: '请输入体育课程名称，比如，“小小运动员”' },
                { label: '体育课程目标', type: 'textarea', required: 1, keyName: 'objectives', tip: '（重要信息）请输入体育课程目标，请详尽列出' },
                {
                    label: '子主题数量',
                    type: 'multiSelect',
                    required: 1,
                    multiple: false,
                    keyName: 'zly', // 不参与接口，仅用于前端展示
                    extend: extendNum1,
                },
                {
                    label: '每个子主题活动数量',
                    type: 'multiSelect',
                    required: 1,
                    multiple: false,
                    keyName: 'activityNum',
                    extend: extendNum2,
                },
                // { label: '子主题名称', type: 'textarea', required: 1, keyName: 'stageList', tip: '用换行分隔，请输入子主题名称，请对应子主题数量，如“子主题一：XX”“子主题二：XX”' },
            ]
        }
    ]
    return {
        fromList
    }
}