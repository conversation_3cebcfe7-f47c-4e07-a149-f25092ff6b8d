<template>
  <view>
    <Base-layout
      :navTitle="navTitle"
      :footerStyle="{ padding: 0 }"
      :contentStyle="{ padding: 0 }"
    >
      <view class="aiWriteTemplatelayout">
        <ai-dynamic-form
          ref="dynamicFormRef"
          :templateId="templateId"
          :formConfig="fromList"
        />
      </view>
      <view class="fixed-bottom-btn">
        <up-button
          type="primary"
          @click="submit"
          shape="circle"
          color="#367CFF"
          :customStyle="customStyle"
          text="开始AI生成"
          :loading="isLoading"
        />
      </view>
    </Base-layout>
  </view>
</template>

<script setup>
import { ref, onMounted, reactive } from "vue";
import BaseLayout from "@/components/base-layout/base-layout.vue";
import { onLoad } from "@dcloudio/uni-app";
import aiDynamicForm from "./components/aiDynamicForm.vue";
import utils from "./utils/aiWriteTemplatel";
import {
  setA<PERSON>themeForm,
  setAiProjectForm,
  setAiDomainForm,
  setAiLifeForm,
  setAiStemForm,
  setAiGymForm,
} from "../api/aiwrite.js";
let navTitle = ref("AI写作模板");
let dynamicFormRef = ref(null);
let templateId = ref("");
let { fromList } = utils();
let customStyle = {
  height: "80rpx",
  fontSize: "30rpx",
  fontWeight: "600",
};
let isLoading = ref(false);
onLoad((options) => {
  console.log(options);
  // 如果路径上没有id的话，就是从课程列表点击再次生成过来的，需要重新请求一次
  templateId.value = Number(options.id) || false;
  navTitle.value = fromList.find((item) => item.id == templateId.value)?.title;
});
const submit = async () => {
  switch (templateId.value) {
    case 3524: // 主题课程
      getRequestType(setAIthemeForm);
      break;
    case 3832: // 项目课程
      getRequestType(setAiProjectForm);
      break;
    case 3834:
      getRequestType(setAiDomainForm);
      break;
    case 3835:
      getRequestType(setAiLifeForm);
      break;
    case 3833:
      getRequestType(setAiStemForm);
      break;
    case 3836:
      getRequestType(setAiGymForm);
      break;
    default:
      break;
  }
};
// 主题请求类型
const getRequestType = async (fn) => {
  const valid1 = await dynamicFormRef.value.validate();
  isLoading.value = true;
  const res = await fn(valid1);
  if (res.status == 0) {
    isLoading.value = false;
    uni.$u.toast("AI生成中...");
    uni.redirectTo({
      url: "/courseDetails/list/draftList",
    });
    isLoading.value = false;
  } else {
    uni.showToast({
      title: res.message || "请求失败，请联系管理员！",
      icon: "error",
    });
    isLoading.value = false;
  }
};
onMounted(() => {});
</script>

<style lang="scss" scoped>
.aiWriteTemplatelayout {
  padding: 0 32rpx;
}

.fixed-bottom-btn {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100vw;
  box-sizing: border-box;
  min-height: 104rpx;
  padding: 16rpx 32rpx;
  background: #ffffff;
  padding-bottom: calc(env(safe-area-inset-bottom) + 16rpx);
}
</style>
