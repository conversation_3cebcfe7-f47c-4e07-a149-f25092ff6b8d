<template>
  <view class="formLayout">
    <up-form
      :model="formData"
      ref="formRef"
      labelPosition="top"
      labelWidth="auto"
    >
      <view
        class="form-item-container"
        :key="index"
        v-for="(item, index) in list"
      >
        <up-form-item
          :label="item.label"
          :prop="item.keyName"
          :required="item.required === 1"
        >
          <!-- input类型 -->
          <up-input
            border="none"
            v-if="item.type === 'input'"
            v-model="formData[item.keyName]"
            :placeholder="item.tip || `请输入${item.label}`"
            placeholderStyle="white-space: warp; color: #B1B3B5;"
          />
          <!-- textarea类型 -->
          <up-textarea
            type="textarea"
            maxlength="-1"
            border="none"
            confirmType="none"
            :autoHeight="true"
            v-if="item.type === 'textarea'"
            v-model="formData[item.keyName]"
            :placeholder="item.tip || `请输入${item.label}`"
            class="form-item-textarea"
            placeholderStyle="white-space: warp; color: #B1B3B5;"
            :style="{ padding: '0', minHeight: '200rpx' }"
            :cursorSpacing="100"
          />
          <!-- picker类型 -->
          <view
            style="width: 100%"
            v-if="item.type === 'picker'"
            @click="onShowPicker(item)"
          >
            {{ getLabelById(formData[item.keyName], item.DICT_NAME) }}
            <text
              v-if="!getLabelById(formData[item.keyName], item.DICT_NAME)"
              class="placeholder"
              >请输入{{ item.label }}</text
            >
          </view>
          <up-input
            v-if="item.type === 'picker'"
            style="display: none"
            v-model="formData[item.keyName]"
          />

          <!-- pickerTime类型 -->
          <view
            style="width: 100%"
            v-if="item.type === 'pickerTime'"
          >
            <!-- @click="onShowPicker('pickerTime')" -->
            <!-- {{ formData.timeText }}
            <text
              v-if="!formData.timeText"
              class="placeholder"
              >请输入{{ item.label }}</text
            > -->
            <uni-datetime-picker
              v-model="formData[item.keyName]"
              type="daterange"
              :border="false"
              @change="pickerTimeConfirm"
            />
            <up-input
              v-if="item.type === 'pickerTime'"
              style="display: none"
              v-model="formData[item.keyName]"
            />
          </view>

          <!-- <view v-if="item.type === 'multiSelect'">{{formData[item.keyName]}}</view> -->
          <!-- MultiSelect类型 -->
          <uni-data-checkbox
            v-if="item.type === 'multiSelect'"
            v-model="formData[item.keyName]"
            :localdata="item.extend"
            :multiple="item.multiple"
            :disabled="
              isChangs &&
              (item.keyName === 'gradeId' || item.keyName === 'term')
            "
            mode="button"
            :placeholder="item.tip"
            @change="onMultiSelect(item)"
          />
          <up-input
            v-if="item.type === 'multiSelect'"
            style="display: none"
            v-model="formData[item.keyName]"
          />
          <!-- Uploader类型 -->
          <up-upload
            v-if="item.type === 'uploader'"
            v-model="formData[item.keyName]"
            :placeholder="item.tip"
          />
          <template
            #right
            v-if="item.type === 'picker'"
          >
            <up-icon name="arrow-down" />
          </template>
        </up-form-item>
        <view
          v-if="item.type === 'multiSelect'"
          class="multiSelect"
          >{{ item.tip }}</view
        >
      </view>
    </up-form>
    <view
      v-if="list.length === 0"
      class="multiSelect"
      style="margin-top: 250rpx; text-align: center"
    >
      暂无模板，请前往“我的”联系客服添加...
    </view>
    <up-picker
      :show="isPickerShow"
      :defaultIndex="[0]"
      :columns="activeOption"
      @confirm="pickerConfirm"
      @cancel="isPickerShow = false"
      keyName="label"
    ></up-picker>
    <!-- <up-datetime-picker
      :show="isDatetimePicker"
      v-model="DatetimePickerValue"
      mode="date"
      @confirm="pickerTimeConfirm"
      @cancel="isDatetimePicker = false"
    ></up-datetime-picker> -->
    <up-calendar
      :show="isDatetimePicker"
      mode="range"
      monthNum="12"
      @close="isDatetimePicker = false"
      @confirm="pickerTimeConfirm"
      allowSameDay
      closeOnClickOverlay
    ></up-calendar>
  </view>
</template>

<script setup>
import { ref, onMounted, reactive, computed, toRefs } from "vue";
import { getDICT } from "@/utils";
import { getCategoryList } from "@/api/api.js"; // 获取年级
import { aiOnceMoreGenerate } from "@/courseDetails/api/subjeckActivity.js";
import { getJavaDictList } from "@/courseDetails/api/editActivity.js";
import { onLoad } from "@dcloudio/uni-app";
const props = defineProps({
  templateId: {
    required: true,
  },
  formConfig: {
    type: Array,
    default: () => [],
  },
});
let list = ref([]);
const formRef = ref(null);
const formData = reactive({
  categoryId: props.templateId,
  classId:
    uni.getStorageSync("USER_INFO")?.currentClassId ||
    uni.getStorageSync("USER_INFO")?.classIds[0],
  schoolId: uni.getStorageSync("USER_INFO")?.currentSchoolId,
  creator: uni.getStorageSync("USER_INFO")?.name,
});
const formDataWithDefaultValue = ref({});
let isPickerShow = ref(false);
let isDatetimePicker = ref(false); // 日期选择器
let activeOptionKey = ref("");
let isChangs = ref(false);
// 获取上次填写内容
const getAiDetails = async (subjectId) => {
  const res = await aiOnceMoreGenerate({ subjectId });
  let data = JSON.parse(res.data);
  console.log(data, "获取详情");

  formData.categoryId = data.categoryId;
  formData.subjectId = Number(subjectId);
  formData.startAt = data.startAt;
  formData.endAt = data.endAt;

  list.value = props.formConfig.find(
    (item) => item.id == formData.categoryId
  )?.config;
  list.value.forEach((item) => {
    if (item.keyName == "zly") {
      formData[item.keyName] = String(data.stageList.length);
      formFor(data.stageList.length, data.stageList);
      return;
    }
    if (item.keyName == "activityNum") {
      formData[item.keyName] = data[item.keyName];
      return;
    }
    if (item.keyName == "activityOrganizationFormList" && item.multiple) {
      formData[item.keyName] = data[item.keyName][0].split(",");
      console.log(item);
      return;
    }
    formData[item.keyName] = data[item.keyName];
  });
  let st = data.startAt.split(" ")[0];
  let et = data.endAt.split(" ")[0];
  formData.grade = [st, et]; // 用于验证
};
onLoad((options) => {
  if (options.subjectId) {
    isChangs.value = options.subjectId;
    // getAiDetails(options.subjectId);
  }
});

onMounted(() => {
  formData.categoryId = props.templateId;
  // 如果没有找到模板就显示联系客服
  if (!props.formConfig.find((item) => item.id == props.templateId)?.config) {
    list.value = [];
    return;
  }

  list.value = props.formConfig.find(
    (item) => item.id == props.templateId
  )?.config;
  console.log("是否执行两次");
  console.log(uni.getStorageSync("subjectId"), isChangs.value);

  if (isChangs.value) getAiDetails(isChangs.value);
  initFormData();
  generateRules(list.value);
});

/** 选项 */
const optionsList = reactive({
  // 学期列表
  termList: [],
  // 用户班级列表
  gradeList: [],
  // ⼦主题数量
  activityNumList: [
    {
      label: "2个",
      id: 2,
    },
    {
      label: "3个",
      id: 3,
    },
    {
      label: "4个",
      id: 4,
    },
    {
      label: "5个",
      id: 5,
    },
    {
      label: "6个",
      id: 6,
    },
    {
      label: "7个",
      id: 7,
    },
    {
      label: "8个",
      id: 8,
    },
  ],
  // 活动形式
  activityOrganizationForm: [],
});

const activeOption = computed(() => {
  return [optionsList[activeOptionKey.value] || []];
});

// 解析选项配置
const parseOptions = (extend) => {
  let options = [];
  if (typeof extend === "string") {
    try {
      options = extend.split(",");
    } catch (e) {
      options = [];
    }
  } else if (Array.isArray(extend)) {
    options = extend;
  }

  // 转换为 uni-select 需要的格式
  return options.map((item) => ({
    value: item,
    text: item,
  }));
};

// MultiSelect 事件
const onMultiSelect = (ev) => {
  const { keyName } = ev;
  if (isChangs.value && keyName == "gradeId") {
    uni.$u.toast("再次生成课程时，此字段不可修改");
    return;
  }
  if (keyName == "zly") {
    formFor(formData[keyName]);
  }
};

// 初始化表单数据
const initFormData = async (config) => {
  // 获取年级列表
  // const res = await getCategoryList({
  //   current: 1,
  //   pageSize: 10,
  //   category: 3,
  // });
  let params = {
    currentPage: 1,
    pageSize: 999,
  };
  params.pageModel = { dictCode: "grade" };
  const res = await getJavaDictList(params);
  optionsList.gradeList = res.data.map((item) => {
    return {
      text: item.dictItemName,
      value: Number(item.dictItemCode),
    };
  });

  // optionsList.gradeList.reverse();

  /** 初始化字典 */
  params.pageModel = { dictCode: "termName" };
  const tres = await await getJavaDictList(params);
  optionsList.termList = tres.data.map((key) => {
    return {
      text: key.dictItemName,
      value: Number(key.dictItemCode),
    };
  });
  params.pageModel = { dictCode: "activityOrganizationForm" };
  const ares = await getJavaDictList(params);
  if (ares.status == 0) {
    optionsList.activityOrganizationForm = ares.data.map((key) => {
      console.log(key);

      return {
        text: key.dictItemName,
        value: key.dictItemCode,
      };
    });
  }
  list.value.forEach((item) => {
    if (item.keyName == "gradeId") {
      item.extend = optionsList.gradeList;
    }
    if (item.keyName == "term") {
      item.extend = optionsList.termList;
    }
    if (item.keyName == "activityOrganizationFormList") {
      item.extend = optionsList.activityOrganizationForm;
    }
  });
};

// 获取对应id的label值
const getLabelById = (id, type) => {
  const item = optionsList[type].find((item) => item.id === id);
  return item ? item.label : "";
};

// 打开Picker
function onShowPicker(key) {
  if (isChangs.value && key.keyName == "gradeId") {
    uni.$u.toast("再次生成课程时，此字段不可修改");
    return;
  }
  if (key == "pickerTime") {
    isDatetimePicker.value = true;
    return;
  }
  activeOptionKey.value = key.DICT_NAME;
  isPickerShow.value = true;
}

// 选择器确认选择
const pickerConfirm = ({ value, fullValue }) => {
  switch (activeOptionKey.value) {
    case "activityNumList":
      formData.activityNum = value[0].id;
      formFor(value[0].id);

      break;
    case "termList":
      formData.term = value[0].id;
      // updateChildList();
      break;
    case "gradeList":
      formData.gradeId = value[0].id;

      break;
    default:
      break;
  }

  isPickerShow.value = false;
};
function numberToChinese(num) {
  const chineseNumerals = [
    "零",
    "一",
    "二",
    "三",
    "四",
    "五",
    "六",
    "七",
    "八",
    "九",
  ];

  return chineseNumerals[num];
}

const formFor = (index, data = []) => {
  formData.stageList = [];
  // 删除开头为child的对象
  list.value = list.value.filter((item) => {
    return !item.keyName.startsWith("child");
  });

  for (let i = 0; i < index; i++) {
    // formData.stageList += `子主题 - ${i + 1}:\n`;
    list.value.push({
      label: `子主题目标 - ` + numberToChinese(i + 1),
      keyName: `child${i + 1}`,
      type: "input",
      required: 1,
      placeholder: "请输入子目标",
    });
    if (data.length > 0) formData[`child${i + 1}`] = data[i];
  }
  // 获取属性名
  let attributes = Object.keys(formData);
  // 过滤以"child"开头的属性
  let childAttributes = attributes.filter((attr) => attr.startsWith("child"));

  // 删除长度超过指定长度的字段
  childAttributes.forEach((attr) => {
    let numer = attr?.match(/\d+/g)?.join("");
    if (numer > index) {
      delete formData[attr];
    }
  });
  generateRules(list.value);
};

// 时间选择器确认选择
const pickerTimeConfirm = (variable) => {
  // formData.grade = variable[0]; // 用于验证
  formData.startAt = `${variable[0]} 00:00:00`;
  formData.endAt = `${variable[variable.length - 1]} 23:59:59`;
  // formData.timeText = `${variable[0]} 至 ${variable[variable.length - 1]}`;
  // isDatetimePicker.value = false;
};

// 生成验证规则
const generateRules = (config) => {
  const rulesObj = {};

  config.forEach((item) => {
    const ruleArray = [];

    switch (item.type) {
      case "input":
        if (item.required === 1) {
          ruleArray.push({
            required: true,
            message: `请输入${item.label}`,
            trigger: ["change", "blur"],
          });
        }
        break;

      case "select":
        if (item.required === 1) {
          ruleArray.push({
            required: true,
            message: `请选择${item.label}`,
            trigger: "change",
          });
        }
        break;

      case "textarea":
        if (item.required === 1) {
          ruleArray.push({
            required: true,
            message: `请选择${item.label}`,
            trigger: "change",
          });
        }
        break;
      case "picker":
        if (item.required === 1) {
          ruleArray.push({
            required: true,
            type: "number",
            message: `请选择${item.label}`,
            trigger: "blur",
          });
        }
        break;
      case "pickerTime":
        if (item.required === 1) {
          ruleArray.push({
            required: true,
            type: "array",
            message: `请选择${item.label}`,
            trigger: "blur",
          });
        }
        break;
      case "multiSelect":
        if (item.required === 1) {
          ruleArray.push({
            required: true,
            type: "any",
            min: 1,
            message: `请至少选择一个${item.label}`,
            trigger: "blur",
          });
        }
        break;

      case "uploader":
        if (item.required === 1) {
          ruleArray.push({
            validator: (rule, value, callback) => {
              if (!value || (Array.isArray(value) && value.length === 0)) {
                callback(new Error(`请上传${item.label}`));
              } else {
                callback();
              }
            },
            trigger: "change",
          });
        }
        break;
    }

    if (ruleArray.length > 0) {
      rulesObj[item.keyName] = ruleArray;
    }
  });

  // 使用 setRules 方法设置规则
  formRef.value?.setRules(rulesObj);
};

// 定义验证方法
const validate = () => {
  return new Promise(async (resolve, reject) => {
    if (!formRef.value) {
      console.error("formRef is null");
      reject(new Error("表单实例不存在"));
      return;
    }
    console.log(formData);

    try {
      const valid = await formRef.value?.validate();
      if (valid) {
        // 将formDataz中所有开头为child的属性中的全部添加到stageList,并删除formData中的child
        const childKeys = Object.keys(formData).filter((key) =>
          key.startsWith("child")
        );
        childKeys.forEach((key) => {
          formData.stageList.push(formData[key]);
          // delete formData[key];
        });
        if (formData.activityOrganizationFormList)
          formData.activityOrganizationFormList = [
            `${formData.activityOrganizationFormList}`,
          ];
        resolve(formData);
      } else {
        reject(new Error("表单验证失败"));
      }
    } catch (error) {
      uni.$u.toast("有未完成的必填项，请检查");
      reject(error);
    }
  });
};

// 暴露方法给父组件
defineExpose({
  formRef,
  validate,
  formData,
  formDataWithDefaultValue,
});
</script>
<script>
export default {
  options: { styleIsolation: "shared" }, //解除样式隔离
};
</script>

<style lang="scss" scoped>
:deep(textarea) {
  min-height: 200rpx !important;
  color: #303133 !important;
}

:deep(.u-form-item__body__left__content) {
  flex-direction: row-reverse;
  flex: initial;

  .u-form-item__body__left__content__required {
    right: -16rpx;
    left: initial;
  }
}

.formLayout {
  padding-bottom: 220rpx;
}

.form-item-container {
  background: #ffffff;
  padding: 14rpx 28rpx;
  margin-bottom: 24rpx;
  border-radius: 28rpx;
  box-shadow: 4rpx 8rpx 16rpx rgba(0, 0, 0, 0.02);
}

.placeholder {
  color: #b1b3b5;
  font-size: 30rpx;
  font-weight: 400;
}

.multiSelect {
  color: #b1b3b5;
  font-size: 26rpx;
  font-weight: 400;
}
</style>
