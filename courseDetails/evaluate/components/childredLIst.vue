<template>
  <Popup
    :show="show"
    @close="emit('close')"
  >
    <view class="child-list">
      <template v-if="tList.length > 0">
        <view class="list-title">老师列表</view>
        <view
          v-for="child in tList"
          :key="child.id"
          class="child-item"
          @click="handleSelect(child)"
        >
          {{ child.title }}
          <up-icon
            v-if="child.isSelect"
            name="checkbox-mark"
            size="40rpx"
            color="#367CFF"
          />
        </view>
      </template>
      <view
        v-else
        class="empty-tip"
      >
        <text>暂无数据</text>
      </view>
      <view class="list-title">儿童列表</view>
      <view class="search-box">
        <up-search
          v-model="searchText"
          placeholder="搜索儿童姓名"
          bgColor="#f5f5f5"
          shape="round"
          @search="search"
          @custom="search"
          @clear="search(clear)"
        />
      </view>
      <template v-if="cList.length > 0">
        <view
          v-for="child in cList"
          :key="child.id"
          class="child-item"
          @click="handleSelect(child)"
        >
          {{ child.title }}
          <up-icon
            v-if="child.isSelect"
            name="checkbox-mark"
            size="40rpx"
            color="#367CFF"
          />
        </view>
      </template>
      <view
        v-else
        class="empty-tip"
      >
        <text>暂无数据，请先添加儿童！</text>
      </view>
    </view>
  </Popup>
</template>

<script setup>
import { ref, computed, watch } from "vue";
import Popup from "@/components/Popup/Popup.vue";

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
    default: true,
  },
  childredList: {
    type: Array,
    default: () => [],
  },
  teacherList: {
    type: Array,
    default: () => [],
  },
  sendSearch: {
    type: Function,
    default: () => {},
  },
});
let arr = [];

const searchText = ref("");
const emit = defineEmits(["select", "close"]);
const search = (type) => {
  if (type === "clear") {
    props.sendSearch();
    return;
  }
  props.sendSearch(searchText.value);
};
const cList = computed(() => {
  return props.childredList;
});
const tList = computed(() => {
  return props.teacherList;
});

const handleSelect = (val) => {
  emit("select", val);
};
</script>

<style lang="scss" scoped>
.search-box {
  margin: 20rpx 0;
}

.list-title {
  width: 100%;
  box-sizing: border-box;
  padding: 20rpx 24rpx;
  font-size: 30rpx;
  font-weight: 600;
  display: flex;
  justify-content: space-between;
  align-items: center;
  & > text:last-child {
    color: #3f79ff;
  }
}
.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
  color: #999;
  font-size: 28rpx;
}
.child-list {
  padding: 0 0 40rpx 0;
  max-height: 60vh;
}

.child-item {
  padding: 24rpx;
  border-bottom: 1rpx solid #eee;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  &:last-child {
    border-bottom: none;
  }

  &:active {
    background-color: #f5f5f5;
  }
}
</style>
