<template>
  <view class="aipoilsh" @tap.stop.prevent="emit('click')">
    <image src="@/static/icon/ai.png" />
    <text>AI修正</text>
  </view>
</template>

<script setup>
import {} from "vue";
const emit = defineEmits(["click"]);
</script>

<style lang="scss" scoped>
.aipoilsh {
  width: fit-content;
  height: 60rpx;
  color: #3f79ff;
  font-size: 24rpx;
  font-weight: 500;
  padding: 0 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 34rpx;
  background: rgba(54, 124, 255, 0.06);
  image {
    width: 40rpx;
    height: 30rpx;
    margin-right: 6rpx;
  }
}
</style>
