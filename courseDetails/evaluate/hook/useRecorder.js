import { ref, onUnmounted, onMounted } from "vue";
import { useRecordAuthStore } from "./recordAuth";
import config from "@/common/config";
const recordAuthStore = useRecordAuthStore();
const WEB_SOCKET_URL = config[config.DEFINE_ENV].WEB_SOCKET_URL;
export default (evaForm, evaFormVlue, recorderLoading, recorderText) => {
    let ws = ref(null); // websocket实例
    let text = ref(''); // 输入框内容
    let currentUID = 0;
    let isAuthed = false; // 是否认证成功
    let oldText = ''; // 保存原始文本
    let timer = null; // 定时器
    let timer1 = null; // 计时器
    let timerTime = 60; // 单位秒
    // let start = 0
    // #ifdef MP-WEIXIN
    const recorderManager = uni.getRecorderManager(); // 获取录音管理器实例

    const options = {
        format: 'mp3', // 音频格式，支持PCM/WAV/AAC
        duration: 60000, // 最大值 600000（10 分钟）,默认值 60000（1 分钟）
        sampleRate: 16000, // 采样率，单位Hz
        numberOfChannels: 1, // 录音通道数，默认值 1
        frameSize: "4", // 指定帧大小，单位 KB。
    }
    function generateUID() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2, 9);
    }
    // 开始录音
    function startRecord() {
        console.log('开始录音');
        // recorderManager.start(options);
        startSending(options.format);
    }
    // 结束录音
    const stopRecord = async () => {
        try {
            // 停止当前录音
            recorderManager.stop();
            recorderLoading.value = false;
            recorderText.value = "语音转文字";
            timer1 && clearInterval(timer1);
        } catch (error) {
            uni.showToast({
                title: '停止录音时出错: ' + error,
                icon: 'error'
            })
        }
    }

    // 	已录制完指定帧大小的文件，会回调录音分片结果数据
    recorderManager.onFrameRecorded((res) => {
        const frameBuffer = res.frameBuffer;
        const audio = uni.arrayBufferToBase64(frameBuffer);
        const sendPackage = { audio: JSON.stringify(audio), isEnd: res.isLastFrame };
        ws.value.send({ data: JSON.stringify(sendPackage) });
        console.log("line-65 是否为最后一个包:", "color:#91bef0", res.isLastFrame);
        if (res.isLastFrame) {
            console.log("录音结束");
            // 录音结束，发送最后一帧数据
            const endPackage = JSON.stringify({ audio: null, isEnd: true });
            ws.value.send({ data: JSON.stringify(endPackage) });
            ws.value.close();
            console.log("line-75 结束包:", "color:#91bef0", endPackage);
            clearState();
        }

    })
    // 结束录音事件
    recorderManager.onStop((res) => {
        console.log("录音结束录音")
        // const fs = uni.getFileSystemManager();
        // const tempFilePath = res.tempFilePath; // 获取录音文件的临时路径
        // 播放录音
        // const innerAudioContext = uni.createInnerAudioContext();
        // innerAudioContext.src = tempFilePath;  // 设置录音文件路径
        // innerAudioContext.play();
    });

    // 开始连接websocket
    const startSending = (fileFormat) => {
        currentUID = generateUID();
        oldText = evaForm.value[evaFormVlue] || ''; // 保存原始文本
        try {
            // 建立websocket连接
            console.log(`${WEB_SOCKET_URL}/${currentUID}`);
            
            ws.value = uni.connectSocket({
                url: `${WEB_SOCKET_URL}/${currentUID}`,
                success: (res) => {
                    console.log('准备建立websocket连接...:', res)
                },
                fail: (err) => {
                    console.error('WebSocket连接失败:', err)
                    new Error(`WebSocket连接失败: ${err.errMsg}`)
                }
            })

            ws.value.onOpen(async () => {
                console.log('WebSocket连接成功, 发送第一帧：', fileFormat)
                const authPackage = {
                    format: fileFormat, // 添加格式参数
                    audio: null,
                    isEnd: false
                };
                ws.value.send({ data: JSON.stringify(authPackage) }); // 发送认证包
            })
            // WebSocket 响应事件
            ws.value.onMessage(res => {
                let data = JSON.parse(res.data); // 解析响应数据为 JSON 对象
                let { code, sequence } = data;
                // 响应第一帧
                if (!isAuthed) {
                    if (code === 1000) {
                        // 认证成功，开始传输
                        isAuthed = true; // 标记认证成功
                        console.log('认证成功，开始传输:', data);
                        recorderManager.start(options); // 开始录音
                        recorderLoading.value = true;
                        // recorderText.value = `${timerTime}s后结束，再次点击结束录音`
                        timer1 = setInterval(countDown, 1000);
                        // 60s后自动停止录音
                        timer = setTimeout(() => {
                            uni.$u.toast('录音时间超过60s，自动停止录音')
                            stopRecord();
                        }, 60000);
                        // clearInterval(timer1); // 清除之前的倒计时
                        
                    } else {
                        // 认证失败，关闭连接
                        console.log('认证失败，关闭连接:', data);
                        uni.$u.toast('认证失败，关闭连接')
                        ws.value.close();
                    }
                    return;
                }
                // 常规响应处理
                console.log('常规响应收到服务器消息:', data);
                if (data?.result?.length === 1) evaForm.value[evaFormVlue] = oldText + data.result[0].text || ''
                if (sequence < 0) recorderLoading.value = true;

            });
            ws.value.onError(err => {
                console.log('WebSocket连接错误:', err);
                uni.$u.toast('WebSocket连接错误,请联系管理员')
                recorderLoading.value = false;
                recorderText.value = '语音转文字';
                clearState();
            })
            // WebSocket 结束事件
            ws.value.onClose(res => {
                console.log('WebSocket连接已关闭:', res);
                clearInterval(timer);
                // 处理连接关闭的逻辑
            })
        }
        catch (e) {
            console.log('WebSocket连接失败:', e);
        }
    }

    // 60s倒计时
    function countDown() {
        timerTime--;
        recorderText.value = `${timerTime}s后结束，再次点击结束`
        if (timerTime <= 1) {
            timerTime = 60;
            clearInterval(timer1);
        }
    }
    // 清空状态
    function clearState() {
        // start = 0;
        // base64Data = '';
        currentUID = 0;
        isAuthed = false;
        oldText = '';
        timerTime = 60
        clearTimeout(timer);
        clearInterval(timer1);
    }
    onMounted(() => {
        recordAuthStore.initAuth(); // 初始化麦克风
    })
    onUnmounted(() => {
        stopRecord();
        // 将 WebSocket 实例设置为 null，以释放资源
        clearState();
    })

    return {
        startRecord,
        stopRecord,
        text,
        timerTime
    }
    // #endif
}