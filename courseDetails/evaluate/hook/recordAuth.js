import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useRecordAuthStore = defineStore('recordAuth', () => {
    const hasRecordAuth = ref(false)

    // 检查录音权限
    const checkRecordAuth = () => {
        return new Promise((resolve) => {
            wx.getSetting({
                success: (res) => {
                    hasRecordAuth.value = !!res.authSetting['scope.record']
                    resolve(hasRecordAuth.value)
                },
                fail: () => {
                    hasRecordAuth.value = false
                    resolve(false)
                }
            })
        })
    }

    // 请求录音权限
    const requestRecordAuth = async () => {
        try {
            await wx.authorize({ scope: 'scope.record' })
            hasRecordAuth.value = true
            return true
        } catch (error) {
            console.error('获取录音权限失败:', error)
            hasRecordAuth.value = false

            // 直接显示提示
            uni.showModal({
                title: '需要录音权限',
                content: '请在设置中允许小程序使用录音功能',
                confirmText: '去设置',
                success: (res) => {
                    if (res.confirm) {
                        openAuthSetting()
                    }
                }
            })
            return false
        }
    }

    // 初始化权限
    const initAuth = async () => {
        const hasAuth = await checkRecordAuth()
        if (!hasAuth) {
            return await requestRecordAuth()
        }
        return true
    }

    // 打开设置页面
    const openAuthSetting = () => {
        wx.openSetting({
            success(res) {
                hasRecordAuth.value = !!res.authSetting['scope.record']
            }
        })
    }

    return {
        hasRecordAuth,
        initAuth,
        openAuthSetting
    }
})
