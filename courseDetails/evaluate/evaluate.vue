<!-- 评价页面 -->
<template>
  <view
    class="evaluate-layout layout"
    :class="{ 'is-detail': !isDetail }"
  >
    <view class="col">
      <view class="title">过程性记录</view>
      <view class="content">
        <view class="content-title cort">
          <text>幼儿与教师的语言互动</text>
        </view>
        <view class="ctbox">
          <textarea
            ref="textareaRef"
            v-model="evaForm.childTeacherInteraction"
            confirmType="none"
            disableDefaultPadding
            placeholder="请输入幼儿与教师的语言互动"
            border="none"
            autoHeight
            autofocus
            :maxlength="-1"
            :cursorSpacing="100"
            :disabled="!isDetail"
            placeholderStyle="font-size:28rpx;font-weight:400; color: #B1B3B5"
            @input="onInput"
            @focus="onFocus"
          />
          <view class="ctbox-action">
            <recorderBtn
              v-if="isDetail"
              @click="onSpeech"
              :recorderText="recorderText"
              :recorderLoading="recorderLoading"
            />
            <EvaAiPolishBtn
              v-if="isDetail"
              @click="onAiPolish"
            />
          </view>
        </view>
      </view>

      <view class="content">
        <view class="content-title">照片</view>
        <view>
          <Upload
            type="image"
            :value="evaForm.childTeacherInteractionPhotoResources"
            @callback="callbackCTIPR"
            @emitDelFile="delFileCTIPR"
            :showDel="isDetail"
          />
        </view>
      </view>
      <view class="content">
        <view class="content-title">上传音视频</view>
        <view>
          <Upload
            type="video"
            :value="evaForm.childTeacherInteractionVideoResources"
            @callback="callbackCTIVR"
            @emitDelFile="delFileCTIVR"
            :showDel="isDetail"
          >
            <image
              src="https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/coures_evaluate_up-a-v.png"
              style="width: 105rpx; height: 74rpx"
            ></image>
          </Upload>
        </view>
      </view>
    </view>
    <view class="col">
      <view class="title">上传作品</view>
      <view class="content">
        <view style="margin-top: 28rpx">
          <Upload
            type="image"
            :value="evaForm.childArtworkResources"
            @callback="callbackCAR"
            @emitDelFile="delFileCAR"
            :showDel="isDetail"
          >
            <view
              style="display: flex; flex-direction: column; align-items: center"
            >
              <image
                src="@/static/icon/u-icon.png"
                style="width: 37rpx; height: 31rpx"
              />
              <text style="font-size: 21rpx; font-weight: 400; color: #808080"
                >上传作品</text
              >
            </view>
          </Upload>
        </view>
      </view>
    </view>
    <view class="col">
      <view class="title">嵌入式儿童评估</view>
      <view class="content">
        <view
          class="col3-rate"
          style="margin-top: 50rpx"
        >
          <view class="title">幼儿对本活动的兴趣程度</view>
          <view class="flex-ac">
            <up-rate
              :readonly="!isDetail"
              v-model="interestScoreValue"
              @change="changeinterestScore"
              size="44rpx"
              style="margin-right: 32rpx"
              activeColor="#FAE665"
            />

            <view>{{ rateTips[evaForm.interestScore] }}</view>
          </view>
        </view>
        <view
          class="col3-rate"
          style="margin-top: 32rpx"
        >
          <view class="title">本活动的难度</view>
          <view class="flex-ac">
            <up-rate
              :readonly="!isDetail"
              v-model="difficultyScoreValue"
              @change="changediff"
              size="44rpx"
              style="margin-right: 32rpx"
              activeColor="#FAE665"
            />
            <view>{{ rateDifficulty[evaForm.difficultyScore] }}</view>
          </view>
        </view>
      </view>
    </view>
    <view class="col">
      <view class="title">活动实施反思</view>
      <view class="content">
        <view
          class="content-title"
          style="font-size: 28rpx; margin-top: 32rpx"
          >活动亮点</view
        >
        <view>
          <up-textarea
            :disabled="!isDetail"
            confirmType="none"
            v-model="evaForm.activityHighlight"
            placeholder="请输入活动亮点"
            autoHeight
            height="130rpx"
            disableDefaultPadding
            maxlength="-1"
          ></up-textarea>
        </view>
      </view>
      <view class="content">
        <view
          class="content-title"
          style="font-size: 28rpx; margin-top: 32rpx"
          >活动存在的问题</view
        >
        <view>
          <up-textarea
            confirmType="none"
            :disabled="!isDetail"
            v-model="evaForm.activityProblem"
            placeholder="请输入活动存在的问题"
            autoHeight
            height="130rpx"
            disableDefaultPadding
            maxlength="-1"
          ></up-textarea>
        </view>
      </view>
      <view class="content">
        <view
          class="content-title"
          style="font-size: 28rpx; margin-top: 32rpx"
          >改进措施</view
        >
        <view>
          <up-textarea
            :disabled="!isDetail"
            confirmType="none"
            v-model="evaForm.improvementMeasure"
            placeholder="请输入改进措施"
            autoHeight
            height="130rpx"
            disableDefaultPadding
            maxlength="-1"
          ></up-textarea>
        </view>
      </view>
    </view>
    <view
      class="action-btn"
      v-if="isDetail"
      :style="{ height: heightBottom + 52 + 'px' }"
    >
      <up-button
        type="primary"
        text="保存"
        color="#367CFF"
        round
        shape="circle"
        @click="send"
      ></up-button>
    </view>
    <ChildredList
      :show="showChildList"
      :childredList="popupData.childrenData"
      :teacherList="popupData.teacherData"
      @select="handleChildSelect"
      @close="showChildList = false"
      :sendSearch="getStaffFn"
    />
  </view>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import {
  getevaDetails,
  setevaDetails,
  getChildrenList,
  getTeacherList,
  getAiPolishCont,
} from "@/courseDetails/api/evaluate.js";
import upButton from "@/uni_modules/uview-plus/components/u-button/u-button.vue";
import ChildredList from "./components/childredLIst.vue";
import recorderBtn from "./components/recorderBtn";
import EvaAiPolishBtn from "./components/EvaAiPolishBtn";
import useRecorder from "./hook/useRecorder.js";

const popupData = reactive({
  childrenData: [],
  teacherData: [],
});
const rateTips = {
  2: "非常不感兴趣",
  4: "比较不感兴趣",
  6: "一般",
  8: "比较感兴趣",
  10: "非常感兴趣",
};
const rateDifficulty = {
  2: "非常简单",
  4: "比较简单",
  6: "一般",
  8: "比较难",
  10: "非常难",
};
let evaForm = ref({
  interestScore: "",
  subjectActivityChildInteractions: [],
  childTeacherInteraction: "",
});

let recorderLoading = ref(false); // 等待录音转文字完成
let recorderText = ref("语音转文字"); // 录音按钮文字
const useRecorders = useRecorder(
  evaForm,
  "childTeacherInteraction",
  recorderLoading,
  recorderText
);
let difficultyScoreValue = ref(0);
let interestScoreValue = ref(0);
let isDetail = ref(true); // 是否是提交后的详情
let id = 0;
let isDes = ref("");
let gradeId = ref("");
let showChildList = ref(false);
let autoHeight = ref(false);
let textareaRef = ref(null); // 引用textarea组件
let cursorIndex = ref(0);

// 监听@出现后，打开学生列表
const onInput = (e) => {
  const { value, cursor } = e.detail;
  cursorIndex.value = cursor;
  console.log(e);
  // evaForm.value.childTeacherInteraction = value;
  // 在当前光标处右边是否有@
  if (value[cursor - 1] == "@") {
    showChildList.value = true;
    uni.hideKeyboard();
  }
};
// 聚焦
const onFocus = (e) => {
  console.log(e);
};

// 选择学生后，替换@+学生名
const handleChildSelect = (val) => {
  console.log(val);
  let str = evaForm.value.childTeacherInteraction;
  let start = cursorIndex.value - 1;
  let end = cursorIndex.value;
  evaForm.value.childTeacherInteraction = `${str.substring(0, start)}@${
    val.title
  }${str.substring(end)}`;
  showChildList.value = false;
};

// start 语音
const onSpeech = () => {
  recorderLoading.value
    ? useRecorders.stopRecord()
    : useRecorders.startRecord();
};
// end 语音
const onAiPolish = async () => {
  if (evaForm.value.childTeacherInteraction == "") {
    return uni.showToast({
      title: "请输入内容",
      icon: "error",
    });
  }
  uni.showLoading({
    title: "修正中，请勿操作!",
    mask: true,
  });
  const data = {
    classId: uni.getStorageSync("USER_INFO").currentClassId,
    text: evaForm.value.childTeacherInteraction,
  };
  const res = await getAiPolishCont(data);
  if (res.status == 0) {
    evaForm.value.childTeacherInteraction = res.data;
  }
  uni.hideLoading();
};

onLoad((option) => {
  uni.setStorageSync("evaluate", true);
  id = option.id;
  isDes.value = option.isDes;
  gradeId.value = option.gradeId;
  if (option.isDetail)
    isDetail.value = option.isDetail == "true" ? true : false;
  getevaDetails(option.id).then((res) => {
    console.log(res);
    evaForm.value = res.data;
    difficultyScoreValue.value = evaForm.value.difficultyScore / 2;
    interestScoreValue.value = evaForm.value.interestScore / 2;
  });
});
onMounted(async () => {
  getStaffFn();
  await nextTick();
  setTimeout(() => {
    autoHeight.value = true;
  }, 0);
});

// 获取所有人员
const getStaffFn = async (title) => {
  // childrenData.value = res.data;
  if (title) {
    getChildrenListFn(title);
    return;
  }
  getTeacherListFn();
  getChildrenListFn(title);
};
// 获取所有儿童列表
const getChildrenListFn = async (title) => {
  let form = {
    current: 1,
    pageSize: 999,
    state: 1,
    classId: uni.getStorageSync("classId"), // 班级id
  };
  if (title) form.title = title;
  let res = await getChildrenList(form);
  if (res.status == 0) {
    let childRes = res.data.map((item) => {
      return {
        id: item.id,
        title: item.title,
      };
    });
    popupData.childrenData = childRes;
  } else {
    uni.showToast({
      title: res.message || "获取教师列表失败",
      icon: "error",
    });
  }
};
// 获取当前班级老师列表
const getTeacherListFn = async () => {
  const classId = uni.getStorageSync("classId");
  let form = {
    classId, // 班级id
  };
  let res = await getTeacherList(form);
  if (res.status == 0) {
    // 过滤掉当前isTeacher == 0的数据
    popupData.teacherData = res.data.reduce((acc, item) => {
      if (item.isTeacher === 1) {
        acc.push({
          id: item.id,
          title: item.name,
          isTeacher: item.isTeacher,
        });
      }
      return acc;
    }, []);
    console.log(popupData.teacherData);
  } else {
    uni.showToast({
      title: res.message || "获取教师列表失败",
      icon: "error",
    });
  }
};

const delFileCTIPR = (item, index) => {
  console.log(item);
  evaForm.value.childTeacherInteractionPhotoResources.splice(index, 1);
};
// 照片的回调
const callbackCTIPR = (list) => {
  evaForm.value.childTeacherInteractionPhotoResources.push(...list);
};

// 音视频
const delFileCTIVR = (item, index) => {
  console.log(item, index);
  evaForm.value.childTeacherInteractionVideoResources.splice(index, 1);
};
const callbackCTIVR = (list) => {
  evaForm.value.childTeacherInteractionVideoResources.push(...list);
};

// 幼儿作品
const delFileCAR = (item, index) => {
  evaForm.value.childArtworkResources.splice(index, 1);
};
const callbackCAR = (list) => {
  console.log(list);

  evaForm.value.childArtworkResources.push(...list);
};

function changeinterestScore(e) {
  evaForm.value.interestScore = e * 2;
}

function changediff(e) {
  evaForm.value.difficultyScore = e * 2;
}
let heightBottom = ref(0);
const { safeAreaInsets } = uni.getSystemInfoSync();
heightBottom.value = safeAreaInsets.bottom;

// 提交评价
async function send() {
  // 提取修改照片的id
  evaForm.value.childTeacherInteractionPhotoResourceIds =
    evaForm.value.childTeacherInteractionPhotoResources.map(
      (item) => item.id
    ) || [];
  evaForm.value.childTeacherInteractionVideoResourceIds =
    evaForm.value.childTeacherInteractionVideoResources?.map(
      (item) => item.id
    ) || [];
  evaForm.value.childArtworkResourceIds =
    evaForm.value.childArtworkResources.map((item) => item.id) || [];

  // 提取对话中提到的孩子ID
  const mentionedChildIds = new Set();
  const regex = /@([^：]+)：/g;
  let match;

  while ((match = regex.exec(evaForm.value.childTeacherInteraction)) !== null) {
    const childName = match[1];
    // 在popupData.childrenData中查找对应孩子的ID
    const child = popupData.childrenData.find((c) => c.title === childName);
    if (child) {
      mentionedChildIds.add(child.id);
    }
  }

  const form = {
    id: evaForm.value.id,
    childTeacherInteraction: evaForm.value.childTeacherInteraction,
    childTeacherInteractionChildIds: [...mentionedChildIds],
    interestScore: evaForm.value.interestScore,
    difficultyScore: evaForm.value.difficultyScore,
    activityHighlight: evaForm.value.activityHighlight,
    activityProblem: evaForm.value.activityProblem,
    improvementMeasure: evaForm.value.improvementMeasure,
    childTeacherInteractionPhotoResourceIds:
      evaForm.value.childTeacherInteractionPhotoResourceIds,
    childTeacherInteractionVideoResourceIds:
      evaForm.value.childTeacherInteractionVideoResourceIds,
    childArtworkResourceIds: evaForm.value.childArtworkResourceIds,
  };

  let res = await setevaDetails(form);
  if (res.status == 0) {
    uni.$u.toast("修改成功");
    if (isDes.value) {
      uni.navigateTo({
        url: `/courseDetails/activityDetails/activityDetails?&id=${id}&gradeId=${gradeId.value}`,
      });
      return;
    }
    uni.redirectTo({
      url: `/courseDetails/evaluate/evaluate?id=${id}&isDetail=false`,
    });
  }
}
</script>
<script>
export default {
  options: { styleIsolation: "shared" }, // 解除样式隔离
};
</script>
<style lang="scss" scoped>
@import "@/uni_modules/uview-plus/index.scss";
// :deep(.u-textarea) {
//   min-height: 140rpx; // 解决小程序中一开始进页面textarea自动高度问题
// }
.none {
  font-size: 28rpx;
  font-weight: 400;
  color: rgba(177, 179, 181, 1);
}
.ctbox {
  border: 1px solid #e6e6e6;
  border-radius: 12px;
  box-sizing: border-box;
  padding: 24rpx;
  position: relative;
  :deep(textarea) {
    width: 100%;
    min-height: 338rpx;
    font-size: 28rpx;
    font-weight: 400;
  }
  &-action {
    display: flex;
    align-items: center;
    flex-direction: row-reverse;
    gap: 16rpx;
    margin-top: 24rpx;
  }
}
.childInteractions {
  display: flex;
  font-size: 28rpx;
  font-weight: 400;
  margin: 20rpx 0;
  &-left {
    // padding: 18rpx 0;
    display: block;
    width: 166rpx;
  }
  :deep(.uni-textarea-textarea) {
    font-size: 28rpx;
    font-weight: 400;
  }
  // :deep(.u-textarea--disabled) {
  //   background: white !important;
  // }
}
.cort {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

// @import '@/common/css/index.scss';

.evaluate-layout {
  padding: 0 32rpx;
  padding-bottom: 192rpx;
  overflow-y: auto;

  .action-btn {
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 50;
    width: 100%;
    box-sizing: border-box;
    padding: 16rpx 32rpx;
    background-color: #fff;
  }

  .col {
    border-radius: 28rpx;
    background: #fff;
    box-shadow: 4rpx 8rpx 16rpx rgba(0, 0, 0, 0.02);
    margin-top: 32rpx;
    padding: 32rpx;
    overflow: hidden;
    box-sizing: border-box;

    .title {
      /** 文本1 */
      font-size: 34rpx;
      font-weight: 600;
      color: rgba(51, 51, 51, 1);
      border-bottom: 2px solid #eeeeee;
      padding-bottom: 26rpx;
    }

    .col3-rate {
      .title {
        font-size: 28rpx;
        font-weight: 500;
        color: rgba(128, 128, 128, 1);
        border-bottom: none;
        padding-bottom: 40rpx;
      }

      padding-bottom: 32rpx;
      border-bottom: 2px solid #eeeeee;
    }

    .content {
      .content-title {
        font-size: 28rpx;
        font-weight: 600;
        margin: 32rpx 0 26rpx 0;
      }
    }
  }
}

.is-detail {
  padding-bottom: 32rpx !important;
}
</style>
