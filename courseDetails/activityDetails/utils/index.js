import { reactive } from "vue"
export default () => {
    // 部分列表
    const activityList = reactive([

        {
            name: '活动名称',
            value: '',
            text: 'name'
        },
        {
            name: '预设生成',
            value: '',
            text: 'plannedGeneration'
        },
        {
            name: '作者',
            value: '',
            text: 'author'
        },
        {
            name: '组织形式',
            value: '',
            text: 'organizationForm'
        },
        {
            name: '活动时长',
            value: '',
            text: 'durationInMinutes'
        },
        {
            name: '实施时间',
            value: '',
            text: 'implementedAt'
        },
        {
            name: '活动背景和目的',
            value: '',
            text: 'activityIdea'
        },
        {
            name: '教学目标',
            value: '',
            text: 'teachingObjective'
        },
        {
            name: '活动重点',
            value: '',
            text: 'activityKeyPoint'
        },
        {
            name: '活动难点',
            value: '',
            text: 'activityDifficulty'
        }
    ])
    return {
        activityList
    }
}