<template>
  <view
    class="activity-details-layout layout"
    :style="{ paddingBottom: heightBottom + 68 + 'px' }"
  >
    <view
      class="bg-img"
      :style="{ display: !isShowLoading ? 'block' : 'none' }"
    >
      <view
        v-show="ispalyVideo"
        class="activity-details-title"
      >
        <view class="theme">{{ _theme }}</view>
        <view class="stage">{{ allformData.subjectStage?.title }}</view>
        <view>{{ allformData.name }}</view>
      </view>
      <view class="bg-vague">
        <view class="activity-details-content">
          <view
            class="col"
            style="margin-top: 0"
          >
            <view class="title">
              <text>基本信息</text>
              <view>
                <text
                  v-if="!disabled"
                  class="title-right"
                  @click="gotoPage('AIActivity')"
                  >AI教案</text
                >
                <text
                  v-if="!disabled"
                  class="title-right"
                  @click="gotoPage('editActivity')"
                  >编辑教案</text
                >
              </view>
            </view>
            <view
              class="content flex-ac"
              style="align-items: flex-start"
              v-for="(item, index) in activityList"
              :key="index"
            >
              <view
                class="content-title"
                style="width: 160rpx"
                >{{ item.name }}:</view
              >
              <view
                class="content-box"
                style="flex: 1; white-space: pre-line"
                >{{ item.value || "-" }}
              </view>
            </view>

            <!-- 核心经验 -->
            <view class="hxjy">
              <view
                class="content-title"
                style="margin-bottom: 16rpx"
                >核心经验</view
              >
              <view
                v-if="allformData?.matrices && allformData?.matrices.length > 0"
              >
                <view
                  class="content-hxjy-box flex-ac"
                  v-for="(item, index) in allformData?.matrices"
                  :key="index"
                >
                  <view class="icon">经验{{ index + 1 }}</view>
                  <view class="flex-f1">
                    <view
                      class="content no-bb-content flex-ac"
                      style="align-items: flex-start; padding: 0 0 20rpx 0"
                    >
                      <up-tag
                        style="margin-right: 40rpx"
                        text="领域"
                        plain
                        plainFill
                        size="mini"
                        color="#608BF0"
                        bgColor="#eff3fd"
                        borderColor="#eff3fd"
                      ></up-tag>
                      <view class="flex-f1 no-bb-content text-content">{{
                        item.matrix1?.title || "-"
                      }}</view>
                    </view>
                    <view
                      class="content no-bb-content flex-ac"
                      style="align-items: flex-start; padding: 0 0 20rpx 0"
                    >
                      <up-tag
                        style="margin-right: 40rpx"
                        text="维度"
                        type="warning"
                        plain
                        plainFill
                        size="mini"
                        color="#F0914D"
                        bgColor="#fdf4ed"
                        borderColor="#fdf4ed"
                      ></up-tag>
                      <view
                        class="flex-f1 no-bb-content text-content"
                        style=""
                        >{{ item.matrix2?.title || "-" }}
                      </view>
                    </view>
                    <view
                      class="content no-bb-content flex-ac"
                      style="align-items: flex-start; padding: 0 0 20rpx 0"
                    >
                      <up-tag
                        style="margin-right: 18rpx"
                        text="子维度"
                        type="success"
                        plain
                        plainFill
                        size="mini"
                        color="#54BA6A"
                        bgColor="#edf8f0"
                        borderColor="#edf8f0"
                      ></up-tag>
                      <view class="flex-f1 no-bb-content text-content">{{
                        item.matrix3?.title || "-"
                      }}</view>
                    </view>
                    <view
                      class="content no-bb-content flex-ac"
                      style="align-items: flex-start; padding: 0"
                    >
                      <up-tag
                        style="margin-right: 40rpx"
                        text="指标"
                        plain
                        plainFill
                        size="mini"
                        color="#6E74E6"
                        bgColor="#f0f1fc"
                        borderColor="#f0f1fc"
                      ></up-tag>
                      <view class="flex-f1 text-content">{{
                        item.target?.title || "-"
                      }}</view>
                    </view>
                  </view>
                </view>
              </view>
              <text v-else>-</text>
            </view>

            <view class="col-content">
              <view class="content-title">教学准备、资源或环境支持</view>
              <view class="content-box">{{
                allformData.resourcesSupport || "-"
              }}</view>
            </view>

            <!-- 图片资源 -->
            <view class="col-content">
              <view class="content-title"
                >教学准备、资源或环境支持（附件）</view
              >
              <view class="content-box">
                <view v-if="imageList1.length > 0">
                  <view
                    v-for="(item, index) in imageList1"
                    :key="index"
                  >
                    <gurudin-audio
                      v-if="item.category == 5 && item.type == 'mp3'"
                      :src="item.uri"
                      :name="item.filename"
                      theme="dark"
                    ></gurudin-audio>
                    <video
                      v-if="item.category == 5 && item.type == 'mp4'"
                      :src="item.uri"
                      controls
                      enable-play-gesture
                    ></video>
                    <view
                      class="flex-ac"
                      v-if="
                        albumList.length == 0 &&
                        item.category != 5 &&
                        item.category == 1
                      "
                    >
                      <text style="margin-right: 32rpx">{{
                        item.filename
                      }}</text>
                      <up-icon
                        name="download"
                        size="28"
                      ></up-icon>
                    </view>
                  </view>
                </view>
                <up-album
                  v-if="albumList.length != 0"
                  :urls="albumList"
                  keyName="uri"
                  multipleSize="100"
                />
                <text v-else>暂未上传</text>
              </view>
            </view>
          </view>
          <!-- 教学流程 -->
          <view
            class="col"
            v-if="
              allformData.teachingSteps && allformData?.teachingSteps.length > 0
            "
          >
            <view class="title no-bb-content">教学流程</view>
            <view
              class="jxlc col-content"
              v-for="(item, index) in allformData.teachingSteps"
              :key="index"
            >
              <view class="position-title">环节{{ index + 1 }}</view>
              <view
                class="flex-ac jxlc-item"
                v-if="item.detailedProcess"
              >
                <!-- <view class="jxlc-content-title">具体流程:</view> -->
                <view class="jxlc-content-box">{{ item.detailedProcess }}</view>
              </view>
              <!-- <view class="flex-ac jxlc-item" v-if="item.note">
                                <view class="jxlc-content-title">活动备注:</view>
                                <view class="jxlc-content-box">{{ item.note }}</view>
                            </view> -->
            </view>
          </view>

          <!-- 评价详情 -->

          <view class="col">
            <view class="title">
              <text>评价详情</text>
              <text
                v-if="!disabled"
                class="title-right"
                @click="gotoPage('evaluate')"
                >编辑评价</text
              >
            </view>
            <evaluate
              :evaForm="allformData"
              :stuList="stuList"
              @playVideo="playVideo"
            ></evaluate>
          </view>
        </view>
      </view>
    </view>
    <up-loading-icon
      style="margin-top: 100rpx"
      v-if="isShowLoading"
    />
    <view
      class="action-btn flex-ac"
      :style="{ paddingBottom: heightBottom + 'px' }"
      v-if="ispalyVideo"
    >
      <up-button
        class="action-btn-item"
        text="上一个活动"
        shape="circle"
        @click="switchDetails('prev')"
      />
      <up-button
        class="action-btn-item"
        text="下一个活动"
        shape="circle"
        @click="switchDetails('next')"
        color="#367CFF"
      />
    </view>
  </view>
</template>

<script setup>
import { ref } from "vue";
import { onLoad, onShareAppMessage, onShow } from "@dcloudio/uni-app";
import {
  getActivityDetail,
  getActivityDetailContent,
} from "../api/editActivity.js";
import gurudinAudio from "../components/gurudin-audio/gurudin-audio.vue";
import utils from "./utils";
import { getDICT, sharePageObj } from "@/utils";
import evaluate from "./components/evaluate.vue";
import dayjs from "dayjs";
import { getChildrenList } from "@/courseDetails/api";
let _id = ref("");
let allformData = ref({});
let _theme = ref(""); // 主题名
// let _stage = ref(""); // 阶段名
let _gradeId = ref(""); // 课程ID
let imageList1 = ref([]);
let imageList2 = ref([]);
let albumList = ref([]);
let ispalyVideo = ref(true);
let isShowLoading = ref(false); // 是否显示加载中
const { safeAreaInsets } = uni.getSystemInfoSync();
let heightBottom = ref(safeAreaInsets.bottom);
let disabled = ref(false);

const { activityList } = utils();

onLoad((option) => {
  const { id, theme, gradeId } = option;
  if (option.disabled) disabled.value = option.disabled;
  _id.value = id;
  _theme.value = theme || uni.getStorageSync("themeName");
  // _stage.value = stage;
  _gradeId.value = gradeId;
});
onShareAppMessage(() =>
  sharePageObj({
    path:
      "/courseDetails/activityDetails/activityDetails?id=" +
      _id.value +
      "&theme=" +
      _theme.value +
      "&stage=" +
      allformData?.subjectStage?.title +
      "&gradeId=" +
      _gradeId.value,
  })
);

const playVideo = (val) => {
  ispalyVideo.value = val;
};

let nextId = ref(0); //下一个活动ID
let prevId = ref(0); //上一个活动ID

const switchDetails = async (type) => {
  // 点击下一个
  if (type == "next") {
    if (nextId.value != 0) {
      getPrevAndNextActivityId(nextId.value);
      getDetails(nextId.value);
      return;
    }
    uni.$u.toast("已经是最后一个活动了");
  }
  if (type == "prev") {
    if (prevId.value != 0) {
      getPrevAndNextActivityId(prevId.value);
      getDetails(prevId.value);
      return;
    }
    uni.$u.toast("已经是第一个活动了");
  }
};

// 编辑评价/活动
const gotoPage = (type) => {
  let url = "";
  if (type == "editActivity") {
    url = `/courseDetails/${type}/${type}?subjectStageId=${allformData.value.subjectStageId}&subjectId=${allformData.value.subjectId}&gradeId=${_gradeId.value}&id=${allformData.value.id}&author=${allformData.value.author}&isDes=true`;
  }
  if (type == "evaluate") {
    url = `/courseDetails/${type}/${type}?id=${allformData.value.id}&isDes=true&gradeId=${_gradeId.value}`;
  }
  if (type == "AIActivity") {
    url = `/courseDetails/aiCreateDetails/aiCreateDetails?id=${allformData.value.id}&subjectId=${allformData.value.subjectId}`;
  }
  uni.navigateTo({ url });
};

// 获取上下活动ID
let getPrevAndNextActivityId = async (id) => {
  let data = {
    id: id || _id.value,
    subjectId: uni.getStorageSync("subjectId"),
  };
  let res = await getActivityDetailContent(data);

  const { nextActivityId, prevActivityId } = res.data;
  if (res.status == 0) {
    nextId.value = nextActivityId;
    prevId.value = prevActivityId;
  }
};

let getDetails = async (id) => {
  isShowLoading.value = true;
  let res = await getActivityDetail(id || _id.value);
  isShowLoading.value = false;
  allformData.value = {};
  allformData.value = res.data;
  allformData.value.interestScoreValue = res.data.interestScore / 2;
  allformData.value.difficultyScoreValue = res.data.difficultyScore / 2;
  allformData.value.stageName = activityList.forEach((item) => {
    for (let key in allformData.value) {
      if (item.text == key) {
        if (key == "durationInMinutes") {
          let Minutes = res.data[key] || "-";
          item.value = Minutes + " " + "分钟";
          return;
        }
        item.value = res.data[key] || "-";
      }
    }
  });
  let data = await getDICT("all");
  imageList1.value = [];
  albumList.value = [];
  allformData.value.electronicPreparationResources.forEach((item, index) => {
    item.type = item.filename.split(".")[1];
    imageList1.value.push(item);
    if (
      (item.category == 1 && item.type == "jpg") ||
      item.type == "jpeg" ||
      item.type == "png"
    ) {
      albumList.value.push(item);
    }
  });
  imageList2.value = [];
  allformData.value.physicalPreparationResources.forEach((item, index) => {
    imageList2.value.push(item.uri);
  });

  activityList[1].value =
    data.SubjectActivityPlannedOrGenerationEnumDesc[
      allformData.value.plannedGeneration
    ];
  activityList[3].value =
    data.SubjectActivityOrganizationFormEnumDesc[
      allformData.value.organizationForm
    ];
  // 通过dayjs判断allformData.value.implementedAt是否是时间格式
  if (dayjs(allformData.value.implementedAt).isValid()) {
    activityList[5].value = dayjs(allformData.value.implementedAt).format(
      "YYYY-MM-DD"
    );
  }
};
const stuList = ref([]);
// 获取当前班级学生
let getStudentLists = async () => {
  let res = await getChildrenList({
    current: 1,
    pageSize: 999,
    state: 1,
    classId: uni.getStorageSync("classId"), // 班级id
  });
  if (res.status == 0) {
    stuList.value = res.data;
  }

  console.log(res, "当前班级学生");
};

onShow(() => {
  getDetails();
  getPrevAndNextActivityId();
  getStudentLists();
});
</script>
<script>
export default {
  options: { styleIsolation: "shared" }, // 解除样式隔离
};
</script>

<style lang="scss" scoped>
.activity-details-layout {
  height: 100%;
  overflow-y: auto;

  .imgfj {
    width: 200rpx;
    height: 200rpx;
  }

  .bg-img {
    width: 100%;
    background: url("https://c.mypacelab.com/vxmp/img/eventDetails_bg_3x.png")
      no-repeat;
    background-position: 100% auto;
    background-size: contain;

    .img {
      width: 100%;
      height: 698rpx;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 0;
    }
  }

  .action-btn {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    box-sizing: border-box;
    padding: 16rpx 32rpx;
    background: #fff;
    opacity: 1;
    z-index: 10;
    display: flex;
    align-items: center;

    .action-btn-item {
      width: 332rpx;
      height: 80rpx;
      font-size: 30rpx;
      font-weight: 400;
      margin: 0 8rpx 16rpx 8rpx;
    }
  }

  .activity-details-title {
    padding-left: 36rpx;
    margin-bottom: 32rpx;
    width: 450rpx;
    font-size: 38rpx;
    font-weight: 600;
    white-space: wrap;
    position: relative;
    z-index: 2;
    padding-top: 88rpx;

    .theme {
      font-size: 24rpx;
      font-weight: 500;
      color: rgba(128, 128, 128, 1);
    }

    .stage {
      font-size: 24rpx;
      font-weight: 500;
      color: rgba(51, 51, 51, 1);
    }
  }

  .bg-vague {
    width: 100%;
    height: 100%;
    padding: 0 32rpx;
    padding-top: 32rpx;
    box-sizing: border-box;
    border-radius: 40rpx 40rpx 0rpx 0rpx;
    background: rgba(255, 255, 255, 0.1);
    box-shadow: inset 0rpx 12rpx 24rpx rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(40rpx);

    .activity-details-content {
      width: 100%;
      box-sizing: border-box;
    }
  }

  .col {
    border-radius: 28rpx;
    background: #fff;
    box-shadow: 4rpx 8rpx 16rpx rgba(0, 0, 0, 0.02);
    margin-top: 32rpx;
    padding: 32rpx;
    overflow: hidden;
    box-sizing: border-box;

    .jxlc {
      position: relative;
      box-sizing: border-box;
      height: 300rpx;
      background: #f9f9f9;
      border-radius: 12rpx;
      padding: 26rpx 12rpx 16rpx 12rpx;
      font-size: 28rpx;
      height: 100%;
      margin-top: 54rpx !important;

      .position-title {
        position: absolute;
        top: -27rpx;
        left: 0;
        width: 98rpx;
        height: 44rpx;
        font-size: 28rpx;
        font-weight: 600;
        color: white;
        background: #3f79ff;
        border-radius: 8.52rpx;
        text-align: center;
        padding: 5rpx 6rpx;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .jxlc-item {
        align-items: flex-start;
        margin: 28rpx 0;
        white-space: pre-line;
      }

      .jxlc-content-title {
        width: 162rpx;
        font-weight: 600;
        // margin-right: 32rpx;
        color: rgba(51, 51, 51, 1);
      }

      .jxlc-content-box {
        font-weight: 400;
        flex: 1;
        color: rgba(51, 51, 51, 1);
        margin-left: 32rpx;
      }
    }

    .title {
      font-size: 30rpx;
      font-weight: 600;
      color: rgba(51, 51, 51, 1);
      border-bottom: 1px solid #eeeeee;
      padding-bottom: 32rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .title-right {
        font-size: 24rpx;
        font-weight: 600;
        padding: 10rpx 16rpx;
        text-align: center;
        color: #3f79ff;
        background: #eef2f9;
        border-radius: 52.8rpx;
        margin-right: 16rpx;
        &:last-child {
          margin-right: 0;
        }
      }
    }

    .col2 {
      padding: 32rpx 0;
      border-bottom: 2px solid #eeeeee;

      .col2-item {
        display: flex;
        align-items: center;
      }
    }

    .col3-rate {
      .title {
        font-size: 28rpx;
        font-weight: 500;
        color: rgba(128, 128, 128, 1);
        border-bottom: none;
        padding-bottom: 40rpx;
      }

      padding-bottom: 32rpx;
      border-bottom: 2px solid #eeeeee;
    }

    .content {
      padding: 32rpx 0;
      border-bottom: 1px solid #eeeeee;
      min-height: 46rpx;

      .content-title {
        font-size: 28rpx;
        font-weight: 600;
        color: rgba(51, 51, 51, 1);
        margin-right: 16rpx;
      }

      .content-hxjy-box {
        .icon {
          padding: 0 27rpx 0 12rpx;
          font-size: 24rpx;
          font-weight: 500;
          color: rgba(51, 51, 51, 1);
        }
      }

      .content-box {
        font-size: 28rpx;
        font-weight: 400;
        color: rgba(51, 51, 51, 1);
      }
    }

    .col-content {
      margin-top: 16rpx;
      padding-bottom: 16rpx;
      border-bottom: 1px solid #eeeeee;

      .content-title {
        font-size: 28rpx;
        font-weight: 600;
        color: rgba(51, 51, 51, 1);
      }

      .content-box {
        font-size: 28rpx;
        font-weight: 400;
        color: rgba(51, 51, 51, 1);
        padding-top: 12rpx;
      }
    }

    .hxjy {
      padding: 16rpx 0;
      border-bottom: 1px solid #eeeeee;

      .content-title {
        font-size: 28rpx;
        font-weight: 600;
        color: rgba(51, 51, 51, 1);
        margin-right: 16rpx;
      }

      .content-hxjy-box {
        margin-top: 20rpx;
        background: linear-gradient(
          90deg,
          rgba(228, 244, 255, 0.4) 0%,
          #ffffff 22%
        );
        border-radius: 23.78rpx 0rpx 0rpx 23.78rpx;
        padding: 26rpx 0;

        .icon {
          width: 120rpx;
          text-align: center;
          font-size: 24rpx;
          font-weight: 500;
          color: rgba(51, 51, 51, 1);
        }
      }

      .text-content {
        font-size: 28rpx;
        font-weight: 400;
        color: rgba(51, 51, 51, 1);
      }

      .content-box {
        height: 100%;
        font-size: 28rpx;
        font-weight: 400;
        color: rgba(51, 51, 51, 1);
      }
    }
  }
}

::v-deep .u-tag {
  height: 32rpx;
  padding: 4rpx 8rpx;
}

.no-bb-content {
  border-bottom: none !important;
  height: 100% !important;
}
</style>
