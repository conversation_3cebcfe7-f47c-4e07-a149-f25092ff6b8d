<template>
  <BaseLayout2
    navTitle="标注姓名"
    :footerStyle="{
      background: '#fff',
      paddingBottom: 'env(safe-area-inset-bottom)',
    }"
  >
    <view
      class="evaluate-marking"
      v-if="isShowPage"
    >
      <view class="tips">选择图中存在的孩子</view>
      <view class="topImg">
        <view
          class="topImg-item"
          v-for="(item, index) in popupData.info"
          :key="index"
        >
          <view
            class="flex-img"
            v-if="item.category == 1"
            @tap="onpenPopup(item, index)"
          >
            <image
              :src="item?.uri"
              mode="aspectFit"
            />
            <view class="topImg-item-imgTip">选择孩子</view>
          </view>

          <view
            class="flex-img"
            style="width: 330rpx;"
            v-if="item.category == 5"
          >
            <gurudin-audio
              v-if="item.type == 'mp3'"
              :src="item.uri"
              :name="item.filename"
              theme="dark"
            />
            <video
              v-if="item.type == 'mp4' || item.type == 'mov'"
              style="width: 100%"
              :src="item.uri"
              controls
              enable-play-gesture
              :direction="0"
            />
            <view class="topImg-item-imgTip" @tap="onpenPopup(item, index)" style="bottom: -80rpx;">选择孩子</view>
          </view>
          
          <view class="topImg-item-text" :style="{'margin-top': item.category == 5 && item.type == 'mp4' ? '100rpx' : ' '}">
            <up-tag
              v-for="(itm, inx) in item.selectChildren"
              :key="inx"
              shape="circle"
              :text="itm.title"
              @close="item.selectChildren.splice(inx, 1)"
              closable
              plain
              color="#3F79FF"
              borderColor="#3F79FF"
            ></up-tag>
          </view>
        </view>
      </view>
    </view>
    <ChildredLIst
      :show="showChildList"
      :childredList="popupData.childrenData"
      @select="getChildrenInfoFn"
      @close="showChildList = false"
      :sendSearch="getChildrenListFn"
    />
    <template
      #footer
      v-if="!showChildList"
    >
      <up-button
        class="btn"
        shape="circle"
        text="保存"
        color="#367CFF"
        @click="Save"
      />
    </template>
  </BaseLayout2>
</template>

<script setup>
import { reactive, ref, onMounted, onUnmounted, watch } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import BaseLayout2 from "@/components/base-layout/base-layout2.vue";
import ChildredLIst from "./components/childredLIst.vue";
import gurudinAudio from "../components/gurudin-audio/gurudin-audio.vue";
import { getChildrenList } from "@/courseDetails/api/evaluate.js";
import {
  updateChildrenWorks,
  getevaDetails,
  setevaDetails,
} from "@/courseDetails/api";
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(["close"]);

const popupData = reactive({
  childrenData: [],
  text: "",
  info: {},
  params: null,
});
const mapTable = {
  childArtworkResources: "childArtworkResourceIds",
  childTeacherInteractionPhotoResources:
    "childTeacherInteractionPhotoResourceIds",
  childTeacherInteractionVideoResources:
    "childTeacherInteractionVideoResourceIds",
};
let showChildList = ref(false);
let activeImgIndex = ref(null); // 当前选中的图片的下标
let isShowPage = ref(true); // 是否显示当前页面
// 点击图片选择孩子
const onpenPopup = (item, index) => {
  activeImgIndex.value = index;
  showChildList.value = true;
};
// 初始化页面
const initPages = (props) => {
  getActivityDetails(props);
  getChildrenListFn();
};
// 获取活动详情
const getActivityDetails = async (props) => {
  isShowPage.value = false;
  const res = await getevaDetails(props.id);
  if (res.status == 0) {
    if (!props?.type) {
      return;
    }
    popupData.info = [];
    const data = res.data[props.type];
    // const o = data.filter((it) => it.childIdsFromArtwork === null);
    const o = data.filter((it) => it);
    o.forEach((item) => {
      console.log(item);

      console.log(popupData.params.paramKey);
      if (item[popupData.params.paramKey]) {
        item.selectChildren = item[popupData.params.paramKey].map((itm) => {
          return {
            id: itm,
            title: "",
          };
        });
      }
    });
    popupData.info = o;
    console.log(popupData.info, "popupData.info");
  } else {
    uni.showToast({
      title: res.message || "获取教师列表失败",
      icon: "error",
    });
  }
  isShowPage.value = true;
};

// 获取所有儿童列表
const getChildrenListFn = async (title) => {
  let form = {
    current: 1,
    pageSize: 999,
    state: 1,
    classId: uni.getStorageSync("classId"), // 班级id
  };
  if (title) form.title = title;
  let res = await getChildrenList(form);
  if (res.status == 0) {
    let childRes = res.data.map((item) => {
      return {
        id: item.id,
        title: item.title,
        isSelect: false,
        ...item,
      };
    });
    popupData.childrenData = childRes;
  } else {
    uni.showToast({
      title: res.message || "失败",
      icon: "error",
    });
  }
};
// 获取孩子组件
const getChildrenInfoFn = (item) => {
  console.log(item);
  popupData.info[activeImgIndex.value].selectChildren = item.map((item) => {
    return {
      title: item.title,
      id: item.id,
    };
  });
  // popupData.selectChildren = item;
  popupData.childrenData.forEach((item) => (item.isSelect = false));
  showChildList.value = false;
};
let isOne = true;
watch(
  () => popupData,
  (nv, ov) => {
    if (nv.info.length > 0 && nv.childrenData.length > 0 && isOne) {
      isOne = false;
      nv.info.forEach((item) => {
        item.type = item.filename.split(".")[1];
        if (item?.selectChildren) {
          item.selectChildren.forEach((itm) => {
            itm.title = nv.childrenData.find((it) => it.id == itm.id)?.title;
          });
        }
      });
    }
  },
  { deep: true }
);

const Save = async () => {
  // 打标接口完成后，需要保存活动。可以单独保存对应的字段的
  let updateResources = [];
  // 循环popupData.info

  popupData.info.forEach((item) => {
    if (item.selectChildren.length > 0) {
      updateResources.push({
        id: Number(item.id),
        [popupData.params.paramKey]: item.selectChildren.map((it) =>
          Number(it.id)
        ),
      });
    }
  });
  console.log(updateResources);

  try {
    const res = await updateChildrenWorks({ updateResources });
    console.log(res, "打标接口");

    if (res.status == 0) {
      // 打标完成后，先请求最新的活动详情。
      const p1 = await getevaDetails(popupData.params.id);
      console.log(p1, "打标完成后，先请求最新的活动详情");
      if (p1.status == 0) {
        // 保存活动后作品那边才可以显示
        const acDeParams = {
          id: Number(popupData.params.id), // 活动id
          [mapTable[popupData.params.type]]: [
            ...p1.data[popupData.params.type].map((item) => item.id),
          ], // 幼儿与教师的语言互动
        };

        const p = await setevaDetails(acDeParams);
        if (p.status == 0) {
          uni.$u.toast("标记成功");
          uni.navigateBack();
        }
      }
    }
  } catch (err) {
    uni.$u.toast(err.message || "标记失败");
  }
};

onLoad((options) => {
  popupData.params = options;
  popupData.params.paramKey =
    popupData.params.type == "childArtworkResources"
      ? "childIdsFromArtwork"
      : "childIdsFromFacialRecognition";
});

onMounted(() => {
  initPages(popupData.params);
});
onUnmounted(() => {
  uni.removeStorageSync("MarkingItem");
});
</script>

<style lang="scss" scoped>
.tips {
  margin-bottom: 40rpx;
  font-weight: 400;
  font-size: 30rpx;
  color: #333333;
}
.evaluate-marking {
  height: 100%;
  padding: 32rpx;
  box-sizing: border-box;
  .btn {
    margin-top: 30rpx;
  }
  .topImg {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20rpx;
    box-sizing: border-box;
    &-item {
      // border: 1px solid #e5e5e5;
      display: flex;
      flex-direction: column;
      align-items: center;
      .flex-img {
        position: relative;
      }
      .topImg-item-imgTip {
        position: absolute;
        bottom: 20rpx;
        border-radius: 32rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 160rpx;
        height: 60rpx;
        line-height: 65rpx;
        font-size: 28rpx;
        font-weight: 500;
        color: white;
        text-align: center;
        background: rgba(0, 0, 0, 0.5);
      }
      &-text {
        margin-top: 20rpx;
        text-align: center;
        color: #333;
      }
      .item-input {
        width: 80vw;
      }
    }
    image {
      width: 316rpx;
      height: 316rpx;
      border-radius: 20rpx;
    }
  }
}
</style>
