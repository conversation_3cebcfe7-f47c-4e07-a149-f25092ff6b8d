<!-- 作品打标 展示 打标的容器 -->
<template>
  <view class="file">
    <view
      class="file-item"
      :style="{
        display:
          type == 'childTeacherInteractionVideoResources' ? 'block' : 'grid',
      }"
    >
      <view
        v-for="(item, index) in fileList"
        :key="index"
      >
        <image
          v-if="item.category == 1"
          mode="aspectFit"
          :src="item.uri"
          @tap="preview(item, fileList)"
        />

        <gurudin-audio
          v-if="item.type == 'mp3' && item.category == 5"
          :src="item.uri"
          :name="item.filename"
          theme="dark"
        ></gurudin-audio>
        <video
          style="width: 100%"
          v-if="
            (item.category == 5 && item.type == 'mp4') || item.type == 'mov'
          "
          :src="item.uri"
          controls
          enable-play-gesture
          :direction="0"
        />
        <text
          class="file-item-name"
          v-for="(it, i) in item[workKey]"
          :key="i"
          >{{ getName(it) }}</text
        >
      </view>
      <view v-if="fileList.length == 0">暂未上传</view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from "vue";
import gurudinAudio from "../../components/gurudin-audio/gurudin-audio.vue";
const props = defineProps({
  fileList: {
    type: Array,
    default: () => [],
  },
  id: {
    type: String,
    default: "",
  },
  type: {
    type: String,
    default: "",
  },
  getName: {
    type: Function,
    default: () => {},
  },
});
let workKey = ref("");
const preview = (item, urlList) => {
  console.log(item, urlList);
  uni.previewImage({
    current: item.uri,
    urls: urlList.map((item) => item.uri),
  })

};
onMounted(() => {
  workKey.value =
    props.type == "childArtworkResources"
      ? "childIdsFromArtwork"
      : "childIdsFromFacialRecognition";
});
</script>

<style lang="scss" scoped>
@import "@/common/css/index.scss";

.file {
  color: #333333;
  box-sizing: border-box;

  &-item {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10rpx;
    position: relative;


    .file-item-name {
      color: $text-color;
      font-size: 28rpx;
    }

    &-name {
      font-size: 28rpx;
      font-weight: 400;
      margin-right: 10rpx;
    }

    image {
      border-radius: 11.42rpx;
      width: 200rpx;
      height: 200rpx;
    }
  }
}
</style>
