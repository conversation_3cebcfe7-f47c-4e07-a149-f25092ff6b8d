<!-- 评价 -->
<template>
  <view class="evaluate-layout">
    <!-- 过程性记录 -->
    <view class="col">
      <view class="title">过程性记录</view>
      <view class="content">
        <view class="content-title">幼儿与教师的语言互动</view>
        <view class="interaction">
          <text>{{ evaForm.childTeacherInteraction || "-" }}</text>
        </view>
      </view>
      <view
        class="content"
        style="margin-top: 32rpx"
      >
        <view class="content-title title">
          <text>照片</text>
          <text
            v-if="evaForm?.childTeacherInteractionPhotoResources?.length > 0"
            class="mark"
            @tap="gotoPages('childTeacherInteractionPhotoResources')"
            >标记儿童</text
          >
        </view>
        <view class="content-title-img">
          <EvaluateMarkingList
            v-if="imageList1.length > 0"
            :fileList="evaForm.childTeacherInteractionPhotoResources"
            :id="evaForm.id"
            :getName="getStuName"
            type="childTeacherInteractionPhotoResources"
          />
          <text v-else>暂未上传</text>
        </view>
      </view>
    </view>
    <!-- 音视频 -->
    <view class="col">
      <view class="title">
        <text>音视频</text>
        <text
          v-if="evaForm?.childTeacherInteractionVideoResources?.length > 0"
          class="mark"
          @tap="gotoPages('childTeacherInteractionVideoResources')"
          >标记儿童</text
        >
      </view>
      <view class="content">
        <!-- <view v-if="imageList3.length > 0 && imageList3">
          <view
            v-for="(item, index) in imageList3"
            :key="index"
          >
            <gurudin-audio
              v-if="item.type == 'mp3' && item.category == 5"
              :src="item.uri"
              :name="item.filename"
              theme="dark"
            ></gurudin-audio>
            <video
              id="videoPlay"
              style="width: 100%"
              v-if="(item.category == 5 && item.type == 'mp4') || item.type == 'mov'"
              :src="item.uri"
              controls
              enable-play-gesture
              :direction="0"
              @fullscreenchange="fullscreenclick"
            ></video>
          </view>
        </view>
        <text v-else>暂未上传</text> -->
        <EvaluateMarkingList
          v-if="imageList3.length > 0"
          :fileList="evaForm.childTeacherInteractionVideoResources"
          :id="evaForm.id"
          :getName="getStuName"
          type="childTeacherInteractionVideoResources"
        />
      </view>
    </view>
    <!-- 幼儿作品 -->
    <view class="col">
      <view class="title">
        <text>幼儿作品</text>
        <text
          v-if="evaForm?.childArtworkResources?.length > 0"
          class="mark"
          @tap="gotoPages('childArtworkResources')"
          >标记儿童</text
        >
      </view>
      <view class="content">
        <!-- <view @click="uni.navigateTo({url: '/courseDetails/activityDetails/evaluateMarking'})">
          <up-album
            v-if="imageList2.length > 0"
            :urls="imageList2"
            space="5"
            mode="aspectFit"
            multipleSize="200rpx"
			
          ></up-album>
          <text v-else>暂未上传</text>
        </view> -->
        <EvaluateMarkingList
          v-if="imageList2.length > 0"
          :fileList="evaForm.childArtworkResources"
          :id="evaForm.id"
          :getName="getStuName"
          type="childArtworkResources"
        />
        <text v-else>暂未上传</text>
      </view>
    </view>

    <!-- 兴趣和难度评价 -->
    <view class="col">
      <view class="title">兴趣和难度评价</view>
      <view class="content">
        <view class="col3-rate">
          <view class="rate">
            <view class="title">幼儿对本活动的兴趣程度</view>
            <view class="flex-ac">
              <up-rate
                readonly
                v-model="evaForm.interestScoreValue"
                size="44rpx"
                activeColor="#FAE665"
                style="margin-right: 32rpx"
              ></up-rate>
              <view>{{ rateTips[evaForm.interestScore] }}</view>
            </view>
          </view>
        </view>
        <view class="col3-rate">
          <view class="rate">
            <view class="title">本活动的难度</view>
            <view class="flex-ac">
              <up-rate
                readonly
                v-model="evaForm.difficultyScoreValue"
                size="44rpx"
                activeColor="#FAE665"
                style="margin-right: 32rpx"
              ></up-rate>
              <view>{{ rateDifficulty[evaForm.difficultyScore] }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- 活动实施反思 -->
    <view class="col">
      <view class="title">活动实施反思</view>
      <view class="bottom-bg">
        <view
          class="content flex-ac"
          style="align-items: flex-start; border-bottom: none"
        >
          <view
            class="content-title"
            style="width: 112rpx; font-weight: 500; margin-right: 32rpx"
            >活动亮点
          </view>
          <view class="flex-f1">
            <text class="bottom-text">{{
              evaForm.activityHighlight || "-"
            }}</text>
          </view>
        </view>
        <view
          class="content flex-ac"
          style="align-items: flex-start; border-bottom: none"
        >
          <view
            class="content-title"
            style="width: 112rpx; font-weight: 500; margin-right: 32rpx"
            >活动存在的问题
          </view>
          <view class="flex-f1">
            <text class="bottom-text">{{
              evaForm.activityProblem || "-"
            }}</text>
          </view>
        </view>
        <view
          class="content flex-ac"
          style="align-items: flex-start; border-bottom: none"
        >
          <view
            class="content-title"
            style="width: 112rpx; font-weight: 500; margin-right: 32rpx"
            >改进措施
          </view>
          <view class="flex-f1">
            <text class="bottom-text">{{
              evaForm.improvementMeasure || "-"
            }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, watch } from "vue";
import gurudinAudio from "../../components/gurudin-audio/gurudin-audio.vue";
import EvaluateMarkingList from "./evaluateMarkingList.vue";
import { union } from "lodash-es";
let imageList1 = ref([]);
let imageList2 = ref([]);
let imageList3 = ref([]);
const props = defineProps({
  evaForm: Object,
  stuList: Array,
});

const emit = defineEmits(["playVideo"]);

watch(
  () => props.evaForm,
  (nVal) => {
    console.log(nVal, "123");

    if (nVal) {
      imageList1.value = [];
      imageList2.value = [];
      imageList3.value = [];
      if (nVal.childTeacherInteractionPhotoResources.length > 0) {
        // imageList1.value = []
        nVal.childTeacherInteractionPhotoResources.forEach((item, index) => {
          imageList1.value.push(item.uri);
        });
      }
      if (nVal.childArtworkResources.length != 0) {
        // imageList2.value = []
        nVal.childArtworkResources.forEach((item, index) => {
          imageList2.value.push(item.uri);
        });
      }
      if (nVal.childTeacherInteractionVideoResources.length > 0) {
        // imageList3.value = []
        nVal.childTeacherInteractionVideoResources.forEach((item, index) => {
          item.type = item.filename.split(".")[1];
          imageList3.value.push(item);
        });
      }
      console.log(imageList1.value, "照片");
      console.log(imageList2.value, "幼儿作品");
      console.log(imageList3.value, "音视频");
    }
  },
  { deep: true }
);

const rateTips = {
  2: "非常不感兴趣",
  4: "比较不感兴趣",
  6: "一般",
  8: "比较感兴趣",
  10: "非常感兴趣",
};
const rateDifficulty = {
  2: "非常简单",
  4: "比较简单",
  6: "一般",
  8: "比较难",
  10: "非常难",
};

function fullscreenclick(event) {
  // 不知道为什么 外面组件的.img 和 。activity-details-title（类名）会把video组件的全屏给顶下来，只能出此下策
  if (event.detail.fullScreen) {
    emit("playVideo", false);
  } else {
    emit("playVideo", true);
  }
}

const gotoPages = (url) => {
  uni.navigateTo({
    url: `/courseDetails/activityDetails/evaluateMarking?id=${props.evaForm.id}&type=${url}`,
  });
};

// 返回学生姓名
const getStuName = (id) => props.stuList.find((item) => item.id == id)?.title;
</script>
<script>
export default {
  options: {
    styleIsolation: "shared", // 解除样式隔离
  },
};
</script>

<style lang="scss" scoped>
.evaluate-layout {
  margin-top: 32rpx;

  .col {
    margin-bottom: 32rpx;

    .bottom-bg {
      border-radius: 12rpx;
      background: rgba(249, 249, 249, 1);
      border: 1rpx solid rgba(238, 238, 238, 1);
      padding: 32rpx;
      box-sizing: border-box;

      .bottom-text {
        font-size: 28rpx;
        font-weight: 400;
        color: rgba(51, 51, 51, 1);
      }
    }

    .title {
      /** 文本1 */
      font-size: 28rpx;
      font-weight: 600;
      color: rgba(51, 51, 51, 1);
      padding-bottom: 32rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .mark {
        font-size: 28rpx;
        font-weight: 400;
        color: #367cff;
      }
    }

    .col3-rate {
      padding-bottom: 32rpx;
      margin-top: 24rpx;

      .rate {
        border-radius: 12rpx;
        background: rgba(249, 249, 249, 1);
        border: 1rpx solid rgba(238, 238, 238, 1);
        padding: 24rpx;

        .title {
          font-size: 28rpx;
          font-weight: 400;
          color: rgba(128, 128, 128, 1);
          border-bottom: none;
          padding-bottom: 24rpx;
        }
      }
    }

    .content {
      padding-bottom: 33rpx;
      border-bottom: 1px solid #eeeeee;

      .content-title {
        font-size: 28rpx;
        font-weight: 600;
        padding-bottom: 26rpx;
      }

      .interaction {
        border-radius: 12rpx;
        background: rgba(249, 249, 249, 1);
        border: 1rpx solid rgba(238, 238, 238, 1);
        padding: 24rpx;
        font-size: 28rpx;
        font-weight: 400;
        color: rgba(51, 51, 51, 1);
      }
    }
  }
}
</style>
