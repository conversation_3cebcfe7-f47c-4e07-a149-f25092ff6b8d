<!-- Ai生成教案 -->
<template>
  <view>
    <base-layout
      :navTitle="navTitle"
      :footerStyle="{ padding: 0 }"
      :contentStyle="{ padding: 0 }"
    >
      <view class="aiLayout">
        <ai-create-details-form
          ref="aiFormRef"
          :formConfig="formConfig"
          :formData="formData"
        />
        <view class="fixed-bottom-btn">
          <up-button
            type="primary"
            @click="submit"
            shape="circle"
            color="#367CFF"
            text="开始AI生成"
          />
        </view>
        <view
          class="mask"
          v-if="isMask"
          @click.stop.prevent="() => {}"
        ></view>
        <up-modal
          :show="isModal"
          :content="content"
          showCancelButton
          confirmText="继续生成"
          @cancel="cancel"
          @confirm="isModal = false"
        />
      </view>
    </base-layout>
  </view>
</template>

<script setup>
import { ref, onMounted, reactive } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import baseLayout from "@/components/base-layout/base-layout.vue";
import aiCreateDetailsForm from "./components/aiCreateDetailsForm.vue";
import utils from "./components/utils.js";
import { getAiPrepare, getAiSubmit } from "../api/aiwrite";
import { getActivityDetailContent } from "../api/editActivity";
const { formConfig } = utils();
const formData = ref({
  creator: uni.getStorageSync("USER_INFO")?.name,
});
let isMask = ref(false);
let isModal = ref(false);
let content = ref("");
const navTitle = "AI生成教案";
let aiFormRef = ref(null);
let formId = reactive({
  prevActivityId: 0,
  activityId: 0,
});
onLoad((options) => {
  let { id, subjectId } = options;
  formId.activityId = Number(id);
  getPrevAndNextActivityId({ id, subjectId });
});

const submit = async () => {
  const valid1 = await aiFormRef.value.validate();
  // console.log("🚀 ~ submit ~ valid1:", valid1.durationInMinutes);
  let obj = {
    ...valid1,
    ...formId,
  };
  // obj.durationInMinutes = Number(obj.durationInMinutes);
  isMask.value = true;
  uni.showLoading({
    title: "AI生成中, 请勿操作...",
  });

  let res = await getAiSubmit(obj);

  if (res.status == 0) {
    isMask.value = false;
    uni.hideLoading();
    uni.navigateBack();
  }
};
const cancel = () => {
  isModal.value = false;
  uni.navigateBack();
};

// 获取上下活动ID
let getPrevAndNextActivityId = async (obj) => {
  let res = await getActivityDetailContent(obj);
  if (res.status == 0) {
    let { prevActivityId } = res.data;
    formId.prevActivityId = prevActivityId;
    let res1 = await getAiPrepare({
      preActivityId: prevActivityId,
      activityId: Number(obj.id),
    });
    if (res1.status == 0) {
      console.log(res.data.alertTips);

      if (res1.data.alertTips) {
        isModal.value = true;
        content.value = res1.data.alertTips;
      }
      const data = res1.data;
      formData.value = { ...formData.value, ...data };

      // formData.value.durationInMinutes = Number(
      //   formData.value.durationInMinutes
      // );
    }
  }
};
</script>

<style lang="scss" scoped>
.aiLayout {
  padding: 32rpx;
  padding-bottom: 220rpx;
  box-sizing: border-box;
  position: relative;
  .fixed-bottom-btn {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100vw;
    box-sizing: border-box;
    min-height: 104rpx;
    padding: 16rpx 32rpx;
    background: #ffffff;
    padding-bottom: calc(env(safe-area-inset-bottom) + 16rpx);
    :deep(.u-button__text) {
      font-size: 30rpx !important;
      font-weight: 600;
    }
  }
  .mask {
    width: 100vw;
    height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 10;
    background: rgba(0, 0, 0, 0.5);
  }
}
</style>
