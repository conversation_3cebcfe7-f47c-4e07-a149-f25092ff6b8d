import { ref, reactive, onMounted } from "vue";
import { getDICT } from '@/utils/index.js'

/** 初始化字典 */
const optionsList = reactive({
    organizational: [],
});


// onMounted( () => {
//     getDICT('organizational')
// })

export default () => {


    const formConfig = reactive(
        [
            { label: '活动名称', type: 'input', key: 'name', required: true },
            {
                label: '组织形式',
                type: 'radio',
                key: 'organizationForm',
                required: true,
                placeholder: '请输入组织形式',
                options: []
            },
            // { label: '活动时长', time: true, type: 'input', iptType: 'number', key: 'durationInMinutes', required: true, placeholder: '请输入活动时长（单位：分钟）' },
            // { label: '活动思路(活动背景)', type: 'textarea', key: 'activityIdea', required: true },
            // { label: '教学目标(活动目标)', type: 'textarea', key: 'teachingObjective', required: true },
            { label: '领域', type: 'radio', key: 'domain', required: true,placeholder: '请输入领域', options: [
                { label: '语言', name: '语言' },
                { label: '科学', name: '科学' },
                { label: '数学', name: '数学' },
                { label: '美术', name: '美术' },
                { label: '音乐', name: '音乐' },
                { label: '艺术', name: '艺术' },
                { label: '健康', name: '健康' },
                { label: '运动', name: '运动' },
                { label: '社会', name: '社会' },
            ]},
            { label: '活动大致流程', type: 'textarea', key: 'activitySteps', required: true },
        ]
    )

    return {
        formConfig
    }
}