<template>
  <view>
    <up-form
      :model="formData"
      ref="formRef"
      labelPosition="top"
      labelWidth="auto"
    >
      <view
        class="form-item-container"
        :key="index"
        v-for="(item, index) in list"
      >
        <up-form-item
          :label="item.label"
          :prop="item.key"
          :required="item.required"
        >
          <!-- input类型 -->
          <up-input
            border="none"
            v-if="item.type === 'input'"
            :type="item.iptType || 'text'"
            v-model="formData[item.key]"
            :placeholder="item.placeholder || `请输入${item.label}`"
          />
          <template
            v-if="item.type === 'input' && item.time"
            #right
          >
            <text>分钟</text>
          </template>
          <!-- textarea类型 -->
          <up-textarea
            class="form-item-textarea"
            type="textarea"
            maxlength="-1"
            border="none"
            autoHeight
            confirm-type="none"
            v-if="item.type === 'textarea'"
            v-model="formData[item.key]"
            :placeholder="item.placeholder || `请输入${item.label}`"
            placeholderStyle="color: rgb(192, 196, 204)"
            :style="{ padding: '0', minHeight: '200rpx' }"
            :cursorSpacing="100"
          />
          <!-- Radio类型 -->
          <up-radio-group
            v-if="item.type === 'radio'"
            v-model="formData[item.key]"
            placement="row"
          >
            <up-radio
              v-for="(rItem, rindex) in item.options"
              :key="rindex"
              :label="rItem.label"
              :name="rItem.name"
              :shape="rItem.shape || 'square'"
            >
            </up-radio>
          </up-radio-group>
          <!-- Checkbox类型 -->
          <up-checkbox-group
            v-if="item.type === 'checkbox'"
            v-model="formData[item.key]"
            placement="row"
          >
            <up-checkbox
              :customStyle="{ marginRight: '16rpx' }"
              v-for="(cItem, cIndex) in item.options"
              :key="cIndex"
              :label="cItem.label"
              :name="cItem.name"
            >
            </up-checkbox>
          </up-checkbox-group>
        </up-form-item>
        <view
          class="placeholder"
          v-if="item.type === 'checkbox' || item.type === 'radio'"
          >{{ item.placeholder }}</view
        >
        <view
          v-if="item.type === 'multiSelect' && !item.multiple"
          class="multiSelect"
          >{{ item.tip }}</view
        >
      </view>
    </up-form>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from "vue";
import { getDICT } from "@/utils";
const props = defineProps({
  formConfig: {
    type: Array,
    default: () => [],
  },
  formData: {
    type: Object,
    default: () => {},
  },
});
let formRef = ref(null);
let list = ref([]);

const formItemStyle = `
  border-radius: 28rpx;
  padding: 0 28rpx;
  background: #fff;
  margin-bottom: 24rpx;
`;
// 初始化表单数据
const initFormData = async (config) => {
  let dics = await getDICT("SubjectActivityOrganizationFormEnumDesc");
  /** 初始化字典 */
  config[1].options = Object.keys(dics).map((key) => {
    return {
      label: dics[key],
      name: key,
    };
  });
};
// 生成验证规则
const generateRules = (config) => {
  const rulesObj = {};

  config.forEach((item) => {
    const ruleArray = [];
    switch (item.type) {
      case "input":
        if (item.required) {
          ruleArray.push({
            required: true,
            type: item.iptType || "string",
            message: `请输入${item.label}`,
            trigger: ["change", "blur"],
          });
        }
        break;

      case "textarea":
        if (item.required) {
          ruleArray.push({
            required: true,
            message: `请选择${item.label}`,
            trigger: "change",
          });
        }
        break;

      case "checkbox":
        if (item.required) {
          ruleArray.push({
            required: true,
            type: "any",
            min: 1,
            message: `请至少选择一个${item.label}`,
            trigger: "change",
          });
        }
        break;
      case "radio":
        if (item.required) {
          ruleArray.push({
            required: true,
            type: "any",
            min: 1,
            message: `请至少选择一个${item.label}`,
            trigger: "change",
          });
        }
        break;
    }

    if (ruleArray.length > 0) {
      rulesObj[item.key] = ruleArray;
    }
  });
  // 使用 setRules 方法设置规则
  formRef.value?.setRules(rulesObj);
};
// 定义验证方法
const validate = () => {
  return new Promise(async (resolve, reject) => {
    if (!formRef.value) {
      console.error("formRef is null");
      reject(new Error("表单实例不存在"));
      return;
    }

    try {
      const valid = await formRef.value?.validate();
      if (valid) {
        console.log("表单验证通过");
        resolve(props.formData);
      } else {
        reject(new Error("表单验证失败"));
      }
    } catch (error) {
      reject(error);
    }
  });
};
onMounted(() => {
  list.value = props.formConfig;
  initFormData(list.value);
  nextTick(() => {
    generateRules(props.formConfig);
  });
});
// 暴露方法给父组件
defineExpose({
  formRef,
  validate,
});
</script>
<script>
export default {
    options: { styleIsolation: 'shared' } //解除样式隔离
};
</script>

<style lang="scss" scoped>
:deep(.u-form-item__body__left__content){
  flex-direction: row-reverse;
  flex: initial;
  .u-form-item__body__left__content__required{
    right: -16rpx;
    left: initial;
  }
}
.form-item-textarea {
  min-height: 200rpx;
}
.form-item-container {
  background: #ffffff;
  padding: 14rpx 28rpx;
  margin-bottom: 24rpx;
  border-radius: 28rpx;
  box-shadow: 4rpx 8rpx 16rpx rgba(0, 0, 0, 0.02);
}
.placeholder {
  color: rgb(192, 196, 204);
  font-size: 30rpx;
  font-weight: 400;
}
</style>
