import { reactive } from "vue"
export default () => {
    const rules = reactive({
        name: [{
            required: true,
            message: '请输入活动名称',
            trigger: ['blur', 'change'],
        }],
        plannedGeneration: [{
            required: true,
            message: '请选择预设生成',
            trigger: ['blur', 'change'],
            // validator: (rule, value, callback) => {
            //     console.log(rule, value, '12312313');

            // },
        }],
        author: [{
            required: true,
            message: '请输入作者',
            trigger: ['blur'],
            // validator: (rule, value, callback) => {
            //     return true
            // },
        }],
        organizationForm: [{
            required: true,
            message: '请输入组织形式',
            trigger: ['blur'],
        }],
        durationInMinutes: [{
            required: true,
            message: '请输入活动时长',
            trigger: ['blur'],
            validator: (rule, value, callback) => {
                if (value) {
                    return true
                }
                console.log(value, 'durationInMinutes');

            },
        }],
        implementedAt: [{
            required: true,
            message: '请输入实施时间',
            trigger: ['blur'],
        }]
        ,
        teachingObjective: [{
            required: true,
            message: '请输入教学目标',
            trigger: ['blur'],
        }]

    })
    const coreFormRules = reactive({
        matrix1Id: [{
            required: true,
            type: 'array',
            message: '请选择',
            trigger: ['blur', 'change'],
        }],
        matrix2Id: [{
            required: true,
            message: '请选择',
            trigger: ['blur', 'change'],
        }],
        matrix3Id: [{
            required: true,
            message: '请选择',
            trigger: ['blur', 'change'],
        }],
        targetId: [{
            required: true,
            message: '请选择',
            trigger: ['blur', 'change'],

        }]
    })
    return {
        rules,
        coreFormRules
    }
}