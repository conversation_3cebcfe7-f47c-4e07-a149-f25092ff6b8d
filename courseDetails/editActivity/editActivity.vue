<template>
  <view class="edit-activity-layout layout">
    <view class="col">
      <view class="title">活动基本信息</view>
      <view class="content">
        <activity-add-form ref="formRefAc" @change="changeActivity" @sendformData="sendformData"
          :allformData="allformData" />
      </view>
    </view>
    <view class="col">
      <view class="title">教学流程</view>
      <view class="content">
        <TeachingAdd @change="changeTeaching" :allformData="allformData" :coreFormList="coreFormList" />
      </view>
    </view>
    <view class="action-btn" :style="{ height: heightBottom + 52 + 'px' }" v-if="_isDetail">
      <up-button type="primary" text="保存" color="#367CFF" round shape="circle" @click="send"></up-button>
    </view>
  </view>
</template>

<script setup>
import { reactive, ref, onMounted } from "vue";
let heightBottom = ref(0);
import { onLoad } from "@dcloudio/uni-app";
import { editActivity, getActivityDetail, updateCoreExpList } from "../api/editActivity.js";
import activityAddForm from "./components/activityAddForm.vue";
import TeachingAdd from "./components/TeachingAdd.vue";
const { safeAreaInsets } = uni.getSystemInfoSync();
heightBottom.value = safeAreaInsets.bottom;
let _subjectId = ref("");
let _id = ref("");
let formRefAc = ref(null);
let allformData = ref({});
let coreFormList = ref([]);
let _isDetail = ref(true);
let _gradeId = ref("");
let _subjectStageId = ref("");
let _author = ref("");
let _isDes = ref(null);

onLoad((option) => {
  const { gradeId, subjectStageId, subjectId, author, id, isDetail, isDes } =
    option;
  uni.setStorageSync("editActivity", true);
  _gradeId.value = gradeId;
  _subjectStageId.value = subjectStageId;
  _subjectId.value = subjectId;
  _author.value = author;
  _id.value = id;
  _isDes.value = isDes;

  if (isDetail == "1") {
    _isDetail.value = false;
  }
});
const data = reactive({
  hxjy: {}, // 核心经验
  jxlc: [], // 教学流程
});

// 更新核心经验
const updateMatrix = async (coreParas) => {
  let data = {
    relId: Number(_id.value),
    matchType: 'subject',
    list: []
  }
  data.list = coreParas.map((item) => {
    item.schoolId = uni.getStorageSync("USER_INFO").currentSchoolId;
    return {
      relId: Number(_id.value),
      matchType: 'subject',
      matrix1Id: item.matrix1Id,
      matrix1Name: item.matrix1Name,
      matrix2Id: item.matrix2Id,
      matrix2Name: item.matrix2Name,
      matrix3Id: item.matrix3Id,
      matrix3Name: item.matrix3Name,
      targetId: item.targetId,
      targetName: item.targetName,
    }
  })
  let res = await updateCoreExpList(data);
  if (res.status != 0) {
    uni.hideLoading()
    uni.$u.toast(res?.message || "核心经验保存失败");
    return
  }
  uni.$u.toast("修改核心经验成功");
}

async function send() {
  // formRefAc.value.coreValidate()
  formRefAc.value.validate();
}
// 真正发送请求
async function sendformData() {
  uni.showLoading({
    title: "保存中",
    mask: true
  })
  if (JSON.stringify(data.jxlc) == "[]") data.jxlc = allformData.value.teachingSteps;
  if (JSON.stringify(data.hxjy) == "{}") data.hxjy = allformData.value;
  let formData = {
    ...data.hxjy,
    teachingSteps: data.jxlc,
    subjectId: _subjectId.value,
  };
  formData.electronicPreparationResourceIds = formData.electronicPreparationResources.map((item) => item.id);
  console.log(formData);
  // 获取核心经验
  // formData.matrices = formRefAc.value.coreFormData;

  let res = await editActivity(formData);
  if (res.status == 0) {
    uni.$u.toast("修改教案成功");
    await updateMatrix(formRefAc.value.coreFormData)
    console.log(123);

    uni.hideLoading();
    // 判断是否从详情也跳过来
    if (_isDes.value) {
      uni.navigateTo({
        url: `/courseDetails/activityDetails/activityDetails?&id=${_id.value}&gradeId=${_gradeId.value}`,
      });
      return;
    }
    uni.redirectTo({
      url: `/courseDetails/editActivity/editActivity?subjectStageId=${_subjectStageId.value}&subjectId=${_subjectId.value}&gradeId=${_gradeId.value}&id=${_id.value}&author=${_author.value}&isDetail=1`,
    });
  }
}

function changeActivity(el) {
  console.log(el, "el");
  data.hxjy = {};
  data.hxjy = el;
}
function changeTeaching(el) {
  console.log(el, "el");
  data.jxlc = [];
  data.jxlc = el;
}

onMounted(() => {
  getActivityDetail(_id.value).then((res) => {
    console.log(res);
    allformData.value = res.data;
  });
});
</script>

<style lang="scss" scoped>
.edit-activity-layout {
  padding: 0 32rpx;
  padding-bottom: 130rpx;
  overflow-y: auto;

  .action-btn {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    box-sizing: border-box;
    padding: 16rpx 32rpx;
    background: #fff;
    opacity: 1;
    z-index: 50;
  }

  .col {
    width: 100%;
    border-radius: 28rpx;
    background: #fff;
    box-shadow: 4rpx 8rpx 16rpx rgba(0, 0, 0, 0.02);
    margin-top: 32rpx;
    padding: 32rpx;
    overflow: hidden;
    box-sizing: border-box;

    .title {
      /** 文本1 */
      font-size: 34rpx;
      font-weight: 600;
      color: rgba(51, 51, 51, 1);
      border-bottom: 2px solid #eeeeee;
      padding-bottom: 26rpx;
    }

    .col3-rate {
      .title {
        font-size: 28rpx;
        font-weight: 500;
        color: rgba(128, 128, 128, 1);
        border-bottom: none;
        padding-bottom: 40rpx;
      }

      padding-bottom: 32rpx;
      border-bottom: 2px solid #eeeeee;
    }

    .content {
      .content-title {
        font-size: 28rpx;
        font-weight: 600;
        margin: 32rpx 0 26rpx 0;
      }
    }
  }
}
</style>
