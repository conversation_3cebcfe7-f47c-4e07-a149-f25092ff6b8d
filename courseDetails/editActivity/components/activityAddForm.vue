<!-- 编辑活动 -->
<template>
  <view class="addform-layout">
    <view>
      <up-form
        ref="formRef"
        labelPosition="top"
        :model="formData"
        label-width="auto"
        :labelStyle="{ fontSize: '28rpx', fontWeight: '600' }"
        :rules="rules"
      >
        <up-form-item
          label="活动名称"
          prop="name"
          required
        >
          <up-input
            v-model="formData.name"
            :disabled="!_isDetail"
            placeholder="请输入活动名称"
            @click="changeData"
          ></up-input>
        </up-form-item>
        <view class="flex-jsb-ac">
          <up-form-item
            style="flex: 1; margin-right: 19rpx"
            label="预设生成"
            prop="plannedGeneration"
            required
          >
            <uni-data-select
              v-model="formData.plannedGeneration"
              :localdata="yssc"
              clear
              :disabled="!_isDetail"
              @change="changeData"
            />
          </up-form-item>
          <up-form-item
            style="flex: 1"
            label="作者"
            prop="author"
            required
            labelWidth="200rpx"
          >
            <up-input
              v-model="formData.author"
              :disabled="!_isDetail"
              placeholder="请输入内容"
            ></up-input>
          </up-form-item>
        </view>
        <view class="flex-jsb-ac">
          <up-form-item
            style="flex: 1; margin-right: 19rpx"
            label="组织形式"
            prop="organizationForm"
            required
          >
            <uni-data-select
              v-model="formData.organizationForm"
              :disabled="!_isDetail"
              :localdata="zzxs"
              clear
              placement="top"
              @change="changeData"
            ></uni-data-select>
            <up-input
              v-model="formData.organizationForm"
              style="display: none"
            />
          </up-form-item>
          <up-form-item
            style="flex: 1"
            label="活动时长"
            prop="durationInMinutes"
            required
          >
            <up-input
              v-model="formData.durationInMinutes"
              :disabled="!_isDetail"
              type="number"
              placeholder="请输入内容"
              @click="changeData"
            >
              <template #suffix>
                <view style="font-size: 28rpx">分钟</view>
              </template>
            </up-input>
          </up-form-item>
        </view>
        <up-form-item
          label="实施时间"
          prop="implementedAt"
          required
        >
          <uni-datetime-picker
            :disabled="!_isDetail"
            type="date"
            v-model="formData.implementedAt"
            @change="datetime"
          />
        </up-form-item>
        <up-form-item
          label="阶段"
          prop="creator"
        >
          <uni-data-select
            :disabled="!_isDetail"
            v-model="formData.subjectStageId"
            :localdata="stageList"
            clearplacement="top"
            @change="changeData"
          ></uni-data-select>
        </up-form-item>
        <up-form-item
          label="活动背景和目的"
          prop="activityIdea"
          labelWidth="300rpx"
        >
          <up-textarea
            confirmType="none"
            v-model="formData.activityIdea"
            :disabled="!_isDetail"
            placeholder="请输入活动背景和目的"
            :height="autoHeight ? 'auto' : '100rpx'"
            :autoHeight="autoHeight"
            maxlength="-1"
            @blur="changeData"
          ></up-textarea>
        </up-form-item>
        <up-form-item
          label="教学目标"
          prop="teachingObjective"
          required
        >
          <up-textarea
            confirmType="none"
            v-model="formData.teachingObjective"
            :disabled="!_isDetail"
            placeholder="请输入教学目标"
            :autoHeight="autoHeight"
            maxlength="-1"
            @blur="changeData"
          ></up-textarea>
        </up-form-item>
        <!-- <up-form-item label="重难点" prop="keyQuestion">
					<up-textarea confirmType="none" v-model="formData.keyQuestion" :disabled="!_isDetail"
						placeholder="请输入重难点" :autoHeight="autoHeight" maxlength="-1"
						@blur="changeData"></up-textarea>
				</up-form-item> -->
        <up-form-item
          label="活动重点"
          prop="activityKeyPoint"
        >
          <up-textarea
            confirmType="none"
            v-model="formData.activityKeyPoint"
            :disabled="!_isDetail"
            placeholder="请输入活动重点"
            :autoHeight="autoHeight"
            maxlength="-1"
            @blur="changeData"
          ></up-textarea>
        </up-form-item>
        <up-form-item
          label="活动难点"
          prop="activityDifficulty"
        >
          <up-textarea
            confirmType="none"
            v-model="formData.activityDifficulty"
            :disabled="!_isDetail"
            placeholder="请输入活动难点"
            :autoHeight="autoHeight"
            disableDefaultPadding
            maxlength="-1"
            @blur="changeData"
          ></up-textarea>
        </up-form-item>
      </up-form>
    </view>
    <!-- 核心经验 -->
    <up-form
      v-if="coreFormData"
      ref="coreRef"
      :model="coreFormData"
      :rules="coreFormRules"
      labelPosition="left"
      label-width="140rpx"
    >
      <view
        style="margin-top: 50rpx"
        v-for="(item, index) in coreFormData"
        :key="index"
      >
        <view class="flex-jsb-ac">
          <view style="font-size: 30rpx; font-weight: 600">核心经验</view>
          <up-icon
            v-if="_isDetail"
            @click="delCoreFormItem(index)"
            name="trash"
            size="36rpx"
          />
        </view>
        <up-form-item
          label="领域"
          prop="matrix1Id"
          required
        >
          <uni-data-select
            class="dataSelect"
            v-model="item.matrix1Id"
            :localdata="localdata.domainData"
            :disabled="!_isDetail"
            @change="
              coreFormChange(
                item.matrix1Id,
                item,
                'DimensionData',
                'matrix1Id',
                localdata.domainData
              )
            "
            clear
          />
        </up-form-item>
        <up-form-item
          label="维度"
          prop="matrix2Id"
          required
        >
          <uni-data-select
            :key="item.key"
            class="dataSelect"
            v-model="item.matrix2Id"
            :localdata="item.DimensionData"
            :disabled="!_isDetail"
            @change="
              coreFormChange(
                item.matrix2Id,
                item,
                'subDimensionData',
                'matrix2Id',
                item.DimensionData
              )
            "
            clear
          />
        </up-form-item>
        <up-form-item
          label="子维度"
          prop="matrix3Id"
          required
        >
          <uni-data-select
            :key="item.key"
            class="dataSelect"
            v-model="item.matrix3Id"
            :localdata="item.subDimensionData"
            :disabled="!_isDetail"
            @change="
              coreFormChange(
                item.matrix3Id,
                item,
                'indexData',
                'matrix3Id',
                item.subDimensionData
              )
            "
            clear
          />
        </up-form-item>
        <up-form-item
          label="指标"
          prop="targetId"
          required
        >
          <uni-data-select
            :key="item.key"
            class="dataSelect"
            v-model="item.targetId"
            :localdata="item.indexData"
            :disabled="!_isDetail"
            @change="
              coreFormChange(
                item.targetId,
                item,
                false,
                'targetId',
                item.indexData
              )
            "
            clear
            placement="top"
          />
        </up-form-item>
      </view>
    </up-form>
    <view
      v-else
      style="font-size: 30rpx; font-weight: 600"
      >核心经验: 暂无数据</view
    >
    <view
      v-if="_isDetail"
      style="
        color: rgba(63, 121, 255, 1);
        border-bottom: 1px solid #eee;
        font-weight: 600;
        font-size: 30rpx;
        margin: 20rpx 0;
        padding-bottom: 20rpx;
        text-align: center;
      "
      @click="addCoreFormItem"
      >新增核心经验</view
    >

    <view class="content">
      <view class="content-title">教学准备、资源或环境支持</view>
      <view>
        <up-textarea
          confirmType="none"
          v-model="formData.resourcesSupport"
          :disabled="!_isDetail"
          placeholder="请输入教学准备，资源或环境支持"
          :autoHeight="autoHeight"
          height="170rpx"
          maxlength="-1"
          @blur="changeData"
        ></up-textarea>
      </view>
    </view>
    <view class="content">
      <view class="content-title">教学准备、资源或环境支持（附件）</view>
      <view>
        <Upload
          type="all"
          :value="formData.electronicPreparationResources"
          @callback="callback"
          @emitDelFile="delFile"
          :showDel="_isDetail"
        >
          <view
            style="display: flex; flex-direction: column; align-items: center"
          >
            <image
              src="@/static/icon/u-icon.png"
              style="width: 37rpx; height: 31rpx"
            />
            <text style="font-size: 21rpx; font-weight: 400; color: #808080"
              >上传附件</text
            >
          </view>
        </Upload>
      </view>
      <!-- <view>{{ formData.electronicPreparationResources }}</view> -->
    </view>

    <!-- <view class="content">
			<view class="content-title">预设成果或环创</view>
			<view>
				<up-textarea confirmType="none" v-model="formData.expectedOutcome"
					placeholder="请输入本活动结束后，预计产出的幼儿作品、教学成果或环境创设等" :autoHeight="autoHeight" :disabled="!_isDetail" height="148rpx"
				 maxlength="-1" @blur="changeData"></up-textarea>
			</view>
		</view> -->
  </view>
</template>

<script setup>
import { reactive, ref, watch, nextTick, onMounted } from "vue";
import { enumList, getMatrixList, getTargetList } from "@/api/index.js";
import { onLoad } from "@dcloudio/uni-app";
import {
  getStageList,
  getCoreExp,
  getJavaDictList,
} from "../../api/editActivity.js";
import utils from "../utils";
import { timeFormat } from "@/uni_modules/uview-plus";
import Upload from "../../../components/Upload/Upload.vue";
const { rules, coreFormRules } = utils();
let _gradeId = ref("");
let _subjectStageId = ref("");
let _subjectId = ref("");
let _author = ref("");
let _id = ref("");
let _isDetail = ref(true);
let formRef = ref(null);
let coreRef = ref(null);
let autoHeight = ref(false); // 修复一开始进页面autoheight高度异常的问题
onLoad((option) => {
  const { gradeId, subjectStageId, subjectId, author, id, isDetail } = option;
  _gradeId.value = gradeId;
  _subjectStageId.value = subjectStageId;
  _subjectId.value = subjectId;
  _author.value = author;
  _id.value = id;

  if (isDetail == "1") {
    _isDetail.value = false;
  }
});

const props = defineProps({
  allformData: Object,
  coreFormList: Array,
});

const formData = ref({
  id: _id.value,
  subjectStageId: "",
  author: _author.value,
  name: "",
  organizationForm: "",
  plannedGeneration: "",
  durationInMinutes: "",
  childTeacherInteraction: "",
  implementedAt: "",
  electronicPreparationResourceIds: [],
});
let isWatchOne = ref(true); // 防止监听多次触发
watch(
  () => props.allformData,
  (newVal, oldVal) => {
    if (newVal.id && isWatchOne.value) {
      formData.value = newVal;
      const data = new Date(newVal.implementedAt);
      if (formData.value.implementedAt)
        formData.value.implementedAt = timeFormat(data, "yyyy-mm-dd");
      // if (newVal?.matrices.length > 0) {
      // 	coreFormData.value = [];
      // 	newVal?.matrices.forEach((item, index) => {
      // 		coreFormData.value.push({
      // 			matrix1Id: item.matrix1Id,
      // 			matrix2Id: item.matrix2Id,
      // 			matrix3Id: item.matrix3Id,
      // 			targetId: item.targetId,
      // 			DimensionData: [
      // 				{ value: item.matrix2Id, text: item.matrix2.title },
      // 			],
      // 			subDimensionData: [
      // 				{ value: item.matrix3Id, text: item.matrix3.title },
      // 			],
      // 			indexData: [{ value: item.targetId, text: item.target.title }],
      // 			key: new Date().getTime(),
      // 		});
      // 	});
      // 	coreFormData.value.forEach((item, index) => {
      // 		getMatricesData(item.matrix1Id, item.DimensionData);
      // 		getMatricesData(item.matrix2Id, item.subDimensionData);
      // 		getMatricesData(item.matrix3Id, item.indexData, "indexData");
      // 	});
      // }
      isWatchOne.value = false;
    }
  },
  { deep: true }
);

const delFile = (item, index) => {
  formData.value.electronicPreparationResources.splice(index, 1);
};

const callback = (list) => {
  formData.value.electronicPreparationResources.push(...list);
};

const getMatricesData = (pid, arr, type = "") => {
  return new Promise(async (resolve, reject) => {
    let res;
    if (type == "indexData") {
      res = await getTargetList({
        matrix3Id: pid,
        current: 1,
        pageSize: 1000,
        gradeId: _gradeId.value,
      });
    } else {
      res = await getMatrixList({
        pid,
        current: 1,
        pageSize: 1000,
      });
    }
    if (res.data) {
      res.data.forEach((el) => {
        arr.push({
          value: el.id,
          text: el.title,
        });
      });
      console.log(arr, "arr");
      arr.shift();
    }
  });
};

// 组织形式的下拉选择数据
let zzxs = reactive([]); // 数据格式 { value: 0, text: "预设" },
// 预设生产的下拉选择数据
let yssc = reactive([
  { value: "Planned", text: "预设" },
  { value: "Generated", text: "生成" },
]);
// 预设生产的下拉选择数据
let stageList = reactive([]);
let localdata = reactive({
  domainData: [], // 领域的下拉选择数据
  DimensionData: [], // 维度的下拉选择数据
  subDimensionData: [], // 子维度的下拉选择数据
  indexData: [], // 指标的下拉选择数据
});
let coreFormData = ref([]);

// 验证方法并且暴露出去
function validate() {
  formRef.value
    .validate()
    .then((valid) => {
      if (valid) {
        console.log(coreFormData.value);
        // if (coreFormData.value.length == 0) {
        // 	uni.$u.toast("请添加核心经验");
        // }
        // // 通过验证后提交请求
        // coreValidate().then((res) => {
        // 	if (res) emit("sendformData");
        // });
        emit("sendformData");
      } else {
        uni.$u.toast("校验不通过");
      }
    })
    .catch(() => {
      uni.$u.toast("有必填项未填");
    });
}
// 验证核心经验
// function coreValidate() {
// 	let coreList = [];
// 	return new Promise((resolve, reject) => {
// 		if (coreRef.value) {
// 			coreRef.value
// 				.validate()
// 				.then((valid) => { })
// 				.catch((res) => {
// 					if (coreFormData.value.length >= 1) {
// 						coreFormData.value.forEach((obj, index) => {
// 							coreList.push({
// 								matrix1Id: obj.matrix1Id,
// 								matrix2Id: obj.matrix2Id,
// 								matrix3Id: obj.matrix3Id,
// 								targetId: obj.targetId,
// 							});
// 						});
// 						let flag = !coreList.some((value) =>
// 							Object.values(value).some(
// 								(value) =>
// 									value == undefined ||
// 									value == null ||
// 									value == "" ||
// 									value == 0
// 							)
// 						);

// 						if (flag) {
// 							// debugger
// 							resolve(true);
// 							coreRef.value.clearValidate();
// 						} else {
// 							reject(false);
// 							uni.$u.toast("请添加核心经验");
// 						}
// 					}
// 				});
// 		}
// 	});
// }

defineExpose({
  validate,
  // coreValidate,
  coreFormData,
});

// 处理时间
function datetime(e) {
  let data = new Date(e);
  formData.value.implementedAt = data.toISOString();
  emit("change", formData.value);
}

const changeData = () => {
  emit("change", formData.value);
};

const getenumList = async () => {
  let params = {
    currentPage: 1,
    pageSize: 999,
    pageModel: {
      dictCode: "activityOrganizationForm",
    },
  };
  let res = await getJavaDictList(params);
  console.log(res, "组织形式");
  if (res.status == 0) {
    let data = res.data;
    data.forEach((item) => {
      zzxs.push({
        value: item.dictItemCode,
        text: item.dictItemName,
      });
    });
  }
  //   const obj1 = res.data.SubjectActivityOrganizationFormEnumDesc;
};
const getMatrix = async (pid, arr) => {
  let res = await getMatrixList({
    pid,
    current: 1,
    pageSize: 1000,
  });
  if (res.data) {
    res.data.forEach((el) => {
      arr.push({
        value: el.id,
        text: el.title,
      });
    });
  }
};
// 请求指标下拉数据
const getTarget = async (matrix3Id, arr) => {
  let res = await getTargetList({
    matrix3Id,
    current: 1,
    pageSize: 1000,
    gradeId: _gradeId.value,
  });
  if (res.data) {
    res.data.forEach((el) => {
      arr.push({
        value: el.id,
        text: el.title,
      });
    });
  }
};

const idToNameMap = {
  matrix1Id: "matrix1Name",
  matrix2Id: "matrix2Name",
  matrix3Id: "matrix3Name",
  targetId: "targetName",
};

function coreFormChange(el, data, type, ftype, localdata) {
  if (idToNameMap[ftype])
    data[idToNameMap[ftype]] = localdata.find(
      (el) => el.value == data[ftype]
    ).text;
  if (!type) {
    return;
  }
  if (type == "indexData") {
    data[type] = [];
    getTarget(el, data[type]);
  }
  if (el && type != "indexData") {
    data[type] = [];
    getMatrix(el, data[type]);
  }
}

function addCoreFormItem() {
  coreFormData.value.push({
    matrix1Id: "",
    matrix2Id: "",
    matrix3Id: "",
    targetId: "",
    domainData: [],
    DimensionData: [],
    subDimensionData: [],
    indexData: [],
  });
}
function delCoreFormItem(i) {
  coreFormData.value.splice(i, 1);
  console.log(coreFormData.value);
}

async function getStage() {
  let id = _subjectId.value || uni.getStorageSync("subjectId");
  let res = await getStageList(id);

  res.data.forEach((el) => {
    stageList.push({
      value: el.id,
      text: el.title,
    });
  });

  formData.value.subjectStageId = Number(_subjectStageId.value);
}
// 获取核心经验
const getCoreExpList = async (relId) => {
  let res = await getCoreExp({
    relId,
    matchType: "subject",
  });
  if (res.status == 0) {
    coreFormData.value = [];
    res.data.forEach((item, index) => {
      coreFormData.value.push({
        matrix1Id: item.matrix1Id,
        matrix2Id: item.matrix2Id,
        matrix3Id: item.matrix3Id,
        targetId: item.targetId,
        matrix1Name: item.matrix1Name,
        matrix2Name: item.matrix2Name,
        matrix3Name: item.matrix3Name,
        targetName: item.targetName,
        DimensionData: [{ value: item.matrix2Id, text: item.matrix2Name }],
        subDimensionData: [{ value: item.matrix3Id, text: item.matrix3Name }],
        indexData: [{ value: item.targetId, text: item.targetName }],
        key: new Date().getTime(),
      });
    });
    coreFormData.value.forEach((item, index) => {
      getMatricesData(item.matrix1Id, item.DimensionData);
      getMatricesData(item.matrix2Id, item.subDimensionData);
      getMatricesData(item.matrix3Id, item.indexData, "indexData");
    });
  }
  console.log(res, "核心经验列表");
};

onMounted(async () => {
  getCoreExpList(_id.value);
  getStage();
  getenumList();
  getMatrix(0, localdata.domainData);
  await nextTick();
  autoHeight.value = true;
});
// 处理 核心经验
const emit = defineEmits(["change", "sendformData"]);
</script>
<script>
export default {
  options: { styleIsolation: "shared" }, // 解除样式隔离
};
</script>

<style lang="scss" scoped>
// @import "@/common/css/index.scss";
// @import "@/uni_modules/uview-plus/index.scss";
.file-item {
  display: flex;
  align-items: center;
  flex-direction: column;
  height: 100%;
}

.file-item-content {
  width: 100%;
  height: 190rpx;
  margin-top: 10rpx;
  position: relative;
}

.file-item-image,
.file-item-video {
  height: 180rpx;
  max-width: 200rpx;
  margin: 0 auto;
  display: block;
  margin-bottom: 10rpx;
}

.file-item-name {
  width: 100%;
}

.files-name {
  padding: 0 10rpx;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  white-space: normal;
  word-wrap: break-word;
}

.dataSelect {
  ::v-deep .uni-select__input-text {
    width: 400rpx;
  }
}

.addform-layout {
  // height: 100%;
  box-sizing: border-box;
}

.add-form-title {
  display: flex;
  align-items: center;
  justify-content: space-between;

  view:first-child {
    font-size: 34rpx;
    font-weight: 600;
  }

  view:last-child {
    font-size: 30rpx;
    font-weight: 500;
    color: rgba(63, 121, 255, 1);
  }
}

::v-deep .u-textarea {
  min-height: 100rpx !important;
  line-height: 30rpx !important;
}

::v-deep .uni-select__selector-item {
  border-bottom: 1px solid #eee;
  box-sizing: border-box;

  uni-text {
    width: 100%;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
    overflow: hidden !important;
  }
}

::v-deep .uni-select__selector-item:last-child {
  border-bottom: none;
}

::v-deep .uni-select {
  height: 76rpx;
  border: 1px solid #dadbde !important;
}

.content {
  .content-title {
    font-size: 28rpx;
    font-weight: 600;
    margin: 32rpx 0 26rpx 0;
  }
}

:deep(.u-form-item__body__left__content) {
  flex-direction: row-reverse;
  flex: initial;

  .u-form-item__body__left__content__required {
    right: -16rpx;
    left: initial;
  }
}
</style>
