<!-- 教学流程 -->
<template>
    <view class="teaching-layout">
        <view v-for="(item, index) in teachingData" :key="index">
            <view class="content">
                <!-- <view class="flex-jsb-ac">
                    <view class="content-title">环节{{ index + 1 }}</view>
                    <up-icon v-if="_isDetail" @click="deleteCoreFormItem(index)" name="trash" size="36rpx" />
                </view> -->
                <!-- <view>
                    <up-textarea confirmType="none" v-model="item.stepName" :disabled="!_isDetail" placeholder="请输入引入活动"
                        autoHeight maxlength="-1" @blur="changeData" />
                </view> -->
            </view>
            <!-- <view class="content">
                <view class="content-title">对应教学目标</view>
                <view>

                    <up-textarea confirmType="none" v-model="item.objective" :disabled="!_isDetail"
                        placeholder="请输入教学目标" autoHeight maxlength="-1"
                        @blur="changeData" />
                </view>
            </view> -->
            <view class="content">
                <view class="flex-jsb-ac" style="margin-bottom: 16rpx;">
                    <view class="content-title">环节{{ index + 1 }}</view>
                    <up-icon v-if="_isDetail" @click="deleteCoreFormItem(index)" name="trash" size="36rpx" />
                </view>
                <view>
                    <up-textarea confirmType="none" v-model="item.detailedProcess" :disabled="!_isDetail"
                        placeholder="请输入具体教学流程" autoHeight maxlength="-1" @blur="changeData" />

                </view>
            </view>
            <!-- <view class="content">
                <view class="content-title">备注</view>
                <view>
                    <up-textarea confirmType="none" v-model="item.note" :disabled="!_isDetail" placeholder="请输入备注"
                        autoHeight maxlength="-1" @blur="changeData" />
                </view>
            </view> -->
        </view>
        <view v-if="_isDetail" class="teachingBtn" @click="addCoreFormItem">新增环节</view>
        <up-empty v-if="!_isDetail && teachingData.length == 0" style="margin-top: 100rpx;" mode="data" />
    </view>
</template>

<script setup>
import { ref, watch } from 'vue'
import { onLoad } from "@dcloudio/uni-app"
const props = defineProps({
    allformData: Object
})
let _isDetail = ref(true)
onLoad((option) => {
    const { isDetail } = option
    if (isDetail == '1') {
        _isDetail.value = false
    }
})



watch(() => props.allformData, (newVal) => {
    if (newVal.teachingSteps) {
        teachingData.value = newVal.teachingSteps
    }

}, { deep: true })

let teachingData = ref([{
    detailedProcess: ''
}])

const changeData = () => {
    emit('change', teachingData.value)
}

function addCoreFormItem() {
    console.log(teachingData.value);

    teachingData.value.push(
        {
            stepName: "",
            objective: "",
            detailedProcess: "",
            note: "",
        }
    )
}

// 删除
function deleteCoreFormItem(i) {
    teachingData.value.splice(i, 1)
}
const emit = defineEmits(['change'])
</script>
<script>
export default {
    options: { styleIsolation: 'shared' }  // 解除样式隔离
}
</script>
<style lang="scss" scoped>
::v-deep .u-textarea {
    min-height: 110rpx;
}

.teachingBtn {
    color: rgba(63, 121, 255, 1);
    font-weight: 600;
    font-size: 30rpx;
    margin: 20rpx 0;
    padding-bottom: 20rpx;
    text-align: center;
}

.content {
    margin: 32rpx 0;

    .content-title {
        font-size: 28rpx;
        font-weight: 600;
    }
}
</style>