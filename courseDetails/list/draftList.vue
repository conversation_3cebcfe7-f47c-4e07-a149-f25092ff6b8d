<!-- 草稿列表 -->
<template>
  <BaseLayout2
    navTitle="草稿箱"
    autoBack
    :contentStyle="{
      paddingTop: '16rpx',
    }"
    :footerStyle="{
      backgroundColor: '#fff',
      paddingBottom: 'calc(20rpx + env(safe-area-inset-bottom))',
      marginTop: '16rpx',
    }"
    :refresherEnabled="true"
    :refresherTriggered="refreshing"
    enableLoadMore
    @refresherrefresh="handleRefresherRefresh"
    @scrolltolower="handleScrolltolower"
  >
    <!-- 页面内容 -->
    <view style="padding: 0 32rpx">
      <LibList
        :data="courseList"
        :rightSlot="true"
        height="100%"
        :status="isScrollLoading"
        emptyText="暂无草稿，请现在下方添加！"
        @click="gotoDetails"
        @rightChlick="handleRefresherRefresh"
      />
    </view>
    <template #footer>
      <up-button
        class="add-btn"
        shape="circle"
        color="#367CFF"
        text="新增课程"
        @click="goto"
      />
    </template>
  </BaseLayout2>
</template>
<script setup>
import { onLoad, onShow, onPullDownRefresh } from "@dcloudio/uni-app";
import { onMounted, reactive, ref, nextTick } from "vue";
import BaseLayout2 from "@/components/base-layout/base-layout2.vue";
// import { getAllCouresLib } from "@/courseDetails/api";
import { getAiclassList } from "@/api";
import { getCategoryList } from "@/api/api.js";
import LibList from "./components/libList.vue";
let refreshing = ref(false); // 下拉刷新
let listHeight = ref(0);
let courseList = ref([]); // 课程列表
let isScrolltolower = ref(true); // 是否滚动到底部
let isScrollLoading = ref("");
// 映射表
const typeMap = {
  currentClassSubject: "",
  currentSchoolSubject: "园本课程库",
  platformPublicSubject: "公共课程库",
};

const paging = reactive({
  currentPage: 1,
  pageSize: 10,
  term: null,
  categoryId: null,
});

// 跳转详情页
const gotoDetails = (item) => {
  console.log(item.id);
  console.log(item.gradeId);
  uni.navigateTo({
    url: `/courseDetails/classHome/classHomeV1?id=${item.id}&gradeId=${item.gradeId}`,
  });
};

// 当前班级的课程列表
const getCurClassList = async () => {
  isScrollLoading.value = "loading";
  const userInfo = uni.getStorageSync("USER_INFO");
  const params = {
    currentPage: paging.currentPage,
    pageSize: paging.pageSize,
    pageModel: {
      schoolId: userInfo.currentSchoolId,
      classId: userInfo.currentClassId,
      statusList: [2, 4],
    },
  };
  try {
    let res = await getAiclassList(params);
    if (res.status == 0) {
      isScrollLoading.value = "noMore";
      courseList.value = courseList.value.concat(res.data);
      if (paging.currentPage >= Math.ceil(res.metadata.count / paging.pageSize))
        isScrolltolower.value = false;
    }
  } catch (error) {
    console.log(error);
    uni.$u.toast("获取班级课程列表失败");
  }
};

// 下拉刷新
const handleRefresherRefresh = async () => {
  refreshing.value = true;
  paging.currentPage = 1;
  courseList.value = [];
  isScrolltolower.value = true;
  await getCurClassList();
  refreshing.value = false;
};

// 上拉加载
const handleScrolltolower = async () => {
  if (isScrolltolower.value) {
    paging.currentPage++;
    await getCurClassList();
  }
};

const goto = () => {
  uni.navigateTo({
    url: "/courseDetails/aiWrite/aiWrite",
  });
};

// 获取当前类名到屏幕底部的距离
const getDistanceToBottom = () => {
  let distance = 0;
  nextTick(() => {
    const query = uni.createSelectorQuery().in(this);
    query.select(".filter").boundingClientRect();
    query.selectViewport().scrollOffset();
    query.exec((res) => {
      const titleRect = res[0];
      const windowHeight = uni.getSystemInfoSync().windowHeight;
      listHeight.value =
        Math.ceil(windowHeight - titleRect.top - titleRect.height) - 88;
      console.log("距离底部的距离", listHeight.value);
    });
  });
};

// 获取课程类别列表
// const getCategory = async () => {
//   // 获取学期列表
//   let list = uni.getStorageSync("ENUM")["SubjectTermDescEnum"] || [];
//   termList.value = Object.keys(list).map((key) => {
//     return { id: list[key], title: key };
//   });
//   try {
//     let res = await getCategoryList({
//       currentPage: 1,
//       pageSize: 10,
//       category: 31,
//     });
//     if (res.status == 0) {
//       categoryList.value = res.data;
//       // 添加课程类别 判断长度是否一样
//       const storedCategoryList = uni.getStorageSync("CATEGORY_LIST");
//       if (
//         !storedCategoryList ||
//         storedCategoryList.length !== categoryList.value.length
//       ) {
//         uni.setStorageSync("CATEGORY_LIST", res.data);
//       }
//     }
//   } catch (e) {
//     console.log(e);
//     uni.$u.toast("获取课程类别失败");
//   }
// };

// 初始化数据
const initPar = reactive({
  current: 1,
  pageSize: 10,
});
onLoad((options) => {
  // navTitle.value = options.classTitle
  //   ? options.classTitle
  //   : typeMap[options.type];
  // if (options.type == "platformPublicSubject")
  //   initPar.subjectSharingLevel = "PlatformPublic";
  // if (options.type == "currentClassSubject")
  //   initPar.classId = Number(options.classId);
});
// 下拉刷新
onPullDownRefresh(() => getCurClassList());
onShow(() => {
  getCurClassList();
});
onMounted(() => {
  // getCategory();
  // getDistanceToBottom();
});
</script>

<style lang="scss" scoped></style>
