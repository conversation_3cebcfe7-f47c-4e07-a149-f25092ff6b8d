<template>
  <BaseLayout2
    :nav-title="navTitle"
    :footerStyle="
      isFooterBtn
        ? {
            backgroundColor: '#fff',
            paddingBottom: 'calc(20rpx + env(safe-area-inset-bottom))',
          }
        : 'display: none'
    "
    :scrollEnabled="false"
    :refresherEnabled="true"
    :refresherTriggered="refreshing"
    @refresherrefresh="handleRefresherRefresh"
  >
    <view style="padding: 0 32rpx">
      <up-search
        @click="gotoSearch"
        bgColor="#ffffff"
        placeholder="搜索课程..."
        disabled
        :showAction="false"
        style="margin-bottom: 32rpx"
      />
      <!-- 筛选项 -->
      <view class="filter">
        <view class="filter-col">
          <text
            class="filter-item"
            :class="{ 'active-filter-item': categoryActiveIndex == 0 }"
            @click="onFilter('categoryall')"
            >全部</text
          >
          <text
            class="filter-item"
            :class="{ 'active-filter-item': categoryActiveIndex == item.id }"
            v-for="(item, index) in categoryList"
            :key="index"
            @click="onFilter('category', item.id)"
            >{{ item.value }}</text
          >
        </view>
        <view class="filter-col">
          <text
            class="filter-item"
            :class="{ 'active-filter-item': termActiveIndex == 0 }"
            @click="onFilter('termall')"
            >全部</text
          >
          <text
            class="filter-item"
            :class="{ 'active-filter-item': termActiveIndex == item.id }"
            v-for="(item, index) in termList"
            :key="index"
            @click="onFilter('term', item.id)"
            >{{ item.title }}</text
          >
        </view>
      </view>

      <!-- 页面内容 -->
      <LibList
        :data="courseList"
        :height="listHeight"
        :status="isScrollLoading"
        @click="gotoDetails"
        @rightChlick="handleRefresherRefresh"
        @scrolltolower="handleScrolltolower"
        :rightSlot="true"
        :type="initPar.subjectSharingLevel"
        :emptyText="
          isFooterBtn
            ? '暂无课程，请到下方草稿箱修改课程状态或新增课程'
            : '暂无课程，请到草稿箱修改课程状态或新增课程'
        "
      />
    </view>

    <template
      #footer
      v-if="isFooterBtn"
    >
      <view class="footer">
        <view
          class="footer-left"
          @click="goto('draft')"
        >
          <view class="bor">
            <image
              src="https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/course_list_draft.svg"
            />
          </view>
          <text>草稿箱</text>
        </view>
        <up-button
          class="add-btn"
          shape="circle"
          color="#367CFF"
          text="新增课程"
          @click="goto"
        />
      </view>
    </template>
  </BaseLayout2>
</template>
<script setup>
import { onLoad, onShow } from "@dcloudio/uni-app";
import { onMounted, reactive, ref, getCurrentInstance } from "vue";
import BaseLayout2 from "@/components/base-layout/base-layout2.vue";
import { getAiclassList } from "@/api";
import { getCategoryList } from "@/api/api.js";
import LibList from "./components/libList.vue";

let instance = getCurrentInstance();
let navTitle = ref(" ");
let termActiveIndex = ref(0);
let categoryActiveIndex = ref(0);
let termList = ref([]);
let categoryList = ref([]);
let listHeight = ref(0);
let courseList = ref([]); // 课程列表
let isScrolltolower = ref(true);
let isScrollLoading = ref("loading"); // 是否正在加载
let isFooterBtn = ref(true); // 是否显示底部按钮 默认显示
let refreshing = ref(false);
// 映射表
const typeMap = {
  currentClassSubject: "",
  currentSchoolSubject: "园本课程库",
  platformPublicSubject: "公共课程库",
};

const paging = reactive({
  currentPage: 1,
  pageSize: 10,
  term: null,
  categoryId: null,
});

// 初始化数据
const initPar = reactive({
  subjectSharingLevel: null,
  classId: null,
});
onLoad((options) => {
  navTitle.value = options.classTitle
    ? options.classTitle
    : typeMap[options.type];
  let type = options.type;
  if (type === "currentSchoolSubject" || type === "platformPublicSubject") {
    isFooterBtn.value = false;
    if (options.type === "platformPublicSubject") {
      initPar.subjectSharingLevel = "PlatformPublic";
    }
  }
  if (type == "currentClassSubject") initPar.classId = Number(options.classId);
});

// 跳转详情页
const gotoDetails = (item) => {
  if (item.classId) uni.setStorageSync("classId", item.classId);
  let url = `/courseDetails/classHome/classHomeV1?id=${item.id}&gradeId=${item.gradeId}`;
  if (initPar.subjectSharingLevel == "PlatformPublic") {
    url += "&isPublic=true";
  }
  uni.navigateTo({
    url,
  });
};

// 当前班级的课程列表
const getCurClassList = async (param) => {
  isScrollLoading.value = "loading";
  const userInfo = uni.getStorageSync("USER_INFO");
  const params = {
    currentPage: paging.currentPage,
    pageSize: paging.pageSize,
    pageModel: {
      ...param,
      schoolId: userInfo.currentSchoolId,
      statusList: [1],
    },
  };
  if (initPar.subjectSharingLevel)
    params.pageModel.subjectSharingLevel = initPar.subjectSharingLevel;
  if (initPar.classId) params.pageModel.classId = initPar.classId;
  if (paging.term) params.pageModel.term = paging.term;
  if (paging.categoryId) params.pageModel.categoryId = paging.categoryId;
  try {
    let res = await getAiclassList(params);
    if (res.status == 0) {
      isScrollLoading.value = "nomore";
      courseList.value = courseList.value.concat(res.data);
      uni.stopPullDownRefresh();
      if (paging.currentPage >= Math.ceil(res.metadata.count / paging.pageSize))
        isScrolltolower.value = false;
    }
  } catch (error) {
    console.log(error);
    uni.$u.toast("获取班级课程列表失败");
  }
};

const goto = (type) => {
  let url =
    type == "draft"
      ? "/courseDetails/list/draftList"
      : "/courseDetails/aiWrite/aiWrite";

  uni.navigateTo({ url });
};

// 获取当前类名到屏幕底部的距离
const getDistanceToBottom = () => {
  const query = uni.createSelectorQuery().in(instance);
  query.select(".filter").boundingClientRect();
  query.selectViewport().scrollOffset();
  query.exec((res) => {
    const titleRect = res[0];
    const windowHeight = uni.getSystemInfoSync().windowHeight;
    const isIos = uni.getSystemInfoSync().osName === "ios";
    const iosHeight = uni.getSystemInfoSync()?.safeAreaInsets.bottom - 14;
    const h = Math.ceil(windowHeight - titleRect.top - titleRect.height) - 24;
    const pt = uni.getSystemInfoSync().uniPlatform;
    console.log("系统信息", uni.getSystemInfoSync());
    console.log("IOS底部距离", iosHeight);
    console.log("IOS", isIos); 
    console.log("平台", pt); 
    if (isIos) h - iosHeight;
    listHeight.value = isFooterBtn.value ? h - 60 : h;
    if (pt == "mp-weixin") listHeight.value -= 34;
    console.log("距离底部的距离", listHeight.value);
  });
};

// 获取课程类别列表
const getCategory = async () => {
  // 获取学期列表
  let list = uni.getStorageSync("ENUM")["SubjectTermDescEnum"] || [];
  termList.value = Object.keys(list).map((key) => {
    return { id: list[key], title: key };
  });
  categoryList.value = uni.getStorageSync("CATEGORY_LIST") || [];
  if (categoryList.value.length) return;
  try {
    let res = await getCategoryList({
      currentPage: 1,
      pageSize: 10,
      category: 31,
    });
    if (res.status == 0) categoryList.value = res.data;
  } catch (e) {
    console.log(e);
    uni.$u.toast("获取课程类别失败");
  }
};

function gotoSearch() {
  uni.navigateTo({
    url: "/courseDetails/search/search",
  });
}

// 筛选函数
function onFilter(type, id) {
  console.log(id);
  let params = {};
  switch (type) {
    case "termall":
      termActiveIndex.value = 0;
      paging.term = null;
      break;
    case "term":
      termActiveIndex.value = id;
      params = { term: id };
      paging.term = id;
      break;
    case "categoryall":
      categoryActiveIndex.value = 0;
      paging.categoryId = null;
      break;
    case "category":
      categoryActiveIndex.value = id;
      params = { categoryId: id };
      paging.categoryId = id;
      break;
  }
  getCurClassList(params);
}

// 下拉刷新
const handleRefresherRefresh = async () => {
  refreshing.value = true;
  courseList.value = [];
  isScrolltolower.value = true;
  paging.currentPage = 1;
  await getCurClassList();
  refreshing.value = false;
};
// 上拉加载
const handleScrolltolower = async () => {
  if (isScrolltolower.value) {
    paging.currentPage++;
    await getCurClassList();
  }
};
onShow(() => {
  courseList.value = [];
  getCurClassList();
});
onMounted(() => {
  getCategory();
  getDistanceToBottom();
});
</script>

<style lang="scss" scoped>
.footer {
  display: flex;
  align-items: center;

  .u-button {
    margin: 0 !important;
  }

  &-left {
    flex: 1;
    font-size: 20rpx;
    font-weight: 400;
    display: flex;
    align-items: center;
    flex-direction: column;

    .bor {
      width: 50rpx;
      height: 50rpx;
      border-radius: 50%;
      background: #f5f5f5;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    text {
      margin-top: 6rpx;
    }

    image {
      width: 26rpx;
      height: 26rpx;
    }
  }

  .add-btn {
    width: 546rpx;
  }
}

.filter {
  font-size: 24rpx;
  margin-bottom: 28rpx;

  .filter-col {
    white-space: nowrap;
    overflow-y: auto;
    margin-bottom: 24rpx;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .filter-item {
    display: inline-block;
    height: 48rpx;
    line-height: 48rpx;
    text-align: center;
    // box-sizing: border-box;
    padding: 0 20rpx;
    background: #fff;
    font-weight: 400;
    border-radius: 52.8rpx;
    margin-right: 16rpx;

    &:last-child {
      margin-right: 0;
    }
  }

  .active-filter-item {
    background: #3f79ff;
    color: #fff;
    font-weight: 600;
  }
}
</style>
