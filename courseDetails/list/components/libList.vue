<template>
  <view>
    <up-list
      :height="height || 0"
      style="box-sizing: border-box"
      v-if="list && list.length > 0"
      enableFlex
      @scrolltolower="emit('scrolltolower')"
    >
      <up-list-item
        v-for="(item, index) in list"
        :key="index"
      >
        <view class="basic-container">
          <view
            class="left"
            @tap="emit('click', item)"
          >
            <image
              src="https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/index_icon-1.png"
            />
            <text
              class="img-bg-text"
              v-if="item.aiFlag == 1"
              >青禾AI</text
            >
          </view>
          <view
            class="center"
            @tap="emit('click', item)"
          >
            <view class="title">
              <text class="title-text">{{ item.title }} </text>
              <text
                class="tag"
                :style="[
                  { marginLeft: rightSlot ? 'none' : 'auto' },
                  gradeStyleMap(gradeMap(item.categoryId)),
                ]"
                v-if="gradeMap(item.categoryId)"
                >{{ gradeMap(item.categoryId) }}</text
              >
            </view>
            <view class="content">
              <template v-if="item.subjectSharingLevel != 'PlatformPublic'">
                <view
                  >{{ item?.schoolClass?.title }} | {{ termMap(item.term) }} |
                  {{ item.creator }}</view
                >
                <view>{{ item?.schoolClass?.schoolTitle }}</view>
              </template>
              <!-- <view>活动：2/10 | 评价：2/10</view> -->
              <view
                >实施周期：{{ formatTime(item.stratAt) }} -
                {{ formatTime(item.endAt) }}</view
              >
              <view
                >最新修改日期：{{ formatTime(item.latestImplementedAt) }}</view
              >
            </view>
          </view>
          <view
            class="right"
            v-if="rightSlot"
          >
            <up-icon
              @click="onListClick(item, index)"
              :stop="true"
              size="42rpx"
              name="more-dot-fill"
            />
          </view>
          <view
            class="mask"
            v-if="item.state == 2 || item.state == 3"
            @click.stop.prevent="onMask(item.state)"
          />
        </view>
      </up-list-item>
      <up-loadmore :status="status" />
      <view
        v-if="list.length == 0"
        class="empty-text"
        >{{ emptyText }}</view
      >
    </up-list>

    <Popup
      :show="isShowPopup"
      @close="close"
    >
      <add-form
        v-if="isShowAddForm"
        @confirm="confirmEdit"
        title="编辑课程"
        :data="curData"
      />
      <view v-else>
        <popup-item
          v-if="type == 'PlatformPublic'"
          icon="/static/icon/index_copy_3x.png"
          text="复制到草稿箱"
          @tap="headleAction(3)"
        />
        <popup-item
          v-if="type != 'PlatformPublic'"
          icon="/static/common/editor.png"
          text="编辑"
          @tap="headleAction(1)"
        />
        <popup-item
          v-if="type != 'PlatformPublic'"
          icon="/static/common/delete.png"
          text="删除"
          @tap="headleAction(2)"
        />
      </view>
    </Popup>

    <up-modal
      :show="deletePar.isShowDelete"
      showCancelButton
      asyncClose
      @cancel="deletePar.isShowDelete = false"
      @confirm="confirmDelete"
      width="622rpx"
    >
      <view>
        本课程的所有阶段、活动、评价信息等都将被删除，删除后将不能找回，确定要删除
        <text style="color: red">{{ curData?.title }}</text> 吗？
      </view>
    </up-modal>
  </view>
</template>

<script setup>
import { computed, ref, reactive } from "vue";
import dayjs from "dayjs";
import Popup from "@/components/Popup/Popup.vue";
import popupItem from "@/components/Popup-item/Popup-item.vue";
import addForm from "@/courseDetails/components/addForm/addForm.vue";
import { deleteItemList } from "@/api/api.js";
import { copyToDraft } from "@/courseDetails/api";
let isShowPopup = ref(false); // 是否显示弹窗
let isShowAddForm = ref(false); // 是否显示添加表单
let curData = ref(null); // 当前选中数据
let deletePar = reactive({
  content: "",
  isShowDelete: false, // 是否显示删除弹窗
}); // 删除参数

const list = computed(() => props.data);
const termMap = (id) => {
  const map = uni.getStorageSync("ENUM").SubjectTermEnum;
  return map[id] || `-`;
};

const categoryStyleMap = [
  {
    value: "主题课程",
    itemStyle: {
      background: "#FDF2EA",
      color: "#F0A14D",
    },
  },
  {
    value: "项目课程",
    itemStyle: {
      background: "#EDF2FE",
      color: "#5283F7",
    },
  },
  {
    value: "领域/特色课程",
    itemStyle: {
      background: "#FCEDEE",
      color: "#ED6F72",
    },
  },
  {
    value: "生活课程",
    itemStyle: {
      background: "#F0F1FC",
      color: "#6E74E6",
    },
  },
  {
    value: "STEM课程",
    itemStyle: {
      background: "#EDF8F0",
      color: "#54BA6A",
    },
  },
  {
    value: "体育课程",
    itemStyle: {
      background: "#F0F1F6",
      color: "#7278A6",
    },
  },
  {
    value: "学习活动",
    itemStyle: {
      background: "#FDF2EA",
      color: "#F0A14D",
    },
  },
];

//
const close = () => {
  isShowPopup.value = false;
  setTimeout(() => {
    isShowAddForm.value = false;
  }, 350);
};

// 课程类别样式映射表
const gradeStyleMap = (title) => {
  // let data = uni.getStorageSync("CATEGORY_LIST");
  let style = {};
  categoryStyleMap.forEach((item) => {
    if (item.value == title) {
      style = item.itemStyle;
    }
  });
  return style;
};

// 课程类别映射表
const gradeMap = (id) => {
  let data = uni.getStorageSync("CATEGORY_LIST");
  if (data) {
    const map = data.find((item) => item.id == id);
    return map?.value;
    // 失败提示
  }
  return "-";
};
const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
  height: {
    type: Number || String,
  },
  emptyText: {
    type: String,
    default: "暂无数据",
  },
  rightSlot: {
    type: Boolean,
    default: false,
  },
  // 判断是什么页面类型
  type: {
    type: String,
    default: null, // default: 默认, course: 课程
  },
  status: {
    type: String,
    default: "loadmore", // 加载前值为loadmore，加载中为loading，没有数据为nomore
  },
});

// 确认删除
const confirmDelete = async (e) => {
  const { id, index } = curData.value;
  let res = await deleteItemList({ id, forceFlag: true });
  if (res.status == 0) {
    emit("rightChlick");
    uni.$u.toast("删除成功");
  } else {
    uni.$u.toast(res?.message || "删除失败");
  }
  deletePar.isShowDelete = false;
  isShowPopup.value = false;
};

// 点击列表一项
const onListClick = (e, i) => {
  curData.value = e;
  curData.value.index = i;
  isShowPopup.value = true;
};

// 重置状态
const resetState = () => {
  isShowPopup.value = false;
  isShowAddForm.value = false;
  curData.value = null;
  deletePar.isShowDelete = false;
};

// 操作区
const headleAction = async (e) => {
  const { id, title } = curData.value;
  // 1 编辑 2 删除
  switch (e) {
    case 1:
      isShowPopup.value = false;
      setTimeout(() => {
        isShowAddForm.value = true;
        isShowPopup.value = true;
      }, 350);
      break;
    case 2:
      deletePar.content = `${title}  吗？`;
      deletePar.isShowDelete = true;
      break;
    case 3:
      resetState();
      uni.showLoading({
        title: "正在复制...",
        mask: true,
      });
      let res = await copyToDraft(id);
      uni.hideLoading();
      if (res.status == 0) {
        uni.$u.toast("复制成功");
        return;
      }
      uni.$u.toast(res?.message || "复制失败");
    default:
      break;
  }

  // console.log(e);
  isShowPopup.value = false;
};

// 确认编辑按钮,成功后会触发
const confirmEdit = async (e) => {
  isShowPopup.value = false;
  setTimeout(() => {
    isShowAddForm.value = false;
    emit("rightChlick"); // 重新获取列表
  }, 300);
};

// 点击遮罩
function onMask(state) {
  state == 2
    ? uni.$u.toast("课程正在生成中...")
    : uni.$u.toast("AI生成失败，请联系管理员！");
}
// 格式化时间
const formatTime = (time, type = "/") => {
  if (time) {
    if (!dayjs(time).isValid()) return "-";
    return dayjs(time).format(`YYYY${type}MM${type}DD`);
  }
  return '-'
};
const emit = defineEmits(["click", "rightChlick", "scrolltolower"]);
</script>

<style lang="scss" scoped>
.empty-text {
  margin-top: 200rpx;
  font-size: 26rpx;
  font-weight: 500;
  text-align: center;
  color: #808080;
}

.basic-container {
  border-radius: 28rpx;
  background: rgba(255, 255, 255, 1);
  padding: 28rpx;
  box-sizing: border-box;
  min-height: 156rpx;
  display: flex;
  margin-bottom: 24rpx;
  position: relative;

  .mask {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 28rpx;
    z-index: 10;
  }

  .center {
    flex: 1;

    .content {
      font-size: 24rpx;
      font-weight: 500;
      color: #808080;
      display: flex;
      flex-direction: column;
      gap: 10rpx;
    }

    .title {
      font-size: 30rpx;
      font-weight: 600;
      color: #333333;
      display: flex;
      align-items: center;
      margin-bottom: 10rpx;

      &-text {
        width: 290rpx;
        white-space: nowrap;
        /* 不换行 */
        overflow: hidden;
        /* 隐藏超出部分 */
        text-overflow: ellipsis;
        /* 显示省略号 */
        margin-right: 8rpx;
      }

      .tag {
        font-size: 20rpx;
        line-height: 24rpx;
        font-weight: 500;
        display: inline-block;
        min-width: 92rpx;
        height: 32rpx;
        border-radius: 5.84rpx;
        color: #f0a14d;
        padding: 6rpx 12rpx;
        box-sizing: border-box;
        background: rgba(240, 161, 77, 0.12);
        white-space: nowrap;
      }
    }
  }

  .right {
    width: 63rpx;
    display: flex;
    justify-content: center;
    align-items: flex-start;
  }

  .left {
    width: 130rpx;
    height: 130rpx;
    background: #d7f2b3;
    border-radius: 24rpx;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 28rpx;

    .img-bg-text {
      display: inline-block;
      position: absolute;
      top: 0;
      left: 0;
      font-size: 18rpx;
      font-weight: 500;
      height: 30rpx;
      line-height: 30rpx;
      padding: 0 10rpx;
      border-radius: 16rpx 0rpx 16rpx 0rpx;
      background: rgba(51, 51, 51, 0.6);
      z-index: 5;
      text-align: center;
      color: #fff;
    }

    image {
      width: 89rpx;
      height: 89rpx;
    }
  }
}
</style>
