import { reactive, ref } from 'vue';
import { getCourseList } from '@/api/api.js';
import { getclassList, getAiclassList } from "@/api/index.js"
export default () => {
	let keyword = ref('')
	let isSearchTag = ref(true) // 是否显示固定标签
	let isLoading = ref(false)
	let tagIndex = ref(0)
	let searchData = reactive({
		tagData: [],
		listData: [],
	})

	function sendSearch() {
		let data = {}
		if (tagIndex.value != 0) data.classId = tagIndex.value
		if (keyword.value) data.subjectName = keyword.value
		getList(data)
	}
	function clear() {
		keyword.value = ''
		tagIndex.value = 0
		getList()
	}

	const getList = async (data = {}) => {
		isLoading.value = true
		let res = await getAiclassList({

			currentPage: 1,
			pageModel: {
				schoolId: uni.getStorageSync("USER_INFO").currentSchoolId,
				...data,
				statusList: [1],
			},
			pageSize: 10
		});
		searchData.listData = res.data
		isLoading.value = false
	}

	const getclass = async () => {
		let res = await getclassList();
		console.log(res, "班级");
		searchData.tagData = res.data
	}

	function cardTime(item) {
		let string = item?.split(' ')[0]
		return string?.replace(/-/g, '.')
	}

	function onClickTag(item) {
		tagIndex.value = item.id
		let data = {}
		if (tagIndex.value != 0) data.classId = tagIndex.value
		if (keyword.value) data.title = keyword.value
		// isSearchTag.value = false
		getList(data)
	}

	function gotoClassDetails(item) {
		console.log(item);
		let { id, gradeId } = item
		// http://localhost:5173/#/courseDetails/classHome/classHomeV1?id=1439&gradeId=2820
		uni.redirectTo({
			url: `/courseDetails/classHome/classHomeV1?id=${id}&gradeId=${gradeId}`
		})
	}

	return {
		keyword,
		sendSearch,
		getList,
		getclass,
		searchData,
		cardTime,
		isSearchTag,
		isLoading,
		onClickTag,
		tagIndex,
		clear,
		gotoClassDetails
	}
}