<template>
  <view class="search-layout layout">
    <view class="search">
      <up-search
        bgColor="#f2f2f2"
        height="64rpx"
        placeholder="请输入课程名字"
        v-model="keyword"
        @search="sendSearch"
        @custom="sendSearch"
        @clear="clear"
        :animation="true"
        :clearabled="true"
        :focus="true"
      />
    </view>
    <view
      class="search-tag"
      style="margin-top: 30rpx; padding: 0 30rpx"
      v-if="isSearchTag"
    >
      <view class="title flex-ac">
        <view class="barline"></view>
        <view class="text">班级</view>
      </view>
      <view class="tag">
        <up-tag
          v-for="(item, index) in searchData.tagData"
          :name="item.id"
          :key="index"
          class="tag-item"
          @click="onClickTag(item)"
          :text="item.title"
          :plain="tagIndex == item.id ? false : true"
        />
      </view>
    </view>
    <!-- 搜索列表 -->
    <view style="margin-top: 30rpx; padding: 0 30rpx">
      <view
        style="position: relative"
        v-if="searchData.listData && searchData.listData.length > 0"
      >
        <view
          class="card"
          @click="gotoClassDetails(item)"
          v-for="(item, index) in searchData.listData"
          :key="item.classId + index"
        >
          <view class="img-bg">
            <text
              class="img-bg-text"
              v-if="item.aiFlag == 1"
              >青禾AI</text
            >
            <image
              style="width: 110rpx; height: 110rpx"
              src="https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/index_icon-1.png"
            >
            </image>
          </view>
          <view class="content">
            <view class="card-title">
              {{ item.title }}
            </view>
            <view class="card-text">{{ item.schoolClass?.schoolTitle }}</view>
            <view class="card-text"
              >{{ item.schoolClass?.title || "-" }} |
              {{ item.creator || "-" }}
            </view>
            <view class="card-date"
              >{{ cardTime(item.startAt) }}-{{ cardTime(item.endAt) }}
            </view>
          </view>
          <!-- <view class="card-icon">
						<up-icon @click="openPopup(item, index)" size="42rpx" name="more-dot-fill" />
					</view> -->
        </view>
      </view>
      <up-empty
        style="margin-top: 200rpx"
        v-if="!isLoading && searchData.listData && searchData.listData.length == 0"
        mode="data"
      />
      <up-loading-icon
        style="margin-top: 200rpx"
        v-if="isLoading"
        mode="circle"
      />
    </view>
  </view>
</template>

<script setup>
import search from "./utils/search.js";
const {
  keyword,
  sendSearch,
  getList,
  getclass,
  cardTime,
  searchData,
  isSearchTag,
  isLoading,
  onClickTag,
  tagIndex,
  clear,
  gotoClassDetails,
} = search();
getclass();
</script>

<style lang="scss" scoped>
.search-layout {
  height: 100%;
  overflow-y: auto;
  padding-bottom: 80rpx;
  .search {
    padding: 12rpx 24rpx;
    background: #ffffff;
  }

  // height: 30rpx;

  .title {
    height: 36rpx;

    .barline {
      width: 10rpx;
      height: 100%;
      border-radius: 30rpx;
      background: #367cff;
      margin-right: 14rpx;
    }

    .text {
      font-weight: 400;
    }
  }

  .tag {
    padding-top: 24rpx;

    .tag-item {
      margin: 12rpx;
    }
  }

  .card {
    height: 216rpx;
    opacity: 1;
    border-radius: 28rpx;
    margin-bottom: 24rpx;
    background: #ffffff;
    box-sizing: border-box;
    padding: 28rpx;
    display: flex;
    align-items: center;
    position: relative;

    .card-icon {
      height: 100%;
      position: absolute;
      top: 28rpx;
      right: 28rpx;
    }

    .img-bg {
      width: 160rpx;
      height: 160rpx;
      background: rgba(215, 242, 179, 1);
      border-radius: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 24rpx;
      
      .img-bg-text {
        display: inline-block;
        position: absolute;
        top: 0;
        left: 0;
        font-size: 18rpx;
        font-weight: 500;
        width: 74rpx;
        height: 30rpx;
        line-height: 30rpx;
        border-radius: 16rpx 0rpx 16rpx 0rpx;
        background: rgba(51, 51, 51, 0.6);
        z-index: 5;
        text-align: center;
        color: #fff;
      }
    }

    .content {
      flex: 1;
      font-size: 24rpx;
      font-weight: 500;
      letter-spacing: 0rpx;
      line-height: 33.6rpx;
      color: rgba(128, 128, 128, 1);

      .card-title {
        width: 376rpx;
        font-size: 30rpx;
        font-weight: 700;
        line-height: 42rpx;
        color: rgba(51, 51, 51, 1);
        margin-bottom: 4rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}
</style>
