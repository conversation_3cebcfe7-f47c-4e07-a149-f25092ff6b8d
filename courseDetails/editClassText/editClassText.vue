<!-- 编辑课程文本 -->
<template>
  <view class="edit-classText-layout">
    <view class="col">
      <up-form
        ref="formRef"
        labelPosition="top"
        :model="formData.data"
        label-width="140rpx"
        :labelStyle="labelStyle"
      >
        <up-form-item
          label="缘起"
          prop="origin"
        >
          <up-textarea
            class="textareaStyle"
            placeholder="请输入缘起"
            confirm-type="noen"
            maxlength="-1"
            v-model="formData.data.origin"
            :autoHeight="autoHeight"
            disableDefaultPadding
          />
        </up-form-item>
        <up-form-item
          label="教师先行"
          class="form-item1"
          prop="name"
        >
          <view
            v-for="(item, index) in formData.data.prospect"
            :key="index"
            style="width: 100%; margin-top: 20rpx"
          >
            <view
              style="
                display: flex;
                align-items: center;
                justify-content: space-between;
              "
            >
              <text class="tipstext">教师的困惑 {{ index + 1 }}</text>
              <up-icon
                @click="delFormItem(index)"
                name="trash"
                size="36rpx"
              />
            </view>
            <up-input
              v-model="item.question"
              placeholder="请输入教师的困惑"
              clearable
              style="margin-top: 20rpx"
            />
            <text
              class="tipstext"
              style="margin-top: 20rpx"
              >自学知识</text
            >
            <up-textarea
              class="textareaStyle"
              placeholder="请输入自学知识"
              confirm-type="noen"
              maxlength="-1"
              v-model="item.answer"
              :autoHeight="autoHeight"
              disableDefaultPadding
            />
          </view>
          <view
            class="newlist"
            @click="formData.data.prospect.push({ answer: '', question: '' })"
            >新增困惑</view
          >
        </up-form-item>
        <up-form-item
          label="学情分析"
          prop="interest"
        >
          <up-textarea
            class="textareaStyle"
            placeholder="请输入学情分析"
            confirm-type="noen"
            :autoHeight="autoHeight"
            maxlength="-1"
            v-model="formData.data.studyAnalyze"
            disableDefaultPadding
          />
        </up-form-item>
        <up-form-item
          label="幼儿兴趣"
          prop="interest"
        >
          <up-textarea
            class="textareaStyle"
            v-model="formData.data.interest"
            maxlength="-1"
            placeholder="请输入幼儿兴趣"
            confirm-type="noen"
            :autoHeight="autoHeight"
            disableDefaultPadding
          />
        </up-form-item>
        <up-form-item
          label="课程目标"
          prop="interest"
        >
          <up-textarea
            class="textareaStyle"
            placeholder="请输入课程目标"
            confirm-type="noen"
            :autoHeight="autoHeight"
            maxlength="-1"
            v-model="formData.data.objectives"
            disableDefaultPadding
          />
        </up-form-item>
        <up-form-item
          label="课程重点"
          prop="keynote"
        >
          <up-textarea
            class="textareaStyle"
            v-model="formData.data.keynote"
            placeholder="请输入课程重点"
            confirm-type="noen"
            maxlength="-1"
            :autoHeight="autoHeight"
            disableDefaultPadding
          />
        </up-form-item>
        <up-form-item label="课程难点">
          <up-textarea
            class="textareaStyle"
            v-model="formData.data.difficulties"
            placeholder="请输入课程难点"
            confirm-type="noen"
            maxlength="-1"
            :autoHeight="autoHeight"
            disableDefaultPadding
          />
        </up-form-item>
        <up-form-item label="资源使用">
          <up-textarea
            class="textareaStyle"
            v-model="formData.data.resourceUtilization"
            placeholder="请输入资源使用"
            confirm-type="noen"
            maxlength="-1"
            :autoHeight="autoHeight"
            disableDefaultPadding
          />
        </up-form-item>
        <up-form-item label="材料投放">
          <up-textarea
            class="textareaStyle"
            v-model="formData.data.materialPlacement"
            placeholder="请输入材料投放"
            confirm-type="noen"
            maxlength="-1"
            :autoHeight="autoHeight"
            disableDefaultPadding
          />
        </up-form-item>
        <up-form-item label="环创建议">
          <up-textarea
            class="textareaStyle"
            v-model="formData.data.environmentSuggest"
            placeholder="请输入环创建议"
            confirm-type="noen"
            maxlength="-1"
            :autoHeight="autoHeight"
            disableDefaultPadding
          />
        </up-form-item>
        <up-form-item label="结题活动">
          <up-textarea
            class="textareaStyle"
            v-model="formData.data.completionActivity"
            placeholder="请输入结题活动"
            confirm-type="noen"
            maxlength="-1"
            :autoHeight="autoHeight"
            disableDefaultPadding
          />
        </up-form-item>
      </up-form>
    </view>
    <!-- 保存 -->
    <view
      class="action-btn"
      :style="{ height: heightBottom + 52 + 'px' }"
    >
      <up-button
        type="primary"
        text="保存"
        round
        shape="circle"
        color="#367CFF"
        @click="updataMatrixList"
        :disabled="isBtnDisabled"
      />
    </view>
  </view>
</template>

<script setup>
import { nextTick, ref, onMounted, watch, reactive } from "vue";
// import { onLoad } from "@dcloudio/uni-app";
import { getClassText, getMatrixList, updataMatrix } from "../api/class.js";
let autoHeight = ref(false);
const { safeAreaInsets } = uni.getSystemInfoSync();
let heightBottom = ref(safeAreaInsets.bottom);
let labelStyle = {
  fontSize: "28rpx",
  fontWeight: "600",
};
let isBtnDisabled = ref(false); // 保存后 按钮不给点击
let formData = reactive({
  data: {
    origin: "",
    interest: "",
    keynote: "",
    prospect: [],
    // summary: "",
    resourceUtilization: "",
    materialPlacement: "",
    environmentSuggest: "",
    completionActivity: "",
    studyAnalyze: "",
    objectives: "",
    difficulties: "",
  },
  matrix1List: [],
  // matrixList: [
  //   {
  //     matrix1Id: "",
  //     matrix2Id: "",
  //     matrix3Id: "",
  //     matrix1List: [], // uni-data-select组件想要回显，那么必须设置value和text
  //     matrix2List: [],
  //     matrix3List: [],
  //   },
  // ],
});

// 添加一项主题目标
// const addMatrixList = () => {
//   formData.matrixList.push({
//     matrix1Id: "",
//     matrix2Id: "",
//     matrix3Id: "",
//     matrix1List: [], // uni-data-select组件想要回显，那么必须设置value和text
//     matrix2List: [],
//     matrix3List: [],
//   });
// };

// 下拉框
// const selctMatrix = async (pid, arr) => {
//   console.log(arr.length);
//   if (arr.length > 0) arr.length = 0;
//   // getMatrix(pid, arr)
//   let res = await getMatrixList({
//     pid,
//     current: 1,
//     pageSize: 1000,
//   });
//   if (res.status == 0) {
//     res.data.forEach((el) => {
//       arr.push({
//         value: el.id,
//         text: el.title,
//       });
//     });
//   }
// };

const delFormItem = (index) => {
  formData.data.prospect.splice(index, 1);
};
const delmatrixItem = (index) => {
  formData.matrixList.splice(index, 1);
};

// api start
const ClassText = async (id) => {
  let res = await getClassText(id);
  if (res.status == 0) {
    formData.data = res.data;
    if (!formData.data.prospect || formData.data.prospect == "[]") {
      formData.data.prospect = [];
    } else {
      formData.data.prospect = JSON.parse(formData.data.prospect);
    }
    autoHeight.value = true;
  }
};
const updataMatrixList = async () => {
  isBtnDisabled.value = false;
  // const { interest, keynote, origin, prospect } = formData.data;
  // let form = {
  //   id: uni.getStorageSync("subjectId"),
  //   interest,
  //   keynote,
  //   origin,
  //   prospect,
  // };
  let form = {
    id: Number(uni.getStorageSync("subjectId")),
    origin: formData.data.origin,
    interest: formData.data.interest,
    keynote: formData.data.keynote,
    prospect: formData.data.prospect,
    resourceUtilization: formData.data.resourceUtilization,
    materialPlacement: formData.data.materialPlacement,
    environmentSuggest: formData.data.environmentSuggest,
    completionActivity: formData.data.completionActivity,
    studyAnalyze: formData.data.studyAnalyze,
    objectives: formData.data.objectives,
    difficulties: formData.data.difficulties,
  };
  const res = await updataMatrix(form);
  if (res.status == 0) {
    uni.$u.toast("修改成功，1s后返回");
    isBtnDisabled.value = true;
    setTimeout(() => {
      uni.navigateBack();
    }, 1000);
  }
};
// api end
onMounted(async () => {
  ClassText(uni.getStorageSync("subjectId"));
});
</script>
<script>
export default {
  options: { styleIsolation: "shared" }, // 解除样式隔离
};
</script>

<style lang="scss" scoped>
.edit-classText-layout {
  height: 100%;
  overflow-y: auto;
  box-sizing: border-box;
  padding: 0 32rpx;
  background: #f5f5f5;
  // overflow: hidden;
  padding-bottom: 144rpx;

  .newlist {
    height: 40rpx;
    font-size: 28rpx;
    font-weight: 500;
    line-height: 40rpx;
    color: rgba(63, 121, 255, 1);
    text-align: center;
    margin-top: 28rpx;
  }

  .action-btn {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100vw;
    box-sizing: border-box;
    padding: 16rpx 32rpx;
    background: #fff;
    opacity: 1;
    z-index: 10;
  }

  .tipstext {
    display: block;
    font-weight: bold;
  }

  ::v-deep .dataSelect {
    margin: 20rpx 0;
  }

  .form-item1 {
    ::v-deep .u-form-item__body__right__content__slot {
      width: 100%;
      flex-direction: column !important;
    }

    ::v-deep .u-input__content__field-wrapper__field {
      // font-size: 24rpx !important;
      font-weight: 400 !important;
    }
  }

  .textareaStyle {
    border-radius: 12rpx;
    border: 1px solid #dadbde;
    padding: 20rpx;
    width: 100%;
    min-height: 130rpx;
    box-sizing: border-box;
    font-size: 24rpx;
    font-weight: 400;
    margin-top: 20rpx;
  }

  .col {
    width: 100%;
    border-radius: 28rpx;
    background: #ffffff;
    box-shadow: 4rpx 8rpx 16rpx #eee;
    box-sizing: border-box;
    padding: 32rpx;
    margin: 24rpx 0;
  }
}
</style>
