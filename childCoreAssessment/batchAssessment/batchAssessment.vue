<!-- 儿童核心经验批量评价 -->
<template>
    <base-layout navTitle="儿童核心经验批量评价" :footerStyle="{ paddingBottom: heightBottom + 'px' }">
        <view class="ba-layout">
            <view class="fs20-fw4">本核心经验体系知识产权归深圳市甲辰教育科技有限公司所有<br />仅限您个人学习使用，严禁分享或转发。</view>
            <view class="col">
                <view class="no-item">
                    <text class="text-icon ly">领域</text>
                    <text class="fs28-fw4" style="margin-right: 28rpx;">{{ matrix.matrix1Title }}</text>
                    <text class="text-icon wd">维度</text>
                    <text class="fs28-fw4">{{ matrix.matrix2Title }}</text><br />
                    <text class="text-icon zwd" style="margin-bottom: 0;">子维度</text>
                    <text class="fs28-fw4">{{ matrix.matrix3Title }}</text>
                </view>
            </view>
            <!-- 评分依据 basis -->
            <view class="fs28-fw5 text-pfyj">评分依据</view>
            <view class="col">
                <view class="fs28-fw5 top" v-for="(item, index) in basis" :key="index">
                    <view class="fraction">{{ index + 1 }}分：</view>
                    <view style="flex: 1; white-space: pre-line;">
                        <template v-if="isArray(item.desc)">
                            <text style="margin-right: 8rpx; font-weight: bold;">{{ item.descTitle }}：</text>
                            <br /><text v-for="(subItem, subIndex) in item.desc" :key="subIndex">{{ subIndex + 1 }}、{{
                                subItem.title }}<br /></text>
                        </template>
                        <text v-else>{{ item.desc }}</text>
                    </view>
                </view>
            </view>
            <!-- 学生列表 -->
            <view class="fs28-fw5 student-list">学生列表</view>
            <!-- <view class="col"> -->
            <view class="student-fraction col" v-for="(item, index) in stuRecords" :key="index">
                <view class="fs28-fw4 flex-ac" style="width: fit-content;">
                    <text style="width: 120rpx;">{{ item.name }}</text>
                    <view v-if="item.score == 0" class="red-fraction" style="margin: 0 30rpx 0 32rpx; width: 85rpx;">待评价
                    </view>
                    <view v-else style="margin: 0 30rpx 0 32rpx; width: 85rpx;">{{ item.score }}分</view>
                </view>
                <view style="flex: 1">
                    <up-slider v-model="item.score" step="1" min="0" max="6" height="15rpx" blockSize="22" />
                </view>
            </view>
        </view>
        <!-- </view> -->
        <template #footer>
            <view class="action-btn flex-ac">
                <up-button class="action-btn-item" text="提交" type="primary" shape="circle" @click="BatchSave" />
                <!-- <up-button class="action-btn-item" type="primary" text="提交并继续下一项" shape="circle" color="#367CFF" /> -->
            </view>
        </template>
    </base-layout>
</template>

<script setup>
import { ref, reactive } from "vue"
import { onLoad, onShareAppMessage } from "@dcloudio/uni-app"
import upButton from "@/uni_modules/uview-plus/components/u-button/u-button.vue"
import { getBatchList, setBatchSave } from "../api/index";
import { isArray } from "lodash-es";
import { sharePageObj } from "@/utils";


const { safeAreaInsets } = uni.getSystemInfoSync()
let heightBottom = ref(safeAreaInsets.bottom)

const basis = ref([])  // 评分依据
const stuRecords = ref([])  // 评分记录
let _evaluationId = '';
let _matrixId = '';
const matrix = reactive({
    matrix1Title: '-',
    matrix2Title: '-',
    matrix3Title: '-'
})
onLoad((options) => {
    console.log(options);
    let { evaluationId, matrixId } = options
    _evaluationId = evaluationId
    _matrixId = matrixId
    batchList({
        evaluationId,
        matrixId,
        current: 1,
        pageSize: 20
    })
})
onShareAppMessage(() => sharePageObj({
    path: `/childCoreAssessment/batchAssessment/batchAssessment?matrixId=${_matrixId}&evaluationId=${_evaluationId}`,
}))
const batchList = async (obj) => {
    uni.getStorage({
        key: 'matrix',
        success(res) {
            const { matrix1Title, matrix2Title, matrix3Title } = res.data
            matrix.matrix1Title = matrix1Title
            matrix.matrix2Title = matrix2Title
            matrix.matrix3Title = matrix3Title
        }
    });
    let res = await getBatchList(obj)
    if (res.status == 0) {
        let { answers, records } = res.data
        basis.value = answers
        records.forEach(item => {
            console.log(typeof item.lastestScore.score);
            stuRecords.value.push({
                childId: item.child.id,
                score: JSON.stringify(item.lastestScore?.score) ? item.lastestScore.score : 0,
                name: item.child.title,
            })
        });
    }
}
// 批量保存
const BatchSave = async () => {
    let data = {
        evaluationId: Number(_evaluationId),
        matrixId: Number(_matrixId),
        childrenScores: stuRecords.value
    }
    console.log(data);
    let res = await setBatchSave(data)
    if (res.status == 0) {
        uni.$u.toast('提交成功')
    }

}


</script>
<script>
export default {
    options: { styleIsolation: 'shared' }  // 解除样式隔离
}
</script>

<style lang="scss" scoped>
@import '@/common/css/index.scss';

.text-pfyj {
    margin-bottom: 32rpx;
    margin-top: 40rpx;
}

.action-btn {

    .action-btn-item {
        width: 100%;
        height: 80rpx;
        font-size: 30rpx;
        font-weight: 400;
        margin-bottom: 16rpx;
    }
}

.red-fraction {
    color: red;
}

.student-fraction {
    width: 100%;
    margin: 24rpx 0;
    display: flex;
    align-items: center;
}

::v-deep .u-slider__base {
    border-radius: 6rpx;
}

.student-list {
    font-size: 30rpx;
    font-weight: 500;
    margin-top: 50rpx;
    margin-bottom: 32rpx;
}

.top {
    display: flex;
    margin: 20rpx 0;

    .fraction {
        width: fit-content;
    }
}

.top:last-child {
    // border: 1px solid red;
    margin-bottom: 0;
}

.fs20-fw4 {
    font-size: 20rpx;
    font-weight: 400;
    color: #000;
    opacity: 0.5;
    text-align: center;
    margin-bottom: 20rpx;
}

.fs28-fw4 {
    font-size: 28rpx;
    font-weight: 400;
}

.fs28-fw5 {
    font-size: 28rpx;
    font-weight: 500;
}

.no-item {
    .ly {
        color: #608BF0;
        background: rgba(96, 139, 240, 0.1);
    }

    .wd {
        color: #F0914D;
        background: rgba(240, 145, 77, 0.1);
    }

    .zwd {
        color: #54BA6A;
        background: rgba(84, 186, 106, 0.1);
    }
}

.text-icon {
    display: inline-block;
    font-size: 24rpx;
    font-weight: 500;
    height: 32rpx;
    line-height: 32rpx;
    margin-right: 12rpx;
    margin-bottom: 16rpx;
    padding: 4rpx 8rpx;
    border-radius: 6rpx;
}

.fs28-fw4 {
    font-size: 28rpx;
    font-weight: 400;
    color: #333333;
    vertical-align: middle;
}

.ba-layout {
    padding: 16rpx 32rpx 0 32rpx;
    box-sizing: border-box;
}

::v-deep .content-scroll-view {
    padding: 0 !important;
}

.col {
    width: 100%;
    border-radius: 28rpx;
    background: #FFFFFF;
    box-shadow: 4rpx 8rpx 16rpx #eee;
    box-sizing: border-box;
    padding: 28rpx;
    margin-bottom: 24rpx;
}
</style>