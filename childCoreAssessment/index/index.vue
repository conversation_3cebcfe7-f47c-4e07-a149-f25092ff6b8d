<!-- 儿童核心经验评价 -->
<template>
	<base-layout navTitle="核心经验评价">
		<view class="cca-layout">
			<view class="col user-info-col">
				<view class="avatar">
					<up-icon size="58rpx" name="/static/tabbar/activeMy.png"></up-icon>
				</view>
				<view class="user">
					<view class="user-info">
						<text class="user-class">{{ data.classInfo.title }}</text>
						<view>{{ data.classInfo.schoolTitle }} | 班主任: {{ data.classInfo.masterTitle }}</view>
					</view>
					<text class="user-info">--</text>
				</view>
				<up-icon @click="classShow = !classShow" size="30rpx" style="margin-left: auto"
					name="/childCoreAssessment/static/switch.png"></up-icon>
			</view>
			<!-- 下拉框 -->
			<view>
				<up-dropdown menu-icon-size="32rpx" class="dropdown">
					<up-dropdown-item v-model="data.matrix1Id" :title="data.title1" :options="options1"
						@change="selectDropdown1"></up-dropdown-item>
					<up-dropdown-item v-model="data.term" :title="data.title2" :options="options2" style="flex: 0"
						@change="selectDropdown2"></up-dropdown-item>
				</up-dropdown>
			</view>
			<!-- 未完成 -->
			<!-- <view>
                <view class="text-area">未完成</view>
                <view class="col no-item">
                    <text class="text-icon ly">领域</text>
                    <text class="fs28-fw4" style="margin-right: 28rpx;">学习品质</text>
                    <text class="text-icon wd">维度</text>
                    <text class="fs28-fw4">建构和获取</text><br />
                    <text class="text-icon zwd" style="margin-bottom: 0;">子维度</text>
                    <text class="fs28-fw4">好奇心和主动性</text>
                </view>
            </view> -->
			<!-- 已完成 -->
			<!-- <view>
                <view class="text-area">已完成</view>
                <view class="col no-item">
                    <text class="text-icon ly">领域</text>
                    <text class="fs28-fw4" style="margin-right: 28rpx;">学习品质</text>
                    <text class="text-icon wd">维度</text>
                    <text class="fs28-fw4">建构和获取</text><br />
                    <text class="text-icon zwd" style="margin-bottom: 0;">子维度</text>
                    <text class="fs28-fw4">好奇心和主动性</text>
                </view>
            </view> -->

			<!-- FODO: 接口不支持 未完成和已完成 -->

			<!-- <view>
                <view class="col no-item" v-if="!isLoading" v-for="(item, index) in assessList.records" :key="index"
                    @click="toPage(item)">
                    <text class="text-icon ly">领域</text>
                    <text class="fs28-fw4" style="margin-right: 28rpx;">{{ item.matrix1Title }}</text>
                    <text class="text-icon wd">维度</text>
                    <text class="fs28-fw4">{{ item.matrix2Title }}</text><br />
                    <text class="text-icon zwd" style="margin-bottom: 0;">子维度</text>
                    <text class="fs28-fw4">{{ item.matrix3Title }}</text>
                </view>

                <up-loading-icon size="20" mode="semicircle" style="margin-top: 100rpx;"
                    :show="isLoading"></up-loading-icon>
            </view> -->
			<view>
				<view v-if="!isLoading" v-for="(item, index) in dataList" :key="index" style="margin-bottom: 24rpx">
					<view class="col no1-item" @click="item.isShow = !item.isShow">
						{{ item.matrix1Title }}
						<up-icon name="arrow-down" style="padding-top: 5rpx" size="36rpx" />
					</view>
					<view class="no1-item-table" v-if="item.isShow" v-for="(item2, index2) in item.children"
						:key="index2">
						<view class="no1-item-table-left">{{ item2.matrix2Title }}</view>
						<view class="no1-item-table-right">
							<view class="no1-item-table-right-text" v-for="(item3, index3) in item2.children"
								:key="index3"
								@click="toPage({ matrix1Title: item.matrix1Title, matrix2Title: item2.matrix2Title, matrix3Title: item3.matrix3Title, matrix3Id: item3.matrix3Id })">
								{{ item3.matrix3Title }}
							</view>
						</view>
					</view>
				</view>
				<up-loading-icon size="20" mode="semicircle" style="margin-top: 100rpx" :show="isLoading" />
				<view class="empty" v-if="JSON.stringify(dataList) === '[]'">儿童列表为空/暂无数据</view>
			</view>

			<!-- 班级选择器 -->
			<up-picker :show="classShow" :columns="columns" keyName="title" @confirm="classConfirm"
				@cancel="classShow = false" />
		</view>
	</base-layout>
</template>

<script setup>
import { ref, reactive } from 'vue';
import upDropdown from '@/uni_modules/uview-plus/components/u-dropdown/u-dropdown.vue';
import upDropdownItem from '@/uni_modules/uview-plus/components/u-dropdown-item/u-dropdown-item.vue';
import upIcon from '@/uni_modules/uview-plus/components/u-icon/u-icon.vue';
import upPicker from '@/uni_modules/uview-plus/components/u-picker/u-picker.vue';
import BaseLayout from "@/components/base-layout/base-layout.vue";
import { getclassList, getChildrenList } from '../api/index';
import { getMatrixList } from '@/api/index';
import { onUnload, onShareAppMessage } from '@dcloudio/uni-app';
import { sharePageObj, useCurrentClassId } from '@/utils';

onShareAppMessage(() => sharePageObj());

// 先查班级 学期 维度
let isLoading = ref(true);
let options1 = ref([
	{
		label: '全部',
		value: ''
	}
]);
let options2 = ref([
	{ label: '小小班上', value: 11 },
	{ label: '小小班下', value: 12 },
	{ label: '小班上', value: 13 },
	{ label: '小班下', value: 14 },
	{ label: '中班上', value: 15 },
	{ label: '中班下', value: 16 },
	{ label: '大班上', value: 17 },
	{ label: '大班下', value: 18 },
	{ label: '大大班上', value: 19 },
	{ label: '大大班下', value: 20 }
]);
let classShow = ref(false);
let columns = reactive([[]]);
// 数据集中
let data = reactive({
	matrix1Id: '',
	term: 11,
	title1: '领域',
	title2: '小小班上',
	classInfo: {
		title: '',
		schoolTitle: '',
		masterTitle: '',
		classId: ''
	} // 班级信息
});

let assessList = ref([]); // 评估列表
let dataList = ref([]); // 数据列表
function toPage(item) {
	console.log(item);

	const { evaluation } = assessList.value;
	console.log(evaluation.id);

	uni.setStorage({
		key: 'matrix',
		data: {
			matrix1Title: item.matrix1Title,
			matrix2Title: item.matrix2Title,
			matrix3Title: item.matrix3Title
		},
		success(res) {
			uni.navigateTo({
				url: `/childCoreAssessment/batchAssessment/batchAssessment?matrixId=${item.matrix3Id}&evaluationId=${evaluation.id}`
			});
		}
	});
}
const selectDropdown1 = (e) => {
	data.matrix1Id = e;
	data.title1 = options1.value.find((item) => item.value == e).label;
	childrenList({
		term: data.term,
		classId: data.classInfo.classId,
		matrix1Id: data.matrix1Id
	});
};
const selectDropdown2 = (e) => {
	data.term = e;
	data.title2 = options2.value.find((item) => item.value == e).label;

	childrenList({
		term: e,
		classId: data.classInfo.classId,
		matrix1Id: data.matrix1Id
	});
};
const classConfirm = (e) => {
	const { value, values, indexs } = e;
	data.classInfo.title = value[0].title;
	data.classInfo.schoolTitle = value[0].schoolTitle;
	data.classInfo.masterTitle = value[0].extra?.masterTitle;
	data.classInfo.classId = value[0].id;

	classShow.value = false;
	useCurrentClassId(value[0].id)
	setTimeout(() => {
		childrenList({
			term: data.term,
			classId: data.classInfo.classId,
			matrix1Id: data.matrix1Id
		});
	}, 300)
};

// // 数据分类
const classifyData = (arr) => {
	// 使用 Map 来存储和分类数据
	const map = new Map();

	// 遍历数组，构建 map
	arr.forEach((item) => {
		if (!map.has(item.matrix1Id)) {
			map.set(item.matrix1Id, {
				matrix1Id: item.matrix1Id,
				matrix1Title: item.matrix1Title,
				children: [],
				isShow: false
			});
		}
		const matrix1 = map.get(item.matrix1Id);

		if (!matrix1.children.find((child) => child.matrix2Id === item.matrix2Id)) {
			matrix1.children.push({
				matrix2Id: item.matrix2Id,
				matrix2Title: item.matrix2Title,
				children: []
			});
		}
		const matrix2 = matrix1.children.find((child) => child.matrix2Id === item.matrix2Id);

		if (!matrix2.children.find((child) => child.matrix3Id === item.matrix3Id)) {
			matrix2.children.push({
				matrix3Id: item.matrix3Id,
				matrix3Title: item.matrix3Title
			});
		}
	});
	const result = Array.from(map.values());
	if (result.length == 1) {
		result[0].isShow = true;
	}
	// 将 map 转换为数组并返回
	return result;
};

//api请求

const childrenList = async (item) => {
	isLoading.value = true;
	let data = item;
	if (data.matrix1Id == '') {
		delete data.matrix1Id;
	}
	const res = await getChildrenList(data);
	if (res.status == 0) {
		assessList.value = [];
		assessList.value = res.data;
		// 进行分类 根据matrix1Id来分类
		dataList.value = classifyData(assessList.value.records);
	}

	isLoading.value = false;
};

const matrixList = async () => {
	const res = await getMatrixList({
		pid: 0
	});
	if (res.status == 0) {
		let arr = res.data;
		arr.forEach((item) => {
			options1.value.push({
				label: item.title,
				value: item.id
			});
		});
	}
};

const getAllrequirt = async () => {
	// 班级信息
	const classRes = await getclassList();
	let arr = classRes.data;
	columns[0] = arr;
	console.log('%c line-224 班级信息：', 'color:blue', arr);
	let curClassId = uni.getStorageSync('USER_INFO').currentClassId;
	let index = arr.findIndex((item) => item.id == curClassId);
	// 默认第一个班级
	data.classInfo.title = arr[index].title;
	data.classInfo.schoolTitle = arr[index].schoolTitle;
	data.classInfo.masterTitle = arr[index].extra?.masterTitle;
	data.classInfo.classId = curClassId;

	// 获取评估列表
	childrenList({
		term: data.term,
		classId: data.classInfo.classId
	});
};
onUnload(() => {
	uni.removeStorageSync('matrix');
});
matrixList();
getAllrequirt();
</script>
<script>
export default {
	options: { styleIsolation: 'shared' } // 解除样式隔离
};
</script>

<style lang="scss" scoped>
@import '@/uni_modules/uview-plus/index.scss';
@import '@/uni_modules/uview-plus/theme.scss';

.empty {
        color: #ccc;
        font-weight: 400;
        font-size: 28rpx;
        text-align: center;
        margin-top: 200rpx;
    }

.no1-item {
	font-size: 30rpx;
	font-weight: 500;
	height: 100rpx;
	position: relative;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.no1-item-table {
	border-bottom: 1px solid #e0e0e0;
	display: flex;
	font-size: 28rpx;
	font-weight: 400;
	color: #333333;
	// margin-top: 100rpx;
	// padding-top: -100rpx;

	.no1-item-table-left {
		width: 232rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background: #ffffff;
		// padding-top: 10rpx;
		// margin-top: -10rpx;
		// border-bottom: 1px solid #E0E0E0;
	}

	&:nth-of-type(2) .no1-item-table-left {
		border-top: 1px solid #ffffff;
		border-top-width: 10rpx;
		margin-top: -20rpx;
	}

	&:nth-of-type(2) .no1-item-table-right .no1-item-table-right-text:first-child {
		border-top: 1px solid #e0e0e0;
		border-top-width: 10rpx;
		margin-top: -20rpx;
		line-height: 112rpx;
		height: 112rpx;
	}

	.no1-item-table-right {
		flex: 1;
		background: #f9f9f9;
		// padding-top: 10rpx;
		// margin-top: -10rpx;

		.no1-item-table-right-text {
			height: 92rpx;
			line-height: 92rpx;
			box-sizing: border-box;
			padding-left: 32rpx;
			border-bottom: 1px solid #e0e0e0;
		}

		.no1-item-table-right-text:last-child {
			border-bottom: none;
		}
	}

	// .no1-item-table-left:nth-of-type() {
	//     margin-top: -100rpx;
	//     padding-top: 100rpx;
	//     border-radius: 28rpx 28rpx 0rpx 0rpx;
	//     overflow: hidden;
	//     border: 1px solid red;
	// }

	&:last-child {
		border-bottom: none;
		overflow: hidden;
		border-radius: 0rpx 0rpx 28rpx 28rpx;
		margin-bottom: 29rpx;
	}
}

::v-deep .content-scroll-view {
	padding: 0 !important;
}

.no-item {
	.ly {
		color: #608bf0;
		background: rgba(96, 139, 240, 0.1);
	}

	.wd {
		color: #f0914d;
		background: rgba(240, 145, 77, 0.1);
	}

	.zwd {
		color: #54ba6a;
		background: rgba(84, 186, 106, 0.1);
	}

	margin-bottom: 24rpx;
}

.no-item:last-child {
	margin-bottom: 32rpx;
}

.text-icon {
	display: inline-block;
	font-size: 24rpx;
	font-weight: 500;
	height: 32rpx;
	line-height: 32rpx;
	margin-right: 12rpx;
	margin-bottom: 16rpx;
	padding: 4rpx 8rpx;
	border-radius: 6rpx;
}

.fs28-fw4 {
	font-size: 28rpx;
	font-weight: 400;
	color: #333333;
	vertical-align: middle;
}

.text-area {
	font-size: 24rpx;
	font-weight: 400;
	color: #808080;
	margin-bottom: 28rpx;
}

.dropdown ::v-deep .u-icon--right .u-icon__icon {
	color: #333333 !important;
}

.dropdown ::v-deep .u-dropdown__menu__item {
	// flex: initial;

	.u-dropdown__menu__item__text {
		font-weight: 500;
		color: #333333 !important;
	}
}

.dropdown ::v-deep .u-dropdown__menu__item:first-child {
	position: relative;
}

.dropdown ::v-deep .u-dropdown__menu__item:first-child::after {
	// border-right: 1px solid red;
	content: ' ';
	color: red;
	width: 0px;
	border-left: 1px solid #cccccc;
	height: 20rpx;
	position: absolute;
	right: 0;
	top: 50%;
	transform: translate(-50%, -50%);
}

.user-info-col {
	display: flex;
	align-items: center;
	margin: 16rpx 0;
}

.user {
	width: fit-content;
	display: flex;
	flex-direction: column;
	justify-content: space-between;

	.user-info {
		font-size: 22rpx;
		font-weight: 500;
		color: #808080;
	}

	.user-class {
		font-weight: 600;
		color: #333333;
		margin-right: 16rpx;
		font-size: 30rpx;
	}
}

.cca-layout {
	padding: 0 32rpx;
}

.col {
	width: 100%;
	border-radius: 28rpx;
	background: #ffffff;
	box-shadow: 4rpx 8rpx 16rpx #eee;
	box-sizing: border-box;
	padding: 28rpx;
}

.avatar {
	width: 88rpx;
	height: 88rpx;
	margin-right: 24rpx;
	border-radius: 16rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	border-radius: 16rpx;
	background: rgba(54, 124, 255, 0.1);
}
</style>
