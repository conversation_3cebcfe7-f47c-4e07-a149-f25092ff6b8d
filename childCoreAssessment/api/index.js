import { request } from "@/common/request.js"

// 查询儿童评估列表
export function getChildrenList(data) {
	return request({
		url: `/business/child_evaluation/list`,
		method: 'get',
		data
	})
}

//查询对应班级
export function getclassList(data) {
	return request({
		url: `/business/class/my_list`,
		method: 'GET',
		data
	})
}

//	儿童评估 - 查询列表
export function getBatchList(data) {
	return request({
		url: `/business/child_evaluation/batch`,
		method: 'GET',
		data
	})
}

//	儿童评估 - 批量保存
export function setBatchSave(data) {
	return request({
		url: `/business/child_evaluation/batch_save`,
		method: 'POST',
		data
	})
}

