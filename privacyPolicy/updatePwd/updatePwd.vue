<template>
  <view class="layout">
    <view class="content">
      <up-form
        labelPosition="left"
        labelWidth="80"
        :model="pwdDate"
        ref="form1"
        :rules="rules"
      >
        <up-form-item
          label="旧密码:"
          prop="pwd"
		  required
        >
          <up-input v-model="pwdDate.pwd"></up-input>
        </up-form-item>
        <up-form-item
          label="新密码:"
          prop="newPwd"
		  required
        >
          <up-input v-model="pwdDate.newPwd"></up-input>
        </up-form-item>
        <up-form-item label="确认密码:">
          <up-input v-model="token"></up-input>
        </up-form-item>
      </up-form>
      <up-button
        class="btn"
        type="primary"
        shape="circle"
        text="确认修改"
        @click="setPwd"
      />
    </view>
  </view>
</template>

<script setup>
import { reactive, ref } from "vue";
import { changePwd } from "@/api/login.js";

let token = ref("");
let pwdDate = reactive({
  pwd: "",
  newPwd: "",
});

const rules = {
  pwd: {
    required: true,
    message: "请输入旧密码",
    trigger: ["blur"],
  },
  newPwd: {
    required: true,
    message: "请输入旧密码",
    trigger: ["blur"],
  },
};

async function setPwd() {
  if (token.value === pwdDate.newPwd) {
    try {
      let res = await changePwd(pwdDate);
      uni.reLaunch({
        url: "/pages/index/index",
      });
      uni.showToast({
        title: "修改成功",
        icon: "none",
      });
    } catch (err) {
      console.log(err);
    }
  } else {
    uni.showToast({
      title: "请确认新密码是否不一致",
      icon: "none",
    });
  }
}
</script>

<style lang="scss" scoped>
.layout {
  height: 100vh;
  padding: 30rpx;
}
.content {
  padding: 30rpx;
  background-color: white;
  border-radius: 20rpx;
  .btn {
    margin-top: 20rpx;
  }
}
</style>
