import { ref } from 'vue'
import { schoolDetail } from '@/api/classApi.js'

export function useSchool() {
  const currentSchoolId = ref(
    uni.getStorageSync('CURRENT_SCHOOL_ID') || 
    uni.getStorageSync('USER_INFO').currentSchoolId
  )
  
  const title = ref(uni.getStorageSync('CURRENT_SCHOOL_TITLE') || '')

  const setSchool = (id, schoolTitle) => {
    currentSchoolId.value = id
    title.value = schoolTitle
    uni.setStorageSync('CURRENT_SCHOOL_ID', id)
    uni.setStorageSync('CURRENT_SCHOOL_TITLE', schoolTitle)
  }

  /**
   * 获取当前学校的标题
   *
   * @returns 无返回值
   */
  const getSchoolTitle = async () => {
    try {
      const savedSchoolId = uni.getStorageSync('CURRENT_SCHOOL_ID')
      const savedSchoolTitle = uni.getStorageSync('CURRENT_SCHOOL_TITLE')
      
      if (savedSchoolId) {
        currentSchoolId.value = savedSchoolId
        if (savedSchoolTitle) {
          title.value = savedSchoolTitle
          return
        }
      }

      const res = await schoolDetail(currentSchoolId.value)
      title.value = res.data.title
      uni.setStorageSync('CURRENT_SCHOOL_TITLE', res.data.title)
    } catch (error) {
      console.error('获取学校详情失败:', error)
    }
  }

  return {
    currentSchoolId,
    title,
    setSchool,
    getSchoolTitle
  }
}