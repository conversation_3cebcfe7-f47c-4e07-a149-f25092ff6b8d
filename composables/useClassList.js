import { ref, reactive, toRaw } from 'vue'
import { getclassList } from '@/api/classApi.js'

export function useClassList(currentSchoolId) {
  const classArr = ref([])
  const loading = ref('loadmore')
  const paramsPage = reactive({
    pageSize: 10,
    currentPage: 1
  })

  const getClassArr = async () => {
    if (loading.value === 'loading') return Promise.reject()

    loading.value = 'loading'
    if (paramsPage.currentPage === 1) {
      classArr.value = []
    }

    try {
      const params = {
        schoolId: currentSchoolId.value,
        pageSize: paramsPage.pageSize,
        currentPage: paramsPage.currentPage
      }

      const res = await getclassList(params)
      classArr.value = [...toRaw(classArr.value), ...res.data]
      loading.value = res.total > classArr.value.length ? 'loadmore' : 'nomore'
      return Promise.resolve()
    } catch (error) {
      console.error('获取班级列表失败:', error)
      const tempPage = paramsPage.currentPage - 1
      paramsPage.currentPage = tempPage < 1 ? 1 : tempPage
      loading.value = 'loadmore'
      return Promise.reject()
    }
  }

  const loadMore = () => {
    if (loading.value === 'loading') return
    paramsPage.currentPage++
    getClassArr()
  }

  return {
    classArr,
    loading,
    paramsPage,
    getClassArr,
    loadMore
  }
}