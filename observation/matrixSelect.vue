<template>
  <!-- 显示 -->
  <view class="matrixSelect" v-if="!matric.isEdit">
    <view class="matrixSelect-header">
      <view class="matricText-top">核心经验{{ index + 1 }}</view>
      <up-icon name="trash" size="36rpx" @click="onDelete" />
    </view>
    <view class="matrixSelect-content">
      <text class="matrixSelect-content-text">{{ joinData() }}</text>
    </view>
  </view>
  <!-- 编辑 -->
  <view class="matrixSelect" v-else>
    <view class="matrixSelect-header">
      <view class="matricText-top">核心经验{{ index + 1 }}</view>
      <up-icon name="trash" size="36rpx" @click="onDelete" />
    </view>
    <view class="matrixSelect-content">
      <view class="matricText" @tap="onShowPicker('matrix1Id')">
        <!-- <view class="matricText-top">领域</view> -->
        <view class="matricText-bottom">
          {{ matric.matrix1Name }}
          <template v-if="!matric.matrix1Id">
            <text class="placeholder">请选择领域</text>
          </template>
          <up-icon name="arrow-down" size="30rpx" />
        </view>
      </view>
      <view class="matricText" @tap="onShowPicker('matrix2Id')">
        <!-- <view class="matricText-top">维度</view> -->
        <view class="matricText-bottom">
          {{ matric.matrix2Name }}
          <template v-if="!matric.matrix2Id">
            <text class="placeholder">请选择维度</text>
          </template>
          <up-icon name="arrow-down" size="30rpx" />
        </view>
      </view>
      <view class="matricText" @tap="onShowPicker('matrix3Id')">
        <!-- <view class="matricText-top">子维度</view> -->
        <view class="matricText-bottom">
          {{ matric.matrix3Name }}
          <template v-if="!matric.matrix3Id">
            <text class="placeholder">请选择子维度</text>
          </template>
          <up-icon name="arrow-down" size="30rpx" />
        </view>
      </view>
      <view class="matricText" @tap="onShowPicker('targetId')">
        <!-- <view class="matricText-top">指标</view> -->
        <view class="matricText-bottom">
          {{ matric.targetName }}
          <template v-if="!matric.targetId">
            <text class="placeholder">请选择指标</text>
          </template>
          <up-icon name="arrow-down" size="30rpx" />
        </view>
      </view>
    </view>


    <!-- 老写法，暂时不用 -->
    <!-- <up-form labelPosition="top" :model="matric" labelWidth="80" labelStyle="{fontSize: 30rpx; fontWeight: 600;}"
      labelAlign="left">
      <up-form-item :style="formItemStyle" label="领域" prop="matrix1Id" @click="onShowPicker('matrix1Id')">
        <view class="matricText">
          {{ getOptionLabelByKeyAndId("matrix1Id", matric.matrix1Id) }}
          <text v-if="!getOptionLabelByKeyAndId('matrix1Id', matric.matrix1Id)" class="placeholder">请选择</text>
        </view>
        <template #right>
          <up-icon name="arrow-down" />
        </template>
      </up-form-item>

      <up-form-item :style="formItemStyle" label="维度" prop="matrix2Id" @click="onShowPicker('matrix2Id')">
        <view class="matricText">
          {{ getOptionLabelByKeyAndId("matrix2Id", matric.matrix2Id) }}
          <text v-if="!getOptionLabelByKeyAndId('matrix2Id', matric.matrix2Id)" class="placeholder">请选择</text>
        </view>
        <template #right>
          <up-icon name="arrow-down" />
        </template>
      </up-form-item>
      <up-form-item :style="formItemStyle" label="子维度" prop="matrix3Id" @click="onShowPicker('matrix3Id')">
        <view class="matricText">
          {{ getOptionLabelByKeyAndId("matrix3Id", matric.matrix3Id) }}
          <text v-if="!getOptionLabelByKeyAndId('matrix3Id', matric.matrix3Id)" class="placeholder">请选择</text>
        </view>
        <template #right>
          <up-icon name="arrow-down" />
        </template>
      </up-form-item>
      <up-form-item :style="formItemStyle" label="指标" prop="targetId" @click="onShowPicker('targetId')">
        <view class="matricText">
          {{ getOptionLabelByKeyAndId("targetId", matric.targetId) }}
          <text v-if="!getOptionLabelByKeyAndId('targetId', matric.targetId)" class="placeholder">请选择</text>
        </view>
        <template #right>
          <up-icon name="arrow-down" />
        </template>
      </up-form-item>
    </up-form> -->
    <up-picker :show="isPickerShow" :defaultIndex="[0]" :columns="activeOption" @confirm="pickerConfirm"
      @cancel="isPickerShow = false" keyName="title"></up-picker>
  </view>
</template>

<script setup>
import { getMatrixList, getTargetList } from "@/api";
import {
  reactive,
  defineProps,
  ref,
  computed,
  defineEmits,
  onMounted,
} from "vue";
import { onLoad } from "@dcloudio/uni-app";
const activeOptionKey = ref("matrix1Id");
const isPickerShow = ref(false);
const optionsList = reactive({
  matrix1Id: [],
  matrix2Id: [],
  matrix3Id: [],
  targetId: [],
});
/** 表单样式 */
const formItemStyle = `
  border-radius: 28rpx;
  padding:0 28rpx;
  background: #fff;
  margin-bottom: 24rpx;
  height: 144rpx;
  box-sizing: border-box;
`;

const props = defineProps({
  matric: {
    type: Object,
    required: true,
  },
  matrixList: {
    type: Array,
    required: true,
  },
  index: {
    type: Number,
  },
  isSingle: {
    type: Boolean,
  },
});

const activeItemKey = ref(null);

// 拼接已有数据
const joinData = () => {
  let matric = props.matric;
  return `${matric.matrix1Name}-${matric.matrix2Name}-${matric.matrix3Name}-${matric.targetName}`;
}

function onShowPicker(key, i) {
  if (!checkRequiredFields(key)) return;
  if (i) activeItemKey.value = i
  activeOptionKey.value = key;
  isPickerShow.value = true;
}

const checkRequiredFields = (key) => {
  const requiredFields = {
    matrix1Id: "请先选择领域",
    matrix2Id: "请先选择维度",
    matrix3Id: "请先选择子维度",
  };
  if (key !== "matrix1Id" && !props.matric.matrix1Id) {
    uni.$u.toast(requiredFields.matrix1Id);
    return false;
  }
  if (key === "matrix3Id" && !props.matric.matrix2Id) {
    uni.$u.toast(requiredFields.matrix2Id);
    return false;
  }
  if (key === "targetId" && !props.matric.matrix3Id) {
    uni.$u.toast(requiredFields.matrix3Id);
    return false;
  }
  return true;
};

const activeOption = computed(() => {
  if (activeOptionKey.value === "matrix1Id") {
    return [props.matrixList];
  }
  return [optionsList[activeOptionKey.value] || []];
});

const pickerConfirm = ({ value }) => {
  console.log(value);
  let flag = props.matric[activeOptionKey.value] == value[0].id ? true : false;
  if (flag) {
    isPickerShow.value = false;
    return;
  }
  switch (activeOptionKey.value) {
    case "matrix1Id":
      checkPicker(value[0], "matrix1");
      updateMatrixList(value[0].id, "matrix2Id");
      break;
    case "matrix2Id":
      checkPicker(value[0], "matrix2");
      updateMatrixList(value[0].id, "matrix3Id");
      break;
    case "matrix3Id":
      checkPicker(value[0], "matrix3");
      updateMatrixList(value[0].id, "targetId");
      break;
    case "targetId":
      checkPicker(value[0], "target");
      break;
    default:
      break;
  }
  clearPicker(activeOptionKey.value);
  isPickerShow.value = false;
};

function checkPicker(v, key) {
  props.matric[`${key}Name`] = v.title;
  props.matric[`${key}Id`] = v.id;
}

function headleClearPicker(key) {
  props.matric[`${key}Name`] = null;
  props.matric[`${key}Id`] = null;
}

// 判断点击了什么类型 如果等于matrix1Id 则清空 matrix2Id matrix3Id targetId
// 如果等于matrix2Id 则清空 matrix3Id targetId
// 如果等于matrix3Id 则清空 targetId
// 如果等于targetId 则清空
function clearPicker(key) {
  switch (key) {
    case "matrix1Id":
      headleClearPicker("matrix2");
      headleClearPicker("matrix3");
      headleClearPicker("target");
      break;
    case "matrix2Id":
      headleClearPicker("matrix3");
      headleClearPicker("target");
      break;
    case "matrix3Id":
      headleClearPicker("target");
      break;
    default:
      break;
  }
}


function getOptionLabelByKeyAndId(key, id) {
  const list = key === "matrix1Id" ? props.matrixList : optionsList[key];
  if (typeof id === "string" || typeof id === "number") {
    if (!list) {
      return id;
    }
    const target = list.find((item) => item.id == id);

    return target ? target.title : id;
  }

  return id;
}

function updateMatrixList(pid, key) {
  if (key === "targetId") {
    getTargetList({
      matrix3Id: pid,
      pageSize: 1000,
    }).then((r) => {
      optionsList[key] = r.data;

    });
  } else {
    getMatrixList({
      pid,
      pageSize: 1000,
    }).then((r) => {
      optionsList[key] = r.data;
    });
  }
}

onMounted(() => {
  // 会触发多次
  // if (props.matric.matrix1Id) {
  //   updateMatrixList(props.matric.matrix1Id, "matrix2Id");
  // }
  // if (props.matric.matrix2Id) {
  //   updateMatrixList(props.matric.matrix2Id, "matrix3Id");
  // }
  // if (props.matric.matrix3Id) {
  //   updateMatrixList(props.matric.matrix3Id, "targetId");
  // }
});

const emit = defineEmits(["onDeleteMatrix"]);
const onDelete = () => {
  emit("onDeleteMatrix", props.index);
};
</script>

<style lang="scss" scoped>
.placeholder {
  color: #B1B3B5;
  font-size: 24rpx;
  font-weight: 400;
}

.matrixSelect {
  // border-bottom: 1px solid #ccc;
  font-size: 30rpx;
  color: #333333;
  border-radius: 28rpx;
  background: rgba(255, 255, 255, 1);
  padding: 28rpx;
  box-sizing: border-box;
  margin-bottom: 24rpx;

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    margin-bottom: 28rpx;
  }

  &-content {
    &-text {
      font-size: 28rpx;
      font-weight: 400;
    }

    .matricText {
      width: 100%;
      min-height: 76rpx;
      border: 1px solid #E6E6E6;
      border-radius: 12rpx;
      margin-bottom: 20rpx;
      box-sizing: border-box;
      box-shadow: 4rpx 8rpx 16rpx rgba(0, 0, 0, 0.02);
      padding: 26rpx 24rpx;

      &:last-child {
        margin-bottom: 0;
      }

      &-bottom {
        font-weight: 500;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
  }
}
</style>
