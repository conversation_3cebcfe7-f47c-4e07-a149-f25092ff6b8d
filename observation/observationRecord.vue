<template>
  <base-layout nav-title="观察记录" :contentStyle="{
    padding: '0',
    paddingBottom: '52rpx',
    transform: 'none !important',
  }" :footerStyle="{
    display: 'none',
  }" :refresherEnabled="true" :refresherTriggered="isRefresh" @scrolltolower="onScrollToLower"
    @refresherrefresh="onRefresherrefresh">
    <view style="height: 100vh; transform: none">
      <!-- <up-list
      class="observationRecord"
      v-if="observationList.length != 0"
      :enableFlex="true"
      height="98%"
    >
      <up-list-item
        v-for="item in observationList"
        :key="item.id"
      >
        <view>
          <view class="observationRecord-title">
            <text class="observationRecord-title-left"
              >{{ item.date }}&nbsp;{{ item.dayOfWeek }}</text
            >
            <text class="observationRecord-title-right">{{ item.year }}年</text>
          </view>
          <view
            @click="onViewDetail(itm)"
            class="observationRecord-item"
            v-for="(itm, index) in item.items"
            :key="itm.id + new Date().getTime()"
          >
            <view
              v-if="itm.state == 2"
              class="Mask"
              @tap.capture.stop="onMask"
            />
            <view class="observationRecord-item-header">
              <image
                class="item-header-left"
                :src="createdIcon(itm)"
                alt=""
              />
              <view class="item-header-right">
                <view class="item-header-right-name">{{
                  itm.children.map((v) => v.childName).join("，")
                }}</view>
                <view class="item-header-right-time">{{
                  createdAtS(itm.observationTime)
                }}</view>
              </view>
              <view
                class="item-header-center"
                @click.capture.stop="onOpenPop(itm)"
              >
                <up-icon
                  class="card-icon"
                  size="42rpx"
                  name="more-dot-fill"
                />
              </view>
            </view>
            <view class="observationRecord-item-content">
              <view
                class="content-item"
                v-for="column in columns"
                :key="column.prop"
              >
                <view class="item-label">{{ column.label }}:</view>
                <view class="item-value">{{
                  column.formatter
                    ? column.formatter(itm, optionsList)
                    : itm[column.prop]
                }}</view>
              </view>
            </view>
          </view>
        </view>
      </up-list-item>
    </up-list> -->
      <view class="observationRecord">
        <view v-for="item in observationList" :key="item.id">
          <view class="observationRecord-title">
            <text class="observationRecord-title-left">{{ item.date }}&nbsp;{{ item.dayOfWeek }}</text>
            <text class="observationRecord-title-right">{{ item.year }}年</text>
          </view>
          <view @click="onViewDetail(itm)" class="observationRecord-item" v-for="(itm, index) in item.items"
            :key="itm.id + new Date().getTime()">
            <view v-if="itm.state == 2" class="Mask" @tap.capture.stop="onMask" />
            <view class="observationRecord-item-header">
              <image class="item-header-left" :src="createdIcon(itm)" alt="" />
              <view class="item-header-right">
                <view class="item-header-right-name">{{
                  itm.children.map((v) => v.childName).join("，")
                }}</view>
                <view class="item-header-right-time">{{
                  createdAtS(itm.observationTime)
                }}</view>
              </view>
              <view class="item-header-center" @click.capture.stop="onOpenPop(itm)">
                <up-icon class="card-icon" size="42rpx" name="more-dot-fill" />
              </view>
            </view>
            <view class="observationRecord-item-content">
              <view class="content-item" v-for="column in columns" :key="column.prop">
                <view class="item-label">{{ column.label }}:</view>
                <view class="item-value">{{
                  column.formatter
                    ? column.formatter(itm, optionsList)
                    : itm[column.prop]
                }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view v-if="observationList.length == 0" class="empty">暂无观察记录</view>
    </view>
  </base-layout>
  <Popup :show="isPop" @close="isPop = false">
    <view class="iconAction">
      <view class="deleteIcon" @click="onExportWord">
        <icon-button :size="20" type="text" icon="icon-export" text="" style="margin-right: 19rpx" />
        <view>导出word</view>
      </view>
      <view @click="onDelete">
        <image src="@/static/common/delete.png"></image>
        <view>删除</view>
      </view>
    </view>
  </Popup>
  <PopupExport :show="isShowTemplate" :list="optionsList.observationWordList" keyName="name"
    @close="isShowTemplate = false" @change="onConfirm" />
  <view class="addClass" @click="onAdd" />
</template>

<script setup>
import { ref, reactive, nextTick, onMounted, getCurrentInstance } from "vue";
import { onShareAppMessage, onShow } from "@dcloudio/uni-app";
import { sharePageObj } from "@/utils";
import BaseLayout from "@/components/base-layout/base-layout2.vue";
import Popup from "@/components/Popup/Popup.vue";
import { getDICT } from "@/utils";
import dayjs from "dayjs";
import { getclassList } from "@/api";
import { getUserInfo } from "@/api/login";
import {
  deleteObservation,
  getObservationList,
  getWordTemplate,
  exportWordTemplate,
} from "./api";
import { removeDuplicates } from "./data";
import IconButton from "@/components/icon-button/icon-button.vue";
import { useExportWord } from "./data";
import PopupExport from "./components/popup-export.vue";
import { transform } from "lodash-es";

const instance = getCurrentInstance();
let isPop = ref(false); // 弹框控制
let activeItem = ref(null); // 当前点击的item
let isRefresh = ref(false); // 是否刷新
const userInfo = ref({});
const optionsList = reactive({
  schoolClassId: [],
  observationRecordScene: [],
  observationWordList: [],
});

const createdAtS = (v) => dayjs(v).format("YYYY-MM-DD HH:mm:ss");
let isShowTemplate = ref(false); // 模板选择
const columns = [
  {
    label: "活动名称",
    prop: "activityName",
  },
  // {
  //   label: "背景与目的",
  //   prop: "backgroundAndPurpose",
  // },
  // {
  //   label: "观察场景",
  //   prop: "observationRecordScene",
  //   formatter(row, optionsList) {
  //     const o = optionsList.observationRecordScene.find(
  //       (f) => f.id === row.observationRecordScene
  //     );
  //     return o ? o.label : row.observationRecordScene;
  //   },
  // },
  // {
  //   label: "观察时间",
  //   prop: "observationTime",
  //   formatter(row) {
  //     return row.observationTime
  //       ? dayjs(row.observationTime).format("YYYY-MM-DD")
  //       : "";
  //   },
  // },
  {
    label: "观察地点",
    prop: "observationLocation",
  },
  {
    label: "观察老师",
    prop: "createdByName",
  },
  {
    label: "观察班级",
    prop: "schoolClassId",
    formatter(row, optionsList) {
      // 查找交集
      const o = removeDuplicates(row.children, [], "classId");
      let arr = [];
      o.forEach((v) => {
        arr.push(
          optionsList.schoolClassId.find((f) => f.id === v.classId)?.title
        );
      });
      return arr.join("，") || "-";
    },
  },
  // {
  //   label: "观察对象",
  //   prop: "childIds",
  //   formatter(row) {
  //     return row.children
  //       ? row.children.map((v) => v.childName).join("，")
  //       : "";
  //   },
  // },
];
const observationList = ref([]);
// 下拉刷新
const onRefresherrefresh = async () => {
  console.log("下拉刷新");
  isRefresh.value = true;
  await initObservationList();
  isRefresh.value = false;
};
// 触底刷新
const onScrollToLower = () => {
  console.log("触底");
};

// 多模板选择
const onConfirm = async (e) => {
  const { id } = e;
  await useExportWord({
    id: Number(id),
    observationRecordId: activeItem.value.id,
  });
  isPop.value = false;
  isShowTemplate.value = false;
};

// 删除

// 请求Word模板
const requestWord = async () => {
  optionsList.observationWordList = [];
  let params = {
    documentTemplateCategory: "ObservationRecord",
    subjectType: "School",
    subjectId: uni.getStorageSync("USER_INFO").currentSchoolId,
  };

  try {
    const res = await getWordTemplate(params);
    console.log(res);
    if (res.status == 0) {
      switch (res.data.length) {
        case 0:
          uni.showToast({
            title: "当前学校下暂无模板，请联系客服",
            icon: "none",
          });
          break;
        case 1:
          await useExportWord({
            id: Number(res.data[0].id),
            observationRecordId: activeItem.value.id,
          });
          break;
        default:
          optionsList.observationWordList = res.data;
          await nextTick();
          isShowTemplate.value = true;
          break;
      }
    }
  } catch (e) {
    uni.showToast({
      title: "请求观察记录word模板失败",
      icon: "none",
    });
  }
};
// 导出word
const onExportWord = async () => {
  isPop.value = false;
  // return;
  uni.showLoading({
    title: "获取模板中",
    mask: true,
  });
  console.log(activeItem.value);
  await requestWord();
  uni.hideLoading();
};

const onAdd = () => {
  uni.navigateTo({
    url: "/observation/index",
  });
};

const onMask = () => {
  uni.$u.toast("观察正在⽣成中!");
};

function initObservationList() {
  observationList.value = [];
  getObservationList({
    pageSize: 999,
    pageNo: 1,
    pageModel: {
      schoolClassId: userInfo.value.currentClassId,
      classId: userInfo.value.currentClassId,
    },
    // createdBy: userInfo.value.id,
  }).then((r) => {
    observationList.value = groupDataByDay(r.data);
    //
    observationList.value.sort((a, b) => b.key - a.key);
    console.log(observationList.value, "observationList.value");
  });
}


// 删除一项
function onDelete() {
  console.log("删除");
  console.log(activeItem.value);
  deleteObservation(activeItem.value.id).then((r) => {
    if (r.status === 0) {
      uni.showToast({
        title: "删除成功",
        icon: "none",
      });
      isPop.value = false;
      initObservationList();
    }
  });
}

// 按天进行分组
function groupDataByDay(data) {
  if (JSON.stringify(data) == "[]") {
    return [];
  }
  const groupedData = {};
  const dayOfWeekMap = {
    0: "周日",
    1: "周一",
    2: "周二",
    3: "周三",
    4: "周四",
    5: "周五",
    6: "周六",
  };
  data.forEach((item) => {
    const date = dayjs(item.observationTime);
    const dayKey = date.format("YYYY-MM-DD");
    const year = date.year();
    const dayOfWeek = date.day();
    const month = date.month() + 1; // 月份从0开始，所以需要+1
    const day = date.date();

    if (!groupedData[dayKey]) {
      groupedData[dayKey] = {
        year,
        key: dayjs().year(year).month(month).date(day).valueOf(),
        date: `${month}月${day}日`,
        dayOfWeek: dayOfWeekMap[dayOfWeek],
        items: [],
      };
    }

    groupedData[dayKey].items.push(item);
  });

  return Object.values(groupedData);
}
// 详情
function onViewDetail(item) {
  uni.navigateTo({
    url: `/observation/updateObservation?id=${item.id}&observationRecordScene=${item.observationType}`,
  });
}

function onOpenPop(item) {
  isPop.value = true;
  activeItem.value = "";
  activeItem.value = item;
}

onShow(() => {
  const isRef = uni.getStorageSync("isRef");
  if (isRef) {
    initObservationList();
    uni.removeStorageSync("isRef"); // 清除存储的数据
  }
});

onMounted(() => {
  getUserInfo().then((r) => {
    userInfo.value = r.data;
    initObservationList();
  });
  getDICT("all").then((dics) => {
    const ObservationRecordSceneEnumDesc = dics["ObservationRecordSceneEnumDesc"];
    if (ObservationRecordSceneEnumDesc) {
      optionsList.observationRecordScene = Object.keys(
        dics["ObservationRecordSceneEnumDesc"]
      ).map((key) => {
        return {
          label: ObservationRecordSceneEnumDesc[key],
          id: key,
        };
      });
    }
  });
});

onShareAppMessage(() => sharePageObj());

const defaultUrl =
  "https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_boy.png";
const createdIcon = (item) => {
  if (item.createdByUser) {
    return item.createdByUser.url || defaultUrl;
  }
  return defaultUrl;
};

getclassList()
  .then((r) => {
    optionsList.schoolClassId = r.data.map((d) => ({
      ...d,
      label: d.title,
    }));
  })
  .catch((err) => { });
</script>
<style lang="scss" scoped>
.addClass {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  background: url("https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/course_addClass.png") no-repeat 100%;
  background-size: 140% 140%;
  background-position: -13rpx -7rpx;

  position: fixed;
  right: 32rpx;
  bottom: 120rpx;
}

.empty {
  color: #ccc;
  font-weight: 400;
  font-size: 28rpx;
  text-align: center;
  margin-top: 200rpx;
}

.iconAction {
  &>view {
    height: 88rpx;
    display: flex;
    align-items: center;
    font-size: 30rpx;
    font-weight: 400;
    letter-spacing: 0rpx;
    line-height: 48rpx;
    color: rgba(51, 51, 51, 1);
    text-align: left;
    vertical-align: middle;

    image {
      width: 40rpx;
      height: 40rpx;
      margin-right: 28rpx;
    }
  }
}

.observationRecord {
  box-sizing: border-box;
  position: relative;
  width: 100%;
  padding: 0 32rpx;

  &-title {
    height: 42rpx;
    line-height: 42rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 28rpx;
    font-weight: 400;
    margin-bottom: 22rpx;

    &-left {
      color: #333333;
    }

    &-right {
      color: #808080;
    }
  }

  .observationRecord-item {
    background: #fff;
    margin-bottom: 20rpx;
    border-radius: 12rpx;
    box-shadow: 4rpx 8rpx 16rpx #eee;
    padding: 32rpx;
    border-radius: 28rpx;
    position: relative;

    .Mask {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.5);
      z-index: 20;
    }

    .content-item {
      display: flex;
      // align-items: center;
      min-height: 48rpx;
      font-size: 28rpx;
      font-weight: 400;

      .item-label {
        width: 140rpx;
        color: #808080;
      }

      .item-value {
        color: #333333;
        flex: 1;
      }
    }
  }

  .observationRecord-item-header {
    border-bottom: 1px solid #eeeeee;
    padding-bottom: 24rpx;
    margin-bottom: 24rpx;
    display: flex;
    color: #333333;

    .item-header-center {
      // margin-left: auto;
      position: relative;
      z-index: 2;
    }

    .item-header-left {
      width: 72rpx;
      height: 72rpx;
      border-radius: 50%;
      overflow: hidden;
      margin-right: 20rpx;
    }

    .item-header-right {
      flex: 1;
    }

    .item-header-right-name {
      font-size: 30rpx;
      font-weight: 600;
      padding-bottom: 8rpx;
    }

    .item-header-right-time {
      font-size: 24rpx;
      font-weight: 400;
      color: #808080;
    }
  }
}
</style>
