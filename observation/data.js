import { ref } from "vue";
import { getWordTemplate, exportWordTemplate } from "./api";

export const scenes = [
  {
    label: "学习",
    id: "Learning",
  },
  {
    label: "生活",
    id: "Life",
  },
  {
    label: "区域游戏",
    id: "AreaGame",
  },
  {
    label: "体育活动",
    id: "Sports",
  },
  {
    label: "户外自主游戏",
    id: "OutdoorGame",
  },
];

export const getInitFormData = (args = {}) => ({
  observationType: "",
  schoolClassId: '',
  observationTime: "",
  observationLocation: "",
  activityName: "",
  productDesc: "",
  observationBackground: "",
  observationPurpose: "",
  observationContent: "",
  analysis: "",
  support: "",
  reflection: "",
  childNames: [],
  children: [],
  picList: [], // 附件id
  ...args
});

export const rules = ref({
  "activityName": { type: 'string', required: true, message: '请输入活动名称', trigger: ['blur'] },
  observationLocation: { required: true, message: '请输入观察地点', trigger: ['blur'] },
  children: { type: 'array', min: 1, required: true, message: '请选择观察对象, 如以选择忽略', trigger: ['blur'] },
  picList: { type: 'array', min: 1, required: true, message: '请选择上传附件', trigger: ['blur', 'change'] },
  activityOutline: { required: true, message: '请输入观察地点', trigger: ['blur'] },
  observationTime: { required: true, message: '请输入观察时间, 如以选择忽略', trigger: ['blur'] },
});

/**
 *  observationRecordScene: "Learning",
  schoolClassId: 1487,
  observationTime: "2025-02-27",
  observationLocation: "1",
  activityName: "2",
  productDesc: "3",
  backgroundAndPurpose: "4",
  content: "6",
  analysis: "f",
  support: "s",
  reflection: "a",
  aiComment: "",
  childIds: [3801, 3800],
  attachmentResourceIds: [],
  matrices: [
    { matrix1Id: 1244, matrix2Id: 1252, matrix3Id: 1260, targetId: 2529 },
  ],
 */

// 去除对象中所有为空的属性
export function removeEmpty(oj) {
  let obj = JSON.parse(JSON.stringify(oj));
  Object.keys(obj).forEach((key) => {
    if (obj[key] === null || obj[key] === undefined || obj[key] === "" || (Array.isArray(obj[key]) && obj[key].length === 0)) {
      delete obj[key];
    }
    if (key === 'childNames') {
      delete obj[key];
    }
  });
  return obj;
}

// 整理孩子的信息
export function childrenFilter(arr, className = null) {
  // 判断 是对象还是数组
  if (typeof arr === 'object' && !Array.isArray(arr)) {
    return {
      classId: arr.classId,
      childId: arr.id,
      childName: arr.name,
    }
  }
  let array = JSON.parse(JSON.stringify(arr));
  let result = [];
  array.forEach((item) => {
    result.push({
      classId: item.classId,
      childId: item.id,
      childName: item.name,
      className,
    });
  });
  return result;
}

// 去重
export function removeDuplicates(array1 = [], array2 = [], type = 'childId') {
  const combinedArray = array1.concat(array2);
  const uniqueArray = combinedArray.reduce((accumulator, current) => {
    const exists = accumulator.some(obj => obj[type] === current[type]);
    if (!exists) {
      accumulator.push(current);
    }
    return accumulator;
  }, []);

  return uniqueArray;
}

/** observationRecord 导出相关 **/

// 导出文档
export const useExportWord = async (obj) => {
  // loading
  uni.showLoading({
    title: "正在导出...",
    mask: true,
  });
  try {
    const res = await exportWordTemplate(obj);
    if (res.status === 0) {
      const { uri, filename } = res.data;
      // 在小程序中下载文件
      uni.downloadFile({
        url: uri,
        success: (dlRes) => {
          if (dlRes.statusCode === 200) {
            const fs = wx.getFileSystemManager(); // 获取文件系统管理器
            const newPath = `${wx.env.USER_DATA_PATH}/${filename}`;
            fs.copyFileSync(dlRes.tempFilePath, newPath); // 重命名文件
            // 兼容电脑端下载
            const userAgent = uni.getSystemInfoSync().platform;
            console.log(userAgent,'检测设备');
            
            if (userAgent === 'windows' || userAgent === 'mac') {
              wx.saveFileToDisk({
                filePath: dlRes.tempFilePath,
                success: (saveRes) => {
                  uni.hideLoading();
                  console.log("保存文件成功", saveRes);
                },
                fail: (error) => {
                  uni.hideLoading();
                  console.error("保存文件失败:", error);
                  uni.$u.toast("保存文件失败");
                },
              })
              return;
            }

            uni.openDocument({
              filePath: newPath,
              showMenu: true,
              success: () => {
                uni.hideLoading();
                console.log("打开文档成功");
              },
              fail: (error) => {
                uni.hideLoading();
                console.error("打开文档失败:", error);
                uni.showToast({
                  title: "打开文档失败",
                  icon: "none",
                });
              },
            }); // 打开文档

          } else {
            uni.showToast({
              title: "下载失败",
              icon: "none",
            });
          }
        },
        fail: (error) => {
          uni.hideLoading();
          console.error("下载失败:", error);
          uni.showToast({
            title: "下载失败",
            icon: "none",
          });
        },
      });
    }
  } catch (error) {
    uni.hideLoading();
  }
};

const rename = (dlRes) => {
  if (dlRes?.header) {
    let parts = dlRes.header['Content-Disposition'].split(";")
    let filenamePart = parts.find(part => part.trim().startsWith('filename='));
    // 提取文件名，并去除引号
    let filename = filenamePart.split('=')[1].replace(/["']/g, '');
    let decodedFilename = decodeURIComponent(filename);
    // 替换名字
    // decodedFilename = dlRes.tempFilePath.replace(/\/([^\/]+)(\.docx)$/, `/${decodedFilename}`);
    return decodedFilename;
  }
}


