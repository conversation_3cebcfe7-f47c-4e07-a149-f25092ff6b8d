// 儿童管理
import { request } from "@/common/request.js";

export function addObservation(data) {
  return request({
    url: `/jsapi/business/observation/manualRecord`,
    method: "post",
    data,
  });
}

export function addAIObservation(data) {
  return request({
    url: `/jsapi/business/observation/submitAiObservationRecord`,
    method: "POST",
    data,
  });
}

export function updateObservation(data) {
  return request({
    url: "/jsapi/business/observation/updateSubject",
    method: "POST",
    data,
  });
}

export function getObservationList(data) {
  return request({
    url: "/jsapi/business/observation/pageList",
    method: "POST",
    data,
  });
}

export function getObservationDetailById(id) {
  return request({
    url: `/jsapi/business/observation/detail/${id}`,
    method: "POST",
  });
}

export function deleteObservation(id) {
  return request({
    url: `/jsapi/business/observation/delete/${id}`,
    method: 'POST'
  });
}

// 导出word
export function exportWordTemplate(data) {
  return request({
    url: `/business/document-template/export-document`,
    method: 'POST',
    data
  });
}
// 获取word
export function getWordTemplate(data) {
	return request({
		url: `/business/document-template/list`,
		method: "GET",
		data
	})
}