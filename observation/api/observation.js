// 观察记录
import { request } from "@/common/request.js";
/***
 * @param {number} relId
 * @param {string} matchType observation 观察记录 subject 教案 onetoone 一对一
 * **/
export function getCoreExp(data) {
    return request({
        url: `/jsapi/business/coreExp/getCoreExpById`,
        method: "GET",
        data,
    });
}
export function updateCoreExpList(data) {
    console.log("数据请求参数", data);
    
    return request({
        url: `/jsapi/business/coreExp/updateCoreExpBatch/${data.matchType}/${data.relId}`,
        method: "POST",
        data: data.list,
    })
}