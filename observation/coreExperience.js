// 核心经验
import { getCoreExp } from './api/observation.js'
import { onMounted, ref } from 'vue'
import { getMatrixList } from "@/api";
import { useQueryParams } from "@/common/hooks/useQueryParams.js";
export default () => {
    // 根据id获取 领域 的核心经验
    let matric = ref([])
    let matrix1List = ref([])
    const { params } = useQueryParams("id");
    const getMatrixDetails = async (mId) => {
        let res = await getCoreExp({
            relId: mId,
            matchType: 'observation'
        })
        if (res.status == 0) {
            matric.value = res.data;
            // 每一项添加一个字段，用于判断是否需要编辑
            matric.value.forEach(item => {
                item.isEdit = false;
            })
            return
        }
        console.log("获取核心经验详情：", res.data);
        uni.$u.toast(res.message || '获取核心经验失败')

    }
    // 请求领域的下拉数据
    const matrixList = async (id) => {
        let res = await getMatrixList({
            pid: 0,
            pageSize: 1000,
        })
        if (res.status == 0) {
            matrix1List.value = res.data;
        }
    }


    onMounted(() => {
        console.log("核心经验", params.value.id)
        getMatrixDetails(params.value.id)
        matrixList()
    })
    return { matric, matrix1List }
}