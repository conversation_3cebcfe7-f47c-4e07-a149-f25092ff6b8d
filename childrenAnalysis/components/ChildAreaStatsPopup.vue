<template>
  <Popup :show="show" @close="handleClose">
    <view class="child-stats-popup">
      <view class="popup-header">
        <text class="popup-title">{{ childData.title }}的进区统计</text>
        <text class="close-btn" @click="handleClose">×</text>
      </view>
      
      <!-- Tab 切换 -->
      <view class="tab-container">
        <view 
          class="tab-item" 
          :class="{ active: activeTab === 'radar' }"
          @click="switchTab('radar')"
        >
          雷达图
        </view>
        <view 
          class="tab-item" 
          :class="{ active: activeTab === 'timeline' }"
          @click="switchTab('timeline')"
        >
          时间轴
        </view>
      </view>
      
      <view class="popup-content">
        <!-- 雷达图 Tab -->
        <view v-if="activeTab === 'radar'" class="radar-tab">
          <view class="charts-box">
            <view
              v-if="radarChartData.categories && radarChartData.categories.length > 0"
              class="chart-container"
            >
              <qiun-data-charts
                type="radar"
                :opts="radarOpts"
                :chartData="radarChartData"
                @onChartReady="onRadarChartReady"
              />
            </view>
            <view v-else class="chart-empty">
              <text>暂无区域数据</text>
            </view>
          </view>
        </view>
        
        <!-- 时间轴 Tab -->
        <view v-if="activeTab === 'timeline'" class="timeline-tab">
          <view class="charts-box">
            <view
              v-if="scatterChartData.series && scatterChartData.series.length > 0 && scatterChartData.series[0].data.length > 0"
              class="chart-container"
            >
              <qiun-data-charts
                type="scatter"
                :opts="scatterOpts"
                :chartData="scatterChartData"
                @onChartReady="onScatterChartReady"
              />
            </view>
            <view v-else class="chart-empty">
              <text>该儿童暂无进区记录</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </Popup>
</template>

<script setup>
import Popup from '@/components/Popup/Popup.vue'
import QiunDataCharts from '../qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue'
import { ref, watch } from 'vue'

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  childData: {
    type: Object,
    default: () => ({})
  },
  areaList: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['close'])

// 响应式数据
const activeTab = ref('radar') // 默认显示雷达图
const radarChartData = ref({})
const scatterChartData = ref({})
const radarChartInstance = ref(null)
const scatterChartInstance = ref(null)

// 雷达图配置
const radarOpts = ref({
  color: ['#1890FF'],
  enableScroll: false,
  legend: {
    show: false
  },
  dataPointShape: true,
  extra: {
    radar: {
      gridType: 'circle',
      gridColor: '#E6E6E6',
      labelShow: true,
      opacity: 0.3,
      radius: 120,
      labelShow: true,
      border: true,
      borderWidth: 1,
      labelColor: '#666666',
      borderColor: '#E6E6E6'
    }
  }
})

// 散点图配置
const scatterOpts = ref({
  color: ["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],
  padding: [15,15,0,15],
  dataLabel: false,
  enableScroll: false,
  legend: {},
  xAxis: {
    disableGrid: false,
    gridType: "dash",
    splitNumber: 5,
    boundaryGap: "justify",
    min: 0
  },
  yAxis: {
    disableGrid: false,
    gridType: "dash"
  },
  extra: {
    scatter: {}
  }
})

// Tab 切换
const switchTab = (tab) => {
  activeTab.value = tab
  if (tab === 'radar') {
    generateRadarData()
  } else if (tab === 'timeline') {
    generateScatterData()
  }
}

// 关闭弹窗
const handleClose = () => {
  // 清理图表实例
  if (radarChartInstance.value) {
    try {
      radarChartInstance.value.dispose && radarChartInstance.value.dispose()
    } catch (error) {
      console.warn('清理雷达图实例时出错:', error)
    }
    radarChartInstance.value = null
  }
  
  if (scatterChartInstance.value) {
    try {
      scatterChartInstance.value.dispose && scatterChartInstance.value.dispose()
    } catch (error) {
      console.warn('清理散点图实例时出错:', error)
    }
    scatterChartInstance.value = null
  }
  
  // 清空数据
  radarChartData.value = {}
  scatterChartData.value = {}
  activeTab.value = 'radar'
  
  emit('close')
}

// 生成雷达图数据
const generateRadarData = () => {
  if (!props.areaList || props.areaList.length === 0) {
    radarChartData.value = {}
    return
  }

  // 获取该儿童在各区域的进区次数，确保即使是0也要显示
  const childData = props.areaList.map((area) => {
    if (!props.childData.childAreaEntryCountList) {
      return 0
    }
    const areaEntry = props.childData.childAreaEntryCountList.find((entry) => entry.area === area.area)
    return areaEntry ? areaEntry.count : 0
  })

  // 找到当前儿童进区次数最多的数值
  const currentChildMaxCount = Math.max(...childData)
  
  // 如果当前儿童所有数据都是0，设置默认值为10
  const displayMaxCount = currentChildMaxCount === 0 ? 10 : currentChildMaxCount + 0.5

  // 获取所有区域名称并在后面添加该儿童在该区域的实际进区次数
  const categories = props.areaList.map((area, index) => {
    const count = childData[index]
    return `${area.area}(${count})`
  })

  // 构造雷达图数据
  radarChartData.value = {
    categories: categories,
    series: [
      {
        name: props.childData.title,
        data: childData
      }
    ]
  }

  // 设置雷达图最大值
  radarOpts.value.extra.radar.max = displayMaxCount
}

// 生成散点图数据
const generateScatterData = () => {
  // 暂时显示为空，等待后端数据修复
  scatterChartData.value = {
    categories: [],
    series: []
  }
}

// 图表就绪回调
const onRadarChartReady = (chart) => {
  radarChartInstance.value = chart
}

const onScatterChartReady = (chart) => {
  scatterChartInstance.value = chart
}

// 监听 props 变化
watch(() => props.show, (newVal) => {
  if (newVal) {
    // 弹窗打开时生成对应的图表数据
    if (activeTab.value === 'radar') {
      generateRadarData()
    } else {
      generateScatterData()
    }
  }
})

watch(() => props.childData, () => {
  if (props.show) {
    if (activeTab.value === 'radar') {
      generateRadarData()
    } else {
      generateScatterData()
    }
  }
})
</script>

<style scoped lang="scss">
.child-stats-popup {
  max-height: 80vh;
  background: #fff;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  
  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;
    
    .popup-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }

    .close-btn {
      font-size: 48rpx;
      color: #999;
      font-weight: 300;
      line-height: 1;
      padding: 10rpx;

      &:active {
        color: #666;
      }
    }
  }
  
  .tab-container {
    display: flex;
    border-bottom: 1rpx solid #f0f0f0;
    
    .tab-item {
      flex: 1;
      padding: 24rpx;
      text-align: center;
      font-size: 28rpx;
      color: #666;
      background: #f8f8f8;
      transition: all 0.3s ease;
      
      &.active {
        color: #1890FF;
        background: #fff;
        border-bottom: 2rpx solid #1890FF;
      }
      
      &:active {
        background: #f0f0f0;
      }
    }
  }
  
  .popup-content {
    .radar-tab, .timeline-tab {
      .charts-box {
        width: 100%;
        height: 600rpx;
        padding: 20rpx;

        .chart-container {
          width: 100%;
          height: 100%;
          position: relative;
        }

        .chart-empty {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          height: 100%;
          color: #999;
          font-size: 28rpx;
        }
      }
    }
  }
}
</style>
