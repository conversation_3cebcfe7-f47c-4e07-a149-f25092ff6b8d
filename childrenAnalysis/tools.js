import { reactive } from 'vue'
export default () => {
  let tableData = reactive({
    data: [],
    columns: [
      {
        key: 'matrix1Name',
        title: '领域',
        width: 50,
        align: 'left'
      },
      {
        key: 'matrix2Name',
        title: '维度',
        width: 50,
        align: 'left'
      },
      {
        key: 'matrix3Name',
        title: '子维度',
        width: 50,
        align: 'left'
      },
      {
        key: 'indexCount',
        title: '小班指标',
        width: 50,
        isSort: true,
        align: 'left',
        formatter: (row) => {
          return row || 0
        }
      },
      {
        key: 'materialCount',
        title: '小班材料',
        width: 70,
        align: 'left',
        formatter: (row) => {
          return row || 0
        }
      },
      {
        key: 'materialNameList',
        title: '材料名称',
        width: 50,
        align: 'left',
        formatter: (row) => {
          // 这里的row是单元格的值，不是整行数据
          return row || '-'
        }
      }
    ]
  })

  const columns = [
    {
      key: 'matrix1Name',
      title: '领域',
      width: 50
    },
    {
      key: 'matrix2Name',
      title: '维度',
      width: 50
    },
    {
      key: 'matrix3Name',
      title: '子维度',
      width: 50
    },
    {
      key: 'indexCount',
      title: '小班指标',
      width: 50,
      formatter: (row) => {
        return row || 0
      }
    },
    {
      key: 'materialCount',
      title: '小班材料',
      width: 70,
      formatter: (row) => {
        return row || 0
      }
    },
    {
      key: 'materialNameList',
      title: '材料名称',
      width: 50,
      formatter: (row) => {
        // 这里的row是单元格的值，不是整行数据
        return row || '-'
      }
    }
  ]

  return {
    tableData,
    columns
  }
}
