<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <up-navbar
      title="幼儿进区统计"
      :safeAreaInsetTop="true"
      :fixed="true"
      :placeholder="true"
      bgColor="transparent"
      :autoBack="true"
    >
    </up-navbar>

    <view class="container">
      <!-- 顶部信息栏 -->
      <view class="header-bar">
        <view class="class-info">
          <text class="class-title">{{ currentTitle || '-' }}</text>
        </view>
        <view class="date-filter" @click="openDatePopup">
          <text class="date-text">{{ formatDateRange }}</text>
          <image class="change" src="/static/game/change.svg" />
        </view>
      </view>
      <!-- 加载状态 -->
      <view class="loading-container" v-if="isLoading">
        <view class="loading-content">
          <view class="loading-spinner"></view>
          <text class="loading-text">正在加载数据...</text>
        </view>
      </view>

      <!-- 统计表格 -->
      <view class="table-container" v-else-if="tableData.length > 0">
        <scroll-view class="table-scroll" scroll-y="true" scroll-x="true" show-scrollbar="false">
          <view class="table-content">
            <!-- 区域每周模式的表格 -->
            <template v-if="selectedDateType === 'weeklyByArea'">
              <!-- 表头 -->
              <view class="table-header">
                <view class="header-cell name-cell">周次</view>
                <view
                  class="header-cell area-cell clickable"
                  v-for="area in areaList"
                  :key="area.areaId"
                  :style="tempSelectedDateType === 'weeklyByArea' ? 'color: #3f79ff;' : ''"
                  @click="handleAreaClick(area)"
                >
                  {{ area.area }}
                </view>
              </view>
              <!-- 表体 -->
              <view class="table-body">
                <view class="table-row" v-for="weekData in tableData" :key="weekData.week">
                  <view class="body-cell name-cell" @click="handleWeekClick(weekData)">
                    <view class="week-text">{{ formatWeekDisplay(weekData.week) }}</view>
                  </view>
                  <view class="body-cell area-cell" v-for="area in areaList" :key="area.areaId">
                    {{ getWeekAreaCount(weekData, area.area) }}
                  </view>
                </view>
              </view>
            </template>

            <!-- 原有的儿童模式表格 -->
            <template v-else>
              <!-- 表头 -->
              <view class="table-header">
                <view class="header-cell name-cell">姓名</view>
                <view class="header-cell area-cell" v-for="area in areaList" :key="area.areaId">
                  {{ area.area }}
                </view>
              </view>
              <!-- 表体 -->
              <view class="table-body">
                <view class="table-row" v-for="child in tableData" :key="child.id">
                  <view class="body-cell name-cell" @click="handleNameClick(child)">{{
                    child.title
                  }}</view>
                  <view class="body-cell area-cell" v-for="area in areaList" :key="area.areaId">
                    {{ getChildAreaCount(child, area.area) }}
                  </view>
                </view>
              </view>
            </template>
          </view>
        </scroll-view>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-else>
        <text>暂无数据</text>
      </view>
      <!-- 统计时间选择弹窗 -->
      <Popup :show="isDateShow" @close="closeDatePopup">
        <view class="date-popup">
          <view class="popup-header">
            <text class="popup-title">统计时间</text>
            <text
              class="confirm-btn"
              :class="{ disabled: isLoading }"
              @click="confirmDateSelection"
            >
              {{ isLoading ? '加载中...' : '确定' }}
            </text>
          </view>
          <view class="popup-content">
            <text class="popup-subtitle">请选择查询时间范围</text>
            <view class="date-options">
              <view class="date-row">
                <view
                  class="date-option"
                  :class="{ active: tempSelectedDateType === 'semester', disabled: isLoading }"
                  @click="selectDateType('semester')"
                >
                  本学期
                </view>
                <view
                  class="date-option"
                  :class="{ active: tempSelectedDateType === 'today', disabled: isLoading }"
                  @click="selectDateType('today')"
                >
                  今日
                </view>
                <view
                  class="date-option"
                  :class="{ active: tempSelectedDateType === 'yesterday', disabled: isLoading }"
                  @click="selectDateType('yesterday')"
                >
                  昨日
                </view>
              </view>
              <view class="date-row">
                <view
                  class="date-option"
                  :class="{
                    active: tempSelectedDateType === 'dayBeforeYesterday',
                    disabled: isLoading
                  }"
                  @click="selectDateType('dayBeforeYesterday')"
                >
                  前日
                </view>
                <view
                  class="date-option"
                  :class="{ active: tempSelectedDateType === 'thisWeek', disabled: isLoading }"
                  @click="selectDateType('thisWeek')"
                >
                  本周
                </view>
                <view
                  class="date-option"
                  :class="{ active: tempSelectedDateType === 'lastWeek', disabled: isLoading }"
                  @click="selectDateType('lastWeek')"
                >
                  上周
                </view>
              </view>
              <view class="date-row">
                <view
                  class="date-option"
                  :class="{ active: tempSelectedDateType === 'last7Days', disabled: isLoading }"
                  @click="selectDateType('last7Days')"
                >
                  最近7天
                </view>
                <view
                  class="date-option"
                  :class="{ active: tempSelectedDateType === 'thisMonth', disabled: isLoading }"
                  @click="selectDateType('thisMonth')"
                >
                  本月
                </view>
                <view
                  class="date-option"
                  :class="{ active: tempSelectedDateType === 'lastMonth', disabled: isLoading }"
                  @click="selectDateType('lastMonth')"
                >
                  上月
                </view>
              </view>
              <view class="date-row">
                <view
                  class="date-option"
                  :class="{ active: tempSelectedDateType === 'last30Days', disabled: isLoading }"
                  @click="selectDateType('last30Days')"
                >
                  最近30天
                </view>
              </view>
              <!-- 分隔线 -->
              <view class="date-separator"></view>
              <!-- 指定日期选项 -->
              <view class="custom-date-row" :class="{ disabled: isLoading }">
                <text class="custom-date-label">指定日期</text>
                <uni-datetime-picker
                  type="daterange"
                  v-model="tempCustomDateRange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  @change="handleCustomDateRange"
                  :border="false"
                  :clear-icon="false"
                  :disabled="isLoading"
                >
                  <template v-slot:default>
                    <view class="custom-date-input">
                      {{ formatCustomDateRange }}
                    </view>
                  </template>
                </uni-datetime-picker>
              </view>
              <!-- 分隔线 -->
              <view class="date-separator"></view>
              <!-- 区域每周选项 -->
              <view class="date-row">
                <view
                  class="date-option weekly-option"
                  :class="{ active: tempSelectedDateType === 'weeklyByArea', disabled: isLoading }"
                  @click="selectDateType('weeklyByArea')"
                >
                  各区域每周进区人数
                </view>
              </view>
            </view>
          </view>
        </view>
      </Popup>

      <!-- 儿童进区统计弹窗 -->
      <ChildAreaStatsPopup
        :show="isRadarShow"
        :childData="selectedChild"
        :areaList="areaList"
        @close="closeRadarPopup"
      />

      <!-- 折线图弹窗 -->
      <Popup :show="isLineChartShow" @close="closeLineChartPopup" :zIndex="9000">
        <view class="line-chart-popup">
          <view class="line-chart-header">
            <text class="line-chart-title">{{ selectedArea.area }}的周进区统计 </text>
            <text class="close-btn" @click="closeLineChartPopup">×</text>
          </view>
          <!-- x轴比例选择 -->
          <view class="chart-controls">
            <text class="control-label">显示比例：</text>
            <uni-data-select
              v-model="currentXAxisScaleValue"
              :localdata="xAxisScaleOptions"
              @change="onXAxisScaleChange"
              :clear="false"
              placeholder="请选择显示比例"
            ></uni-data-select>
          </view>
          <view class="line-chart-content">
            <view class="charts-box">
              <view
                v-if="lineChartData.categories && lineChartData.categories.length > 0"
                class="chart-container"
              >
                <qiun-data-charts
                  type="line"
                  :opts="lineOpts"
                  :chartData="lineChartData"
                  @onChartReady="
                    (chart) => {
                      lineChartInstance = chart
                    }
                  "
                />
              </view>
              <view v-else class="chart-empty">
                <text>暂无数据</text>
                <text class="debug-info">请确保已加载表格数据</text>
              </view>
            </view>
          </view>
        </view>
      </Popup>
    </view>
  </view>
</template>

<script setup>
import Popup from '@/components/Popup/Popup.vue'
import QiunDataCharts from './qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue'
import ChildAreaStatsPopup from './components/ChildAreaStatsPopup.vue'
import { countAreaEntry, listAreaEntryCountByWeek } from './api'
import { ref, computed } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { checks } from '@/utils/index.js'
import dayjs from 'dayjs'

// 基础数据
let currentTitle = ref('') // 当前班级名称
let classId = ref('') // 班级ID
let isLoading = ref(false)

// 日期筛选相关
let isDateShow = ref(false)
let selectedDateType = ref('semester') // 默认本学期
let customDateRange = ref([]) // 日期范围选择

// 临时选择状态（用于弹窗中的选择，点击确定后才应用）
let tempSelectedDateType = ref('semester') // 临时选择的日期类型
let tempCustomDateRange = ref([]) // 临时选择的自定义日期范围

// 表格数据
let tableData = ref([]) // 儿童进区统计数据
let areaList = ref([]) // 区域列表

// 儿童进区统计弹窗相关
let isRadarShow = ref(false) // 弹窗显示状态
let selectedChild = ref({}) // 选中的儿童数据

// 折线图相关
let isLineChartShow = ref(false) // 折线图弹窗显示状态
let selectedArea = ref({}) // 选中的区域数据
let lineChartData = ref({}) // 折线图数据
let lineChartInstance = ref(null) // 折线图实例引用

// x轴比例选择相关
let currentXAxisScaleValue = ref('auto') // 当前选中的x轴比例值
let currentXAxisScale = ref({ value: 'auto', text: '自动' }) // 当前选中的x轴比例
let xAxisScaleOptions = ref([
  { value: 'auto', text: '自动' },
  { value: 'all', text: '显示全部' },
  { value: '6', text: '显示6个' },
  { value: '8', text: '显示8个' },
  { value: '10', text: '显示10个' },
  { value: '12', text: '显示12个' },
  { value: '15', text: '显示15个' }
]) // x轴比例选项

// 折线图配置
let lineOpts = ref({
  color: ['#1890FF'],
  padding: [15, 15, 0, 15], // 增加左右边距
  enableScroll: false,
  legend: {
    show: false // 隐藏图例，因为只有一条线
  },
  xAxis: {
    disableGrid: true,
    axisLabel: true,
    axisLabelColor: '#666666',
    fontSize: 10, // 调整字体大小
    rotateLabel: false, // 不旋转标签
    itemCount: 8, // 最多显示8个x轴标签
    scrollShow: true // 当数据点多时启用滚动
  },
  yAxis: {
    gridType: 'dash',
    dashLength: 2,
    gridColor: '#E6E6E6',
    axisLabel: true,
    axisLabelColor: '#666666',
    fontSize: 10
  },
  extra: {
    line: {
      type: 'curve', // 使用曲线
      width: 3, // 增加线条宽度
      activeType: 'hollow', // 空心点
      pointShape: 'circle', // 圆形数据点
      pointSize: 6, // 数据点大小
      pointBorderWidth: 2, // 数据点边框宽度
      pointBorderColor: '#1890FF',
      pointFillColor: '#FFFFFF'
    }
  }
})

// 日期类型映射
const dateTypeMap = {
  today: '今日',
  yesterday: '昨日',
  dayBeforeYesterday: '前日',
  thisWeek: '本周',
  lastWeek: '上周',
  last7Days: '最近7天',
  thisMonth: '本月',
  lastMonth: '上月',
  last30Days: '最近30天',
  semester: '本学期',
  weeklyByArea: '各区域每周进区人数'
}

// 计算格式化的日期范围显示
const formatDateRange = computed(() => {
  if (selectedDateType.value === 'custom') {
    if (customDateRange.value && customDateRange.value.length === 2) {
      const startDate = dayjs(customDateRange.value[0]).format('YYYY.MM.DD')
      const endDate = dayjs(customDateRange.value[1]).format('YYYY.MM.DD')
      return `${startDate}-${endDate}`
    }
    return '指定日期'
  }
  return dateTypeMap[selectedDateType.value] || '本学期'
})

// 格式化自定义日期范围显示
const formatCustomDateRange = computed(() => {
  if (tempCustomDateRange.value && tempCustomDateRange.value.length === 2) {
    const startDate = dayjs(tempCustomDateRange.value[0]).format('YYYY.MM.DD')
    const endDate = dayjs(tempCustomDateRange.value[1]).format('YYYY.MM.DD')
    return `${startDate} 至 ${endDate}`
  }
  return '请选择日期范围'
})

// 打开日期选择弹窗
const openDatePopup = () => {
  // 同步当前状态到临时状态
  tempSelectedDateType.value = selectedDateType.value
  tempCustomDateRange.value = [...customDateRange.value]
  isDateShow.value = true
}

// 关闭日期选择弹窗（取消操作，恢复临时状态）
const closeDatePopup = () => {
  // 恢复临时状态为当前状态
  tempSelectedDateType.value = selectedDateType.value
  tempCustomDateRange.value = [...customDateRange.value]
  isDateShow.value = false
}

// 选择日期类型（仅更新临时状态）
const selectDateType = (type) => {
  if (isLoading.value) return // 防止加载时切换
  tempSelectedDateType.value = type
}

// 处理自定义日期范围选择（仅更新临时状态，不执行筛选）
const handleCustomDateRange = (e) => {
  if (isLoading.value) return // 防止加载时操作

  if (e && e.length === 2) {
    tempCustomDateRange.value = e
    tempSelectedDateType.value = 'custom'
    // 注意：这里只更新临时选择状态，不执行筛选逻辑
    // 筛选逻辑只在点击确定按钮时执行
  }
}

// 确认日期选择
const confirmDateSelection = async () => {
  if (isLoading.value) return // 防止重复点击

  // 应用临时选择状态到正式状态
  selectedDateType.value = tempSelectedDateType.value
  customDateRange.value = tempCustomDateRange.value

  isDateShow.value = false
  // 重新获取数据
  await getTableData()
}

// 获取儿童在指定区域的进区次数
const getChildAreaCount = (child, areaName) => {
  if (!child.childAreaEntryCountList) return 0

  const areaEntry = child.childAreaEntryCountList.find((entry) => entry.area === areaName)
  return areaEntry ? areaEntry.count : 0
}

// 获取指定周在指定区域的进区次数
const getWeekAreaCount = (weekData, areaName) => {
  if (!weekData.arreaEntryCountList) return 0

  const areaEntry = weekData.arreaEntryCountList.find((entry) => entry.area === areaName)
  return areaEntry ? areaEntry.count : 0
}

// 处理姓名点击事件
const handleNameClick = (child) => {
  selectedChild.value = child
  isRadarShow.value = true
}

// 处理周次点击事件
const handleWeekClick = (weekData) => {
  selectedChild.value = { title: weekData.week, week: weekData.week }
  isRadarShow.value = true
}

// 处理区域点击事件
const handleAreaClick = (area) => {
  selectedArea.value = area
  generateLineChartData(area)
  isLineChartShow.value = true
}

// 处理x轴比例选择
const onXAxisScaleChange = (e) => {
  const selectedValue = e
  currentXAxisScaleValue.value = selectedValue
  const selectedOption = xAxisScaleOptions.value.find((option) => option.value === selectedValue)
  if (selectedOption) {
    currentXAxisScale.value = selectedOption
  }

  // 重新生成图表数据
  if (selectedArea.value && selectedArea.value.area) {
    generateLineChartData(selectedArea.value)
  }
}

// 格式化周次显示（换行显示）
const formatWeekDisplay = (week) => {
  // 检查 week 是否为空或未定义
  if (!week) {
    return '未知周次'
  }

  if (week === '本学期总计') {
    return week
  }

  // 将日期范围分成两行显示
  const parts = week.split(' - ')
  if (parts.length === 2) {
    return `${parts[0]} -\n${parts[1]}`
  }
  return week
}

// 生成折线图数据
const generateLineChartData = (area) => {
  // 过滤掉总计数据，只使用周数据
  const weekDataList = tableData.value.filter((item) => item.week !== '本学期总计')

  if (weekDataList.length === 0) {
    lineChartData.value = {
      categories: [],
      series: []
    }
    return
  }

  // 按周智能采样数据点
  let sampledCategories = []
  let sampledAreaData = []

  // 根据用户选择的x轴比例确定显示策略
  const dataLength = weekDataList.length
  let maxDisplayPoints = 12 // 默认最多显示12个点
  let samplingStrategy = 'week' // 采样策略：week(按周), biweek(双周), month(按月)

  // 根据用户选择的x轴比例确定显示策略
  const scaleValue = currentXAxisScale.value.value

  if (scaleValue === 'all') {
    // 用户选择显示全部
    maxDisplayPoints = dataLength
    samplingStrategy = 'all'
  } else if (scaleValue !== 'auto') {
    // 用户选择了具体数量
    maxDisplayPoints = parseInt(scaleValue)
    // 根据目标点数和数据量确定采样策略
    if (dataLength <= maxDisplayPoints) {
      samplingStrategy = 'all'
    } else if (dataLength <= maxDisplayPoints * 2) {
      samplingStrategy = 'biweek'
    } else if (dataLength <= maxDisplayPoints * 4) {
      samplingStrategy = 'month'
    } else {
      samplingStrategy = 'quarter'
    }
  } else {
    // 自动模式：根据数据量确定采样策略和最大显示点数
    if (dataLength <= 12) {
      // 数据较少，显示所有数据点
      maxDisplayPoints = dataLength
      samplingStrategy = 'all'
    } else if (dataLength <= 24) {
      // 中等数据量，按双周采样，显示12个点左右
      maxDisplayPoints = 12
      samplingStrategy = 'biweek'
    } else if (dataLength <= 48) {
      // 较多数据，按月采样
      maxDisplayPoints = 12
      samplingStrategy = 'month'
    } else {
      // 大量数据，按季度采样
      maxDisplayPoints = 10
      samplingStrategy = 'quarter'
    }
  }

  if (samplingStrategy === 'all') {
    // 显示所有数据点
    sampledCategories = weekDataList.map((weekData) => {
      return formatWeekLabel(weekData.week)
    })

    sampledAreaData = weekDataList.map((weekData) => {
      if (!weekData.arreaEntryCountList) return 0
      const areaEntry = weekData.arreaEntryCountList.find((entry) => entry.area === area.area)
      return areaEntry ? areaEntry.count : 0
    })
  } else {
    // 按策略采样数据
    const step = Math.ceil(dataLength / maxDisplayPoints)

    for (let i = 0; i < dataLength; i += step) {
      const endIndex = Math.min(i + step, dataLength)
      const groupData = weekDataList.slice(i, endIndex)

      // 计算这个时间段的数据
      let groupValue = 0
      let validDataCount = 0

      groupData.forEach((weekData) => {
        if (weekData.arreaEntryCountList) {
          const areaEntry = weekData.arreaEntryCountList.find((entry) => entry.area === area.area)
          if (areaEntry) {
            groupValue += areaEntry.count
            validDataCount++
          }
        }
      })

      // 根据采样策略决定是求和还是平均值
      let finalValue = 0
      if (validDataCount > 0) {
        if (samplingStrategy === 'week' || step === 1) {
          // 按周采样时使用平均值
          finalValue = Math.round((groupValue / validDataCount) * 10) / 10
        } else {
          // 按双周或月采样时使用总和
          finalValue = groupValue
        }
      }

      // 生成标签
      const label = generateGroupLabel(groupData, samplingStrategy)

      sampledCategories.push(label)
      sampledAreaData.push(finalValue)
    }
  }

  // 动态调整折线图配置
  const updatedLineOpts = { ...lineOpts.value }

  // 根据数据点数量调整x轴配置，不启用滚动
  updatedLineOpts.xAxis.itemCount = sampledCategories.length // 显示所有标签
  updatedLineOpts.enableScroll = false // 禁用滚动

  // 如果标签太多，调整字体大小
  if (sampledCategories.length > 10) {
    updatedLineOpts.xAxis.fontSize = 8 // 减小字体
  } else if (sampledCategories.length > 6) {
    updatedLineOpts.xAxis.fontSize = 9
  } else {
    updatedLineOpts.xAxis.fontSize = 10
  }

  // 更新配置
  lineOpts.value = updatedLineOpts

  // 构造折线图数据
  lineChartData.value = {
    categories: sampledCategories,
    series: [
      {
        name: area.area,
        data: sampledAreaData
      }
    ]
  }
}

// 格式化周标签
const formatWeekLabel = (week) => {
  if (!week) return '未知'

  // 如果是特殊标签，直接返回
  if (week === '本学期总计') return week

  // 解析日期范围 "YYYY-MM-DD - YYYY-MM-DD"
  const weekRange = week.split(' - ')
  if (weekRange.length === 2) {
    const startDate = weekRange[0]
    const endDate = weekRange[1]

    // 提取月日信息 MM-DD
    const startMonthDay = startDate.substring(5) // 去掉年份
    const endMonthDay = endDate.substring(5)

    // 如果是同一个月，只显示开始日期的月日
    if (startDate.substring(5, 7) === endDate.substring(5, 7)) {
      return startMonthDay
    } else {
      // 跨月显示，显示开始月日
      return startMonthDay
    }
  }

  return week
}

// 生成分组标签
const generateGroupLabel = (groupData, strategy) => {
  if (groupData.length === 0) return '未知'

  const firstWeek = groupData[0].week
  const lastWeek = groupData[groupData.length - 1].week

  if (!firstWeek) return '未知'

  if (strategy === 'week' || groupData.length === 1) {
    // 单周显示
    return formatWeekLabel(firstWeek)
  } else if (strategy === 'biweek') {
    // 双周显示：显示第一周的开始日期
    const firstWeekRange = firstWeek.split(' - ')
    if (firstWeekRange.length === 2) {
      return firstWeekRange[0].substring(5) // MM-DD格式
    }
    return formatWeekLabel(firstWeek)
  } else if (strategy === 'month') {
    // 按月显示：显示月份
    const firstWeekRange = firstWeek.split(' - ')
    if (firstWeekRange.length === 2) {
      const monthDay = firstWeekRange[0].substring(5, 7) // MM
      return `${monthDay}月`
    }
    return formatWeekLabel(firstWeek)
  } else if (strategy === 'quarter') {
    // 按季度显示：显示季度
    const firstWeekRange = firstWeek.split(' - ')
    if (firstWeekRange.length === 2) {
      const month = parseInt(firstWeekRange[0].substring(5, 7)) // MM
      const quarter = Math.ceil(month / 3)
      return `Q${quarter}`
    }
    return formatWeekLabel(firstWeek)
  }

  return formatWeekLabel(firstWeek)
}

// 关闭儿童进区统计弹窗
const closeRadarPopup = () => {
  selectedChild.value = {}
  isRadarShow.value = false
}

// 关闭折线图弹窗
const closeLineChartPopup = () => {
  // 先清理图表实例
  if (lineChartInstance.value) {
    try {
      // 销毁图表实例
      lineChartInstance.value.dispose && lineChartInstance.value.dispose()
    } catch (error) {
      console.warn('清理折线图实例时出错:', error)
    }
    lineChartInstance.value = null
  }

  // 清空折线图数据
  lineChartData.value = {}
  selectedArea.value = {}

  // 延迟关闭弹窗，确保图表完全清理
  setTimeout(() => {
    isLineChartShow.value = false
  }, 50)
}

// 获取统计数据
const getTableData = async () => {
  isLoading.value = true

  try {
    const params = {
      classId: classId.value
    }

    let res

    // 根据选择的日期类型调用不同接口
    if (selectedDateType.value === 'weeklyByArea') {
      // 调用区域每周统计接口
      res = await listAreaEntryCountByWeek(params)
    } else {
      // 调用原有的统计接口
      if (selectedDateType.value === 'custom') {
        if (customDateRange.value && customDateRange.value.length === 2) {
          params.startDate = dayjs(customDateRange.value[0]).format('YYYY-MM-DD')
          params.endDate = dayjs(customDateRange.value[1]).format('YYYY-MM-DD')
        }
      } else {
        params.dateType = selectedDateType.value
      }
      res = await countAreaEntry(params)
    }

    if (res.status === 0 && res.data) {
      // 如果是区域每周模式，重新排序数据
      if (selectedDateType.value === 'weeklyByArea') {
        // 分离总计数据和周数据
        const allData = res.data.find((item) => item.week === 'all')
        const weekData = res.data.filter((item) => item.week !== 'all')

        // 按周次排序，最新的在最后，然后添加总计数据
        weekData.sort((a, b) => {
          // 简单的日期字符串比较，假设格式为 "YYYY-MM-DD - YYYY-MM-DD"
          return a.week.localeCompare(b.week)
        })

        // 将总计数据添加到最后，并修改显示文本
        if (allData) {
          allData.week = '本学期总计'
          weekData.push(allData)
        }

        tableData.value = weekData
        // 提取区域列表（使用周数据格式）
        areaList.value = extractAreaListFromWeekData(res.data)
      } else {
        tableData.value = res.data
        // 提取区域列表（使用儿童数据格式）
        areaList.value = extractAreaList(res.data)
      }
    } else {
      tableData.value = []
      areaList.value = []
      uni.showToast({
        title: res.message || '获取数据失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('获取数据失败', error)
    tableData.value = []
    areaList.value = []
    uni.showToast({
      title: '获取数据失败',
      icon: 'none'
    })
  } finally {
    isLoading.value = false
  }
}

// 提取区域列表
const extractAreaList = (data) => {
  const areaMap = new Map()

  // 收集所有区域信息，使用Map避免重复
  data.forEach((child) => {
    if (child.childAreaEntryCountList) {
      child.childAreaEntryCountList.forEach((entry) => {
        if (!areaMap.has(entry.area)) {
          areaMap.set(entry.area, {
            areaId: entry.areaId,
            area: entry.area,
            areaAlias: entry.areaAlias
          })
        }
      })
    }
  })

  // 转换为数组并按常见区域顺序排序
  const commonAreas = ['建构区', '艺术区', '语言区', '生活区', '科学区', '运动区', '扮演区']
  const areas = Array.from(areaMap.values()).sort((a, b) => {
    const indexA = commonAreas.indexOf(a.area)
    const indexB = commonAreas.indexOf(b.area)
    if (indexA !== -1 && indexB !== -1) {
      return indexA - indexB
    } else if (indexA !== -1) {
      return -1
    } else if (indexB !== -1) {
      return 1
    } else {
      return a.area.localeCompare(b.area)
    }
  })

  return areas
}

// 从周数据中提取区域列表
const extractAreaListFromWeekData = (data) => {
  const areaMap = new Map()

  // 收集所有区域信息，使用Map避免重复
  data.forEach((weekData) => {
    if (weekData.arreaEntryCountList) {
      weekData.arreaEntryCountList.forEach((entry) => {
        if (!areaMap.has(entry.area)) {
          areaMap.set(entry.area, {
            areaId: entry.areaId,
            area: entry.area,
            areaAlias: entry.areaAlias
          })
        }
      })
    }
  })

  // 转换为数组并按常见区域顺序排序
  const commonAreas = ['建构区', '艺术区', '语言区', '生活区', '科学区', '运动区', '扮演区']
  const areas = Array.from(areaMap.values()).sort((a, b) => {
    const indexA = commonAreas.indexOf(a.area)
    const indexB = commonAreas.indexOf(b.area)
    if (indexA !== -1 && indexB !== -1) {
      return indexA - indexB
    } else if (indexA !== -1) {
      return -1
    } else if (indexB !== -1) {
      return 1
    } else {
      return a.area.localeCompare(b.area)
    }
  })

  return areas
}

onLoad(async (options) => {
  // 从URL参数获取className和classId
  if (options.className) {
    currentTitle.value = options.className
  }

  if (options.classId) {
    classId.value = options.classId
  } else {
    // 如果没有传classId，从用户信息中获取
    const userInfo = uni.getStorageSync('USER_INFO')
    classId.value = userInfo.currentClassId
  }

  // 获取初始数据
  await getTableData()
})

onShow(async () => {
  checks()
})
</script>

<script>
export default {
  options: { styleIsolation: 'shared', multipleSlots: true }
}
</script>

<style scoped lang="scss">
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f5f5 url('https://c.mypacelab.com/vxmp/img/history_background_3x.png') no-repeat
    center top;
  background-size: contain;
}

.container {
  flex: 1;
  min-height: 0;
  margin: 20rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

// 顶部信息栏
.header-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  // margin-bottom: 24rpx;
  padding: 20rpx 0;

  .class-info {
    .class-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
    }
  }

  .date-filter {
    display: flex;
    align-items: center;
    padding: 12rpx 20rpx;
    .date-text {
      font-size: 28rpx;
      color: rgba(128, 128, 128, 1);
      margin-right: 8rpx;
    }

    .change {
      width: 24rpx;
      height: 24rpx;
    }
  }
}

// 统计表格
.table-container {
  flex: 1;
  min-height: 0;
  border-radius: 28rpx;
  overflow: hidden;
  background: linear-gradient(0deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)),
    linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
  box-shadow: 4rpx 8rpx 16rpx rgba(0, 0, 0, 0.02);
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .table-scroll {
    flex: 1;
    min-height: 0;
    white-space: nowrap;

    .table-content {
      display: inline-block;
      min-width: 100%;
      box-sizing: border-box;
      position: relative;
    }

    .table-header {
      display: flex;
      background: #fff;
      border-bottom: 1rpx solid rgba(247, 247, 247, 1);
      position: sticky;
      top: 0;
      z-index: 10;

      .header-cell {
        padding: 24rpx 16rpx;
        font-size: 24rpx;
        font-weight: 400;
        color: rgba(128, 128, 128, 1);
        text-align: center;
        background: #fff;

        &:last-child {
          border-right: none;
        }

        &.name-cell {
          width: 180rpx;
          flex-shrink: 0;
          position: sticky;
          left: 0;
          z-index: 11;
          padding-left: 30rpx;
        }

        &.area-cell {
          min-width: 120rpx;
          flex-shrink: 0;
          width: 120rpx;

          &:last-child {
            padding-right: 30rpx;
          }
        }
      }
    }

    .table-body {
      .table-row {
        display: flex;
        border-bottom: 1rpx solid rgba(247, 247, 247, 1);

        &:last-child {
          border-bottom: none;
        }

        .body-cell {
          padding: 20rpx 16rpx;
          font-size: 24rpx;
          color: rgba(51, 51, 51, 1);
          text-align: center;

          &:last-child {
            border-right: none;
          }

          &.name-cell {
            width: 180rpx;
            flex-shrink: 0;
            background: #fff;
            color: rgba(63, 121, 255, 1);
            font-weight: 500;
            cursor: pointer;
            position: sticky;
            left: 0;
            z-index: 9;
            padding-left: 30rpx;

            .week-text {
              white-space: pre-line;
              line-height: 1.3;
              text-align: left;
            }
          }

          &.area-cell {
            min-width: 120rpx;
            flex-shrink: 0;
            width: 120rpx;

            &:last-child {
              padding-right: 30rpx;
            }
          }
        }
      }
    }
  }
}

// 加载状态
.loading-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400rpx;

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;

    .loading-spinner {
      width: 60rpx;
      height: 60rpx;
      border: 4rpx solid #f3f3f3;
      border-top: 4rpx solid #3f79ff;
      border-radius: 50%;
      animation: loading-spin 1s linear infinite;
      margin-bottom: 20rpx;
    }

    .loading-text {
      font-size: 28rpx;
      color: #666;
    }
  }
}

@keyframes loading-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 空状态
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  color: #999;
  font-size: 28rpx;
}

// 日期选择弹窗
.date-popup {
  background: #fff;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #eee;

    .popup-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }

    .confirm-btn {
      font-size: 28rpx;
      color: #3f79ff;
      font-weight: 500;

      &.disabled {
        color: #ccc;
        pointer-events: none;
      }
    }
  }

  .popup-content {
    padding: 30rpx;

    .popup-subtitle {
      font-size: 24rpx;
      color: #666;
      margin-bottom: 30rpx;
    }

    .date-options {
      .date-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20rpx;

        .date-option {
          flex: 1;
          margin: 0 10rpx;
          padding: 20rpx;
          background: #f5f5f5;
          border-radius: 12rpx;
          text-align: center;
          font-size: 26rpx;
          color: #333;
          transition: all 0.3s ease;

          &.active {
            background: #3f79ff;
            color: #fff;
          }

          &.disabled {
            opacity: 0.5;
            pointer-events: none;
          }

          &:first-child {
            margin-left: 0;
          }

          &:last-child {
            margin-right: 0;
          }

          &.weekly-option {
            font-size: 24rpx;
            font-weight: 500;
          }
        }
      }

      .date-separator {
        height: 1rpx;
        background: #e6e6e6;
        margin: 30rpx 0 20rpx 0;
      }

      .custom-date-row {
        display: flex;
        align-items: center;
        margin-top: 30rpx;
        padding-top: 20rpx;
        border-top: 1rpx solid #eee;

        &.disabled {
          opacity: 0.5;
          pointer-events: none;
        }

        .custom-date-label {
          font-size: 26rpx;
          color: #333;
          margin-right: 20rpx;
        }

        .custom-date-input {
          flex: 1;
          padding: 20rpx;
          background: #f5f5f5;
          border-radius: 12rpx;
          text-align: center;
          font-size: 26rpx;
          color: #333;
        }
      }
    }
  }
}

// 折线图弹窗
.line-chart-popup {
  max-height: 80vh;
  .line-chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;
    .line-chart-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }

    .close-btn {
      font-size: 48rpx;
      color: #999;
      font-weight: 300;
      line-height: 1;
      padding: 10rpx;

      &:active {
        color: #666;
      }
    }
  }
  .chart-controls {
    display: flex;
    align-items: center;
    padding: 20rpx 30rpx;
    .control-label {
      font-size: 26rpx;
      color: #666;
      margin-right: 20rpx;
      white-space: nowrap;
    }
  }
  .line-chart-content {
    .charts-box {
      width: 100%;
      height: 400rpx;

      .chart-container {
        width: 100%;
        height: 100%;
        position: relative;
      }

      .chart-empty {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 100%;
        color: #999;
        font-size: 28rpx;

        .debug-info {
          font-size: 24rpx;
          color: #ccc;
          margin-top: 20rpx;
        }
      }
    }
  }
}

// 可点击的区域表头样式
.clickable {
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f0f8ff;
  }

  &:active {
    background-color: #e6f3ff;
  }
}
::v-deep .uni-select {
  width: 300rpx !important;
}
</style>
