<template>
  <BaseLayout :scrollEnabled="true" nav-title="核心材料经验统计" :content-style="{ padding: '0' }">
    <view class="container">
      <!-- 班级类型筛选器 -->
      <view class="class-bar" @click="isGradeShow = true">
        <text class="class-title">{{ selectedGrade || '-' }}</text>
        <image class="change" src="/static/game/change.svg" />
      </view>
      <view class="col" v-if="tableData.data || tableData.data.length > 0">
        <view class="header">
          <view class="flex-jsb-ac">
            <view class="flex-ac">
              <text class="f-s-30 f-w-500">核心材料经验统计</text>
            </view>
            <view class="flex-ac">
              <image src="@/static/common/refresh.png" @click="getTableData(classId)" />
              <image v-if="sr == -1" src="@/static/icon/aOrder.png" @click="checkSort" />
              <image v-if="sr == 1" src="@/static/icon/dOrder.png" @click="checkSort" />
              <image src="@/static/icon/edit.png" @click="editFormItem" />
            </view>
          </view>
          <!-- <view class="f-s-24 f-w-400 header-tips"
            >统计数据有延迟，每天凌晨统计前一日数据<br />如需查看对应活动等信息，请点击设置按钮进行设置</view
          > -->
        </view>
        <!-- 自定义表格 -->
        <view class="table-container">
          <view class="table-content">
            <!-- 表头 -->
            <view class="table-header">
              <view
                class="header-cell"
                v-for="column in tableData.columns"
                :key="column.key"
                :class="getColumnClass(column.key)"
              >
                {{ column.title }}
              </view>
            </view>

            <!-- 表体 -->
            <view class="table-body" v-if="!isShowTable && processedData.length > 0">
              <view class="table-row" v-for="(row, index) in processedData" :key="index">
                <view
                  class="body-cell"
                  v-for="column in tableData.columns"
                  :key="column.key"
                  :class="getColumnClass(column.key)"
                >
                  <view v-if="column.key === 'materialNameMap'" class="material-display">
                    <template v-for="(materials, areaName) in row[column.key]" :key="areaName">
                      <view v-if="materials && materials.length > 0" class="area-section">
                        <view class="area-name">{{ areaName }}</view>
                        <view
                          class="material-item"
                          v-for="(material, materialIndex) in materials"
                          :key="materialIndex"
                        >
                          {{ materialIndex + 1 }}.{{ material }}
                        </view>
                      </view>
                    </template>
                    <view
                      v-if="!row[column.key] || Object.keys(row[column.key]).length === 0"
                      class="no-data"
                      >-</view
                    >
                  </view>
                  <template v-else>
                    {{
                      column.formatter ? column.formatter(row[column.key], row) : row[column.key]
                    }}
                  </template>
                </view>
              </view>
            </view>

            <!-- 加载状态 -->
            <view v-if="isShowTable" class="loading-state">
              <text>加载中...</text>
            </view>

            <!-- 空状态 -->
            <view v-if="!isShowTable && processedData.length === 0" class="empty-state">
              <text>暂无数据</text>
            </view>
          </view>
        </view>
      </view>
      <Popup :show="isPopup" @click="isPopup = false" @close="closePopup">
        <row-select
          :ParentList="tableData.columns"
          :List="columns"
          label="title"
          name="key"
          @bylChange="bylChange"
        ></row-select>
      </Popup>

      <up-picker
        ref="uGradePicker"
        :show="isGradeShow"
        :columns="[gradeOptions]"
        :defaultIndex="[gradeDefaultIndex]"
        keyName="label"
        @confirm="gradeConfirm"
        @cancel="isGradeShow = false"
      />
    </view>
  </BaseLayout>
</template>

<script setup>
import BaseLayout from '@/components/base-layout/base-layout.vue'
import { getTargetCountByClass } from './api'
import { ref, reactive, computed } from 'vue'
import RowSelect from './row-select.vue'
import tools from './tools.js'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { checks } from '@/utils/index.js'

let className = ref('') // 存储传入的班级名称
let selectedGrade = ref('小班')
let selectedGradeId = ref('2820') // 默认小班ID
const gradeIdMap = reactive({
  小班: '2820',
  中班: '2821',
  大班: '2822'
})

// 班级类型选项
const gradeOptions = reactive([
  { label: '小班', value: '小班' },
  { label: '中班', value: '中班' },
  { label: '大班', value: '大班' }
])

// 下拉选择器相关
let isGradeShow = ref(false)
let uGradePicker = ref(null)
let gradeDefaultIndex = ref(0) // 默认选中小班

// 表格相关数据
let classId = ref('')
let isShowTable = ref(false)
let isPopup = ref(false)
let sr = ref(-1) // 排序  1降序 -1升序

const { tableData, columns } = tools()

// 处理接口返回的数据，提取选中班级的指标和材料数
const processedData = computed(() => {
  if (!tableData.data || tableData.data.length === 0) return []

  return tableData.data.map((item) => {
    // 查找选中班级的数据
    const gradeData = item.gradeCount.find(
      (grade) => grade.gradeId.toString() === selectedGradeId.value
    )

    return {
      ...item,
      // 如果找到对应班级数据则使用，否则置为0
      indexCount: gradeData ? gradeData.indexCount : 0,
      materialCount: gradeData ? gradeData.materialCount : 0,
      // 使用materialNameMap而不是materialNameList
      materialNameMap: gradeData && gradeData.materialCount > 0 ? gradeData.materialNameMap : {}
    }
  })
})

// 更新列标题
const updateColumnTitles = () => {
  const indexColumn = tableData.columns.find((col) => col.key === 'indexCount')
  const materialColumn = tableData.columns.find((col) => col.key === 'materialCount')
  const materialNameColumn = tableData.columns.find((col) => col.key === 'materialNameMap')

  if (indexColumn) {
    indexColumn.title = `${selectedGrade.value}指标`
  }

  if (materialColumn) {
    materialColumn.title = `${selectedGrade.value}材料`
  }

  if (materialNameColumn) {
    // materialNameColumn.title = `${selectedGrade.value}材料名称`
    materialNameColumn.title = `材料名称`
  }
}

// 获取列样式类
const getColumnClass = (columnKey) => {
  const classes = []

  // if (columnKey === 'matrix1Name' || columnKey === 'matrix2Name' || columnKey === 'matrix3Name') {
  //   classes.push('fixed-column')
  // }

  if (columnKey === 'materialNameMap') {
    classes.push('material-column')
  }

  if (columnKey === 'materialCount') {
    classes.push('material-count-column')
  }

  return classes.join(' ')
}

// 初始化表格列，默认隐藏小班指标列
const initColumns = () => {
  // 过滤掉indexCount列
  const filteredColumns = columns.filter((col) => col.key !== 'indexCount')
  tableData.columns = filteredColumns
  updateColumnTitles()
}

const bylChange = (list) => {
  console.log('%c list: ', 'color:#86a9d4;', list)
  const commonElements = columns.filter((element) => list.includes(element.key))
  tableData.columns = commonElements
  updateColumnTitles() // 确保列标题更新
  isPopup.value = false
}

function closePopup() {
  isPopup.value = false
}

// 从className匹配班级类型
const matchGradeFromClassName = (classNameStr) => {
  if (!classNameStr) return '小班'

  if (classNameStr.includes('小') || classNameStr.includes('小班')) {
    return '小班'
  } else if (classNameStr.includes('中') || classNameStr.includes('中班')) {
    return '中班'
  } else if (classNameStr.includes('大') || classNameStr.includes('大班')) {
    return '大班'
  }

  return '小班' // 默认返回小班
}

// 班级类型选择确认
const gradeConfirm = (e) => {
  const { value } = e
  selectedGrade.value = value[0].value
  selectedGradeId.value = gradeIdMap[value[0].value]
  updateColumnTitles()
  isGradeShow.value = false
  // 不需要重新获取数据，因为数据已经包含了所有班级类型的信息
}

// 数组排序 - 修改为按材料数量排序
const checkSort = () => {
  if (sr.value == 1) {
    sr.value = -1
    // 按材料数量升序排序
    tableData.data = [...tableData.data].sort((a, b) => {
      const aGrade = a.gradeCount.find((g) => g.gradeId.toString() === selectedGradeId.value) || {
        materialCount: 0
      }
      const bGrade = b.gradeCount.find((g) => g.gradeId.toString() === selectedGradeId.value) || {
        materialCount: 0
      }
      return aGrade.materialCount - bGrade.materialCount
    })
    return
  }
  if (sr.value == -1) {
    sr.value = 1
    // 按材料数量降序排序
    tableData.data = [...tableData.data].sort((a, b) => {
      const aGrade = a.gradeCount.find((g) => g.gradeId.toString() === selectedGradeId.value) || {
        materialCount: 0
      }
      const bGrade = b.gradeCount.find((g) => g.gradeId.toString() === selectedGradeId.value) || {
        materialCount: 0
      }
      return bGrade.materialCount - aGrade.materialCount
    })
    return
  }
}

const editFormItem = () => {
  isPopup.value = true
}

const getTableData = async (userClassId) => {
  isShowTable.value = true
  tableData.data = []
  classId.value = userClassId

  try {
    const params = {
      classId: userClassId
    }

    let res = await getTargetCountByClass(params)
    console.log('%c res: ', 'color:#86a9d4;', res)

    if (res.status == 0 && res.data) {
      tableData.data = res.data
      updateColumnTitles()
    }
  } catch (error) {
    console.error('获取数据失败', error)
  } finally {
    isShowTable.value = false
  }
}

onLoad(async (options) => {
  // 从URL参数获取className
  if (options.className) {
    // 保存原始班级名称用于显示
    className.value = options.className
    // 根据班级名称匹配小班/中班/大班
    selectedGrade.value = matchGradeFromClassName(options.className)
    selectedGradeId.value = gradeIdMap[selectedGrade.value]

    // 设置下拉选择器的默认选中索引
    const gradeIndex = gradeOptions.findIndex((item) => item.value === selectedGrade.value)
    gradeDefaultIndex.value = gradeIndex >= 0 ? gradeIndex : 0
  }

  // 初始化表格列，默认隐藏小班指标
  initColumns()

  const userInfo = uni.getStorageSync('USER_INFO')
  getTableData(userInfo.currentClassId)
})

onShow(async () => {
  checks()
})
</script>

<script>
export default {
  options: { styleIsolation: 'shared', multipleSlots: true }
}
</script>

<style scoped lang="scss">
.container {
  height: 100%;
  margin: 20rpx;
  background: #fff;
  border-radius: 28rpx;
  box-shadow: 4rpx 8rpx 16rpx rgba(0, 0, 0, 0.02);
  box-sizing: border-box;
  padding: 20rpx 30rpx;
  margin-bottom: 30rpx;
  height: auto;

  .class-bar {
    display: flex;
    align-items: center;
    font-size: 36rpx;
    font-weight: bold;
    margin: 32rpx 0 24rpx 0;
    color: #333;

    .change {
      width: 32rpx;
      height: 32rpx;
      margin-left: 8rpx;
    }
  }

  .class-title {
    font-size: 36rpx;
    font-weight: bold;
  }
}
.col {
  border-radius: 28rpx;
  background: rgba(255, 255, 255, 1);
  box-shadow: 4rpx 8rpx 16rpx rgba(0, 0, 0, 0.02);
  box-sizing: border-box;
  padding: 0rpx;
  margin-bottom: 24rpx;
  height: auto;
}

.header {
  .header-tips {
    color: #808080;
    line-height: 33rpx;
    margin-top: 12rpx;
  }

  image {
    width: 36rpx;
    height: 36rpx;
    margin-left: 28rpx;
  }

  image:first-child {
    margin-left: 0;
  }
}

// 自定义表格样式
.table-container {
  border-radius: 28rpx;
  overflow: hidden;
  // background: linear-gradient(0deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)),
  //   linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
  // box-shadow: 4rpx 8rpx 16rpx rgba(0, 0, 0, 0.02);
  // box-sizing: border-box;

  .table-content {
    width: 100%;
    // padding: 20rpx 30rpx;
    box-sizing: border-box;
  }

  .table-header {
    display: flex;
    border-bottom: 1rpx solid rgba(247, 247, 247, 1);

    .header-cell {
      flex: 1;
      padding:  15rpx;
      font-size: 24rpx;
      font-weight: 500;
      color: rgba(128, 128, 128, 1);
      text-align: center;

      &:last-child {
        border-right: none;
      }

      &.material-column {
        flex: 2; // 材料列占用更多空间
      }

      &.material-count-column {
        flex: 1.3; // 材料数量列
      }
    }
  }

  .table-body {
    .table-row {
      display: flex;
      border-bottom: 1rpx solid rgba(247, 247, 247, 1);

      &:last-child {
        border-bottom: none;
      }

      .body-cell {
        flex: 1;
        padding: 15rpx;
        font-size: 24rpx;
        color: rgba(51, 51, 51, 1);
        text-align: center;
        word-wrap: break-word;
        word-break: break-all;
        &:last-child {
          border-right: none;
        }

        &.fixed-column {
          color: rgba(63, 121, 255, 1);
          font-weight: 500;
        }

        &.material-column {
          flex: 2; // 材料列占用更多空间
          text-align: left;
          padding: 16rpx;
        }

        &.material-count-column {
          flex: 1.3; // 材料数量列
        }

        .material-display {
          .area-section {
            margin-bottom: 16rpx;

            &:last-child {
              margin-bottom: 0;
            }

            .area-name {
              font-weight: bold;
              color: #333;
              margin-bottom: 8rpx;
              line-height: 1.4;
            }

            .material-item {
              font-size: 20rpx;
              color: #666;
              line-height: 1.4;
              margin-bottom: 4rpx;
              margin-left: 16rpx;

              &:last-child {
                margin-bottom: 0;
              }
            }
          }

          .no-data {
            color: #999;
            text-align: center;
          }
        }
      }
    }
  }

  .loading-state,
  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400rpx;
    color: #999;
    font-size: 28rpx;
  }
}

// 原有表格样式保留以防需要回滚
::v-deep .uni-table-td:first-child {
  padding-left: 0;
}

::v-deep .uni-table-td:last-child {
  padding-right: 0;
}

::v-deep .uni-table-td {
  font-size: 22rpx;
  padding: 16rpx 18rpx;
  margin: 0 18rpx !important;
}

::v-deep .uni-table-th {
  padding: 24rpx 18rpx;
}

::v-deep .uni-table-th:first-child {
  padding-left: 0;
}

::v-deep .uni-table-th:last-child {
  padding-right: 0;
}
</style>
