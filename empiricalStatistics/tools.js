import { reactive } from 'vue'
export default () => {
  let tableData = reactive({
    data: [],
    columns: [
      {
        key: 'matrix1Name',
        title: '领域',
        width: 40,
        align: 'left'
      },
      {
        key: 'matrix2Name',
        title: '维度',
        width: 40,
        align: 'left'
      },
      {
        key: 'matrix3Name',
        title: '子维度',
        width: 50,
        align: 'left'
      },
      {
        key: 'indexCount',
        title: '小班指标',
        width: 50,
        isSort: true,
        align: 'left',
        formatter: (row) => {
          return row || 0
        }
      },
      {
        key: 'materialCount',
        title: '小班材料',
        width: 80,
        align: 'left',
        formatter: (row) => {
          return row || 0
        }
      },
      {
        key: 'materialNameMap',
        title: '材料名称',
        width: 130,
        align: 'left',
        formatter: (row, rowData) => {
          // row是单元格的值materialNameMap，rowData是整行数据
          if (!row || typeof row !== 'object') return '-'
          
          // 构建显示文本：区域名称作为标题，下面是编号的材料列表
          let result = []
          for (const [areaName, materials] of Object.entries(row)) {
            if (materials && materials.length > 0) {
              result.push(areaName)
              materials.forEach((material, index) => {
                result.push(`${index + 1}.${material}`)
              })
            }
          }
          
          return result.length > 0 ? result.join('\n') : '-'
        }
      }
    ]
  })

  const columns = [
    {
      key: 'matrix1Name',
      title: '领域',
      width: 40
    },
    {
      key: 'matrix2Name',
      title: '维度',
      width: 40
    },
    {
      key: 'matrix3Name',
      title: '子维度',
      width: 50
    },
    {
      key: 'indexCount',
      title: '小班指标',
      width: 50,
      formatter: (row) => {
        return row || 0
      }
    },
    {
      key: 'materialCount',
      title: '小班材料',
      width: 70,
      formatter: (row) => {
        return row || 0
      }
    },
    {
      key: 'materialNameMap',
      title: '材料名称',
      width: 130,
      formatter: (row, rowData) => {
        // row是单元格的值materialNameMap，rowData是整行数据
        if (!row || typeof row !== 'object') return '-'
        
        // 构建显示文本：区域名称作为标题，下面是编号的材料列表
        let result = []
        for (const [areaName, materials] of Object.entries(row)) {
          if (materials && materials.length > 0) {
            result.push(areaName)
            materials.forEach((material, index) => {
              result.push(`${index + 1}.${material}`)
            })
          }
        }
        
        return result.length > 0 ? result.join('\n') : '-'
      }
    }
  ]

  return {
    tableData,
    columns
  }
}
