<template>
  <BaseLayout nav-title="幼儿进区" :content-style="{ padding: '0' }">
    <view class="container">
      <view class="title">
        <text>{{ className }}</text>
        <view class="date-container" @click="showDatePicker = true">
          <text class="date">{{ formatDate }}</text>
        </view>
      </view>

      <!-- 日期选择器组件 -->
      <up-datetime-picker
        :show="showDatePicker"
        v-model="selectedTimestamp"
        mode="date"
        :minDate="minDate"
        :maxDate="maxDate"
        @confirm="dateConfirm"
        @cancel="showDatePicker = false"
      ></up-datetime-picker>

      <!-- Tab 切换 -->
      <view class="tabs">
        <view
          class="tab-item"
          :class="{ active: activeTab === 'notEntered' }"
          @click="switchTab('notEntered')"
        >
          未进区
        </view>
        <view
          class="tab-item"
          :class="{ active: activeTab === 'entered' }"
          @click="switchTab('entered')"
        >
          已进区
        </view>
      </view>

      <!-- 区域选择区域 -->
      <view class="area-section">
        <view class="section-title">选择区域</view>
        <view class="area-list">
          <view
            v-for="(area, index) in areaList"
            :key="index"
            class="area-item"
            :class="{ active: currentArea && currentArea.areaId === area.areaId }"
            @click="selectArea(area)"
          >
            <view class="area-name">
              <view class="area-name-text">{{ area.area }}</view>
              <view class="area-alias" v-if="area.areaAlias">({{ area.areaAlias }})</view>
            </view>
            <view class="area-count"
              >已有 {{ area.childCount || 0 }}/{{ area.limitNum || area.maxCount || '-' }} 人</view
            >
            <!-- <view
              v-if="activeTab === 'entered' && area.childCount > 0"
              class="area-exit-btn"
              @click.stop="handleExitArea(area.areaId)"
            >
              出区
            </view> -->
          </view>
        </view>
      </view>

      <!-- 全班出区按钮 -->
      <!-- <view v-if="activeTab === 'entered'" class="exit-all-section">
        <button class="exit-all-btn" @click="handleExitClass">全班出区</button>
      </view> -->

      <!-- 儿童列表 -->
      <view class="child-section" v-if="currentArea">
        <view class="section-header">
          <view class="section-title">
            {{ activeTab === 'notEntered' ? '点击姓名后加号进区' : '已进区儿童' }}
          </view>
          <button
            block
            v-if="activeTab === 'entered'"
            class="delete-records-btn"
            @click="showDeleteRecords"
          >
            删除进区记录
          </button>
        </view>
        <view
          class="child-list"
          :class="{
            'single-column':
              (activeTab === 'notEntered' && girlsList.length === 0 && boysList.length === 0) ||
              (activeTab === 'entered' && childList.length === 0)
          }"
        >
          <!-- 未进区模式：按性别分列显示 -->
          <template v-if="activeTab === 'notEntered'">
            <view class="child-column girls-column">
              <view v-for="(child, index) in girlsList" :key="'girl-' + index" class="child-item">
                <view class="child-avatar">
                  <image class="avatar-img" :src="getChildAvatar(child)" mode="aspectFill"></image>
                </view>
                <view class="child-info">
                  <view class="child-name">{{ child.title }}</view>
                  <view class="child-status">{{ child.status === 1 ? '已进区' : '未进区' }}</view>
                </view>
                <view class="action-btn" v-if="child.status !== 1" @click.stop="handleEntry(child)">
                  <image class="icon-plus" src="/childrenInArea/imgs/add.svg" />
                </view>
              </view>
            </view>
            <view class="child-column boys-column">
              <view v-for="(child, index) in boysList" :key="'boy-' + index" class="child-item">
                <view class="child-avatar">
                  <image class="avatar-img" :src="getChildAvatar(child)" mode="aspectFill"></image>
                </view>
                <view class="child-info">
                  <view class="child-name">{{ child.title }}</view>
                  <view class="child-status">{{ child.status === 1 ? '已进区' : '未进区' }}</view>
                </view>
                <view class="action-btn" v-if="child.status !== 1" @click.stop="handleEntry(child)">
                  <image class="icon-plus" src="/childrenInArea/imgs/add.svg" />
                </view>
              </view>
            </view>
          </template>

          <!-- 已进区模式：按API顺序均分两列 -->
          <template v-else>
            <view class="child-column left-column">
              <view
                v-for="(child, index) in leftColumnChildren"
                :key="'child-' + index"
                class="child-item"
              >
                <view class="child-avatar">
                  <image class="avatar-img" :src="getChildAvatar(child)" mode="aspectFill"></image>
                </view>
                <view class="child-info">
                  <view class="child-name">{{ child.title }}</view>
                  <view class="child-status">{{ child.area }}</view>
                </view>
                <view class="action-btn exit" v-if="isToday" @click.stop="handleExitChild(child)">
                  <image class="icon-plus" src="/childrenInArea/imgs/minus.svg" />
                </view>
              </view>
            </view>
            <view class="child-column right-column">
              <view
                v-for="(child, index) in rightColumnChildren"
                :key="'child-' + index"
                class="child-item"
              >
                <view class="child-avatar">
                  <image class="avatar-img" :src="getChildAvatar(child)" mode="aspectFill"></image>
                </view>
                <view class="child-info">
                  <view class="child-name">{{ child.title }}</view>
                  <view class="child-status">{{ child.area }}</view>
                </view>
                <view class="action-btn exit" v-if="isToday" @click.stop="handleExitChild(child)">
                  <image class="icon-plus" src="/childrenInArea/imgs/minus.svg" />
                </view>
              </view>
            </view>
          </template>

          <view class="empty-tip" v-if="childList.length === 0 && !isLoading">
            {{ activeTab === 'notEntered' ? '暂无未进区儿童' : '暂无已进区儿童' }}
          </view>
        </view>
      </view>

      <!-- 提示信息 -->
      <view class="tip-message" v-if="tipMessage">{{ tipMessage }}</view>
    </view>

    <!-- 删除记录弹窗 -->
    <up-popup :show="showDeleteModal" mode="bottom" :round="20" @close="closeDeleteModal">
      <view class="delete-modal-content">
        <view class="delete-modal-header">
          <text class="delete-modal-title">删除进区记录</text>
          <text class="close-btn" @click="closeDeleteModal">×</text>
        </view>
        <view class="delete-modal-body">
          <text class="delete-modal-subtitle">{{ formatDate }} 进区儿童</text>
          <scroll-view class="children-list" scroll-y="true">
            <view
              v-for="child in allEnteredChildren"
              :key="`${child.childId}-${child.areaId}`"
              class="child-record-item"
            >
              <view class="child-record-info">
                <view class="child-record-avatar">
                  <image class="avatar-img" :src="getChildAvatar(child)" mode="aspectFill"></image>
                </view>
                <view class="child-record-details">
                  <text class="child-record-name">{{ child.title }}</text>
                  <text class="child-record-area">{{ child.area }}</text>
                </view>
              </view>
              <button class="delete-record-btn" @click="deleteChildRecord(child)">删除</button>
            </view>
            <view v-if="allEnteredChildren.length === 0" class="empty-records"> 暂无进区记录 </view>
          </scroll-view>
        </view>
      </view>
    </up-popup>
  </BaseLayout>
</template>

<script setup>
import BaseLayout from '@/components/base-layout/base-layout.vue'
import { onLoad } from '@dcloudio/uni-app'
import {
  listArea,
  entry,
  listChild,
  exitAllAreas,
  historyEntry,
  listChildHistory,
  listAreaHistory,
  deleteChildEntry
} from '@/api/game'
import { ref, computed, watch } from 'vue'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn' // 引入中文语言包

// 设置为中文
dayjs.locale('zh-cn')

// 页面状态
const className = ref('-')
const classId = ref('')
const currentArea = ref(null)
const areaList = ref([])
const childList = ref([])
const activeTab = ref('notEntered')
const tipMessage = ref('')
const userId = ref('')
const showDatePicker = ref(false)
const selectedTimestamp = ref(Date.now()) // 使用当前时间戳
const isLoading = ref(false) // 添加加载状态
const showDeleteModal = ref(false) // 删除记录弹窗
const allEnteredChildren = ref([]) // 所有进区儿童列表
// 设置最小日期为一年前，允许查看历史数据
const minDate = ref(dayjs().subtract(1, 'year').startOf('day').valueOf()) // 使用valueOf获取时间戳
// 设置最大日期为今天
const maxDate = ref(dayjs().endOf('day').valueOf()) // 今天结束

// 添加选择的日期字符串，用于API调用
const selectedDate = computed(() => {
  return dayjs(selectedTimestamp.value).format('YYYY-MM-DD')
})

// 转换为API需要的日期格式 YYYY-MM-DD
const formattedEntryDate = computed(() => {
  // 确保selectedTimestamp是有效的时间戳
  if (!selectedTimestamp.value || isNaN(selectedTimestamp.value)) {
    // 如果无效，使用当前日期
    return dayjs().format('YYYY-MM-DD')
  }

  try {
    const date = dayjs(selectedTimestamp.value)
    // 检查dayjs对象是否有效
    if (date.isValid()) {
      return date.format('YYYY-MM-DD')
    } else {
      console.error('Invalid date from timestamp:', selectedTimestamp.value)
      return dayjs().format('YYYY-MM-DD')
    }
  } catch (e) {
    console.error('Error formatting date:', e)
    return dayjs().format('YYYY-MM-DD')
  }
})

// 判断是否为当日
const isToday = computed(() => {
  return selectedDate.value === dayjs().format('YYYY-MM-DD')
})

// 添加日期计算
const formatDate = computed(() => {
  const date = dayjs(selectedTimestamp.value)
  const weekDay = date.format('dddd') // 星期几
  const dateStr = date.format('YYYY年MM月DD日') // 年月日
  return `${dateStr} ${weekDay}`
})

// 获取班级区域列表
const fetchAreaList = async () => {
  try {
    const apiFunction = isToday.value ? listArea : listAreaHistory
    const params = {
      classId: classId.value
    }

    if (!isToday.value) {
      params.entryDate = formattedEntryDate.value
    }

    const res = await apiFunction(params)
    if (res && res.data) {
      // Filter out the area named "未进区"
      areaList.value = res.data.filter((area) => area.area !== '未进区')

      // Select first area by default if available
      if (areaList.value.length > 0 && !currentArea.value) {
        currentArea.value = areaList.value[0]
      }
    }
  } catch (error) {
    console.error('获取区域列表失败', error)
  }
}

// 获取儿童列表
const fetchChildList = async () => {
  try {
    let areaId = null

    if (activeTab.value === 'notEntered') {
      areaId = -1
    } else if (activeTab.value === 'entered') {
      if (currentArea.value) {
        areaId = currentArea.value.areaId
      } else {
        areaId = 0
      }
    }

    const apiFunction = isToday.value ? listChild : listChildHistory

    const params = {
      classId: classId.value,
      areaId: areaId
    }

    if (!isToday.value) {
      params.entryDate = formattedEntryDate.value
    }

    // 保留旧数据直到新数据加载成功
    // childList.value = [];

    const res = await apiFunction(params)
    if (res && res.data) {
      childList.value = res.data
    } else {
      childList.value = []
    }
  } catch (error) {
    console.error('获取儿童列表失败', error)
    childList.value = []
  }
}

// 日期确认事件
const dateConfirm = (event) => {
  console.log('Date picker confirmed with event:', event)

  // 确保timestamp是有效的 - 处理对象格式 {value: timestamp, mode: "date"}
  let timestamp
  if (typeof event === 'object' && event !== null && 'value' in event) {
    timestamp = event.value
  } else {
    timestamp = event
  }

  if (timestamp && !isNaN(timestamp)) {
    selectedTimestamp.value = timestamp
    console.log('Selected timestamp set to:', selectedTimestamp.value)
    console.log('Formatted date:', formattedEntryDate.value)
  } else {
    console.error('Invalid timestamp received:', event)
    // 设置为当前日期
    selectedTimestamp.value = Date.now()
  }

  showDatePicker.value = false

  // 添加短暂延迟确保UI更新后再获取数据
  setTimeout(() => {
    fetchAreaList()
    fetchChildList()
  }, 100)
}

// 切换标签页
const switchTab = (tab) => {
  if (activeTab.value === tab) return // 避免重复点击

  isLoading.value = true
  activeTab.value = tab

  // 使用 Promise 确保数据加载完成后再更新UI
  fetchChildList().finally(() => {
    setTimeout(() => {
      isLoading.value = false
    }, 100) // 短暂延迟确保UI更新
  })
}

// 选择区域
const selectArea = (area) => {
  currentArea.value = area
  fetchChildList()
}

// 进区操作
const handleEntry = async (child) => {
  if (!currentArea.value) {
    tipMessage.value = '请先选择区域'
    setTimeout(() => {
      tipMessage.value = ''
    }, 2000)
    return
  }

  try {
    isLoading.value = true
    const apiFunction = isToday.value ? entry : historyEntry
    const params = {
      childId: child.childId,
      areaId: currentArea.value.areaId,
      entryDate: formattedEntryDate.value
    }

    const res = await apiFunction(params)
    if (res.status === 0) {
      tipMessage.value = '进区成功'
      // 刷新数据
      await fetchAreaList()
      await fetchChildList()
    } else {
      // tipMessage.value = res.message || '进区失败'
      uni.showToast({
        title: res.error.stack || '进区失败',
        icon: 'none'
      })
    }

    setTimeout(() => {
      tipMessage.value = ''
      isLoading.value = false
    }, 2000)
  } catch (error) {
    console.error('进区操作失败', error)
    tipMessage.value = '进区操作失败'
    setTimeout(() => {
      tipMessage.value = ''
      isLoading.value = false
    }, 2000)
  }
}

// 出区操作 - 单个儿童
const handleExitChild = async (child) => {
  try {
    isLoading.value = true
    const apiFunction = isToday.value ? exitAllAreas : exitAllAreas // Using same API for now, can be replaced with historical API if added

    const params = {
      childId: child.childId
    }

    if (!isToday.value) {
      params.entryDate = formattedEntryDate.value
    }

    const res = await apiFunction(params)
    if (res && res.data) {
      tipMessage.value = '出区成功'
      // 刷新数据
      await fetchAreaList()
      await fetchChildList()
    } else {
      tipMessage.value = res.message || '出区失败'
    }

    setTimeout(() => {
      tipMessage.value = ''
      isLoading.value = false
    }, 2000)
  } catch (error) {
    console.error('出区操作失败', error)
    tipMessage.value = '出区操作失败'
    setTimeout(() => {
      tipMessage.value = ''
      isLoading.value = false
    }, 2000)
  }
}

// 显示删除记录弹窗
const showDeleteRecords = async () => {
  try {
    isLoading.value = true

    // 获取当前日期所有进区的儿童
    const apiFunction = isToday.value ? listChild : listChildHistory
    const params = {
      classId: classId.value,
      areaId: 0 // 获取所有区域的儿童
    }

    if (!isToday.value) {
      params.entryDate = formattedEntryDate.value
    }

    const res = await apiFunction(params)
    if (res && res.data) {
      allEnteredChildren.value = res.data
      showDeleteModal.value = true
    } else {
      tipMessage.value = '获取进区儿童列表失败'
      setTimeout(() => {
        tipMessage.value = ''
      }, 2000)
    }
  } catch (error) {
    console.error('获取进区儿童列表失败', error)
    tipMessage.value = '获取进区儿童列表失败'
    setTimeout(() => {
      tipMessage.value = ''
    }, 2000)
  } finally {
    isLoading.value = false
  }
}

// 删除指定儿童的进区记录
const deleteChildRecord = async (child) => {
  try {
    isLoading.value = true

    const params = {
      childId: child.childId,
      areaId: child.areaId
    }

    // 如果不是今天，传递指定日期
    if (!isToday.value) {
      params.entryDate = formattedEntryDate.value
    }

    const res = await deleteChildEntry(params)
    if (res.status === 0) {
      tipMessage.value = '删除记录成功'
      // 从列表中移除该儿童
      allEnteredChildren.value = allEnteredChildren.value.filter(
        (c) => c.childId !== child.childId || c.areaId !== child.areaId
      )
      // 刷新数据
      await fetchAreaList()
      await fetchChildList()
    } else {
      tipMessage.value = res.message || '删除记录失败'
    }

    setTimeout(() => {
      tipMessage.value = ''
      isLoading.value = false
    }, 2000)
  } catch (error) {
    console.error('删除记录失败', error)
    tipMessage.value = '删除记录失败'
    setTimeout(() => {
      tipMessage.value = ''
      isLoading.value = false
    }, 2000)
  }
}

// 关闭删除记录弹窗
const closeDeleteModal = () => {
  showDeleteModal.value = false
  allEnteredChildren.value = []
}

// 出区操作 - 整个区域
const handleExitArea = async (areaId) => {
  try {
    isLoading.value = true
    const apiFunction = isToday.value ? exitAllAreas : exitAllAreas // Using same API for now, can be replaced with historical API if added

    const params = {
      areaId: areaId
    }

    if (!isToday.value) {
      params.entryDate = formattedEntryDate.value
    }

    const res = await apiFunction(params)
    if (res && res.data) {
      tipMessage.value = '区域出区成功'
      // 刷新数据
      await fetchAreaList()
      await fetchChildList()
    } else {
      tipMessage.value = res.message || '区域出区失败'
    }

    setTimeout(() => {
      tipMessage.value = ''
      isLoading.value = false
    }, 2000)
  } catch (error) {
    console.error('区域出区操作失败', error)
    tipMessage.value = '区域出区操作失败'
    setTimeout(() => {
      tipMessage.value = ''
      isLoading.value = false
    }, 2000)
  }
}

// 出区操作 - 整个班级
const handleExitClass = async () => {
  try {
    isLoading.value = true
    const apiFunction = isToday.value ? exitAllAreas : exitAllAreas // Using same API for now, can be replaced with historical API if added

    const params = {
      classId: classId.value
    }

    if (!isToday.value) {
      params.entryDate = formattedEntryDate.value
    }

    const res = await apiFunction(params)
    if (res && res.data) {
      tipMessage.value = '全班出区成功'
      // 刷新数据
      await fetchAreaList()
      await fetchChildList()
    } else {
      tipMessage.value = res.message || '全班出区失败'
    }

    setTimeout(() => {
      tipMessage.value = ''
      isLoading.value = false
    }, 2000)
  } catch (error) {
    console.error('全班出区操作失败', error)
    tipMessage.value = '全班出区操作失败'
    setTimeout(() => {
      tipMessage.value = ''
      isLoading.value = false
    }, 2000)
  }
}

// 页面加载
onLoad((options) => {
  className.value = decodeURIComponent(options.className || '')
  const userInfo = uni.getStorageSync('USER_INFO')
  classId.value = userInfo.currentClassId
  userId.value = userInfo.id
  if (classId.value) {
    fetchAreaList().then(() => {
      fetchChildList()
    })
  } else {
    tipMessage.value = '缺少班级参数'
    setTimeout(() => {
      tipMessage.value = ''
    }, 2000)
  }
})

// 监听当前区域变化和标签页变化
watch([currentArea, activeTab], () => {
  fetchChildList()
})

// 处理头像地址，添加阿里云OSS图片处理参数
const getChildAvatar = (child) => {
  let avatarUrl = child.childAvatar
  if (!avatarUrl) {
    avatarUrl =
      child.sex === 2
        ? 'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_girl.png'
        : 'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_boy.png'
  }

  // 直接在图片地址后面拼接OSS处理参数
  return avatarUrl + '?x-oss-process=image/resize,m_fill,w_600'
}

// 创建过滤后的列表
const girlsList = computed(() => {
  return childList.value.filter((child) => child.sex === 2)
})

const boysList = computed(() => {
  return childList.value.filter((child) => child.sex === 1)
})

const leftColumnChildren = computed(() => {
  // 将childList分成左右两列，左边列包含前半部分
  return childList.value.filter((_, index) => index % 2 === 0)
})

const rightColumnChildren = computed(() => {
  // 右边列包含后半部分
  return childList.value.filter((_, index) => index % 2 === 1)
})
</script>

<style lang="scss" scoped>
.title {
  font-size: 34rpx;
  color: #333;
  font-weight: 600;
  padding: 32rpx 32rpx 0 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .date-container {
    display: flex;
    align-items: center;
    background-color: #f5f5f5;
    padding: 8rpx 16rpx;
    border-radius: 8rpx;
  }

  .date {
    font-size: 28rpx;
    color: rgba(128, 128, 128, 1);
    font-weight: normal;
  }

  .date-icon {
    margin-left: 10rpx;
    font-size: 28rpx;
  }
}

.tabs {
  display: flex;
  padding: 20rpx 32rpx;
  .tab-item {
    width: 124rpx;
    height: 68rpx;
    line-height: 68rpx;
    border-radius: 20rpx;
    background: rgba(255, 255, 255, 1);
    margin-right: 20rpx;
    font-size: 28rpx;
    font-weight: 600;
    color: rgba(51, 51, 51, 1);
    text-align: center;

    &.active {
      color: rgba(63, 121, 255, 1);
      background: linear-gradient(0deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)),
        linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
      border: 1rpx solid rgba(63, 121, 255, 1);
      box-shadow: 4rpx 8rpx 12rpx rgba(0, 0, 0, 0.02);
    }
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 32rpx;

  .section-title {
    font-size: 28rpx;
    color: #666;
  }

  .delete-records-btn {
    padding: 0rpx 15rpx;
    background: #ff4d4f;
    color: #fff;
    border: none;
    font-size: 22rpx;
    font-weight: 500;
    &:active {
      opacity: 0.8;
    }
  }
}

.section-title {
  font-size: 28rpx;
  color: #666;
  padding: 20rpx 32rpx;
}

.area-section {
  padding-bottom: 20rpx;

  .area-list {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20rpx;
    padding: 0 32rpx;
    box-sizing: border-box;
    .area-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 160rpx;
      height: 124rpx;
      border-radius: 24rpx;
      background: linear-gradient(0deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)),
        linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
      box-shadow: 4rpx 8rpx 12rpx rgba(0, 0, 0, 0.02);
      position: relative;

      .area-name {
        color: rgba(51, 51, 51, 1);
        margin-bottom: 10rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .area-name-text {
          font-size: 30rpx;
          font-weight: 600;
        }
        .area-alias {
          font-size: 24rpx;
          color: rgba(128, 128, 128, 1);
        }
      }

      .area-count {
        font-size: 24rpx;
        color: rgba(128, 128, 128, 1);
      }

      .area-exit-btn {
        position: absolute;
        top: 10rpx;
        right: 10rpx;
        padding: 6rpx 16rpx;
        background-color: #ff4d4f;
        color: #fff;
        border-radius: 6rpx;
        font-size: 24rpx;
      }
      &.active {
        background: linear-gradient(0deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)),
          linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
        border: 1rpx solid rgba(63, 121, 255, 1);
        .area-name-text {
          color: rgba(63, 121, 255, 1);
        }
        .area-alias {
          color: rgba(63, 121, 255, 1);
        }
        .area-count {
          color: rgba(63, 121, 255, 1);
        }
      }
    }
  }
}

.child-section {
  .child-list {
    display: flex;
    padding: 0 32rpx;
    box-sizing: border-box;
    gap: 30rpx;

    &.single-column {
      .child-column {
        width: 100%;
      }
    }

    .child-column {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 30rpx;
    }

    .child-item {
      height: 128rpx;
      border-radius: 24rpx;
      background: linear-gradient(0deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)),
        linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
      box-shadow: 2rpx 4rpx 6rpx rgba(0, 0, 0, 0.02);
      display: flex;
      align-items: center;
      padding: 0 20rpx;

      .child-avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 20rpx;

        .avatar-img {
          width: 100%;
          height: 100%;
        }
      }

      .child-info {
        flex: 1;

        .child-name {
          font-weight: 600;
          font-size: 30rpx;
          color: rgba(51, 51, 51, 1);
          margin-bottom: 6rpx;
        }
        .child-status {
          font-size: 24rpx;
          color: rgba(128, 128, 128, 1);
        }
      }

      .action-btn {
        .icon-plus {
          width: 44rpx;
          height: 44rpx;
        }
        .icon-minus {
          width: 44rpx;
          height: 44rpx;
        }
      }
    }

    .empty-tip {
      text-align: center;
      padding: 40rpx 0;
      color: #999;
      font-size: 28rpx;
      width: 100%;
    }
  }
}

.tip-message {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 20rpx 30rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  z-index: 999;
}

.exit-all-section {
  padding: 20rpx 32rpx;

  .exit-all-btn {
    width: 100%;
    height: 80rpx;
    line-height: 80rpx;
    background-color: #ff4d4f;
    color: #fff;
    border-radius: 10rpx;
    font-size: 28rpx;
    text-align: center;
  }
}

// 删除记录弹窗样式
.delete-modal-content {
  background: #fff;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .delete-modal-header {
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .delete-modal-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }

    .close-btn {
      font-size: 48rpx;
      color: #999;
      font-weight: 300;
      line-height: 1;
      padding: 10rpx;

      &:active {
        color: #666;
      }
    }
  }

  .delete-modal-body {
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;
    width: 90%;

    .delete-modal-subtitle {
      font-size: 28rpx;
      color: #666;
      padding: 20rpx 30rpx 10rpx;
    }

    .children-list {
      flex: 1;
      min-height: 0;
      padding: 0 30rpx 30rpx;

      .child-record-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20rpx 0;
        border-bottom: 1rpx solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .child-record-info {
          display: flex;
          align-items: center;
          flex: 1;

          .child-record-avatar {
            width: 60rpx;
            height: 60rpx;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 20rpx;

            .avatar-img {
              width: 100%;
              height: 100%;
            }
          }

          .child-record-details {
            flex: 1;

            .child-record-name {
              font-size: 28rpx;
              font-weight: 600;
              color: #333;
              display: block;
              margin-bottom: 4rpx;
            }

            .child-record-area {
              font-size: 24rpx;
              color: #666;
              display: block;
            }
          }
        }

        .delete-record-btn {
          padding: 0rpx 15rpx;
          background: #ff4d4f;
          color: #fff;
          border: none;
          font-size: 20rpx;
          font-weight: 500;
          min-width: 60rpx;
          white-space: nowrap;

          &:active {
            opacity: 0.8;
          }
        }
      }

      .empty-records {
        text-align: center;
        padding: 60rpx 0;
        color: #999;
        font-size: 28rpx;
      }
    }
  }
}
</style>
