<template>
  <view class="custom-layout">
    <!-- 顶部导航栏 -->
    <up-navbar
      :title="currentTitle"
      :safeAreaInsetTop="true"
      :fixed="true"
      :placeholder="true"
      bgColor="transparent"
      :autoBack="true"
    ></up-navbar>

    <view class="main-container">
      <!-- 搜索栏 -->
      <view class="search-box">
        <input
          type="text"
          placeholder="搜索材料"
          v-model="searchText"
          class="search-input"
          @input="handleSearch"
        />
        <image class="search-icon" src="/static/icon/search.png"></image>
      </view>

      <view class="content-container">
        <!-- 左侧区域菜单 -->
        <view class="menu-container">
          <scroll-view scroll-y class="menu-scroll">
            <view class="menu-list">
              <view
                v-for="item in areaMenuList"
                :key="item.id"
                class="menu-item"
                :class="{ 'menu-item-active': selectedAreaId === item.id }"
                @click="selectArea(item.id)"
              >
                <!-- <text>{{ item.area }}</text> -->
                <text v-if="item.areaAlias">{{ item.areaAlias }}</text>
                <text v-else>{{ item.area }}</text>
              </view>
            </view>
          </scroll-view>
        </view>

        <!-- 右侧材料列表 -->
        <view class="material-container">
          <scroll-view
            scroll-y
            class="material-scroll"
            refresher-enabled
            :refresher-triggered="refresherTriggered"
            @refresherrefresh="onRefresh"
          >
            <view class="region-list">
              <view
                v-for="(item, index) in materialList"
                :key="item.id"
                class="region-item"
                @click="jumpToDetails(item)"
              >
                <view class="region-item-content">
                  <image
                    class="region-img"
                    :src="
                      item.displayedImage
                        ? item.displayedImage.split(',')[0]
                        : '/static/game/placeholder.png'
                    "
                    mode="aspectFill"
                    :lazy-load="true"	
                  />
                  <view class="region-info">
                    <view class="region-title-box">
                      <view class="region-title" :class="item.name.length > 10 ? 'ellipsis' : ''">{{
                        item.name
                      }}</view>
                    </view>
                    <view class="region-area-box">
                      <view>
                        {{ item.areaAlias ? item.areaAlias : item.area }}
                      </view>
                      <view
                        class="region-area-box-play-status"
                        v-if="item.childInterest"
                        :class="{
                          'very-low': item.childInterest == getInterestCodeByName('非常不感兴趣'),
                          low: item.childInterest == getInterestCodeByName('不感兴趣'),
                          medium: item.childInterest == getInterestCodeByName('一般感兴趣'),
                          high: item.childInterest == getInterestCodeByName('比较感兴趣'),
                          'very-high': item.childInterest == getInterestCodeByName('非常感兴趣')
                        }"
                      >
                        {{ getChildInterestName(item.childInterest) }}
                      </view>
                    </view>
                    <view class="region-play-count" v-if="item.sourceType === 'toy'">
                      <view> {{ item.playCount || 0 }}种玩法 </view>
                      <view class="play-status" v-if="item.unAdoptPlayCount > 0">
                        仍有玩法待确认
                      </view>
                    </view>
                  </view>
                  <view
                    class="region-item-more"
                    @click.stop.prevent="openAction(item, index)"
                    @touchend.stop.prevent
                  >
                    <up-icon name="more-dot-fill" size="36rpx" />
                  </view>
                </view>
                <view class="region-item-tag" v-if="item.playStatus < 0">
                  <up-icon class="region-item-tag-icon" name="clock" color="#f29100"></up-icon>
                  <text style="color: #f29100">未生成</text>
                </view>
                <view class="region-item-tag" v-if="item.playStatus == 0">
                  <up-icon class="region-item-tag-icon" name="clock" color="#18b566"></up-icon>
                  <text style="color: #18b566">生成中</text>
                </view>
                <view class="region-item-tag" v-if="item.playStatus == 99">
                  <up-icon class="region-item-tag-icon" name="clock" color="#f56c6c"></up-icon>
                  <text style="color: #f56c6c">生成异常</text>
                </view>
              </view>
              <view v-if="materialList.length === 0" class="empty-state">
                <!-- <image src="/static/game/empty.png" class="empty-image" /> -->
                <view class="empty-text">暂无材料</view>
              </view>
            </view>
          </scroll-view>
        </view>
      </view>

      <view class="add-btn-wrap">
        <view class="export-btn" @click="exportList">
          <image src="/classMaterials/static/export.svg" />
          <text>导出清单</text>
        </view>
        <button class="add-btn" @click="gotoAdd">添加材料</button>
      </view>

      <Popup :show="showAction" @close="showAction = false">
        <view class="iconAction">
          <view
            v-if="currentItem && currentItem.materialDetailTemplate && currentItem.materialDetailTemplate.length > 0"
            @click="handleExportTemplate"
          >
            <image
              src="/static/common/leaving.png"
              style="width: 40rpx; height: 40rpx; margin-right: 28rpx"
            />
            <view>导出材料明细表</view>
          </view>
          <view @click="deleteRegion">
            <image
              src="/static/common/delete.png"
              style="width: 40rpx; height: 40rpx; margin-right: 28rpx"
            />
            <view>删除</view>
          </view>
        </view>
      </Popup>

      <up-modal
        :show="showDeleteModal"
        content="确定要删除该材料吗？"
        showCancelButton
        asyncClose
        @cancel="showDeleteModal = false"
        @confirm="confirmDelete"
      />

      <up-modal
        :show="showRegenerateModal"
        content="材料生成异常，是否重新生成材料玩法？"
        showCancelButton
        asyncClose
        @cancel="showRegenerateModal = false"
        @confirm="confirmRegenerate"
      />

      <Popup :show="showExportModal" @close="showExportModal = false">
        <view class="iconAction">
          <view @click="handleExportMaterialList">
            <image
              src="/static/common/leaving.png"
              style="width: 40rpx; height: 40rpx; margin-right: 28rpx"
            />
            <view>导出材料清单</view>
          </view>
          <!-- <view @click="showExportModal = false">
            <image
              src="/static/common/leaving.png"
              style="width: 40rpx; height: 40rpx; margin-right: 28rpx"
            />
            <view>取消</view>
          </view> -->
        </view>
      </Popup>

      <!-- 模板选择弹窗 -->
      <Popup :show="showTemplateModal" @close="showTemplateModal = false">
        <view class="template-modal">
          <view class="template-modal-title">选择导出的材料明细表</view>
          <view class="template-list">
            <view
              v-for="template in currentTemplateList"
              :key="template.id"
              class="template-item"
              @click="downloadTemplate(template)"
            >
              <image
                src="/static/common/leaving.png"
                style="width: 40rpx; height: 40rpx; margin-right: 28rpx"
              />
              <view class="template-name">{{ template.name }}</view>
            </view>
          </view>
        </view>
      </Popup>
    </view>
  </view>
</template>

<script setup>
import { ref, onUnmounted } from 'vue'
import Popup from '@/components/Popup/Popup.vue'
import { debounce } from 'lodash-es'
import {
  getClassMaterialArea,
  getClassCombinedMaterial,
  deleteMaterialById,
  queryClassCombinedMaterial,
  getDictByCode,
  exportMaterialInfo,
  getOutSideMaterialArea,
  getOutSideCombinedMaterial,
  queryOutSideCombinedMaterial,
  exportMaterialDetail,
  generatePlayList
} from '@/api/game'
import { getclassList } from '@/api/index'
// import { exportFile } from '@/utils/index'
import { onShow } from '@dcloudio/uni-app'

const classId = ref(null)
// 区域菜单数据
const areaMenuList = ref([])

// 当前选中的区域ID
const selectedAreaId = ref(null)

// 搜索文本
const searchText = ref('')

// 模拟材料数据
const materialList = ref([])

const showAction = ref(false)
const showDeleteModal = ref(false)
const currentIndex = ref(-1)
const currentItem = ref(null)

const childInterestDict = ref([])

const showExportModal = ref(false)
const showTemplateModal = ref(false)
const currentTemplateList = ref([])
const schoolId = ref(null)
const isOutside = ref(0)
const currentTitle = ref('班级材料')
const className = ref('')

// 重新生成玩法相关状态
const showRegenerateModal = ref(false)
const currentRegenerateItem = ref(null)

// 下拉刷新相关状态
const refresherTriggered = ref(false)

const jumpToDetails = (item) => {
  if (item.playStatus == 99) {
    // 生成异常，检查重试次数
    if (item.retryTime > 2) {
      // 重试次数超过2次，提醒用户重新录入
      uni.$u.toast('生成失败请重新录入')
      return
    } else {
      // 重试次数未超过2次，弹窗询问是否重新生成
      currentRegenerateItem.value = item
      showRegenerateModal.value = true
      return
    }
  }
  if (item.playStatus <= 0) {
    uni.$u.toast('材料生成中,请稍后再试')
    return
  }
  // 保存当前选中的区域ID，以便从details页面返回时恢复
  uni.setStorageSync('SELECTED_AREA_ID', selectedAreaId.value)
  uni.navigateTo({
    url: `/classMaterials/components/details?id=${item.id}&sourceType=${item.sourceType}`
  })
}

const selectArea = async (areaId) => {
  selectedAreaId.value = areaId
  let areaItem = areaMenuList.value.find((item) => item.id === areaId)
  let area = areaItem.area
  // 特殊情况：当区域名称为"全部"时，手动传-1的areaId
  let finalAreaId = area === '全部' ? -1 : areaId
  await fetchMaterialList(finalAreaId)
}

const fetchMaterialList = async (areaId = null) => {
  try {
    let params = {
      classId: classId.value
    }

    if (areaId) {
      params.areaId = areaId
    }

    if (searchText.value) {
      params.materialName = searchText.value
      let res = null
      if (isOutside.value == 1) {
        params.schoolId = schoolId.value
        delete params.classId
        res = await queryOutSideCombinedMaterial(params)
      } else {
        params.schoolId = schoolId.value
        res = await queryClassCombinedMaterial(params)
      }
      materialList.value = res.data[0].teacherCombinedMaterialVOList
    } else {
      // 如果搜索文本为空，使用getClassCombinedMaterial获取列表
      let res = null
      if (isOutside.value == 1) {
        params.schoolId = schoolId.value
        delete params.classId
        res = await getOutSideCombinedMaterial(params)
      } else {
        params.schoolId = schoolId.value
        res = await getClassCombinedMaterial(params)
      }
      materialList.value = res.data
    }
  } catch (error) {
    // uni.$u.toast('获取数据失败')
    console.log(error)
  }
}

// 原始的搜索处理函数
const doSearch = async () => {
  const selectedArea = areaMenuList.value.find((item) => item.id === selectedAreaId.value)
  if (selectedArea) {
    // 特殊情况：当区域名称为"全部"时，手动传-1的areaId
    const finalAreaId = selectedArea.area === '全部' ? -1 : selectedArea.id
    await fetchMaterialList(finalAreaId)
  }
}

// 使用debounce包装的搜索函数，300ms防抖
const handleSearch = debounce(doSearch, 300)

// 下拉刷新处理函数
const onRefresh = async () => {
  refresherTriggered.value = true

  try {
    // 刷新当前选中区域的材料列表
    const selectedArea = areaMenuList.value.find((item) => item.id === selectedAreaId.value)
    if (selectedArea) {
      // 特殊情况：当区域名称为"全部"时，手动传-1的areaId
      const finalAreaId = selectedArea.area === '全部' ? -1 : selectedArea.id
      await fetchMaterialList(finalAreaId)
    }
  } catch (error) {
    console.error('刷新失败:', error)
  } finally {
    // 结束刷新状态
    setTimeout(() => {
      refresherTriggered.value = false
    }, 500)
  }
}

const gotoAdd = () => {
  // 保存当前选中的区域ID，以便从添加页面返回时恢复
  uni.setStorageSync('SELECTED_AREA_ID', selectedAreaId.value)

  // 获取当前选中的区域信息
  const selectedArea = areaMenuList.value.find((item) => item.id === selectedAreaId.value)
  let url = '/classMaterials/components/add'

  // 如果选中的不是"全部"区域，则将区域信息传递给添加页面
  if (selectedArea && selectedArea.area !== '全部') {
    const areaParam = encodeURIComponent(selectedArea.area)
    const areaIdParam = selectedArea.id
    const areaAliasParam = selectedArea.areaAlias ? encodeURIComponent(selectedArea.areaAlias) : ''
    url += `?area=${areaParam}&areaId=${areaIdParam}&areaAlias=${areaAliasParam}`
  }

  uni.navigateTo({ url })
}

const openAction = (item, index) => {
  currentIndex.value = index
  currentItem.value = item
  showAction.value = true
}

const deleteRegion = () => {
  showAction.value = false
  showDeleteModal.value = true
}

const confirmDelete = async () => {
  // 获取材料的sourceType，如果没有则默认为'toy'
  const sourceType = currentItem.value.sourceType || 'toy'

  const res = await deleteMaterialById(currentItem.value.id, classId.value, sourceType)
  if (res.status == 0) {
    uni.$u.toast('删除成功')
    // 删除成功后，刷新页面
    const selectedArea = areaMenuList.value.find((item) => item.id === selectedAreaId.value)
    if (selectedArea) {
      // 特殊情况：当区域名称为"全部"时，手动传-1的areaId
      const finalAreaId = selectedArea.area === '全部' ? -1 : selectedArea.id
      await fetchMaterialList(finalAreaId)
    }
  }

  showDeleteModal.value = false
}

// 根据code获取幼儿兴趣名称
const getChildInterestName = (code) => {
  if (!code || !childInterestDict.value || childInterestDict.value.length === 0) return ''
  const interest = childInterestDict.value.find((item) => item.dictItemCode == code)
  return interest ? interest.dictItemName : ''
}

// 根据名称获取幼儿兴趣code (用于class绑定，如果需要的话)
const getInterestCodeByName = (name) => {
  if (!name || !childInterestDict.value || childInterestDict.value.length === 0) return ''
  const interest = childInterestDict.value.find((item) => item.dictItemName === name)
  return interest ? interest.dictItemCode : ''
}

const exportList = () => {
  showExportModal.value = true
}

const handleExportMaterialList = async () => {
  if (!materialList.value || materialList.value.length === 0) {
    uni.$u.toast('暂无可导出的材料')
    showExportModal.value = false
    return
  }

  // 构建新的数据格式
  const list = materialList.value.map((item) => ({
    combinedId: item.id,
    // 如果材料有sourceType字段则使用，否则默认为'toy'
    sourceType: item.sourceType || 'toy'
  }))

  uni.showLoading({ title: '正在导出...' })
  try {
    // 调用接口获取下载URL
    const res = await exportMaterialInfo({
      list,
      fields: []
    })

    console.log('导出接口返回：', res)

    if (!res || !res.data) {
      throw new Error('导出接口异常')
    }

    const downloadUrl = res.data

    // 微信小程序端下载文件
    wx.downloadFile({
      url: downloadUrl,
      success: (downloadRes) => {
        if (downloadRes.statusCode === 200) {
          uni.openDocument({
            filePath: downloadRes.tempFilePath,
            showMenu: true,
            fileType: 'xlsx',
            success: () => {
              uni.$u.toast('导出成功')
            },
            fail: (err) => {
              uni.$u.toast('打开文件失败')
              console.error('打开文件失败', err)
            }
          })
        } else {
          uni.$u.toast('下载失败')
        }
      },
      fail: (err) => {
        uni.$u.toast('下载失败')
        console.error('下载失败', err)
      }
    })
  } catch (error) {
    uni.$u.toast(error.message || '导出失败，请重试')
    console.error('导出异常', error)
  } finally {
    uni.hideLoading()
    showExportModal.value = false
  }
}

// 处理导出模板
const handleExportTemplate = () => {
  showAction.value = false

  if (!currentItem.value || !currentItem.value.materialDetailTemplate || currentItem.value.materialDetailTemplate.length === 0) {
    uni.$u.toast('暂无可导出的材料明细表')
    return
  }

  const templates = currentItem.value.materialDetailTemplate

  if (templates.length === 1) {
    // 只有一个模板，直接下载
    downloadTemplate(templates[0])
  } else {
    // 多个模板，显示选择弹窗
    currentTemplateList.value = templates
    showTemplateModal.value = true
  }
}

// 下载模板文件
const downloadTemplate = async (template) => {
  showTemplateModal.value = false

  if (!template.url) {
    uni.$u.toast('文件不存在')
    return
  }

  uni.showLoading({ title: '正在生成材料明细表...' })

  try {
    // 先调用 exportMaterialDetail 接口生成模板
    const exportRes = await exportMaterialDetail({
      id: currentItem.value.id,
      url: template.url,
      className: className.value
    })

    console.log('导出模板接口返回：', exportRes)

    if (!exportRes || !exportRes.data) {
      throw new Error('生成模板失败')
    }

    const downloadUrl = exportRes.data

    uni.showLoading({ title: '正在下载...' })

    // 微信小程序端下载文件
    // #ifdef MP-WEIXIN
    wx.downloadFile({
      url: downloadUrl,
      success: (downloadRes) => {
        if (downloadRes.statusCode === 200) {
          uni.openDocument({
            filePath: downloadRes.tempFilePath,
            showMenu: true,
            fileType: getFileType(template.fileName),
            success: () => {
              uni.$u.toast('下载成功')
            },
            fail: (err) => {
              uni.$u.toast('打开文件失败')
              console.error('打开文件失败', err)
            }
          })
        } else {
          uni.$u.toast('下载失败')
        }
      },
      fail: (err) => {
        uni.$u.toast('下载失败')
        console.error('下载失败', err)
      }
    })
    // #endif

    // 其他平台使用 uni.downloadFile
    // #ifndef MP-WEIXIN
    uni.downloadFile({
      url: downloadUrl,
      success: (downloadRes) => {
        if (downloadRes.statusCode === 200) {
          uni.openDocument({
            filePath: downloadRes.tempFilePath,
            showMenu: true,
            fileType: getFileType(template.fileName),
            success: () => {
              uni.$u.toast('下载成功')
            },
            fail: (err) => {
              uni.$u.toast('打开文件失败')
              console.error('打开文件失败', err)
            }
          })
        } else {
          uni.$u.toast('下载失败')
        }
      },
      fail: (err) => {
        uni.$u.toast('下载失败')
        console.error('下载失败', err)
      }
    })
    // #endif
  } catch (error) {
    uni.$u.toast(error.message || '下载失败，请重试')
    console.error('下载异常', error)
  } finally {
    uni.hideLoading()
  }
}

// 根据文件名获取文件类型
const getFileType = (fileName) => {
  if (!fileName) return 'unknown'
  const ext = fileName.split('.').pop().toLowerCase()
  const typeMap = {
    'doc': 'doc',
    'docx': 'docx',
    'pdf': 'pdf',
    'xls': 'xls',
    'xlsx': 'xlsx',
    'ppt': 'ppt',
    'pptx': 'pptx'
  }
  return typeMap[ext] || 'unknown'
}

// 重新生成材料玩法
const confirmRegenerate = async () => {
  if (!currentRegenerateItem.value) {
    return
  }

  showRegenerateModal.value = false

  try {
    uni.showLoading({
      title: '重新生成中...'
    })

    // 调用 generatePlayList 接口
    const res = await generatePlayList({
      combinedId: currentRegenerateItem.value.id
    })

    uni.hideLoading()

    if (res.status === 0) {
      uni.$u.toast('重新生成成功')

      // 刷新列表
      const selectedArea = areaMenuList.value.find((item) => item.id === selectedAreaId.value)
      if (selectedArea) {
        // 特殊情况：当区域名称为"全部"时，手动传-1的areaId
        const finalAreaId = selectedArea.area === '全部' ? -1 : selectedArea.id
        await fetchMaterialList(finalAreaId)
      }
    } else {
      uni.$u.toast('重新生成失败，请重试')
    }
  } catch (error) {
    uni.hideLoading()
    uni.$u.toast('重新生成失败，请重试')
    console.error('重新生成材料玩法失败', error)
  } finally {
    currentRegenerateItem.value = null
  }
}

onShow(async () => {
  const userInfo = uni.getStorageSync('USER_INFO')
  classId.value = userInfo.currentClassId || userInfo.classIds[0]
  schoolId.value = userInfo.currentSchoolId
  isOutside.value = userInfo.isOutside
  currentTitle.value = userInfo.isOutside == 1 ? '户外材料' : '班级材料'

  // 获取班级名称
  try {
    const classListRes = await getclassList({ schoolId: schoolId.value })
    if (classListRes.status === 0 && classListRes.data) {
      const classInfo = classListRes.data.find(item => item.id === classId.value)
      if (classInfo) {
        className.value = classInfo.title
      }
    }
  } catch (error) {
    console.error('获取班级信息失败:', error)
  }

  uni.showLoading({
    title: '加载中...'
  })
  try {
    // 获取幼儿兴趣字典
    const interestRes = await getDictByCode({ code: 'childInterest' })
    if (interestRes && interestRes.data) {
      childInterestDict.value = interestRes.data
    }
    let res = null
    if (userInfo.isOutside == 1) {
      // 户外材料区域列表
      res = await getOutSideMaterialArea({
        schoolId: userInfo.currentSchoolId
      })
    } else {
      // 班级材料区域列表
      res = await getClassMaterialArea({
        schoolId: userInfo.currentSchoolId,
        classId: classId.value
      })
    }
    areaMenuList.value = res.data

    // 尝试恢复之前保存的选中区域ID
    const savedAreaId = uni.getStorageSync('SELECTED_AREA_ID')
    let targetAreaId = res.data[0].id // 默认选择第一个区域

    if (savedAreaId) {
      // 检查保存的区域ID是否在当前区域列表中存在
      const savedArea = res.data.find((item) => item.id === savedAreaId)
      if (savedArea) {
        targetAreaId = savedAreaId
      }
      // 清除保存的区域ID，避免影响下次进入页面
      uni.removeStorageSync('SELECTED_AREA_ID')
    }

    selectedAreaId.value = targetAreaId
    // 特殊情况：当区域名称为"全部"时，手动传-1的areaId
    const selectedArea = res.data.find((item) => item.id === targetAreaId)
    const finalAreaId = selectedArea && selectedArea.area === '全部' ? -1 : targetAreaId
    await fetchMaterialList(finalAreaId)
  } catch (error) {
    uni.$u.toast('获取数据失败')
    console.log(error)
  }
  uni.hideLoading()
})

onUnmounted(() => {
  const userInfo = uni.getStorageSync('USER_INFO')
  userInfo['isOutside'] = 0
  uni.setStorageSync('USER_INFO', userInfo)
})
</script>

<style lang="scss" scoped>
.custom-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f5f5 url('https://c.mypacelab.com/vxmp/img/history_background_3x.png') no-repeat
    center top;
  background-size: contain;
}

.main-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 88rpx); /* 减去导航栏高度 */
  position: relative;
  overflow: hidden;
}

.search-box {
  width: 680rpx;
  height: 64rpx;
  border-radius: 70rpx;
  background: rgba(255, 255, 255, 1);
  border: 1rpx solid rgba(238, 238, 238, 1);
  margin: 20rpx auto;
  padding: 0rpx 30rpx;
  position: relative;
  z-index: 10;
  .search-icon {
    width: 32rpx;
    height: 32rpx;
    position: absolute;
    right: 30rpx;
    top: 50%;
    transform: translateY(-50%);
  }
  ::v-deep .search-input {
    height: 64rpx;
    line-height: 64rpx;
    font-size: 28rpx;
  }
}

.content-container {
  display: flex;
  position: relative;
  height: calc(100vh - 180rpx); /* 减去导航栏和搜索栏高度 */
  padding-bottom: 140rpx; /* 为底部按钮腾出空间 */
  overflow: hidden;
}

.menu-container {
  width: 200rpx;
  position: absolute;
  left: 0;
  top: 0;
  bottom: 140rpx; /* 为底部按钮腾出空间 */
  background: rgba(255, 255, 255, 1);
  z-index: 5;

  .menu-scroll {
    height: 100%;
  }

  .menu-list {
    padding: 24rpx 0;

    .menu-item {
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      font-size: 26rpx;
      color: rgba(128, 128, 128, 1);
      position: relative;
      // 超出省略
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      &.menu-item-active {
        font-size: 26rpx;
        font-weight: 600;
        color: rgba(51, 51, 51, 1);
        background: rgba(245, 245, 245, 1);
        padding-left: 20rpx;
        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 6rpx;
          height: 36rpx;
          background: #3f79ff;
          border-radius: 4rpx;
        }
      }
    }
  }
}

.material-container {
  flex: 1;
  margin-left: 200rpx; /* 左侧菜单宽度 */
  height: 100%;
  position: relative;

  .material-scroll {
    height: 100%;
  }

  .title {
    font-size: 30rpx;
    color: #333;
    font-weight: 600;
    padding: 0 24rpx 24rpx 24rpx;
  }
}

.region-list {
  box-sizing: border-box;
  padding: 0 24rpx;
  padding-bottom: 140rpx;
}
.region-item {
  box-sizing: border-box;
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 20rpx 25rpx;
  position: relative;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  .region-item-tag {
    margin-top: 15rpx;
    font-size: 24rpx;
    display: flex;
    align-items: center;
    .region-item-tag-icon {
      margin-right: 10rpx;
    }
  }
  .region-item-content {
    display: flex;
    align-items: center;
  }
  .region-img {
    width: 110rpx;
    height: 110rpx;
    border-radius: 16rpx;
    margin-right: 15rpx;
  }
  .region-item-more {
    position: absolute;
    right: 20rpx;
    top: 30rpx;
    z-index: 3;
  }
  .region-info {
    .region-title-box {
      display: flex;
      align-items: center;
    }
    .region-title {
      font-size: 30rpx;
      font-weight: 600;
      color: rgba(51, 51, 51, 1);
      margin-right: 10rpx;
    }
    .region-area-box {
      display: flex;
      align-items: center;
      font-size: 24rpx;
      color: rgba(128, 128, 128, 1);
      .region-area-box-play-status {
        margin-left: 10rpx;
        border-radius: 8rpx;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 4rpx 8rpx 4rpx 8rpx;

        &.very-low {
          background: rgba(252, 237, 238, 1);
          color: rgba(237, 111, 114, 1);
        }

        &.low {
          background: rgba(253, 244, 237, 1);
          color: rgba(240, 145, 77, 1);
        }

        &.medium {
          background: rgba(239, 243, 253, 1);
          color: rgba(96, 139, 240, 1);
        }

        &.high {
          background: rgba(240, 241, 252, 1);
          color: rgba(110, 116, 230, 1);
        }

        &.very-high {
          background: rgba(234, 246, 237, 1);
          color: rgba(84, 186, 106, 1);
        }
      }
    }
    .region-play-count {
      display: flex;
      font-size: 24rpx;
      color: rgba(128, 128, 128, 1);
      margin-top: 10rpx;
      align-items: center;
      .play-status {
        margin-left: 10rpx;
        border-radius: 8rpx;
        background: rgba(252, 237, 238, 1);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 4rpx 8rpx 4rpx 8rpx;
        color: rgba(237, 111, 114, 1);
      }
    }
    .ellipsis {
      width: 280rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;

  .empty-image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 24rpx;
  }

  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}

.add-btn-wrap {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100vw;
  background: #fff;
  box-shadow: 0 -2rpx 16rpx rgba(0, 0, 0, 0.04);
  padding: 30rpx 20rpx 50rpx 20rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  .export-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-right: 15rpx;
    image {
      width: 50rpx;
      height: 50rpx;
    }
    text {
      font-size: 20rpx;
      color: rgba(128, 128, 128, 1);
    }
  }
  .add-btn {
    width: 75%;
    height: 80rpx;
    line-height: 80rpx;
    background: #3f79ff;
    color: #fff;
    border-radius: 44rpx;
    font-size: 30rpx;
    font-weight: 600;
    border: none;
    margin: 0 !important;
  }
}

.iconAction > view {
  height: 88rpx;
  display: flex;
  align-items: center;
  font-size: 30rpx;
  font-weight: 400;
  letter-spacing: 0rpx;
  line-height: 48rpx;
  color: #333;
  text-align: left;
  vertical-align: middle;
}

.deleteIcon {
  color: #f56c6c;
}

.template-modal {
  padding: 40rpx;

  .template-modal-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    text-align: center;
    margin-bottom: 40rpx;
  }

  .template-list {
    .template-item {
      height: 88rpx;
      display: flex;
      align-items: center;
      font-size: 30rpx;
      font-weight: 400;
      color: #333;
      border-bottom: 1rpx solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .template-name {
        flex: 1;
      }
    }
  }
}
</style>
