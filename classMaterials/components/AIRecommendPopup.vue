<template>
  <up-popup
    :show="show"
    @close="handleClose"
    mode="bottom"
    :round="10"
    :closeable="true"
    closeIconPos="top-right"
  >
    <view class="ai-popup-content">
      <view class="ai-popup-title">AI 推荐玩法</view>
      <view class="ai-popup-form">
        <view class="ai-form-item">
          <view class="ai-form-label">请输入玩法流程</view>
          <view class="ai-textarea-container">
            <textarea
              class="ai-form-textarea"
              v-model="aiRecommendData.userPlayStep"
              placeholder="请输入以推荐更精准的玩法"
            />
            <!-- 语音转文字按钮 -->
            <view class="voice-btn" @click="handleVoiceClick">
              <!-- 录音状态显示 -->
              <template v-if="audioRecordStatus === 'recording'">
                <view class="recording-timer">{{ formatTime(recordingTime) }}</view>
                <text class="voice-text">点击暂停</text>
              </template>
              <!-- 空闲状态显示 -->
              <template v-else>
                <image src="/static/icon/evaluate_speech.png" class="voice-icon" />
                <text class="voice-text">语音转文字</text>
              </template>
            </view>
          </view>
        </view>

        <view class="ai-upload-section">
          <Upload
            type="image"
            :value="aiRecommendData.attachmentResourceIds"
            @callback="aiUploadCallback"
            @emitDelFile="aiDelFile"
            :showDel="true"
          >
            <view class="ai-upload-btn">
              <image
                src="https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/game_add.svg"
                mode="aspectFill"
                class="add-icon"
              ></image>
              <text>补充图片</text>
            </view>
          </Upload>
        </view>

        <button class="ai-generate-btn" @click="generateAIRecommend">生成</button>
      </view>
    </view>
  </up-popup>
</template>

<script setup>
import { reactive, ref, onMounted, onBeforeUnmount, watch } from 'vue'
import { generatePlayList } from '@/api/game'
import Upload from '@/components/Upload/Upload.vue'
import { useRecordingWithASR } from './hook/useRecordingWithASR.js'

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  materialId: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['close', 'generate-success'])

// AI推荐相关数据
const aiRecommendData = reactive({
  userPlayStep: '',
  attachmentResourceIds: [],
  imgUrl: ''
})

// 创建一个包装对象来适配useRecordingWithASR hook
const formDataWrapper = ref({
  audioText: ''
})

// 使用录音和语音转文字hook
const {
  // 状态
  audioRecordStatus,
  recordingTime,
  // 方法
  initRecorderManager,
  startRecording,
  stopRecording,
  cancelRecording,
  clearASRState
} = useRecordingWithASR(formDataWrapper)

// 关闭弹窗
const handleClose = () => {
  // 如果正在录音，先停止录音
  if (audioRecordStatus.value === 'recording') {
    stopRecording()
  }
  emit('close')
}

// 格式化时间显示
const formatTime = (seconds) => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 语音按钮点击事件
const handleVoiceClick = () => {
  if (audioRecordStatus.value === 'idle') {
    // 开始录音
    startRecording()
  } else if (audioRecordStatus.value === 'recording') {
    // 停止录音
    stopRecording()
  }
}

// 监听语音转文字结果，同步到输入框
watch(() => formDataWrapper.value.audioText, (newText) => {
  if (newText) {
    aiRecommendData.userPlayStep = newText
  }
}, { immediate: true })

// 初始化录音管理器
onMounted(() => {
  initRecorderManager()
})

// 组件卸载前清理资源
onBeforeUnmount(() => {
  if (audioRecordStatus.value === 'recording') {
    stopRecording()
  }
  clearASRState()
})

// AI推荐图片上传回调
const aiUploadCallback = (list) => {
  aiRecommendData.attachmentResourceIds.push(...list)
  // 将所有图片的URL用逗号拼接
  if (aiRecommendData.attachmentResourceIds.length > 0) {
    const urls = aiRecommendData.attachmentResourceIds
      .map((item) => item.serviceUri || item.url || item.path)
      .filter(Boolean)
    aiRecommendData.imgUrl = urls.join(',')
  } else {
    aiRecommendData.imgUrl = ''
  }
}

// AI推荐图片删除回调
const aiDelFile = (item, index) => {
  aiRecommendData.attachmentResourceIds.splice(index, 1)
  // 将所有图片的URL用逗号拼接
  if (aiRecommendData.attachmentResourceIds.length > 0) {
    const urls = aiRecommendData.attachmentResourceIds
      .map((item) => item.serviceUri || item.url || item.path)
      .filter(Boolean)
    aiRecommendData.imgUrl = urls.join(',')
  } else {
    aiRecommendData.imgUrl = ''
  }
}

// AI推荐生成
const generateAIRecommend = async () => {
  if (!props.materialId) {
    uni.$u.toast('材料ID不存在')
    return
  }

  try {
    uni.showLoading({
      title: '生成中...'
    })

    await generatePlayList({
      combinedId: props.materialId,
      userPlayStep: aiRecommendData.userPlayStep,
      imgUrl: aiRecommendData.imgUrl
    })

    uni.hideLoading()
    uni.$u.toast('生成成功')
    
    // 清空表单数据
    aiRecommendData.userPlayStep = ''
    aiRecommendData.attachmentResourceIds = []
    aiRecommendData.imgUrl = ''
    
    // 通知父组件刷新数据
    emit('generate-success')
    
    // 关闭弹窗
    handleClose()
  } catch (error) {
    uni.hideLoading()
    uni.$u.toast('生成失败，请重试')
    console.error('AI推荐生成失败', error)
  }
}
</script>

<style lang="scss" scoped>
/* AI推荐弹窗样式 */
.ai-popup-content {
  width: 650rpx;
  padding: 30rpx;
}

.ai-popup-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
  margin-bottom: 30rpx;
}

.ai-popup-form {
  margin-top: 20rpx;
}

.ai-form-item {
  margin-bottom: 20rpx;
}

.ai-form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.ai-textarea-container {
  position: relative;
}

.ai-form-textarea {
  width: 100%;
  height: 300rpx;
  border: 1rpx solid #eee;
  border-radius: 12rpx;
  padding: 20rpx;
  padding-bottom: 80rpx; /* 为语音按钮留出空间 */
  box-sizing: border-box;
  font-size: 28rpx;
  background-color: #f9f9f9;
}

/* 语音按钮样式 - 参考evaluate.vue中的recorderBtn */
.voice-btn {
  position: absolute;
  bottom: 20rpx;
  right: 20rpx;
  width: fit-content;
  height: 60rpx;
  color: #3f79ff;
  font-size: 24rpx;
  font-weight: 500;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 34rpx;
  background: rgba(54, 124, 255, 0.06);
}

.voice-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 6rpx;
}

.voice-text {
  font-size: 24rpx;
  color: #3f79ff;
}

/* 录音计时器样式 */
.recording-timer {
  font-size: 24rpx;
  color: #3f79ff;
  font-weight: 600;
  margin-right: 6rpx;
  min-width: 60rpx;
  text-align: center;
}

.ai-upload-section {
  display: flex;
  justify-content: flex-start;
  margin: 30rpx 0;
}

.ai-upload-btn {
  width: 180rpx;
  height: 180rpx;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 24rpx;
}

.add-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 10rpx;
}

.ai-generate-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #3f79ff 0%, #6fa3ff 100%);
  border-radius: 12rpx;
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  margin-top: 20rpx;
}

.ai-generate-btn:active {
  opacity: 0.8;
}
</style>
