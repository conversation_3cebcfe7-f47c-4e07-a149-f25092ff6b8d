import { ref, onBeforeUnmount } from 'vue'

export const useRecordingWithASR = (formData) => {
  // 录音相关状态
  const audioRecordStatus = ref('idle') // idle, recording, completed
  const recordingTime = ref(0)
  const recordingInterval = ref(null)
  const recorderManager = ref(null)
  const canRecord = ref(false) // 是否有麦克风权限
  const isRecordInterrupted = ref(false) // 是否被中断

  // 语音转文字相关状态
  const ws = ref(null) // websocket实例
  let currentUID = 0
  let isAuthed = false // 是否认证成功
  let oldText = '' // 保存原始文本
  let asrTimer = null // ASR定时器
  let asrCountdownTimer = null // ASR倒计时定时器

  let currentSegment = 1 // 当前录音片段
  const WEB_SOCKET_URL = 'wss://ai.mypacelab.com/ai/audio' // WebSocket URL
  
  // 录音配置
  const recordingOptions = {
    format: 'mp3', // 音频格式，支持PCM/WAV/AAC
    duration: 600000, // 最大值 600000（10 分钟）
    sampleRate: 16000, // 采样率，单位Hz
    numberOfChannels: 1, // 录音通道数，默认值 1
    frameSize: "4", // 指定帧大小，单位 KB。
  }

  // ASR管理配置
  const ASR_SEGMENT_DURATION = 60000 // ASR每段处理时长：60秒
  const MAX_RECORDING_DURATION = 600000 // 最大录音时长：10分钟

  // 生成唯一ID
  const generateUID = () => {
    return Date.now().toString(36) + Math.random().toString(36).substring(2, 11)
  }

  // 开始语音转文字WebSocket连接
  const startASRConnection = (fileFormat) => {
    currentUID = generateUID()
    oldText = (formData && formData.value && formData.value.audioText) ? formData.value.audioText : '' // 保存原始文本
    
    try {
      console.log(`连接ASR WebSocket: ${WEB_SOCKET_URL}/${currentUID}`)
      
      ws.value = uni.connectSocket({
        url: `${WEB_SOCKET_URL}/${currentUID}`,
        success: (res) => {
          console.log('准备建立ASR WebSocket连接...:', res)
        },
        fail: (err) => {
          console.error('ASR WebSocket连接失败:', err)
          uni.showToast({
            title: 'ASR连接失败',
            icon: 'none'
          })
        }
      })

      ws.value.onOpen(() => {
        console.log('ASR WebSocket连接成功, 发送认证包：', fileFormat)
        const authPackage = {
          format: fileFormat,
          audio: null,
          isEnd: false
        }
        ws.value.send({ data: JSON.stringify(authPackage) })
      })

      // WebSocket 响应事件
      ws.value.onMessage(res => {
        let data = JSON.parse(res.data)
        let { code, sequence } = data
        
        // 响应认证包
        if (!isAuthed) {
          if (code === 1000) {
            // 认证成功，开始传输
            isAuthed = true
            console.log('ASR认证成功，开始录音:', data)

            // 开始录音（如果是第一次）或继续录音（如果是续录）
            if (currentSegment === 1) {
              // 第一次录音，启动录音管理器
              recorderManager.value.start(recordingOptions)
            } else {
              // 续录，录音管理器已经在运行，只需要重新开始ASR处理
              console.log(`第${currentSegment}段ASR已准备就绪，继续录音`)
            }

            // 60s后暂停ASR处理并重新连接
            asrTimer = setTimeout(() => {
              handleASRSegmentTimeout()
            }, ASR_SEGMENT_DURATION)
            
          } else {
            // 认证失败，关闭连接
            console.log('ASR认证失败，关闭连接:', data)
            uni.showToast({
              title: 'ASR认证失败',
              icon: 'none'
            })
            ws.value.close()
          }
          return
        }
        
        // 常规响应处理
        console.log('收到ASR转文字结果:', data)
        if (data?.result?.length === 1) {
          if (formData && formData.value) {
            formData.value.audioText = oldText + (data.result[0].text || '')
          }
        }
        if (sequence < 0) {
          // 最后一包数据
          console.log('收到最后一包ASR数据')
        }
      })

      ws.value.onError(err => {
        console.log('ASR WebSocket连接错误:', err)
        uni.showToast({
          title: 'ASR连接错误',
          icon: 'none'
        })
        clearASRState()
      })

      ws.value.onClose(res => {
        console.log('ASR WebSocket连接已关闭:', res)
        clearTimeout(asrTimer)
      })
    } catch (e) {
      console.log('ASR WebSocket连接失败:', e)
    }
  }

  // 处理ASR段超时（60秒）- 重新连接ASR但不停止录音
  const handleASRSegmentTimeout = () => {
    console.log(`第${currentSegment}段ASR处理结束，准备重新连接ASR`)

    // 检查是否已达到最大录音时长
    if (recordingTime.value * 1000 >= MAX_RECORDING_DURATION) {
      console.log('已达到最大录音时长10分钟，自动结束录音')
      stopRecording()
      return
    }

    // 发送结束包到当前ASR连接
    if (ws.value && isAuthed) {
      const endPackage = { audio: null, isEnd: true }
      ws.value.send({ data: JSON.stringify(endPackage) })
      console.log('发送ASR结束包')
    }

    // 关闭当前ASR连接
    if (ws.value) {
      ws.value.close()
      ws.value = null
    }
    isAuthed = false

    // 立即重新连接ASR开始下一段处理
    setTimeout(() => {
      continueASRProcessing()
    }, 100) // 短暂延迟确保ASR连接完全关闭
  }

  // 继续ASR处理 - 重新连接ASR但录音不中断
  const continueASRProcessing = () => {
    currentSegment++
    console.log(`开始第${currentSegment}段ASR处理，录音继续进行`)

    // 重新连接ASR开始新的处理段
    // 注意：不重置oldText，保持累积的文本内容
    startASRConnection('mp3')
  }

  // 继续录音（保留原函数名以兼容）
  const continueRecording = () => {
    continueASRProcessing()
  }

  // 结束录音
  const finishRecording = () => {
    clearASRState()
    audioRecordStatus.value = 'completed'
    
    uni.showToast({
      title: '录音已结束',
      icon: 'success'
    })
  }

  // 清空ASR状态
  const clearASRState = () => {
    currentUID = 0
    isAuthed = false
    oldText = ''
    clearTimeout(asrTimer)
    clearTimeout(asrCountdownTimer)
    if (ws.value) {
      ws.value.close()
      ws.value = null
    }
  }

  // 检查麦克风权限
  const checkRecordPermission = () => {
    return new Promise((resolve) => {
      uni.getSetting({
        success: (res) => {
          const auth = res.authSetting['scope.record']
          if (auth === true) {
            // 用户已经同意授权
            canRecord.value = true
            resolve(true)
          } else if (auth === false) {
            // 用户已拒绝授权
            uni.showModal({
              title: '需要录音权限',
              content: '请在设置中打开麦克风权限',
              confirmText: '去设置',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  uni.openSetting({
                    success: (settingRes) => {
                      if (settingRes.authSetting['scope.record']) {
                        canRecord.value = true
                        resolve(true)
                      } else {
                        canRecord.value = false
                        resolve(false)
                      }
                    }
                  })
                } else {
                  canRecord.value = false
                  resolve(false)
                }
              }
            })
          } else {
            // 首次发起授权
            uni.authorize({
              scope: 'scope.record',
              success: () => {
                canRecord.value = true
                resolve(true)
              },
              fail: () => {
                canRecord.value = false
                resolve(false)
              }
            })
          }
        },
        fail: () => {
          canRecord.value = false
          resolve(false)
        }
      })
    })
  }

  // 格式化播放时间
  const formatPlayTime = (seconds) => {
    if (!seconds) return '00:00'
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.floor(seconds % 60)
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  // 格式化时长显示
  const formatDuration = (seconds) => {
    if (!seconds) return '00:00'
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  // 初始化录音管理器
  const initRecorderManager = () => {
    if (uni.getRecorderManager) {
      recorderManager.value = uni.getRecorderManager()

      // 监听录音开始事件
      recorderManager.value.onStart(() => {
        console.log('开始录音')
        audioRecordStatus.value = 'recording'
        recordingTime.value = 0

        // 启动计时器
        recordingInterval.value = setInterval(() => {
          recordingTime.value++

          // 检查是否达到最大录音时长（10分钟）
          if (recordingTime.value >= MAX_RECORDING_DURATION / 1000) {
            console.log('达到最大录音时长，自动停止录音')
            stopRecording()
          }
        }, 1000)
      })

      // 监听录音实时数据 - 用于语音转文字
      recorderManager.value.onFrameRecorded((res) => {
        const frameBuffer = res.frameBuffer
        const isLastFrame = res.isLastFrame

        if (ws.value && isAuthed) {
          const audio = uni.arrayBufferToBase64(frameBuffer)
          const sendPackage = {
            audio: JSON.stringify(audio),
            isEnd: isLastFrame
          }
          ws.value.send({ data: JSON.stringify(sendPackage) })
          console.log('发送音频数据到ASR，是否为最后一帧:', isLastFrame)

          if (isLastFrame) {
            console.log('录音结束，发送结束包到ASR')
            const endPackage = { audio: null, isEnd: true }
            ws.value.send({ data: JSON.stringify(endPackage) })
          }
        }
      })

      // 监听录音结束事件
      recorderManager.value.onStop(async (res) => {
        console.log('录音结束', res)
        clearInterval(recordingInterval.value)

        // If we're not in recording mode anymore, don't process this recording
        if (audioRecordStatus.value !== 'recording') {
          console.log('录音已取消或重置，不处理文件')
          return
        }

        // 检查录音时长是否太短
        if (!res || !res.duration || res.duration < 1000) {
          uni.showToast({
            title: '录音时间不能小于1秒',
            icon: 'none',
            duration: 2000
          })
          audioRecordStatus.value = 'idle'
          return
        }

        // 录音完成，直接设置为完成状态
        audioRecordStatus.value = 'completed'

        // uni.showToast({
        //   title: '录音已完成',
        //   icon: 'success'
        // })
      })

      // 监听录音错误事件
      recorderManager.value.onError((res) => {
        console.error('录音错误:', res)
        clearInterval(recordingInterval.value)
        audioRecordStatus.value = 'idle'

        uni.showToast({
          title: '录音出错，请重试',
          icon: 'none'
        })
      })

      // 监听录音被中断开始事件
      recorderManager.value.onInterruptionBegin(() => {
        console.log('录音被中断')
        isRecordInterrupted.value = true

        uni.showToast({
          title: '录音被中断',
          icon: 'none'
        })
      })

      // 监听录音中断结束事件
      recorderManager.value.onInterruptionEnd(() => {
        console.log('录音中断结束')
        isRecordInterrupted.value = false

        uni.showToast({
          title: '录音可以继续了',
          icon: 'none'
        })
      })
    } else {
      console.error('当前环境不支持录音功能')
    }

    // 检查录音权限
    checkRecordPermission()
  }





  // 开始录音
  const startRecording = async () => {
    // 先检查权限
    const hasPermission = await checkRecordPermission()
    if (!hasPermission) {
      uni.showToast({
        title: '没有麦克风权限',
        icon: 'none'
      })
      return
    }

    if (!recorderManager.value) {
      uni.showToast({
        title: '录音功能不可用',
        icon: 'none'
      })
      return
    }

    // 重置录音相关数据
    recordingTime.value = 0
    currentSegment = 1

    console.log('开始新的录音会话')

    // 先启动语音转文字连接，连接成功后会自动开始录音
    startASRConnection('mp3')
  }

  // 停止录音
  const stopRecording = () => {
    if (recorderManager.value && audioRecordStatus.value === 'recording') {
      // 清除60秒定时器，标记为用户主动停止
      clearTimeout(asrTimer)
      asrTimer = null

      // 发送结束包到ASR
      if (ws.value && isAuthed) {
        const endPackage = { audio: null, isEnd: true }
        ws.value.send({ data: JSON.stringify(endPackage) })
      }

      // 停止录音
      recorderManager.value.stop()

      // 清理ASR状态
      clearASRState()
    }
  }

  // 取消录音
  const cancelRecording = () => {
    if (recorderManager.value && audioRecordStatus.value === 'recording') {
      // Set flag to prevent processing in onStop
      audioRecordStatus.value = 'idle'

      // Stop recording
      recorderManager.value.stop()

      // Clear interval
      if (recordingInterval.value) {
        clearInterval(recordingInterval.value)
        recordingInterval.value = null
      }

      // Clear ASR state
      clearASRState()

      // Reset all recording-related values
      recordingTime.value = 0

      uni.showToast({
        title: '已取消录音',
        icon: 'none'
      })
    }
  }

  // 删除录音文件
  const deleteAudioFile = () => {
    uni.showModal({
      title: '删除录音',
      content: '确定要删除当前录音文件吗？',
      success: (res) => {
        if (res.confirm) {
          // 重置所有录音相关值
          audioRecordStatus.value = 'idle'

          uni.showToast({
            title: '录音已删除',
            icon: 'success'
          })
        }
      }
    })
  }

  // 清理资源
  const cleanup = () => {
    // 清除计时器
    if (recordingInterval.value) {
      clearInterval(recordingInterval.value)
      recordingInterval.value = null
    }

    // 如果还在录音，停止录音
    if (audioRecordStatus.value === 'recording' && recorderManager.value) {
      recorderManager.value.stop()
    }

    // 清理ASR状态
    clearASRState()
  }

  // 组件卸载时清理
  onBeforeUnmount(() => {
    cleanup()
  })

  return {
    // 状态
    audioRecordStatus,
    recordingTime,
    canRecord,
    isRecordInterrupted,

    // 内部状态（供外部访问）
    recorderManager,
    recordingInterval,

    // 方法
    checkRecordPermission,
    startASRConnection,
    clearASRState,
    formatPlayTime,
    formatDuration,
    generateUID,
    handleASRSegmentTimeout,
    continueRecording,
    finishRecording,
    initRecorderManager,
    startRecording,
    stopRecording,
    cancelRecording,
    deleteAudioFile,
    cleanup,
    continueASRProcessing
  }
}
