<template>
  <BaseLayout nav-title="添加材料" :content-style="{ padding: '0' }">
    <view class="container">
      <view class="form-wrap">
        <view class="select-link-type">
          添加方式
          <view class="select-link-type-box">
            <view class="link-type-item">
              <radio
                :checked="linkType === 'taobao'"
                @click="linkType = 'taobao'"
                color="#3f79ff"
              />
              <text>淘宝链接</text>
            </view>
            <view class="link-type-item">
              <radio
                :checked="linkType === 'manual'"
                @click="linkType = 'manual'"
                color="#3f79ff"
              />
              <text>自行编辑</text>
            </view>
          </view>
        </view>
        <view class="select-link-type" v-if="linkType == 'manual'">
          材料类型
          <view class="select-link-type-box">
            <view class="link-type-item">
              <radio
                :checked="materialType === 'toy'"
                @click="materialType = 'toy'"
                color="#3f79ff"
              />
              <text>玩具类</text>
            </view>
            <view class="link-type-item">
              <radio
                :checked="materialType === 'book'"
                @click="materialType = 'book'"
                color="#3f79ff"
              />
              <text>图书类</text>
            </view>
          </view>
        </view>

        <view class="input-taobao" v-if="linkType === 'taobao'">
          <text>淘宝链接</text>
          <view class="taobao-link-wrap">
            <textarea
              v-model="formData.taobaoLink"
              placeholder="请粘贴淘宝链接"
              class="taobao-input"
              auto-height
            />
          </view>
        </view>
        <view class="taobao-link-tips" v-if="!isTaobaoLinkValid && linkType === 'taobao'">
          注：建议优先使用淘宝链接，获取材料信息更丰富，可使用淘宝识图方式复制淘宝链接
        </view>
        <view v-if="isTaobaoLinkValid" class="centerTitle"> 链接抓取信息 </view>

        <!-- 玩具类表单 -->
        <block v-if="(linkType === 'manual' && materialType === 'toy') || isTaobaoLinkValid">
          <up-form
            ref="formRef"
            :labelStyle="{ color: 'rgba(51, 51, 51, 1)', fontSize: '30rpx', fontWeight: '400' }"
            :rules="rules"
            :model="formData"
            labelWidth="120"
          >
            <view class="wrap">
              <up-form-item label="选择区域" prop="area" required>
                <view class="select-input" @click="showRegionPicker = true">
                  <text :class="{ placeholder: !tempRegionName }">{{
                    tempRegionName || '请选择区域'
                  }}</text>
                  <up-icon name="arrow-down" size="28rpx" color="#bbb" />
                </view>
              </up-form-item>
            </view>

            <view class="wrap">
              <up-form-item label="材料名称" prop="taobaoName" required>
                <view class="input-wrap">
                  <up-input
                    v-model="formData.taobaoName"
                    placeholder="请输入材料名称"
                    border="none"
                  />
                </view>
              </up-form-item>
            </view>

            <view class="wrap">
              <up-form-item label="材料品牌" prop="brand">
                <view class="input-wrap">
                  <up-input v-model="formData.brand" placeholder="请输入材料品牌" border="none" />
                </view>
              </up-form-item>
            </view>

            <view class="wrap" v-if="linkType === 'manual'">
              <up-form-item label="淘宝链接">
                <view class="input-wrap">
                  <up-textarea
                    v-model="formData.taobaoLink"
                    placeholder="请输入淘宝链接"
                    border="none"
                    autoHeight
                  />
                </view>
              </up-form-item>
            </view>

            <view class="wrap" v-if="linkType === 'manual'">
              <up-form-item labelPosition="top" label="材料图片" required>
                <view class="upload-tips">
                  拍照时请将材料平铺，照片须含有材料所有组件，如有包装也需拍摄，确保识别准确率。请上传多张照片保证识别准确度。
                </view>
                <view class="upload-container">
                  <Upload
                    type="image"
                    :value="formData.attachmentResourceIds"
                    @callback="uploadCallback"
                    @emitDelFile="delFile"
                    :showDel="true"
                  >
                    <view class="upload-img-wrap">
                      <image src="@/static/icon/u-icon.png" class="upload-img" mode="aspectFill" />
                      <view class="upload-img-text">上传图片</view>
                    </view>
                  </Upload>
                </view>
              </up-form-item>
            </view>
          </up-form>
        </block>

        <!-- 图书类表单 -->
        <block v-if="linkType === 'manual' && materialType === 'book'">
          <up-form
            ref="bookFormRef"
            :labelStyle="{ color: 'rgba(51, 51, 51, 1)', fontSize: '30rpx', fontWeight: '400' }"
            :rules="bookRules"
            :model="bookFormData"
            labelWidth="120"
          >
            <view class="wrap">
              <up-form-item label="选择区域" prop="area" required>
                <view class="select-input" @click="showRegionPicker = true">
                  <text :class="{ placeholder: !tempRegionName }">{{
                    tempRegionName || '请选择区域'
                  }}</text>
                  <up-icon name="arrow-down" size="28rpx" color="#bbb" />
                </view>
              </up-form-item>
            </view>

            <view class="wrap">
              <up-form-item label="书籍名称" prop="bookName" required>
                <view class="input-wrap">
                  <up-input
                    v-model="bookFormData.bookName"
                    placeholder="请输入书籍名称"
                    border="none"
                  />
                </view>
              </up-form-item>
            </view>

            <view class="wrap">
              <up-form-item labelPosition="top" label="书籍图片" required>
                <view class="upload-tips"> 请拍摄书籍封面图片 </view>
                <view class="upload-container">
                  <Upload
                    type="image"
                    :value="bookFormData.attachmentResourceIds"
                    @callback="uploadBookCallback"
                    @emitDelFile="delBookFile"
                    :showDel="true"
                  >
                    <view class="upload-img-wrap">
                      <image src="@/static/icon/u-icon.png" class="upload-img" mode="aspectFill" />
                      <view class="upload-img-text">上传图片</view>
                    </view>
                  </Upload>
                </view>
              </up-form-item>
            </view>
          </up-form>
        </block>

        <!-- 渲染根据淘宝链接返回的图片 -->
        <view v-if="isTaobaoLinkValid" class="taobao-img-wrap">
          <view class="title"
            >材料图片 <text class="select-all" @click="selectAllImages">全选</text></view
          >
          <view class="desc"> 请点击图片右上角，勾选所有符合的图片 </view>
          <view class="imgs">
            <view class="pic" v-for="(item, index) in taobaoImgList" :key="index">
              <view class="select-box" @click.stop="selectImg(item)">
                <image
                  v-if="!item.isSelected"
                  src="/classMaterials/static/radio-icon.svg"
                  class="select"
                />
                <image v-else src="/classMaterials/static/active.svg" class="select" />
              </view>
              <image :src="item.url" class="taobao-img" @click="previewImage(item.url)" />
            </view>
          </view>
        </view>
        <view v-if="!isTaobaoLinkValid && linkType === 'taobao'">
          <button plain class="parse-btn" @click="parseTaobaoLink">获取材料信息</button>
        </view>
      </view>

      <view class="submit-btn-wrap" v-if="linkType === 'manual' && materialType === 'toy'">
        <button class="submit-btn" @click="submit">AI分析</button>
      </view>
      <view class="submit-btn-wrap" v-if="linkType === 'manual' && materialType === 'book'">
        <button
          class="submit-btn"
          :class="{ 'disabled-btn': isSubmittingBook }"
          :disabled="isSubmittingBook"
          @click="submitBook"
        >
          {{ isSubmittingBook ? '提交中...' : '提交' }}
        </button>
      </view>
      <view class="submit-btn-wrap" v-if="isTaobaoLinkValid">
        <button class="submit-btn" @click="submit">AI分析</button>
      </view>

      <!-- 区域选择弹窗 -->
      <up-picker
        :show="showRegionPicker"
        @cancel="showRegionPicker = false"
        @confirm="confirmRegion"
        :columns="regionColumns"
        title="选择区域"
      ></up-picker>
    </view>
  </BaseLayout>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import BaseLayout from '@/components/base-layout/base-layout.vue'
import Popup from '@/components/Popup/Popup.vue'
import Upload from '@/components/Upload/Upload.vue'
import {
  getMaterialByTaobaoUrl,
  getClassMaterialArea,
  saveMaterialInfo,
  generatePlayList,
  getOutSideMaterialArea
} from '@/api/game'
const formRef = ref(null)
const bookFormRef = ref(null)
const formData = reactive({
  area: '',
  taobaoName: '',
  name: '',
  brand: '',
  taobaoLink: '',
  attachmentResourceIds: [],
  // 用户选择的图片
  selectedImg: '',
  classId: '',
  schoolId: ''
})

// 图书类表单数据
const bookFormData = reactive({
  area: '',
  bookName: '',
  attachmentResourceIds: [],
  selectedImg: '',
  classId: '',
  schoolId: ''
})

// 链接类型：taobao - 淘宝链接，manual - 自行编辑
const linkType = ref('taobao')
// 材料类型：toy - 玩具类，book - 图书类
const materialType = ref('toy')

// 校验规则
const rules = {
  area: [{ required: true, message: '请选择区域' }],
  taobaoName: [{ required: true, message: '请输入材料名称' }],
  img: [{ required: true, message: '请上传材料图片' }],
  taobaoLink: [{ required: true, message: '请输入淘宝链接' }]
}

// 图书类校验规则
const bookRules = {
  area: [{ required: true, message: '请选择区域' }],
  bookName: [{ required: true, message: '请输入书籍名称' }]
}

const showRegionPicker = ref(false)
const regionNameList = ref([])
const tempRegion = ref(null)
const tempRegionName = ref('')

const taobaoImgList = ref([])
// 是否获取到淘宝链接
const isTaobaoLinkValid = ref(false)

onLoad(async (options) => {
  // 获取当前区域
  const userInfo = uni.getStorageSync('USER_INFO')
  const classId = userInfo.currentClassId || userInfo.classIds[0]
  formData.classId = classId
  formData.schoolId = userInfo.currentSchoolId
  bookFormData.classId = classId
  bookFormData.schoolId = userInfo.currentSchoolId
  let res = null
  if (userInfo.isOutside == 1) {
    res = await getOutSideMaterialArea({
      schoolId: userInfo.currentSchoolId
    })
  } else {
    res = await getClassMaterialArea({
      schoolId: userInfo.currentSchoolId,
      classId: classId
    })
  }
  regionNameList.value = res.data
  // 隐藏 全部 区域
  regionNameList.value = res.data.filter((item) => item.area !== '全部')

  // 检查是否从 index 页面传递了区域参数
  if (options.area && options.areaId) {
    const passedArea = decodeURIComponent(options.area)
    const passedAreaId = parseInt(options.areaId)
    const passedAreaAlias = options.areaAlias ? decodeURIComponent(options.areaAlias) : ''

    // 查找匹配的区域对象
    const selectedRegionObj = regionNameList.value.find((item) =>
      item.id === passedAreaId && item.area === passedArea
    )

    if (selectedRegionObj) {
      formData.area = selectedRegionObj.area
      bookFormData.area = selectedRegionObj.area
      tempRegionName.value = selectedRegionObj.areaAlias
        ? `${selectedRegionObj.area}(${selectedRegionObj.areaAlias})`
        : selectedRegionObj.area
      tempRegion.value = selectedRegionObj
    }
  } else if (regionNameList.value.length === 1) {
    // 如果没有传递区域参数且只有一个区域，自动选择它
    const selectedRegionObj = regionNameList.value[0]
    formData.area = selectedRegionObj.area
    bookFormData.area = selectedRegionObj.area
    tempRegionName.value = selectedRegionObj.areaAlias
      ? `${selectedRegionObj.area}(${selectedRegionObj.areaAlias})`
      : selectedRegionObj.area
    tempRegion.value = selectedRegionObj
  }
})

// 区域数据格式化为picker需要的格式
const regionColumns = computed(() => {
  if (regionNameList.value.length === 0) return [[]]

  return [
    regionNameList.value.map((item) => {
      // 只有当areaAlias存在且不为空时才显示括号内容
      if (item.areaAlias) {
        return `${item.area}(${item.areaAlias})`
      } else {
        return item.area
      }
    })
  ]
})

// 修改区域确认方法
const confirmRegion = (e) => {
  const selectedValue = e.value[0]
  // 查找匹配的区域对象，需要处理有areaAlias和没有areaAlias的情况
  const selectedRegionObj = regionNameList.value.find((item) => {
    if (item.areaAlias) {
      return `${item.area}(${item.areaAlias})` === selectedValue
    } else {
      return item.area === selectedValue
    }
  })

  if (selectedRegionObj) {
    formData.area = selectedRegionObj.area
    bookFormData.area = selectedRegionObj.area
    // 同样处理显示内容，有areaAlias才显示括号部分
    tempRegionName.value = selectedRegionObj.areaAlias
      ? `${selectedRegionObj.area}(${selectedRegionObj.areaAlias})`
      : selectedRegionObj.area
    tempRegion.value = selectedRegionObj
  }

  showRegionPicker.value = false
}

// 处理上传回调
const uploadCallback = (list) => {
  console.log('list', list)
  formData.attachmentResourceIds.push(...list)
  // 将所有图片的URL用逗号拼接
  if (formData.attachmentResourceIds.length > 0) {
    const urls = formData.attachmentResourceIds
      .map((item) => item.serviceUri || item.url || item.path)
      .filter(Boolean)
    formData.selectedImg = urls.join(',')
  } else {
    formData.selectedImg = ''
  }
}

// 处理删除文件
const delFile = (item, index) => {
  formData.attachmentResourceIds.splice(index, 1)
  // 将所有图片的URL用逗号拼接
  if (formData.attachmentResourceIds.length > 0) {
    const urls = formData.attachmentResourceIds
      .map((item) => item.serviceUri || item.url || item.path)
      .filter(Boolean)
    formData.selectedImg = urls.join(',')
  } else {
    formData.selectedImg = ''
  }
}

// 处理图书上传回调
const uploadBookCallback = (list) => {
  bookFormData.attachmentResourceIds.push(...list)
  // 将所有图片的URL用逗号拼接
  if (bookFormData.attachmentResourceIds.length > 0) {
    const urls = bookFormData.attachmentResourceIds
      .map((item) => item.serviceUri || item.url || item.path)
      .filter(Boolean)
    bookFormData.selectedImg = urls.join(',')
  } else {
    bookFormData.selectedImg = ''
  }
}

// 处理图书删除文件
const delBookFile = (item, index) => {
  bookFormData.attachmentResourceIds.splice(index, 1)
  // 将所有图片的URL用逗号拼接
  if (bookFormData.attachmentResourceIds.length > 0) {
    const urls = bookFormData.attachmentResourceIds
      .map((item) => item.serviceUri || item.url || item.path)
      .filter(Boolean)
    bookFormData.selectedImg = urls.join(',')
  } else {
    bookFormData.selectedImg = ''
  }
}

// 解析淘宝链接
const parseTaobaoLink = async () => {
  if (!formData.taobaoLink) {
    uni.$u.toast('请输入淘宝链接')
    return
  }
  uni.showLoading({
    title: '解析中...'
  })
  let params = {
    // url: formData.taobaoLink, encodeURIComponent(
    url: encodeURIComponent(formData.taobaoLink),
    classId: formData.classId,
    schoolId: formData.schoolId
  }
  const userInfo = uni.getStorageSync('USER_INFO')
  // 户外模式下删除classId
  if (userInfo.isOutside == 1) {
    delete params.classId
  }
  const res = await getMaterialByTaobaoUrl(params)
  if (res.status == 0 && res.data.imgUrls) {
    isTaobaoLinkValid.value = true
    formData.taobaoName = res.data.taobaoName
    formData.name = res.data.name
    formData.brand = res.data.brand
    // formData['taobaoPrice'] = res.data.taobaoPrice || ''
    // formData['taobaoId'] = res.data.taobaoId || ''
    // formData['linePrice'] = res.data.linePrice || ''
    formData['combinedMaterialId'] = res.data.combinedMaterialId || ''
    formData['combinedMaterialComponentId'] = res.data.combinedMaterialComponentId || ''
    formData['baseMaterialId'] = res.data.baseMaterialId || ''
    // 链接要用返回的
    formData.taobaoLink = res.data.taobaoLink
    // 获取图片列表并默认全选
    taobaoImgList.value = []
    const imgUrls = []
    res.data.imgUrls.forEach((item) => {
      taobaoImgList.value.push({
        isSelected: true,
        url: item
      })
      imgUrls.push(item)
    })
    // 默认设置所有图片为已选中状态
    formData.selectedImg = imgUrls.join(',')
  } else {
    uni.$u.toast(res.error.stack)
  }
  setTimeout(() => {
    uni.hideLoading()
  }, 1200)
}

// 图片预览功能
const previewImage = (url) => {
  let urls = taobaoImgList.value.map((item) => item.url)
  // urls = urls.map((item) => item.replace('//', 'https://'))
  console.log('urls', urls)

  uni.previewImage({
    urls: urls,
    current: url
  })
}

// 选择图片
const selectImg = (item) => {
  // 只切换当前点击的图片状态，而不是所有图片
  item.isSelected = !item.isSelected

  // 如果图片被选中，则添加到formData.selectedImg中
  if (item.isSelected) {
    // 如果selectedImg为空，直接赋值，否则用逗号拼接
    formData.selectedImg = formData.selectedImg ? formData.selectedImg + ',' + item.url : item.url
  } else {
    // 移除已选择的图片URL
    const urls = formData.selectedImg.split(',')
    const filteredUrls = urls.filter((url) => url !== item.url)
    formData.selectedImg = filteredUrls.join(',')
  }
}

const selectAllImages = () => {
  // 如果所有图片都已选中，则取消全选
  const allSelected = taobaoImgList.value.every((item) => item.isSelected)

  // 更新所有图片的选中状态
  taobaoImgList.value.forEach((item) => {
    item.isSelected = !allSelected
  })

  // 更新formData.selectedImg
  if (!allSelected) {
    // 全选时，将所有图片URL用逗号拼接
    formData.selectedImg = taobaoImgList.value.map((item) => item.url).join(',')
  } else {
    // 取消全选时，清空selectedImg
    formData.selectedImg = ''
  }
}

const submit = () => {
  if (linkType.value === 'manual' && formData.attachmentResourceIds.length === 0) {
    uni.$u.toast('请上传材料图片')
    return
  }

  if (linkType.value === 'taobao' && !formData.selectedImg) {
    uni.$u.toast('请选择材料图片')
    return
  }

  // 检查区域是否已选择
  if (!tempRegion.value || !tempRegion.value.id) {
    uni.$u.toast('请选择区域')
    return
  }

  formRef.value.validate().then(async (valid) => {
    if (valid) {
      const userInfo = uni.getStorageSync('USER_INFO')
      formData.classId = userInfo.currentClassId
      formData.schoolId = userInfo.currentSchoolId

      // 确保 name 字段正确设置
      if (!formData.name && formData.taobaoName) {
        formData.name = formData.taobaoName
      }

      if (!formData.baseMaterialId) formData['baseMaterialId'] = null
      if (!formData.combinedMaterialId) formData['combinedMaterialId'] = null
      if (!formData.combinedMaterialComponentId) formData['combinedMaterialComponentId'] = null

      // 添加必要字段
      formData['sourceType'] = 'toy'
      formData['trashImg'] = ''
      formData['areaId'] = tempRegion.value.id
      delete formData.attachmentResourceIds
      if (userInfo.isOutside == 1) {
        delete formData.classId
      }
      const res = await saveMaterialInfo(formData)
      if (res.status == 0) {
        // 生成AI分析
        await generatePlayList({
          combinedId: res.data.combinedId
        })
      }
      uni.$u.toast('AI分析中...')
      setTimeout(() => {
        // 跳转
        uni.navigateBack({
          delta: 1
        })
      }, 500)
    } else {
      uni.$u.toast('请检查必填项')
    }
  })
}

// 提交状态标志
const isSubmittingBook = ref(false)

// 提交图书表单
const submitBook = () => {
  // 防止重复提交
  if (isSubmittingBook.value) {
    return
  }

  if (bookFormData.attachmentResourceIds.length === 0) {
    uni.$u.toast('请上传书籍封面图片')
    return
  }

  // 检查区域是否已选择
  if (!tempRegion.value || !tempRegion.value.id) {
    uni.$u.toast('请选择区域')
    return
  }

  bookFormRef.value.validate().then(async (valid) => {
    if (valid) {
      // 设置提交状态
      isSubmittingBook.value = true

      const userInfo = uni.getStorageSync('USER_INFO')

      // 构建图书提交数据
      const submitData = {
        baseMaterialId: null,
        brand: 'null',
        combinedMaterialComponentId: null,
        combinedMaterialId: null,
        selectedImg: bookFormData.selectedImg,
        trashImg: '',
        name: bookFormData.bookName,
        area: bookFormData.area,
        classId: userInfo.currentClassId,
        schoolId: userInfo.currentSchoolId,
        taobaoLink: '',
        sourceType: 'book',
        areaId: tempRegion.value.id
      }
      // 如果是外部用户，设置区域ID并删除classId
      if (userInfo.isOutside == 1) {
        delete submitData.classId
      }
      try {
        uni.showLoading({
          title: '提交中...'
        })

        const res = await saveMaterialInfo(submitData)

        uni.hideLoading()

        if (res.status == 0) {
          uni.$u.toast('提交成功')
          setTimeout(() => {
            uni.navigateBack({
              delta: 1
            })
          }, 1000)
        } else {
          uni.$u.toast(res.message || '提交失败')
          // 提交失败时重置状态
          isSubmittingBook.value = false
        }
      } catch (error) {
        uni.hideLoading()
        uni.$u.toast('提交失败，请重试')
        console.error('图书提交错误:', error)
        // 提交失败时重置状态
        isSubmittingBook.value = false
      }
    } else {
      uni.$u.toast('请检查必填项')
    }
  })
}
</script>

<style lang="scss" scoped>
.form-wrap {
  padding: 30rpx 32rpx;
  padding-bottom: 130rpx;
  .wrap {
    margin-bottom: 30rpx;
    padding: 20rpx 24rpx;
    border-radius: 28rpx;
    background: #fff;
    box-shadow: 4rpx 8rpx 16rpx rgba(0, 0, 0, 0.02);
  }
}

.select-link-type {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
  padding: 20rpx 24rpx;
  border-radius: 28rpx;
  min-height: 60rpx;
  background: linear-gradient(0deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)),
    linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
  box-shadow: 4rpx 8rpx 16rpx rgba(0, 0, 0, 0.02);
}
.select-link-type-box {
  display: flex;
  align-items: center;
  .link-type-item {
    display: flex;
    align-items: center;
    margin-right: 50rpx;
    font-size: 30rpx;
    text {
      margin-left: 8rpx;
    }
  }
}
.centerTitle {
  margin: 30rpx 0;
  font-size: 30rpx;
  color: rgba(51, 51, 51, 1);
}
.select-region,
.input-taobao {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 24rpx;
  font-size: 30rpx;
  min-height: 60rpx;
  border-radius: 28rpx;
  background: linear-gradient(0deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)),
    linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
  box-shadow: 4rpx 8rpx 16rpx rgba(0, 0, 0, 0.02);
  margin-bottom: 30rpx;
  &:last-child {
    border-bottom: none;
  }

  .select-box {
    display: flex;
    align-items: center;
    color: #333;
    .placeholder {
      color: #bbb;
    }
  }
}
.taobao-img-wrap {
  display: flex;
  flex-direction: column;
  padding: 20rpx 24rpx;
  border-radius: 28rpx;
  background: linear-gradient(0deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)),
    linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
  box-shadow: 4rpx 8rpx 16rpx rgba(0, 0, 0, 0.02);
  .title {
    font-size: 30rpx;
    font-weight: 400;
    color: rgba(51, 51, 51, 1);
    display: flex;
    align-items: center;
    justify-content: space-between;

    .select-all {
      font-size: 28rpx;
      color: #3f79ff;
      font-weight: 400;
    }
  }
  .desc {
    font-size: 24rpx;
    color: rgba(128, 128, 128, 1);
    margin: 10rpx 0;
  }
  .imgs {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16rpx;
    .pic {
      width: 100%;
      height: 200rpx;
      border-radius: 16rpx;
      position: relative;
      .taobao-img {
        width: 100%;
        height: 100%;
        border-radius: 16rpx;
      }
      .select-box {
        position: absolute;
        right: 10rpx;
        top: 10rpx;
        z-index: 10;
        .select {
          width: 60rpx;
          height: 60rpx;
        }
      }
    }
    .taobao-img {
      height: 300rpx;
    }
  }
}

.taobao-link-wrap {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  width: 70%;

  .taobao-input {
    width: 100%;
    font-size: 30rpx;
    padding: 10rpx 0;
  }
}
.taobao-link-tips {
  font-size: 24rpx;
  font-weight: 400;
  letter-spacing: 0px;
  line-height: 32rpx;
  color: rgba(128, 128, 128, 1);
}
.parse-btn {
  margin-top: 20rpx;
  width: 90vw;
  height: 80rpx;
  line-height: 80rpx;
  background: #3f79ff;
  color: #fff;
  border-radius: 44rpx;
  font-size: 30rpx;
  font-weight: 600;
  border: none;
}

.up-form-item {
  margin-bottom: 0;
  border-bottom: 1px solid #f5f5f5;
  padding: 20rpx 0;
  background: #fff;
  border-radius: 0;

  &:last-child {
    border-bottom: none;
  }
}

.select-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 8rpx;
  font-size: 30rpx;
  min-height: 60rpx;
  color: #333;
}

.input-wrap {
  padding: 0 8rpx;
  min-height: 60rpx;
  position: relative;

  ::v-deep .u-input {
    min-height: 60rpx;
  }

  .description-textarea {
    width: 100%;
    height: 160rpx;
    font-size: 30rpx;
    line-height: 1.5;
  }

  .ai-analyze-btn {
    position: absolute;
    right: 0;
    bottom: 10rpx;
    display: flex;
    align-items: center;
    background: #f5f7fe;
    border: 1px solid #3f79ff;
    border-radius: 24rpx;
    padding: 6rpx 18rpx;
    color: #3f79ff;
    font-size: 24rpx;

    .ai-icon {
      width: 28rpx;
      height: 28rpx;
      margin-right: 8rpx;
    }
  }
}

.placeholder {
  color: #bbb;
}
.upload-tips {
  font-size: 24rpx;
  color: rgba(128, 128, 128, 1);
  margin: 20rpx 0;
}

.upload-container {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
}

.upload-img-wrap {
  width: 160rpx;
  height: 160rpx;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  cursor: pointer;
}

.upload-img {
  width: 36rpx;
  height: 30rpx;
  margin-bottom: 20rpx;
}

.upload-img-text {
  font-size: 24rpx;
  color: rgba(128, 128, 128, 1);
}

:deep(.content) {
  width: 100%;
}

:deep(.grid-container) {
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: wrap !important;
  width: 100% !important;
  gap: 16rpx !important;
}

:deep(.file-item) {
  width: 148rpx !important;
  height: 148rpx !important;
}

:deep(.file-item-image) {
  width: 148rpx !important;
  height: 148rpx !important;
  object-fit: cover !important;
}

:deep(.image-border) {
  margin-bottom: 0 !important;
}

.submit-btn-wrap {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100vw;
  background: #fff;
  box-shadow: 0 -2rpx 16rpx rgba(0, 0, 0, 0.04);
  padding: 30rpx 0 50rpx 0;
  display: flex;
  justify-content: center;

  .submit-btn {
    width: 90vw;
    height: 80rpx;
    line-height: 80rpx;
    background: #3f79ff;
    color: #fff;
    border-radius: 44rpx;
    font-size: 30rpx;
    font-weight: 600;
    border: none;
  }

  .disabled-btn {
    background: #cccccc;
    cursor: not-allowed;
  }
}

.picker-popup {
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 32rpx 0 0 0;
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
  margin-bottom: 32rpx;
  font-size: 30rpx;
}

.picker-header-title {
  font-weight: 600;
}

.picker-submit {
  font-weight: 500;
  color: #3f79ff;
}

.picker-list {
  height: 600rpx;
  overflow-y: auto;
  padding: 0 32rpx 32rpx;
}

.picker-item {
  display: flex;
  align-items: center;
  font-size: 30rpx;
  margin-bottom: 32rpx;

  &:last-child {
    margin-bottom: 0;
  }
}
</style>
