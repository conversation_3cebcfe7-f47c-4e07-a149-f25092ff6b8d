<template>
  <up-popup
    :show="show"
    @close="$emit('close')"
    mode="bottom"
    :round="10"
    :closeable="true"
    closeIconPos="top-right"
    safeAreaInsetBottom
  >
    <view class="edit-popup-container">
      <view class="edit-form-header">
        <view class="add-form-title">
          <view>编辑材料分析</view>
          <view @click="saveEdit">完成</view>
        </view>
      </view>
      <scroll-view
        scroll-y="true"
        class="edit-form-scroll"
        :scroll-with-animation="true"
        :enhanced="true"
        :show-scrollbar="false"
      >
        <view class="edit-form-content">
          <view style="padding: 0 20rpx">
            <up-form ref="formRef" labelPosition="left" :model="formData" labelWidth="100">
              <up-form-item label="材料概述" prop="materialSummary" :style="textareaFormItemStyle">
                <up-textarea
                  v-model="formData.materialSummary"
                  placeholder="请输入材料概述（200字以内）"
                  border="none"
                  autoHeight
                  confirmType="none"
                  :cursorSpacing="100"
                />
              </up-form-item>

              <up-form-item label="投放目的" prop="purpose" :style="textareaFormItemStyle">
                <up-textarea
                  v-model="formData.purpose"
                  placeholder="请输入投放目的（教学目标说明）"
                  border="none"
                  autoHeight
                  confirmType="none"
                  :cursorSpacing="100"
                />
              </up-form-item>

              <up-form-item label="适合年龄" prop="age" :style="formItemStyle">
                <view class="age-range-container">
                  <up-input
                    v-model="formData.minAge"
                    placeholder="最小年龄"
                    type="number"
                    style="flex: 1"
                  />
                  <text style="margin: 0 20rpx">-</text>
                  <up-input
                    v-model="formData.maxAge"
                    placeholder="最大年龄"
                    type="number"
                    style="flex: 1"
                  />
                  <text style="margin-left: 20rpx">岁</text>
                </view>
              </up-form-item>

              <up-form-item label="适合人数" prop="people" :style="formItemStyle">
                <view class="people-range-container">
                  <up-input
                    v-model="formData.suitablePeopleMin"
                    placeholder="最少人数"
                    type="number"
                    style="flex: 1"
                  />
                  <text style="margin: 0 20rpx">-</text>
                  <up-input
                    v-model="formData.suitablePeopleMax"
                    placeholder="最多人数"
                    type="number"
                    style="flex: 1"
                  />
                  <text style="margin-left: 20rpx">人</text>
                </view>
              </up-form-item>
            </up-form>
          </view>
        </view>
      </scroll-view>
    </view>
  </up-popup>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { editMaterialAnalysis } from '@/api/game'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  materialAnalysisData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['close', 'success'])

const formRef = ref()

const formData = reactive({
  id: '',
  materialSummary: '',
  purpose: '',
  minAge: '',
  maxAge: '',
  suitablePeopleMin: '',
  suitablePeopleMax: ''
})

const formItemStyle = {
  height: '115rpx',
  justifyContent: 'center'
}

const textareaFormItemStyle = {
  minHeight: '115rpx',
  justifyContent: 'flex-start',
  paddingTop: '20rpx'
}

// 监听props变化，回填数据
watch(
  () => props.materialAnalysisData,
  (newData) => {
    if (newData && Object.keys(newData).length > 0) {
      formData.id = newData.id || ''
      formData.materialSummary = newData.materialSummary || ''
      formData.purpose = newData.purpose || ''
      formData.minAge = newData.minAge || ''
      formData.maxAge = newData.maxAge || ''
      formData.suitablePeopleMin = newData.suitablePeopleMin || ''
      formData.suitablePeopleMax = newData.suitablePeopleMax || ''
    }
  },
  { immediate: true }
)

// 监听弹窗显示状态，确保弹窗打开时数据已回填
watch(
  () => props.show,
  (isShow) => {
    if (
      isShow &&
      props.materialAnalysisData &&
      Object.keys(props.materialAnalysisData).length > 0
    ) {
      const data = props.materialAnalysisData
      formData.id = data.id || ''
      formData.materialSummary = data.materialSummary || ''
      formData.purpose = data.purpose || ''
      formData.minAge = data.minAge || ''
      formData.maxAge = data.maxAge || ''
      formData.suitablePeopleMin = data.suitablePeopleMin || ''
      formData.suitablePeopleMax = data.suitablePeopleMax || ''
    }
  }
)

// 保存编辑
const saveEdit = async () => {
  try {
    uni.showLoading({ title: '保存中...' })

    const res = await editMaterialAnalysis({
      id: formData.id,
      materialSummary: formData.materialSummary,
      purpose: formData.purpose,
      minAge: formData.minAge ? parseInt(formData.minAge) : null,
      maxAge: formData.maxAge ? parseInt(formData.maxAge) : null,
      suitablePeopleMin: formData.suitablePeopleMin ? parseInt(formData.suitablePeopleMin) : null,
      suitablePeopleMax: formData.suitablePeopleMax ? parseInt(formData.suitablePeopleMax) : null
    })

    uni.hideLoading()

    if (res.status === 0) {
      uni.$u.toast('保存成功')
      emit('success', formData)
      emit('close')
    } else {
      uni.$u.toast(res.message || '保存失败')
    }
  } catch (error) {
    uni.hideLoading()
    console.error('保存材料分析失败', error)
    uni.$u.toast('保存失败')
  }
}
</script>

<style lang="scss" scoped>
.edit-popup-container {
  height: 60vh;
  display: flex;
  flex-direction: column;
}
.edit-form-header {
  padding: 30rpx 30rpx 10rpx;
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 10;
}
.add-form-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;

  view:first-child {
    font-size: 34rpx;
    font-weight: 600;
  }

  view:last-child {
    font-size: 30rpx;
    font-weight: 500;
    color: rgba(63, 121, 255, 1);
  }
}
.edit-form-scroll {
  flex: 1;
  height: 0;
}
.edit-form-content {
  padding: 20rpx 0 40rpx;
}
.age-range-container,
.people-range-container {
  display: flex;
  align-items: center;
  width: 100%;
}
::v-deep .u-form-item {
  border-bottom: 1px solid #eee;
}
::v-deep .u-form-item:last-child {
  border-bottom: none;
}
</style>
