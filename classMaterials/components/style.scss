.main-container {
  padding: 30rpx;
}
.row {
  box-sizing: border-box;
  padding: 30rpx;
  border-radius: 26rpx;
  margin-bottom: 30rpx;
  background: linear-gradient(0deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)),
    linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
  box-shadow: 4rpx 8rpx 16rpx rgba(0, 0, 0, 0.02);
  position: relative;
  .edit_icon {
    position: absolute;
    right: 20rpx;
    top: 20rpx;
    width: 30rpx;
    height: 30rpx;
    z-index: 3;
  }
  .play-list {
    & > view {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 30rpx 0;
      border-bottom: 1rpx solid rgba(238, 238, 238, 1);
    }
    & > view:last-child {
      border-bottom: none;
    }
    .play-title {
      font-size: 28rpx;
      font-weight: 400;
      color: rgba(51, 51, 51, 1);
      margin-right: 10rpx;
    }
    .play-interest-tag {
      display: inline-block;
      margin-left: 10rpx;
      border-radius: 8rpx;
      padding: 4rpx 8rpx;
      font-size: 22rpx;

      &.very-low {
        background: rgba(252, 237, 238, 1);
        color: rgba(237, 111, 114, 1);
      }

      &.low {
        background: rgba(253, 244, 237, 1);
        color: rgba(240, 145, 77, 1);
      }

      &.medium {
        background: rgba(239, 243, 253, 1);
        color: rgba(96, 139, 240, 1);
      }

      &.high {
        background: rgba(240, 241, 252, 1);
        color: rgba(110, 116, 230, 1);
      }

      &.very-high {
        background: rgba(234, 246, 237, 1);
        color: rgba(84, 186, 106, 1);
      }
    }
    .play-intro {
      width: 400rpx;
      font-size: 24rpx;
      font-weight: 400;
      color: rgba(128, 128, 128, 1);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
.row2 {
  padding: 0;
  background: linear-gradient(0deg, rgba(252, 252, 252, 1), rgba(252, 252, 252, 1)),
    linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
}
.basicInfo {
  .top_box {
    display: flex;
    margin-bottom: 20rpx;
    .avatar {
      position: relative;
      z-index: 2;
      width: 146rpx;
      height: 146rpx;
      border-radius: 24rpx;
      margin: -40rpx 20rpx 0rpx 0rpx;
      flex-shrink: 0;
      image {
        width: 100%;
        height: 100%;
        border-radius: 24rpx;
        flex-shrink: 0;
      }
    }
    .name {
      font-size: 24rpx;
      color: rgba(128, 128, 128, 1);
      & > view:nth-child(1) {
        // width: 400rpx;
        // overflow: hidden;
        // text-overflow: ellipsis;
        // white-space: nowrap;
        font-size: 38rpx;
        font-weight: 600;
        color: rgba(51, 51, 51, 1);
      }
    }
  }
  .center_box {
    // min-height: 200rpx;
    width: 100%;
    border-radius: 24rpx;
    background: rgba(238, 238, 238, 1);
    border: 1rpx solid rgba(238, 238, 238, 1);
    padding: 20rpx;
    box-sizing: border-box;

    display: flex;
    flex-direction: column;
    justify-content: space-between;
    font-size: 28rpx;
    font-weight: 400;
    color: rgba(51, 51, 51, 1);
    & > view {
      padding: 10rpx 0;
    }
  }
  .btm_box {
    width: 100%;
    height: 106rpx; // 设置一个固定高度
    margin-top: 20rpx;

    .scroll-container {
      width: 100%;
      height: 100%;
      white-space: nowrap; // 防止换行
    }

    .images-wrapper {
      display: inline-flex; // 使用内联弹性布局保持在一行
      flex-direction: row;
      flex-wrap: nowrap;
    }

    .pic {
      width: 106rpx;
      height: 106rpx;
      margin-right: 20rpx;
      border-radius: 24rpx;
      flex-shrink: 0; // 防止图片被压缩
    }
  }
}
.materialAnalysis {
  padding-top: 0rpx;
  padding-bottom: 80rpx; // 为按钮预留空间
  position: relative;

  .content-wrapper {
    transition: max-height 0.3s ease;
    overflow: hidden;
    max-height: 1000rpx; // 足够展示全部内容的高度

    &.content-collapsed {
      max-height: 120rpx; // 折叠时只显示部分内容
    }
    & > view {
      padding: 20rpx 0 30rpx 0;
      border-bottom: 1rpx solid rgba(238, 238, 238, 1);
      font-size: 28rpx;
      font-weight: 400;
      letter-spacing: 0px;
      line-height: 44rpx;
      color: rgba(51, 51, 51, 1);
    }
    & > view:last-child {
      border-bottom: none;
    }
  }

  & > view {
    padding: 20rpx 0 30rpx 0;
    border-bottom: 1rpx solid rgba(238, 238, 238, 1);
    font-size: 28rpx;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 44rpx;
    color: rgba(51, 51, 51, 1);
  }
  & > view:last-child {
    border-bottom: none;
  }
  .title {
    font-size: 34rpx;
    font-weight: 600;
    color: rgba(51, 51, 51, 1);
    position: relative;
    .text_bg {
      position: absolute;
      left: 0rpx;
      bottom: 29rpx;
      width: 100rpx;
      height: 18rpx;
    }
  }
  .sub_title {
    font-size: 30rpx;
    font-weight: 600;
    color: rgba(51, 51, 51, 1);
    margin-bottom: 20rpx;
  }

  .toggle-btn {
    position: absolute;
    bottom: 20rpx;
    left: 50%;
    transform: translateX(-50%);
    font-size: 28rpx;
    color: #4080ff;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10rpx 0;
    width: 100%;
    background: #fff;

    &.collapsed {
      color: rgba(128, 128, 128, 1);
    }

    .toggle-icon {
      margin-left: 10rpx;
      width: 28rpx;
      height: 28rpx;
    }
  }
}
.appliedPlay {
  .title {
    font-size: 34rpx;
    font-weight: 600;
    color: rgba(51, 51, 51, 1);
    position: relative;
    .text_bg {
      position: absolute;
      left: 0rpx;
      bottom: 0rpx;
      width: 100rpx;
      height: 18rpx;
    }
  }
}
.aiRecommendPlay {
  .title {
    font-size: 34rpx;
    font-weight: 600;
    color: rgba(51, 51, 51, 1);
    position: relative;
    .text_bg {
      position: absolute;
      left: 0rpx;
      bottom: 0rpx;
      width: 100rpx;
      height: 18rpx;
    }
    .recommend-btn {
      position: absolute;
      right: 20rpx;
      top: 50%;
      transform: translateY(-50%);
      font-size: 28rpx;
      font-weight: 400;
      color: rgba(63, 121, 255, 1);
    }
  }
}


/* 表单样式 */
.edit-popup-container {
  height: 1000rpx;
  display: flex;
  flex-direction: column;
}

.edit-form-header {
  padding: 30rpx 30rpx 10rpx;
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 10;
}

.add-form-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;

  view:first-child {
    font-size: 34rpx;
    font-weight: 600;
  }

  view:last-child {
    font-size: 30rpx;
    font-weight: 500;
    color: rgba(63, 121, 255, 1);
  }
}

.edit-form-scroll {
  flex: 1;
  height: calc(90vh - 100rpx);
}

::v-deep .u-form-item {
  border-bottom: 1px solid #eee;
}

::v-deep .u-form-item:last-child {
  border-bottom: none;
}

/* 确保弹窗正常显示 */
::v-deep .material-analysis-popup {
  z-index: 9999;
}

/* 玩法详情弹窗样式 */
.play-detail-popup {
  padding: 30rpx;
  height: 80vh; /* 调整弹窗高度，确保有更多可滚动空间 */
  width: 100%;
  box-sizing: border-box;
  position: relative;
}

.play-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.play-detail-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.close-icon {
  padding: 10rpx;
}

.play-detail-content {
  margin-bottom: 40rpx;
}

.play-detail-status {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  margin-bottom: 20rpx;

  text:first-child {
    color: #666;
  }

  text:last-child {
    color: #333;
    margin-right: 20rpx;
  }
}

.delete-play-btn {
  font-size: 24rpx;
  color: #ff3a3a;
  background: none;
  border: 1rpx solid #ff3a3a;
  border-radius: 20rpx;
  padding: 4rpx 16rpx;
  margin-left: auto;
  height: 40rpx;
  line-height: 32rpx;
}

.play-detail-item {
  margin-bottom: 20rpx;
}

.play-detail-item-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.play-detail-item-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.play-detail-intro {
  margin-bottom: 30rpx;
}

.experience-item {
  margin-bottom: 20rpx;
}

.experience-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}

.experience-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.play-action-buttons {
  display: flex;
  justify-content: space-between;
  padding: 30rpx;
  background-color: #fff;
  border-top: 1rpx solid #eee;
  box-sizing: border-box;
}

.ai-play-buttons,
.adopted-play-buttons {
  width: 100%;
  position: sticky;
  bottom: 0;
  background-color: #fff;
  padding-top: 20rpx;
  padding-bottom: 30rpx; /* 增加底部padding，防止被键盘完全遮挡 */
  z-index: 99;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.adopt-btn,
.reject-btn {
  width: 40%;
  height: 80rpx;
  line-height: 80rpx;
  background: #3f79ff;
  color: #fff;
  border-radius: 44rpx;
  font-size: 30rpx;
  font-weight: 600;
  border: none;
}

.reject-btn {
  background-color: #dd6161;
}

/* 已采纳玩法的操作按钮样式 */
.adopted-play-buttons,
.ai-play-buttons {
  width: 100%;
  position: sticky;
  bottom: 0;
  background-color: #fff;
  padding-top: 20rpx;
  padding-bottom: 30rpx; /* 增加底部padding，防止被键盘完全遮挡 */
  z-index: 99;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.edit-btn,
.interest-btn,
.delete-btn {
  width: 30%;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 44rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
}

.edit-btn {
  background: #3f79ff;
  color: #fff;
}

.interest-btn {
  background: #5cb85c;
  color: #fff;
}

.delete-btn {
  background: #dd6161;
  color: #fff;
}

/* 自定义下拉框样式 */
.custom-select {
  width: 100%;
  height: 80rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20rpx;
  box-sizing: border-box;
  background-color: #f9f9f9;
  border-radius: 8rpx;
}

.select-text {
  font-size: 28rpx;
  color: #333;
}

/* 选择弹窗样式 */
.select-popup-content {
  padding: 30rpx;
  max-height: 80vh;
}

.select-popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
  margin-bottom: 30rpx;
}

.select-popup-list {
  max-height: 60vh;
  overflow-y: auto;
}

.select-popup-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 26rpx 20rpx;
  border-bottom: 1rpx solid #eee;
}

.select-popup-item:last-child {
  border-bottom: none;
}

.select-check-icon {
  width: 36rpx;
  height: 36rpx;
}

/* 兴趣相关样式 */
.play-detail-interests {
  position: relative;
  margin-bottom: 30rpx;
}

.interests-content {
  margin-bottom: 20rpx;
}

.select-interests-btn {
  font-size: 24rpx;
  color: #3f79ff;
  background: none;
  border: 1rpx solid #3f79ff;
  border-radius: 20rpx;
  padding: 8rpx 20rpx;
  margin-top: 10rpx;
  display: inline-block;
}

.interest-list {
  padding: 0 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.interest-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #eee;
}

.interest-item:last-child {
  border-bottom: none;
}

.interest-checkbox {
  width: 40rpx;
  height: 40rpx;
  border-radius: 8rpx;
  border: 1rpx solid #ddd;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.interest-checkbox.checked {
  background-color: #3f79ff;
  border-color: #3f79ff;
}

/* 可编辑字段样式 */
.editable-field {
  width: 90%;
  height: 70rpx;
  padding: 0 20rpx;
  // border: 1rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #f9f9f9;
}

.editable-textarea {
  width: 90%;
  min-height: 150rpx;
  padding: 20rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #f9f9f9;
  position: relative;
  z-index: 1;
}

.play-title-edit {
  width: 100%;
  height: 80rpx;
  font-size: 34rpx;
  font-weight: 600;
}

/* 图片管理区域样式 */
.image-management-section {
  padding: 30rpx 20rpx;
  border-top: 1rpx solid #eee;
}

.image-management-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;

  text {
    font-size: 30rpx;
    font-weight: 600;
    color: #333;
  }

  .image-hint {
    font-size: 24rpx;
    color: #999;
    font-weight: normal;
  }
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.image-item {
  width: 200rpx;
  height: 200rpx;
  position: relative;
  border-radius: 12rpx;
  overflow: hidden;

  .preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .image-actions {
    position: absolute;
    right: 10rpx;
    top: 10rpx;
    display: flex;
    flex-direction: column;
    gap: 10rpx;

    .action-btn {
      width: 50rpx;
      height: 50rpx;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.upload-img-wrap {
  width: 200rpx;
  height: 200rpx;
  // border: 1rpx dashed #ddd;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  cursor: pointer;
}

.upload-img {
  width: 36rpx;
  height: 30rpx;
  margin-bottom: 20rpx;
}

.upload-img-text {
  font-size: 24rpx;
  color: rgba(128, 128, 128, 1);
}

:deep(.content) {
  width: 100%;
}

:deep(.grid-container) {
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: wrap !important;
  width: 100% !important;
  gap: 16rpx !important;
}

:deep(.file-item) {
  width: 148rpx !important;
  height: 148rpx !important;
}

:deep(.file-item-image) {
  width: 148rpx !important;
  height: 148rpx !important;
  object-fit: cover !important;
}

:deep(.image-border) {
  margin-bottom: 0 !important;
}

.play-detail-teacher-support {
  margin-bottom: 30rpx;
}

.upload-icon-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-text {
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #999999;
}

.play-title-row {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

/* 确保编辑弹窗中的 UploadNew 组件正确显示 */
:deep(.grid-container) {
  display: flex !important;
  flex-wrap: wrap !important;
  gap: 20rpx !important;
}

.hxjy {
  position: relative;
  display: flex;
  flex-direction: column;
  margin-bottom: 32rpx;
  padding: 32rpx 24rpx 16rpx 24rpx;
  border-radius: 20rpx;
  background: #fff;
  box-shadow: 0 2rpx 8rpx rgba(63, 121, 255, 0.06);
  // border: 1rpx solid #f0f0f0;
  transition: box-shadow 0.2s;
}
.hxjy:hover {
  box-shadow: 0 4rpx 16rpx rgba(63, 121, 255, 0.12);
}
.hxjy_title {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 18rpx;
  position: relative;
}
.hxjy_title_text {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-right: 16rpx;
}
.delete_hxjy {
  position: absolute;
  right: 0;
  top: 0;
  width: 36rpx;
  height: 36rpx;
  cursor: pointer;
  z-index: 2;
}
// .hxjy:not(:last-child)::after {
//   content: '';
//   display: block;
//   height: 1rpx;
//   background: #f0f0f0;
//   margin: 24rpx 0 0 0;
// }
.add_hxjy {
  font-size: 28rpx;
  color: #3f79ff;
  margin-top: 10rpx;
  cursor: pointer;
  text-align: center;
  font-weight: 500;
  letter-spacing: 2rpx;
}

.existing-experience {
  padding: 20rpx;
  background-color: #f0f0f0;
  border-radius: 10rpx;
  margin-bottom: 10rpx;
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
}

.play-detail-popup-container {
  display: flex;
  flex-direction: column;
  height: 80vh;
}

.play-detail-popup {
  padding: 30rpx;
  flex: 1;
  overflow-y: auto;
  width: 100%;
  box-sizing: border-box;
}

.play-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
