<p align="center">
    <img alt="logo" src="https://uiadmin.net/uview-plus/common/logo.png" width="120" height="120" style="margin-bottom: 10px;">
</p>
<h3 align="center" style="margin: 30px 0 30px;font-weight: bold;font-size:40px;">uview-plus 3.0</h3>
<h3 align="center">多平台快速开发的UI框架</h3>

[![stars](https://img.shields.io/github/stars/ijry/uview-plus?style=flat-square&logo=GitHub)](https://github.com/ijry/uview-plus)
[![forks](https://img.shields.io/github/forks/ijry/uview-plus?style=flat-square&logo=GitHub)](https://github.com/ijry/uview-plus)
[![issues](https://img.shields.io/github/issues/ijry/uview-plus?style=flat-square&logo=GitHub)](https://github.com/ijry/uview-plus/issues)
[![release](https://img.shields.io/github/v/release/ijry/uview-plus?style=flat-square)](https://gitee.com/jry/uview-plus/releases)
[![license](https://img.shields.io/github/license/ijry/uview-plus?style=flat-square)](https://en.wikipedia.org/wiki/MIT_License)

## 说明

uview-plus，是uni-app全面兼容vue3/nvue/鸿蒙/uni-app-x的uni-app生态框架，全面的组件和便捷的工具会让您信手拈来，如鱼得水。uview-plus是基于uView2.x移植的支持vue3的版本，感谢uView。

## 可视化设计

uview-plus现已推出免费可视化设计，可以方便的进行页面可视化设计，导出源码即可使用。极大提高前端页面开发效率；如产品经理设计师直接使用更可作为高保真高可用原型制作工具，让设计稿即代码，无需传统的设计稿开发还原步骤。

<img src="https://s3.bmp.ovh/imgs/2024/11/24/fd58d00071e6e5df.png" width="900" height="auto" >
<img src="https://s3.bmp.ovh/imgs/2024/11/24/8e85a519fe627fb1.png" width="900" height="auto" >


## 文档
[官方文档：https://uview-plus.jiangruyi.com](https://uview-plus.jiangruyi.com)
[备用文档：https://uiadmin.net/uview-plus](https://uiadmin.net/uview-plus)


## 预览

您可以通过**微信**扫码，查看最佳的演示效果。
<br>
<br>
<img src="https://uview-plus.jiangruyi.com/common/h5_qrcode.png" width="220" height="220" >

## 链接

- [官方文档](https://uview-plus.jiangruyi.com)
- [更新日志](https://uview-plus.jiangruyi.com/components/changelog.html)
- [升级指南](https://uview-plus.jiangruyi.com/components/changeGuide.html)
- [关于我们](https://uview-plus.jiangruyi.com/cooperation/about.html)

## 交流反馈

欢迎加入我们的QQ群交流反馈：[点此跳转](https://uview-plus.jiangruyi.com/components/addQQGroup.html)

## 关于PR

> 我们非常乐意接受各位的优质PR，但在此之前我希望您了解uview-plus是一个需要兼容多个平台的（小程序、h5、ios app、android app）包括nvue页面、vue页面。
> 所以希望在您修复bug并提交之前尽可能的去这些平台测试一下兼容性。最好能携带测试截图以方便审核。非常感谢！

## 安装

#### **uni-app插件市场链接** —— [https://ext.dcloud.net.cn/plugin?name=uview-plus](https://ext.dcloud.net.cn/plugin?name=uview-plus)

请通过[官网安装文档](https://uview-plus.jiangruyi.com/components/install.html)了解更详细的内容

## 快速上手

请通过[快速上手](https://uview-plus.jiangruyi.com/components/quickstart.html)了解更详细的内容

## 使用方法
配置easycom规则后，自动按需引入，无需`import`组件，直接引用即可。

```html
<template>
	<u-button text="按钮"></u-button>
</template>
```

## 版权信息
uview-plus遵循[MIT](https://en.wikipedia.org/wiki/MIT_License)开源协议，意味着您无需支付任何费用，也无需授权，即可将uview-plus应用到您的产品中。

