import { setLogger } from '@/api';

/**
  * pageLogConfig type值
  * api 接口请求日志
  * back 后端接口埋点日志
  * click 点击操作
  * page 客户端页面跳转日志
  * share 分享操作
  * timeout 前端请求超时
  * view 页面浏览
 */

// 静态变量 - 配置数据
const CONF = {
  DELAY_TIME: 300000, // 延迟上传时间（单位:ms）
  LENGTH: 5, // 每次上传x条
  MAX_UPLOAD_RETRIES: 3 // 最大上传重试次数
};

// 数据埋点，支持队列
class Tracker {
  constructor() {

    // 埋点数据集合
    const bmy__trackers = uni.getStorageSync('bmy__trackers') || [];
    this.tracks = bmy__trackers || []; // wx.getStorageSync('bmy__trackers') || []

    // 初始化 - 数据状态
    this.init();
  }

  /**
   * 初始化 - 数据状态
   */
  init(tid) {
    this.uploading = false; // 是否在上传中
    this.setTimeoutId = tid || 0; // 最后一次需要执行timeout的标记
  }
  /**
 * 清除缓存 - 数据状态
 */
  clear() {
    this.uploading = false; // 是否在上传中
    this.setTimeoutId = 0; // 最后一次需要执行timeout的标记
    this.tracks = [];
  }

  /**
   * 加入队列
   * @param {Object} opt 参数配置
   * @param {String} opt.page 当前页面
   * @param {String} opt.pageOptions 页面参数
   * @param {String} opt.actionType 操作类型：view、share、click
   * @param {String} opt.actionOptions 操作参数
   */
  trigger(opt) {
    const {
      page, // 页面地址或页面名称
      params = '', // 页面参数
      title = '' // 页面标题
    } = opt;
    const timestamp = new Date().getTime(); // 时间戳

    if (!page || typeof page !== 'string') return;
    const log = { page, params, title, timestamp };

    try {
      this.tracks.push(log);
      uni.setStorageSync('bmy__trackers', this.tracks); // FIXME:不该是每次都持久化

      this.retryTimes = 0;

      if (this.tracks.length > CONF.LENGTH && !this.uploading) {
        this.upload();
      } else {
        if (this.setTimeoutId) {
          clearTimeout(this.setTimeoutId);
        }
        this.setTimeoutId = setTimeout(async () => {
          await this.upload();
          this.setTimeoutId = null;
        }, CONF.DELAY_TIME);
      }
    } catch (e) {
      this.init();
    }
  }

  async upload() {
    const logs = this.tracks.slice(0, CONF.LENGTH); // 当前上传的埋点队列
    this.uploading = true;
    try {
      const res = await setLogger({ logs });
      if (res.status === 0) {
        this.tracks = this.tracks.slice(CONF.LENGTH);
        uni.setStorageSync('bmy__trackers', this.tracks);
        this.uploading = false;
      }
    } catch (error) {
      // 打印错误信息
      // this.errorLog(error);

      // 如果重试次数未超过最大尝试次数，则重试
      if (this.retryTimes < CONF.MAX_UPLOAD_RETRIES) {
        this.retryTimes++;
        await this.upload();
      }
    }
  }
}

export default Tracker;