/*
 * @Description: 文件上传相关方法
 * @Author: SaraSL
 * @Date: 2024-02-27 12:03:09
 */
import OSS from "ali-oss";
import CryptoJS from "crypto-js";
import dayjs from "dayjs";

// 声明全局wx和uni对象，解决TypeScript类型问题
declare const wx: any;
declare const uni: any;

function get(obj, path, defaultValue?) {
  if (!Array.isArray(path)) {
    // 将字符串路径转换为数组
    // 使用正则表达式匹配路径中的点（.）和方括号表示法（[]）
    path = path.replace(/$(\d+)$/g, ".$1").split(".");
  }

  for (let i = 0; i < path.length; i++) {
    if (obj == null) {
      return defaultValue;
    }
    obj = obj[path[i]];
  }

  return obj === undefined || obj === null ? defaultValue : obj;
}

// import * as _ from 'lodash';

// import { message } from 'antd';

import {
  createResource,
  fetchResourceByHash,
  fetchUploadToken,
  fileauthV4,
  generateSignature,
} from "../../api/api";
import cryptoJs from "crypto-js";

/**
 * @description 获取字符串的 MD5 值
 * @param {string} str
 * @returns
 */
export const str2Md5 = (str: string): string => {
  return CryptoJS.MD5(CryptoJS.enc.Latin1.parse(str)).toString(
    CryptoJS.enc.Hex
  );
};

/**
 * 从文件路径中提取文件扩展名
 * @param {string} filepath 
 * @returns {string} 返回文件扩展名(不含点)，如果没有则返回空字符串
 */
export const getFileExtension = (filepath?: string): string => {
  if (!filepath) return '';
  
  const dotIndex = filepath.lastIndexOf('.');
  if (dotIndex === -1 || dotIndex === filepath.length - 1) return '';
  
  return filepath.substring(dotIndex + 1);
};

export const fetchClient = async (callback: any) => {
  const res = await fetchUploadToken();
  if (!res) {
    return;
  }
  const params = res.data?res.data:res;
  const __client = new OSS({ ...params });
  callback(__client);
};

/**
 * 文件上传
 * @param client
 * @param file
 * @returns
 */
export const handleUpload = async (client: any, file: any) => {
  console.log("%c Line:178 🌰 file", "color:#6ec1c2", file);
  if (!client) {
    await fetchClient((__client: any) => {
      client = __client;
    });
  }

  const filepath = file?.path || file?.tempFilePath;
  if (!filepath) {
    console.error("文件路径不存在");
    return {};
  }

  // 优先使用传入的filename和name属性
  let filename = file?.filename || file?.name;
  
  // 如果未提供filename，则从路径中提取
  if (!filename) {
    let fileSplitName = "";
    const lastSlashIndex = filepath?.lastIndexOf("/");
    if (lastSlashIndex !== -1 ) {
      fileSplitName = filepath.substring(lastSlashIndex + 1);
    }
    filename = fileSplitName;
  }

  // 确保文件名不为空
  if (!filename) {
    const fileExt = getFileExtension(filepath);
    filename = `file_${dayjs().format("YYYYMMDDHHmmss")}${fileExt ? `.${fileExt}` : ''}`;
  }

  // 确保音频文件有正确的扩展名
  if (file?.fileType === 'audio' || file?.category === 10) {
    if (!filename.toLowerCase().endsWith('.mp3') && !filename.toLowerCase().endsWith('.wav')) {
      filename = `${filename}.mp3`;
    }
  }

  console.log("%c Line:187 🍿 最终文件名", "color:#7f2b82", filename);
  const str = str2Md5(`${filepath}-${Date.now()}`);
  const hashData = await fetchResourceByHash({
    filename: filename,
    hash: str,
  });

  if (hashData?.data?.uri && hashData?.data?.id) {
    return hashData.data;
  } else {
    const result = await generateSignature();

    // 使用月份和文件名构建OSS路径
    const key = `${dayjs().format("YYYYMM")}/${filename}`;
    // 构建表单数据
    const formData = {
      key,
      policy: result?.data?.policy, //表单域
      "x-oss-signature-version": result?.data?.signatureVersion, //指定签名的版本和算法
      "x-oss-credential": result?.data?.credential, //指明派生密钥的参数集
      "x-oss-date": result?.data?.ossDate, //请求的时间
      "x-oss-signature": result?.data?.signature, //签名认证描述信息
      "x-oss-security-token": result?.data?.accessStsToken, //安全令牌
      success_action_status: "200", //上传成功后响应状态码
    };
    
    return await new Promise((resolve) => {
      wx.uploadFile({
        // url: `https://pace-source.oss-cn-shenzhen.aliyuncs.com`, // 替换为你的服务器地址
        url: `https://pace-toy-store.oss-cn-shenzhen.aliyuncs.com`, // 替换为你的服务器地址
        filePath: filepath,
        name: "file",
        formData,
        success: function (res) {
          console.log("文件上传OSS响应:", res);
          if (res?.statusCode === 200) {
            // 准备资源同步对象
            const __resource = {
              hash: str,
              filename: filename,
              filepath: key,
              fileCategory: file?.fileCategory || 0,
              category: file?.category || 1,
              size: file?.size || 0,
              duration: file?.duration || 0,
            };
            
            // 调用资源同步API
            createResource(__resource)
              .then((res) => {
                console.log("%c 资源同步响应", "color:#93c0a4", res);
                if (res?.data?.uri && res?.data?.id) {
                  resolve(res.data);
                } else {
                  console.error("资源同步失败:", res);
                  uni.showToast({ title: "上传失败，请重试!", icon: "none" });
                  resolve({});
                }
              })
              .catch((error) => {
                console.error("资源同步错误:", error);
                uni.showToast({ title: "上传失败，请重试!", icon: "none" });
                resolve({});
              });
          } else {
            console.error("OSS上传失败:", res);
            uni.showToast({ title: "文件上传失败，请重试!", icon: "none" });
            resolve({});
          }
        },
        fail(err) {
          console.error("上传请求失败:", err);
          uni.showToast({ title: "上传失败，请重试!", icon: "none" });
          resolve({});
        },
      });
    });
  }
};
