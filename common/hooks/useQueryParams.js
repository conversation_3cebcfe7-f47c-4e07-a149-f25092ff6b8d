import { onShow } from "@dcloudio/uni-app";
import { ref } from 'vue'

export function useQueryParams(...params) {
  const getPageOptions = () => {
    try {
      const pages = getCurrentPages();
      return pages[pages.length - 1].options ?? {}
    } catch (e) {
      console.warn('获取页面参数失败:', e)
      return {}
    }
  }

  const getPageStack = () => {
    try {
      const pages = getCurrentPages(); // 获取当前页面栈
      console.log('页面栈:', pages);

      const currentPage = pages[pages.length - 1]; // 获取当前页面
      const prevPage = pages[pages.length - 2]; // 获取前一个页面
      return { prevPage, currentPage, pages }
    } catch (e) {
      console.warn('获取页面参数失败:', e)
      return {}
    }
  }

  // 创建响应式对象
  const paramsObj = ref({})

  // 初始化参数
  const initParams = () => {
    const current = getPageOptions()
    params.forEach(param => {
      paramsObj.value[param] = current[param]
    })
  }
  initParams()

  // 更新方法
  const updateParams = () => initParams()

  onShow(() => {
    updateParams()
    getPageStack()
  });

  return {
    params: paramsObj,
    updateParams,
    getPageStack
  }
}
