import config from "@/common/config.js";
import Tracker from "@/common/utils/Tracker.js";
// const Tracker = require('./utils/Tracker.js');
/**
 * @doc https://uniapp.dcloud.net.cn/api/interceptor.html#addinterceptor
 * **/
// 页面白名单，不受拦截
const whiteList = [
	'/',
	'/pages/login/login',
	'/pages/index/index',
	'/pages/user/user',
	'/privacyPolicy/privacy/privacyPolicy',
]

const tracker = new Tracker();

const hasPermission = (url) => {
	//isLogin是登录成功后在本地存储登录标识，存储一个能够判断用户登录的唯一标识就行，根据自己存储的数据类型来判断
	const TOKEN_NAME = config[config.DEFINE_ENV].TOKEN_NAME; // token名称

	let islogin = uni.getStorageSync(TOKEN_NAME) ? true : false;
	// 在白名单中或有登录判断条件可以直接跳转
	let urlArr = url.split('?')

	if (whiteList.indexOf(urlArr[0]) !== -1 || islogin) {
		return true
	}
	uni.showToast({
		title: '你没有权限，请联系管理员或者重新登录',
		icon: 'none',
		duration: 1500
	});
	return false
}

// 注意：拦截uni.switchTab本身没有问题。但是在微信小程序端点击tabbar的底层逻辑并不是触发uni.switchTab。所以误认为拦截无效，此类场景的解决方案是在tabbar页面的页面生命周期onShow中处理。
export default function () {
	const list = ['navigateTo', 'switchTab', 'redirectTo', 'reLaunch']
	list.forEach(item => {
		uni.addInterceptor(item, {
			invoke(e) {
				let urlArr = e.url.split('?')
				if (urlArr[0] == '/pages/login/login') tracker.clear();
				tracker.trigger({
					page: urlArr[0] == '/' ? '/pages/index/index' : urlArr[0],
					params: urlArr[1] ? urlArr[1] : '',
					title: e?.tabBarText ? e.tabBarText : '',
				})
				if (!hasPermission(e.url)) {
					uni.reLaunch({
						url: '/pages/login/login'
					})
					return false
				}
				return true
			},
			success(e) { }
		})
	})
}