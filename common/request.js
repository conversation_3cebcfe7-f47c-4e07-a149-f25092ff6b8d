/**
 * 发起网络请求
 *
 * @see https://uniapp.dcloud.net.cn/api/request/request.html#request
 *
 * 判断环境 给URL
 */
import config from "./config";
import { clearToken } from "@/utils/token.js";
import { wxL<PERSON><PERSON><PERSON><PERSON> } from "@/utils/index.js";
const TRUE_API = config[config.DEFINE_ENV].TRUE_API; // 实际网址
const JAVA_TRUE_API = config[config.DEFINE_ENV].JAVA_API; // 实际网址
const BASE_API = config[config.DEFINE_ENV].BASE_API; // 接口代理
const GLOBAL_API = config[config.DEFINE_ENV].GLOBAL_API; // 接口代理
const JAVA_API_URL = config[config.DEFINE_ENV].JAVA_API_URL; // Java API URL
const JAVA_BASE_API = config[config.DEFINE_ENV].JAVA_BASE_API; // Java API Base URL

//token判断环境的token
// if(process.env.NODE_ENV == config[config.DEFINE_ENV])
export const TOKEN_NAME = config[config.DEFINE_ENV].TOKEN_NAME; // token名称

export const generateUrl = (url) => {
  // 判断是否是Java API请求
  const baseUrl = url.startsWith('/jsapi') ? JAVA_API_URL : `${TRUE_API}${BASE_API}`;
  const finalUrl = url.startsWith('/jsapi') ? url.replace('/jsapi', JAVA_BASE_API) : url;
  return `${baseUrl}${finalUrl}`;
}

console.log("请求网址：", TRUE_API + BASE_API);
console.log("token名称：", TOKEN_NAME);

// 请求前的headers配置
const header = {
  Authorization: uni.getStorageSync(TOKEN_NAME),
  'x-source': "minip-b",
  "X-Instance-Selector": "host=*************"
};

export function request({
  url,
  method = "GET",
  data,
  responseType = "text",
  onSuccess,
  onFail,
  enableChunked = false,
  isGLobal,
  headers = {}, // 添加 headers 参数默认值
}) {
  return new Promise((resolve, reject) => {
    header.Authorization = uni.getStorageSync(TOKEN_NAME);

    const realUrl = generateUrl(url);

    // 创建请求任务
    const requestTask = uni.request({
      url: realUrl,
      header: {
        ...header,
        ...headers, // 合并传入的 headers
      },
      method: method,
      responseType: responseType,
      data,
      enableChunked: enableChunked,
      success: (res) => {
        // 如果是流式响应，直接返回
        if (enableChunked && onSuccess) {
          return onSuccess(res.data);
        }

        const { status, message } = res.data;
        console.log("请求返回：", status);


        // 判断token是否过期,过期跳到登录页面
        if (uni.getStorageSync('EXPIRE_TIME')) {
          const currentTime = Date.now();
          const expireTime = uni.getStorageSync('EXPIRE_TIME');
          if (currentTime > expireTime) {
            // 已过期，删除数据并跳到登录页面
            uni.$u.toast('登录过期，请重新登录')
            clearToken();
          }
        }
        // 常规响应处理
        if (status === 0 || status === 200 || res.statusCode === 200) {
          return resolve(res.data);
        } else if (status === 1) {
          uni.showToast({
            title: message,
            icon: "success",
          });
          return resolve(res.data);
        } else if (status >= 2) {
          uni.showToast({
            title: message,
            icon: "error",
          });
        } else if (res.statusCode == 401) {
          wxLoginApi(); // 静默登录
        } else if (status == -1001) {
          uni.showToast({
            title: message,
            icon: "error",
          });
          clearToken();
        } else {
          reject(res.data);
        }
      },
      fail: (err) => {
        console.log("err in request", err);
        if (onFail) {
          return onFail(err);
        }
        reject(err);
      },
    });

    // 设置分块数据接收回调
    if (enableChunked) {
      requestTask.onChunkReceived((response) => {
        if (onSuccess) {
          onSuccess(response.data);
        }
      });
    }
  });
}
