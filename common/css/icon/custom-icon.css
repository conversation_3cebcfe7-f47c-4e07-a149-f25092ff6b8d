@font-face {
  font-family: "iconfont"; /* Project id 468756 */
  src: url("@/static/fonts/iconfont.ttf") format("truetype");
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-copy:before {
  content: "\e77c";
}

.icon-message:before {
  content: "\e78a";
}

.icon-export:before {
  content: "\ea5a";
}

.icon-history:before {
  content: "\ead4";
}

.icon-el-icon-refresh-left:before {
  content: "\e6c4";
}
