// css规范 
// 标题/正文/主要信息：#333333  次要信息：#808080
$input-text-color: #B1B3B5;
$select-text-color: #525252;
$btn-color: #367CFF;
$text-color: #3F79FF;
$sec-info-color: #808080;

.layout {
	height: 100%;
	box-sizing: border-box;
	background-color: #f2f2f2;
	box-sizing: border-box;
	overflow: hidden;
}

.f-s-22 {
	font-size: 22rpx;
}

.f-s-24 {
	font-size: 24rpx;
}

.f-s-28 {
	font-size: 28rpx;
}

.f-s-30 {
	font-size: 30rpx;
}

.f-w-400 {
	font-weight: 400;
}

.f-w-500 {
	font-weight: 500;
}

.f-w-600 {
	font-weight: 600;
}



.text-Empty {
	color: $input-text-color;
	font-size: 26rpx;
	font-weight: 400;
	text-align: center;
	padding: 32rpx;
}

.flex {
	display: flex;
}

.flex-jc-ac {
	display: flex;
	justify-content: center;
	align-items: center;
}

.flex-jsb-ac {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.flex-ac {
	display: flex;
	align-items: center;
}

.flex-fs {
	display: flex;
	align-items: flex-start;
}

.flex-f1 {
	flex: 1;
}

.imglab {
	.u-image {
		font-size: 0 !important;
	}
}