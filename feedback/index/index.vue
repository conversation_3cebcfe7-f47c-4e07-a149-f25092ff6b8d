<!-- 反馈列表 -->
<template>
  <view class="layout">
    <BaseLayout navTitle="反馈列表">
      <th-table :column="state.columns" :listData="state.listData" height="0.5">
      </th-table>
    </BaseLayout>
  </view>
</template>

<script setup>
import { reactive } from "vue";
import BaseLayout from "@/components/base-layout/base-layout.vue";
import { getFeedbackList } from "../api";
import ThTable from "@/feedback/components/th-table/components/th-table/th-table.vue";
let state = reactive({
  columns: [
    {
      title: "id", // th标题
      key: "id", // 对应字段，
    },
    {
      title: "标题", // th标题
      key: "title", // 对应字段，
    },
    {
      title: "内容", // th标题
      key: "content", // 对应字段，
    },
    {
      title: "链接", // th标题
      key: "link", // 对应字段，
      align: "left"
    },
    {
      title: "状态", // th标题
      key: "state", // 对应字段，
      formatter: (row) => {
        return row == 1 ? "已提交" : "未提交"
      }
    },
  ],
  listData: [],
  parameter: {
    current: 1,
    pageSize: 99
  }
})
getFeedbackList(state.parameter).then(res => {
  console.log(res);

  state.listData = res.data;
})
</script>

<style lang="scss" scoped></style>