<!-- 新增反馈 -->
<template>
  <view class="layout">
    <BaseLayout navTitle="新增反馈">
      <view class="col">
        <up-form labelPosition="top" :model="fromData" ref="uFormRef" label-width="140rpx" :rules="rules">
          <up-form-item label="标题" prop="title" required>
            <up-input v-model="fromData.title" placeholder="请输入姓名"></up-input>
          </up-form-item>
          <up-form-item label="反馈内容" prop="content" required>
            <up-textarea v-model="fromData.content" maxlength="-1" disableDefaultPadding
              :placeholderStyle="{color: '#303133'}" autoHeight placeholder="请输入内容"></up-textarea>
          </up-form-item>
          <up-form-item label="链接" prop="link" required style="display: block;">
            <up-input v-model="fromData.link" placeholder="请输入链接"></up-input>
            <up-text type="info" text="请到有问题的页面，点击右上角三点，选择“复制链接”，粘贴到输入框中" margin="20rpx 0 0 0" align="center"
              size="25rpx" :block="true" style="text-align: center;"></up-text>
          </up-form-item>
          <up-form-item label="照片">
            <Upload type="image" :value="fromData.imageIds" @callback="callback" @emitDelFile="delFile" />
          </up-form-item>
        </up-form>
        <up-button style="margin-top: 100rpx;" @click="submit" color="#367CFF" shape="circle">提交</up-button>
      </view>

    </BaseLayout>
  </view>
</template>

<script setup>
import { reactive, ref } from "vue";
import BaseLayout from "@/components/base-layout/base-layout.vue";
import { subFeedback } from "../api";
let fromData = reactive(
  {
    title: '',
    content: '',
    link: '',
    imageIds: []
  }
)
let rules = reactive({
  title: [{
    required: true,
    message: '请输入姓名',
    trigger: ['blur', 'change']
  }],
  content: [{
    required: true,
    message: '请输入阶段名称',
    trigger: ['blur', 'change']
  }],
  link: [{
    required: true,
    message: '请输入阶段名称',
    trigger: ['blur', 'change']
  }]
})
let uFormRef = ref(null)
const submit = () => {
  console.log(fromData);
  uFormRef.value.validate().then(async valid => {
    if (valid) {
      let res = await subFeedback(fromData)
      if (res.status == 0) {
        uni.$u.toast('提交成功');
        uFormRef.value.resetFields();
      }
    }
  })

}
const callback = (list) => {
  list.forEach(item => fromData.imageIds.push(item.id));
}
// 图片删除
const delFile = (item, index) => {
  fromData.imageIds.splice(index, 1)
}
</script>

<style lang="scss" scoped>
.layout {
  box-sizing: border-box;

  .col {
    width: 100%;
    border-radius: 28rpx;
    background: #ffffff;
    box-shadow: 4rpx 8rpx 16rpx #eee;
    box-sizing: border-box;
    padding: 28rpx;
    margin-bottom: 24rpx;
    margin-top: 30rpx;
  }
}
</style>
