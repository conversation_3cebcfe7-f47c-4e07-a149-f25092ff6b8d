<template>
  <BaseLayout nav-title="童言童语" :content-style="{ padding: '0' }">
    <view class="evaluate-layout">
    <!-- 儿童信息栏 -->
    <view class="child-info-bar">
      <view class="child-info-left">
        <image class="child-avatar" :src="childInfo.avatar" mode="aspectFill" />
        <view class="child-details">
          <view class="child-name-row">
            <text class="child-name">{{ childInfo.name }}</text>
            <up-icon
              v-if="childInfo.gender === 1"
              name="/static/icon/male.png"
              size="28rpx"
            />
            <up-icon
              v-else-if="childInfo.gender === 2"
              name="/static/icon/female.png"
              size="28rpx"
            />
          </view>
          <text class="class-name">{{ classInfo.title || '加载中...' }}</text>
        </view>
      </view>
    </view>

    <!-- 课程和活动选择 -->
    <view class="col">
      <view class="title">课程活动选择</view>
      <view class="content">
        <view class="selection-item" @click="showCourseSelector = true">
          <text class="selection-label">选择课程</text>
          <view class="selection-value">
            <text class="selection-text">{{ selectedCourse.title || '请选择课程' }}</text>
            <up-icon name="arrow-right" size="24rpx" color="#999" />
          </view>
        </view>

        <view class="selection-item" @click="showActivitySelector = true" :class="{ disabled: !selectedCourse.id }">
          <text class="selection-label">选择活动</text>
          <view class="selection-value">
            <text class="selection-text">{{ selectedActivity.name || '请选择活动' }}</text>
            <up-icon name="arrow-right" size="24rpx" color="#999" />
          </view>
        </view>
      </view>
    </view>

    <!-- 过程性记录 -->
    <view class="col">
      <view class="title">过程性记录</view>
      <view class="content">
        <view class="content-title cort">
          <text>幼儿与教师的语言互动</text>
        </view>
        <view class="ctbox">
          <textarea
            ref="textareaRef"
            v-model="evaForm.childTeacherInteraction"
            confirmType="none"
            disableDefaultPadding
            placeholder="请输入幼儿与教师的语言互动"
            border="none"
            autoHeight
            autofocus
            :maxlength="-1"
            :cursorSpacing="100"
            placeholderStyle="font-size:28rpx;font-weight:400; color: #B1B3B5"
            @input="onInput"
            @focus="onFocus"
          />
          <view class="ctbox-action">
            <recorderBtn
              @click="onSpeech"
              :recorderText="recorderText"
              :recorderLoading="recorderLoading"
            />
            <EvaAiPolishBtn
              @click="onAiPolish"
            />
          </view>
        </view>
      </view>

      <view class="content">
        <view class="content-title">照片</view>
        <view>
          <Upload
            type="image"
            :value="evaForm.childTeacherInteractionPhotoResources"
            @callback="callbackCTIPR"
            @emitDelFile="delFileCTIPR"
            :showDel="true"
          />
        </view>
      </view>
    </view>

    <!-- 保存按钮 -->
    <view class="action-btn">
      <up-button
        type="primary"
        text="保存"
        color="#367CFF"
        round
        shape="circle"
        @click="send"
      ></up-button>
    </view>

    <!-- 课程选择弹窗 -->
    <up-picker
      ref="coursePicker"
      :show="showCourseSelector"
      :columns="[courseList]"
      keyName="title"
      :closeOnClickOverlay="true"
      @confirm="selectCourse"
      @cancel="showCourseSelector = false"
      @close="showCourseSelector = false"
    />

    <!-- 活动选择弹窗 -->
    <up-picker
      ref="activityPicker"
      :show="showActivitySelector"
      :columns="[activityList]"
      keyName="name"
      :closeOnClickOverlay="true"
      @confirm="selectActivity"
      @cancel="showActivitySelector = false"
      @close="showActivitySelector = false"
    />

    <!-- 儿童列表弹窗 -->
    <ChildredList
      :show="showChildList"
      :childredList="popupData.childrenData"
      :teacherList="popupData.teacherData"
      @select="handleChildSelect"
      @close="showChildList = false"
      :sendSearch="getStaffFn"
    />
    </view>
  </BaseLayout>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import {
  getChildrenList as getChildrenListEva,
  getTeacherList,
  getAiPolishCont,
  setevaDetails,
  getevaDetails,
} from "./api/evaluate.js";
import { getCourseList } from '@/api/api.js'
import { getChildrenInfo } from './api/children.js'
import { getclassDetail } from '@/api/classApi.js'
import { getActList } from '@/api/classApi.js'
import ChildredList from "./components/childredList.vue";
import recorderBtn from "./components/recorderBtn.vue";
import EvaAiPolishBtn from "./components/EvaAiPolishBtn.vue";
import Upload from "@/components/Upload/Upload.vue";
import BaseLayout from "@/components/base-layout/base-layout.vue";
import useRecorder from "./hook/useRecorder.js";

// 页面参数
const childId = ref('')
const childName = ref('')
const childGender = ref('')
const classId = ref('')

// 页面数据
const childInfo = ref({
  id: '',
  name: '',
  avatar: 'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_boy.png',
  gender: 1
})
const classInfo = ref({})

const popupData = reactive({
  childrenData: [],
  teacherData: [],
});

let evaForm = ref({
  childTeacherInteraction: "",
  childTeacherInteractionPhotoResources: [],
});

let showChildList = ref(false);
let textareaRef = ref(null); // 引用textarea组件
let cursorIndex = ref(0);

// 选择器状态
const showCourseSelector = ref(false)
const showActivitySelector = ref(false)

// 录音相关状态
const recorderLoading = ref(false)
const recorderText = ref('语音转文字')
const useRecorders = useRecorder(
  evaForm,
  "childTeacherInteraction",
  recorderLoading,
  recorderText
);

// 课程和活动数据
const courseList = ref([])
const activityList = ref([])
const selectedCourse = ref({})
const selectedActivity = ref({})

// 页面加载
onLoad((options) => {
  childId.value = options.childId
  childName.value = decodeURIComponent(options.childName || '')
  childGender.value = options.childGender
  classId.value = uni.getStorageSync('USER_INFO').currentClassId

  // 更新 childInfo 的基本信息
  childInfo.value.id = options.childId
  childInfo.value.name = decodeURIComponent(options.childName || '')
  childInfo.value.gender = parseInt(options.childGender) || 1
})

onMounted(() => {
  initPage()
})

// 监听@出现后，打开学生列表
const onInput = (e) => {
  const { value, cursor } = e.detail;
  cursorIndex.value = cursor;

  // 在当前光标处右边是否有@
  if (value[cursor - 1] == "@") {
    showChildList.value = true;
    uni.hideKeyboard();
  }
};

// 聚焦
const onFocus = () => {
  // 聚焦事件处理
};

// 选择学生后，替换@+学生名
const handleChildSelect = (val) => {
  let str = evaForm.value.childTeacherInteraction;
  let start = cursorIndex.value - 1;
  let end = cursorIndex.value;
  evaForm.value.childTeacherInteraction = `${str.substring(0, start)}@${
    val.title
  }${str.substring(end)}`;
  showChildList.value = false;
};

// start 语音
const onSpeech = () => {
  recorderLoading.value
    ? useRecorders.stopRecord()
    : useRecorders.startRecord();
};

// AI润色
const onAiPolish = async () => {
  if (evaForm.value.childTeacherInteraction == "") {
    return uni.showToast({
      title: "请输入内容",
      icon: "error",
    });
  }
  uni.showLoading({
    title: "修正中，请勿操作!",
    mask: true,
  });
  const data = {
    classId: uni.getStorageSync("USER_INFO").currentClassId,
    text: evaForm.value.childTeacherInteraction,
  };
  const res = await getAiPolishCont(data);
  if (res.status == 0) {
    evaForm.value.childTeacherInteraction = res.data;
  }
  uni.hideLoading();
};

// 初始化页面
const initPage = async () => {
  await getChildInfo()
  await getClassInfo()
  await getCourseListData()
  await getChildrenListFn()
  await getTeacherListFn()
}

// 获取儿童信息
const getChildInfo = async () => {
  if (childId.value) {
    try {
      const res = await getChildrenInfo(childId.value)
      if (res.status === 0 && res.data) {
        const child = res.data
        childInfo.value = {
          id: child.id,
          name: child.title,
          avatar: child.headers?.[0]?.uri || (child.sex === 1 ?
            'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_boy.png' :
            'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_girl.png'),
          gender: child.sex
        }
      }
    } catch (error) {
      console.error('获取儿童信息失败:', error)
      // 使用传入的参数作为备用
      childInfo.value.name = childName.value
    }
  } else {
    childInfo.value.name = childName.value
  }
}

// 获取班级信息
const getClassInfo = async () => {
  try {
    const res = await getclassDetail(classId.value)
    if (res.status === 0 && res.data) {
      classInfo.value = res.data
    }
  } catch (error) {
    console.error('获取班级信息失败:', error)
  }
}

// 获取课程列表
const getCourseListData = async () => {
  try {
    const params = {
      classId: classId.value,
      current: 1,
      pageSize: 999
    }
    const res = await getCourseList(params)
    if (res.status === 0 && res.data) {
      courseList.value = res.data.map(course => ({
        id: course.id,
        title: course.title
      }))
    }
  } catch (error) {
    console.error('获取课程列表失败:', error)
  }
}

// 获取所有儿童列表
const getChildrenListFn = async (title) => {
  let form = {
    current: 1,
    pageSize: 999,
    state: 1,
    classId: classId.value, // 班级id
  };
  if (title) form.title = title;

  try {
    let res = await getChildrenListEva(form);
    if (res.status == 0) {
      let childRes = res.data.map((item) => {
        return {
          id: item.id,
          title: item.title,
        };
      });
      popupData.childrenData = childRes;
    } else {
      uni.showToast({
        title: res.message || "获取儿童列表失败",
        icon: "error",
      });
    }
  } catch (error) {
    uni.showToast({
      title: "获取儿童列表失败",
      icon: "error",
    });
  }
};

// 获取教师列表
const getTeacherListFn = async () => {
  const currentClassId = uni.getStorageSync("classId") || classId.value;
  let form = {
    classId: currentClassId, // 班级id
  };

  try {
    let res = await getTeacherList(form);
    if (res.status == 0) {
      // 过滤掉当前isTeacher == 0的数据，与评价页面保持一致
      popupData.teacherData = res.data.reduce((acc, item) => {
        if (item.isTeacher === 1) {
          acc.push({
            id: item.id,
            title: item.name, // 使用name字段，与评价页面保持一致
            isTeacher: item.isTeacher,
          });
        }
        return acc;
      }, []);
    } else {
      uni.showToast({
        title: res.message || "获取教师列表失败",
        icon: "error",
      });
    }
  } catch (error) {
    uni.showToast({
      title: "获取教师列表失败",
      icon: "error",
    });
  }
};

// 搜索功能
const getStaffFn = (title) => {
  getChildrenListFn(title);
  getTeacherListFn(); // 教师列表不支持搜索，直接获取全部
};

// 选择课程
const selectCourse = (e) => {
  const { value } = e
  selectedCourse.value = value[0]
  selectedActivity.value = {} // 重置活动选择
  getActivityList()
  showCourseSelector.value = false
}

// 获取活动列表
const getActivityList = async () => {
  if (!selectedCourse.value.id) {
    return
  }

  try {
    const params = {
      subjectId: selectedCourse.value.id,
      current: 1,
      pageSize: 999
    }

    const res = await getActList(params)

    if (res.status === 0 && res.data) {
      // 提取所有阶段中的活动
      const allActivities = []
      res.data.forEach(stage => {
        if (stage.subjectActivities && stage.subjectActivities.length > 0) {
          stage.subjectActivities.forEach(activity => {
            allActivities.push({
              id: activity.id,
              name: activity.name
            })
          })
        }
      })

      activityList.value = allActivities
    } else {
      uni.showToast({
        title: res.message || '获取活动列表失败',
        icon: 'error'
      })
    }
  } catch (error) {
    uni.showToast({
      title: '获取活动列表失败',
      icon: 'error'
    })
  }
}

// 选择活动
const selectActivity = (e) => {
  const { value } = e
  selectedActivity.value = value[0]
  showActivitySelector.value = false
  // 获取活动详情
  getActivityDetails(value[0].id)
}

// 获取活动详情
const getActivityDetails = async (activityId) => {
  try {
    const res = await getevaDetails(activityId)
    if (res.status === 0 && res.data) {
      const activityData = res.data
      // 将现有的语言互动内容填充到表单中
      evaForm.value.childTeacherInteraction = activityData.childTeacherInteraction || ''
      // 将现有的照片资源填充到表单中
      evaForm.value.childTeacherInteractionPhotoResources = activityData.childTeacherInteractionPhotoResources || []
    }
  } catch (error) {
    console.error('获取活动详情失败:', error)
  }
}

// 照片上传回调
const callbackCTIPR = (list) => {
  evaForm.value.childTeacherInteractionPhotoResources.push(...list);
};

// 删除照片
const delFileCTIPR = (_, index) => {
  evaForm.value.childTeacherInteractionPhotoResources.splice(index, 1);
};

// 保存记录
const send = async () => {
  if (!selectedCourse.value.id || !selectedActivity.value.id) {
    uni.showToast({
      title: '请选择课程和活动',
      icon: 'none'
    })
    return
  }

  if (!evaForm.value.childTeacherInteraction.trim()) {
    uni.showToast({
      title: '请输入语言互动内容',
      icon: 'none'
    })
    return
  }

  try {
    uni.showLoading({
      title: '保存中...',
      mask: true
    })

    // 提取照片资源ID
    const photoResourceIds = evaForm.value.childTeacherInteractionPhotoResources.map(
      (item) => item.id
    ) || []

    // 提取对话中提到的孩子ID
    const mentionedChildIds = new Set()
    const regex = /@([^：]+)：/g
    let match

    while ((match = regex.exec(evaForm.value.childTeacherInteraction)) !== null) {
      const childName = match[1]
      // 在popupData.childrenData中查找对应孩子的ID
      const child = popupData.childrenData.find((c) => c.title === childName)
      if (child) {
        mentionedChildIds.add(child.id)
      }
    }

    // 构造请求参数
    const form = {
      id: selectedActivity.value.id, // 使用选中的活动ID
      childTeacherInteraction: evaForm.value.childTeacherInteraction,
      childTeacherInteractionChildIds: [...mentionedChildIds],
      childTeacherInteractionPhotoResourceIds: photoResourceIds,
    }

    const res = await setevaDetails(form)

    uni.hideLoading()

    if (res.status === 0) {
      uni.showToast({
        title: '保存成功',
        icon: 'success'
      })

      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    } else {
      uni.showToast({
        title: res.message || '保存失败',
        icon: 'error'
      })
    }
  } catch (error) {
    uni.hideLoading()
    console.error('保存失败:', error)
    uni.showToast({
      title: '保存失败',
      icon: 'error'
    })
  }
}
</script>

<style lang="scss" scoped>
@import "@/uni_modules/uview-plus/index.scss";

.child-info-bar {
  padding: 50rpx 0rpx;
  display: flex;
  align-items: center;
  // background: #fff;
  // margin: 0 32rpx 24rpx 32rpx;
  // border-radius: 20rpx;
  // box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);

  .child-info-left {
    display: flex;
    align-items: center;
    flex: 1;

    .child-avatar {
      width: 88rpx;
      height: 88rpx;
      border-radius: 50%;
      margin-right: 24rpx;
    }

    .child-details {
      display: flex;
      flex-direction: column;
      gap: 8rpx;

      .child-name-row {
        display: flex;
        align-items: center;
        gap: 8rpx;

        .child-name {
          font-size: 32rpx;
          font-weight: 600;
          color: #333;
        }
      }

      .class-name {
        font-size: 24rpx;
        color: #666;
      }
    }
  }
}

.selection-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f5f5f5;

  &:last-child {
    border-bottom: none;
  }

  &.disabled {
    opacity: 0.5;
    pointer-events: none;
  }

  .selection-label {
    font-size: 30rpx;
    color: #333;
    font-weight: 500;
  }

  .selection-value {
    display: flex;
    align-items: center;
    gap: 16rpx;

    .selection-text {
      font-size: 28rpx;
      color: #666;
    }
  }
}

.none {
  font-size: 28rpx;
  font-weight: 400;
  color: rgba(177, 179, 181, 1);
}

.ctbox {
  border: 1px solid #e6e6e6;
  border-radius: 12px;
  box-sizing: border-box;
  padding: 24rpx;
  position: relative;
  :deep(textarea) {
    width: 100%;
    min-height: 338rpx;
    font-size: 28rpx;
    font-weight: 400;
  }
  &-action {
    display: flex;
    align-items: center;
    flex-direction: row-reverse;
    gap: 16rpx;
    margin-top: 24rpx;
  }
}

.cort {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.evaluate-layout {
  padding: 0 32rpx;
  padding-bottom: 192rpx;
  overflow-y: auto;

  .action-btn {
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 50;
    width: 100%;
    box-sizing: border-box;
    padding: 16rpx 32rpx 32rpx 32rpx;
    background-color: #fff;
  }

  .col {
    border-radius: 28rpx;
    background: #fff;
    box-shadow: 4rpx 8rpx 16rpx rgba(0, 0, 0, 0.02);
    margin-top: 32rpx;
    padding: 32rpx;
    overflow: hidden;
    box-sizing: border-box;

    .title {
      /** 文本1 */
      font-size: 34rpx;
      font-weight: 600;
      color: rgba(51, 51, 51, 1);
      border-bottom: 2px solid #eeeeee;
      padding-bottom: 26rpx;
    }

    .content {
      .content-title {
        font-size: 28rpx;
        font-weight: 600;
        margin: 32rpx 0 26rpx 0;
      }
    }
  }
}
</style>
