<template>
  <BaseLayout nav-title="发动态" :content-style="{ padding: '0' }" containerClass="newBg">
    <!-- 顶部儿童信息栏 -->
    <view class="child-info-bar" @click="showChildSelector = true">
      <view class="child-info-left">
        <image class="child-avatar" :src="currentChild.avatar" mode="aspectFill" />
        <view class="child-details">
          <view class="child-name-row">
            <text class="child-name">{{ currentChild.name }}</text>
            <up-icon v-if="currentChild.gender === 1" name="/static/icon/male.png" size="28rpx" />
            <up-icon
              v-else-if="currentChild.gender === 2"
              name="/static/icon/female.png"
              size="28rpx"
            />
          </view>
          <text class="class-name">{{ classInfo.title || '加载中...' }}</text>
        </view>
      </view>
      <view class="switch-btn">
        <image src="/static/game/change.svg" class="switch-icon" />
      </view>
    </view>

    <!-- 四个功能板块 -->
    <view class="function-grid">
      <!-- 童言童语 -->
      <view class="function-item tyty-style" @click="navigateToFunction('tyty')">
        <image
          class="function-icon"
          src="https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/dynamics/tyty.svg"
        />
        <text class="function-title ft1">童言童语</text>
      </view>

      <!-- 观察记录 -->
      <view class="function-item gcjl-style" @click="navigateToFunction('gcjl')">
        <image
          class="function-icon"
          src="https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/dynamics/gcjl.svg"
        />
        <text class="function-title ft2">观察记录</text>
      </view>

      <!-- 表征作品/倾听 -->
      <view class="function-item bzzp-style" @click="navigateToFunction('bzzp')">
        <image
          class="function-icon"
          src="https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/dynamics/bzzp.svg"
        />
        <text class="function-title ft3">表征作品/倾听</text>
      </view>

      <!-- 照片 -->
      <view class="function-item zp-style" @click="navigateToFunction('zp')">
        <image
          class="function-icon"
          src="https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/dynamics/zpnew.svg"
        />
        <text class="function-title ft4">照片</text>
      </view>
    </view>

    <!-- 儿童选择弹窗 -->
    <up-picker
      ref="childPicker"
      :show="showChildSelector"
      :columns="[childrenList]"
      keyName="name"
      :closeOnClickOverlay="true"
      @confirm="selectChild"
      @cancel="showChildSelector = false"
      @close="showChildSelector = false"
    />
  </BaseLayout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { getChildrenList } from '@/api/children.js'
import { getclassDetail } from '@/api/classApi.js'
import BaseLayout from '@/components/base-layout/base-layout.vue'

// 页面参数
const classId = ref('')
const schoolId = ref('')

// 页面数据
const classInfo = ref({})
const childrenList = ref([])
const currentChild = ref({
  id: '',
  name: '请选择儿童',
  avatar: 'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_boy.png'
})
const showChildSelector = ref(false)

// 默认头像
const defaultAvatars = [
  'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_boy.png',
  'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_girl.png'
]

// 页面加载
onLoad((options) => {
  classId.value = options.classId || uni.getStorageSync('USER_INFO').currentClassId
  schoolId.value = options.schoolId || uni.getStorageSync('USER_INFO').currentSchoolId
})

onShow(() => {
  initPage()
})

onMounted(() => {
  initPage()
})

// 初始化页面
const initPage = async () => {
  await getClassInfo()
  await getChildrenInfo()
}

// 获取班级信息
const getClassInfo = async () => {
  try {
    const res = await getclassDetail(classId.value)
    if (res.status === 0 && res.data) {
      classInfo.value = res.data
    }
  } catch (error) {
    console.error('获取班级信息失败:', error)
  }
}

// 获取儿童列表
const getChildrenInfo = async () => {
  try {
    const params = {
      classId: classId.value,
      current: 1,
      pageSize: 999,
      state: 1 // 在园状态
    }

    const res = await getChildrenList(params)
    if (res.status === 0 && res.data) {
      const children = res.data.map((child) => {
        // 处理头像，优先使用 headers 中的头像，否则根据性别使用默认头像
        let avatar = defaultAvatars[child.sex === 1 ? 0 : 1] // 1=男孩，2=女孩
        if (child.headers && child.headers.length > 0) {
          avatar = child.headers[0].uri
        } else if (child.header) {
          avatar = child.header
        }

        return {
          id: child.id,
          name: child.title,
          avatar: avatar,
          gender: child.sex
        }
      })

      childrenList.value = children

      // 设置默认选中第一个儿童
      if (children.length > 0) {
        currentChild.value = children[0]
      }
    }
  } catch (error) {
    console.error('获取儿童信息失败:', error)
  }
}

// 选择儿童
const selectChild = (e) => {
  const { value } = e
  currentChild.value = value[0]
  showChildSelector.value = false
}

// 导航到功能页面
const navigateToFunction = (type) => {
  if (!currentChild.value.id) {
    uni.showToast({
      title: '请先选择儿童',
      icon: 'none'
    })
    return
  }

  // 根据功能类型导航到不同页面
  switch (type) {
    case 'tyty':
      // 童言童语
      uni.navigateTo({
        url: `/childrenDynamic/childrensWords?childId=${
          currentChild.value.id
        }&childName=${encodeURIComponent(currentChild.value.name)}&childGender=${
          currentChild.value.gender
        }`
      })
      break
    case 'gcjl':
      // 观察记录
      uni.navigateTo({
        url: `/observation/observationRecord?childId=${currentChild.value.id}&childName=${currentChild.value.name}`
      })
      break
    case 'bzzp':
      // 表征作品/倾听
      uni.navigateTo({
        url: `/oneToOneListening/index?childId=${currentChild.value.id}&childName=${currentChild.value.name}`
      })
      break
    case 'zp':
      // 照片
      uni.navigateTo({
        url: `/childrenDynamic/photos?childId=${
          currentChild.value.id
        }&childName=${encodeURIComponent(currentChild.value.name)}&childAvatar=${encodeURIComponent(
          currentChild.value.avatar
        )}&childGender=${currentChild.value.gender}`
      })
      break
    default:
      break
  }
}
</script>

<style lang="scss" scoped>
.child-info-bar {
  padding: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .child-info-left {
    display: flex;
    align-items: center;
    flex: 1;

    .child-avatar {
      width: 88rpx;
      height: 88rpx;
      border-radius: 50%;
      margin-right: 24rpx;
    }

    .child-details {
      display: flex;
      flex-direction: column;
      gap: 8rpx;

      .child-name-row {
        display: flex;
        align-items: center;
        gap: 8rpx;

        .child-name {
          font-size: 32rpx;
          font-weight: 600;
          color: #333;
        }
      }

      .class-name {
        font-size: 24rpx;
        color: #666;
      }
    }
  }

  .switch-btn {
    .switch-icon {
      width: 30rpx;
      height: 30rpx;
    }
  }
}

.function-grid {
  margin-top: 50rpx;
  padding: 0 32rpx;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;

  .function-item {
    height: 400rpx;
    border-radius: 40rpx;
    border: 6rpx solid rgba(255, 255, 255, 1);
    box-shadow: 4rpx 8rpx 16rpx rgba(0, 0, 0, 0.06);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 24rpx;
    position: relative;

    .function-icon {
      width: 150rpx;
      height: 150rpx;
    }

    .function-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #333;
    }
    .ft1 {
      color: rgba(32, 208, 192, 1);
    }
    .ft2 {
      color: rgba(244, 131, 131, 1);
    }
    .ft3 {
      color: rgba(187, 127, 231, 1);
    }
    .ft4 {
      color: rgba(255, 167, 84, 1);
    }

    &:active {
      transform: scale(0.98);
      transition: transform 0.1s ease;
    }
  }

  // 童言童语样式
  .tyty-style {
    background: linear-gradient(138.99deg, rgba(252, 255, 254, 1) 0%, rgba(222, 255, 252, 1) 100%);
  }
  // 观察记录样式
  .gcjl-style {
    background: linear-gradient(139.61deg, rgba(255, 252, 245, 1) 0%, rgba(255, 237, 219, 1) 100%),
      linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
  }
  // 表征作品/倾听样式
  .bzzp-style {
    background: linear-gradient(136.42deg, rgba(251, 242, 255, 1) 0%, rgba(245, 235, 255, 1) 100%),
      linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
  }
  // 照片样式
  .zp-style {
    background: linear-gradient(136.65deg, rgba(255, 242, 242, 1) 0%, rgba(255, 242, 242, 1) 100%);
  }
}
</style>
