<template>
  <BaseLayout
    nav-title="幼儿动态"
    :content-style="{ padding: '0' }"
    @leftClick="goback"
    @scrolltolower="loadMore"
  >
    <!-- 班级信息和筛选 -->
    <view class="header-info">
      <view class="class-info">
        <text class="class-name">{{ classInfo.title || '加载中...' }}</text>
      </view>
      <view class="filter-btn" @click="showFilter">
        <!-- <uni-icons type="sound" size="16" color="#007AFF"></uni-icons> -->
        <text class="filter-text">筛选</text>
      </view>
    </view>

    <!-- 动态列表 -->
    <view class="dynamic-list">
      <view v-for="(item, index) in dynamicList" :key="index" class="dynamic-item">
        <!-- 用户头像和信息 -->
        <view class="user-info">
          <image class="avatar" :src="item.childAvatar" mode="aspectFill" />
          <view class="user-details">
            <text class="username">{{ item.childName }}</text>
            <text class="time">{{ formatTime(item.latestTime) }}</text>
          </view>
        </view>

        <!-- 动态内容 - 显示该儿童的所有记录 -->
        <view class="content">
          <view
            v-for="(record, recordIndex) in item.recordList"
            :key="recordIndex"
            class="record-item"
            :class="{ 'not-first': recordIndex > 0 }"
          >
            <!-- 观察记录类型 -->
            <template v-if="record.recordType === 0">
              <view class="observation-content">
                <text class="record-type">{{ record.recordTypeDesc }}</text>
                <text class="record-time">{{ formatTime(record.recordTime) }}</text>
                <text class="activity-name" v-if="record.content.activityName"
                  >活动：{{ record.content.activityName }}</text
                >
                <text class="obs-location" v-if="record.content.observationLocation"
                  >观察地点：{{ record.content.observationLocation }}</text
                >
                <text class="obs-time" v-if="record.content.observationTime"
                  >观察时间：{{ formatTime(record.content.observationTime) }}</text
                >
                <text class="obs-type" v-if="record.content.observationType"
                  >观察类型：{{ record.content.observationType }}</text
                >

                <!-- 图片展示 -->
                <view class="photo-content" v-if="record.content.picUrlList">
                  <up-image
                    :src="record.content.picUrlList"
                    width="200"
                    height="150"
                    mode="aspectFill"
                    :lazy-load="true"
                  ></up-image>
                </view>
              </view>
            </template>

            <!-- 表征作品/倾听记录类型 -->
            <template v-if="record.recordType === 1">
              <view class="listening-content">
                <text class="record-type">{{ record.recordTypeDesc }}</text>
                <text class="record-time">{{ formatTime(record.recordTime) }}</text>
                <text class="activity-location" v-if="record.content.activityLocation"
                  >活动地点：{{ record.content.activityLocation }}</text
                >
                <text class="obs-time" v-if="record.content.observationTime"
                  >观察时间：{{ formatTime(record.content.observationTime) }}</text
                >
                <text class="title" v-if="record.content.title"
                  >标题：{{ record.content.title }}</text
                >

                <!-- 录音文本 -->
                <view class="audio-text" v-if="record.content.audioText">
                  <text class="audio-text-content">{{ record.content.audioText }}</text>
                </view>

                <!-- 分析评价 -->
                <view class="analysis" v-if="record.content.analysisEvaluation">
                  <text class="analysis-title">分析评价：</text>
                  <text class="analysis-content">{{ record.content.analysisEvaluation }}</text>
                </view>
              </view>
            </template>

            <!-- 儿童进区类型 -->
            <template v-if="record.recordType === 2">
              <view class="area-entry-content">
                <text class="record-type">{{ record.recordTypeDesc }}</text>
                <text class="record-time">{{ formatTime(record.recordTime) }}</text>
                <text class="area-name"
                  >进入区域：{{ record.content.area
                  }}{{ record.content.areaAlias ? `（${record.content.areaAlias}）` : '' }}</text
                >
                <text class="entry-time">进入时间：{{ formatTime(record.content.entryTime) }}</text>
                <text
                  class="exit-time"
                  v-if="
                    record.content.exitTime && record.content.exitTime !== '2025-07-17 23:00:01'
                  "
                >
                  离开时间：{{ formatTime(record.content.exitTime) }}
                </text>
              </view>
            </template>

            <!-- 儿童心情类型 -->
            <template v-if="record.recordType === 3">
              <view class="mood-content">
                <text class="record-type">{{ record.recordTypeDesc }}</text>
                <text class="record-time">{{ formatTime(record.recordTime) }}</text>
                <text class="mood-name" v-if="record.content.moodName"
                  >心情：{{ record.content.moodName }}</text
                >
                <text class="school-name" v-if="record.content.schoolName"
                  >学校：{{ record.content.schoolName }}</text
                >
                <text class="class-name" v-if="record.content.className"
                  >班级：{{ record.content.className }}</text
                >

                <!-- 心情图片展示 -->
                <view class="mood-image" v-if="record.content.imageUrl">
                  <image
                    :src="record.content.imageUrl"
                    class="mood-img"
                    mode="aspectFit"
                    @click="previewImage(record.content.imageUrl, [record.content.imageUrl])"
                  />
                </view>
              </view>
            </template>

            <!-- 照片/视频/音频类型 -->
            <template v-if="record.recordType === 4">
              <view class="media-content">
                <text class="record-type">{{ record.recordTypeDesc }}</text>
                <text class="record-time">{{ formatTime(record.recordTime) }}</text>
                <text class="media-count" v-if="record.content && record.content.list && record.content.list.length > 0">
                  共{{ record.content.list.length }}个文件
                </text>

                <!-- 媒体文件展示 -->
                <view v-if="record.content && record.content.list && record.content.list.length > 0" class="media-list">
                  <view
                    v-for="(media, mediaIndex) in record.content.list.slice(0, 3)"
                    :key="mediaIndex"
                    class="media-item"
                    :class="{
                      'video-item': isVideoFile(media.name),
                      'file-item-container': isOtherFile(media.name)
                    }"
                    @click="previewMedia(media, record.content.list)"
                  >
                    <!-- 视频文件 -->
                    <template v-if="isVideoFile(media.name)">
                      <video
                        :id="`video_${record.recordId}_${mediaIndex}`"
                        :src="media.url"
                        class="media-video"
                        :poster="`${media.url}?x-oss-process=video/snapshot,t_800`"
                        controls
                        show-mute-btn
                        show-fullscreen-btn
                        show-center-play-btn
                        object-fit="contain"
                        auto-pause-if-open-native
                        auto-pause-if-navigate
                        enable-play-gesture
                        :direction="0"
                      />
                    </template>
                    <!-- 图片文件 -->
                    <template v-else-if="isImageFile(media.name)">
                      <image
                        :src="media.url"
                        class="media-image"
                        mode="aspectFill"
                      />
                    </template>
                    <!-- 其他文件（PDF、文档等） -->
                    <template v-else>
                      <view class="file-item">
                        <image
                          src="https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/dynamics/file.svg"
                          class="file-icon"
                          mode="aspectFit"
                        />
                        <view class="file-info">
                          <text class="file-name">{{ getFileName(media.name) }}</text>
                          <text class="file-ext">{{ getFileExtension(media.name) }}</text>
                        </view>
                      </view>
                    </template>
                  </view>
                  <view v-if="record.content.list.length > 3" class="more-media">
                    +{{ record.content.list.length - 3 }}
                  </view>
                </view>
              </view>
            </template>

            <!-- 童言童语类型 -->
            <template v-if="record.recordType === 5">
              <view class="words-content">
                <text class="record-type">{{ record.recordTypeDesc }}</text>
                <text class="record-time">{{ formatTime(record.recordTime) }}</text>
                <text class="activity-name" v-if="record.content.name"
                  >活动名称：{{ record.content.name }}</text
                >
                <text class="author" v-if="record.content.author"
                  >记录者：{{ record.content.author }}</text
                >
                <text class="duration" v-if="record.content.durationInMinutes"
                  >活动时长：{{ record.content.durationInMinutes }}分钟</text
                >

                <!-- 童言童语内容展示 -->
                <view v-if="record.content.childTeacherInteraction" class="words-text-content">
                  <text class="words-text">{{ formatChildWords(record.content.childTeacherInteraction) }}</text>
                </view>
              </view>
            </template>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <up-loadmore :status="loading" loadmore-text="上拉或点击加载更多" @loadmore="loadMore" />

    <!-- 筛选弹窗 -->
    <Popup :show="showFilterPopup" @close="closeFilter">
      <view class="filter-popup">
        <view class="popup-header">
          <text class="popup-title">筛选条件</text>
          <uni-icons type="closeempty" size="20" @click="closeFilter"></uni-icons>
        </view>

        <view class="filter-content">
          <!-- 记录类型筛选 -->
          <view class="filter-item">
            <text class="filter-label">记录类型</text>
            <view class="type-options">
              <view
                v-for="option in recordTypeOptions"
                :key="option.value"
                class="type-option"
                :class="{ active: tempSelectedRecordTypes.includes(option.value) }"
                @click="toggleRecordType(option.value)"
              >
                <text class="option-text">{{ option.label }}</text>
                <uni-icons
                  v-if="tempSelectedRecordTypes.includes(option.value)"
                  type="checkmarkempty"
                  size="16"
                  color="#007AFF"
                ></uni-icons>
              </view>
            </view>
          </view>

          <!-- 日期范围筛选 -->
          <view class="filter-item">
            <text class="filter-label">日期范围</text>
            <uni-datetime-picker
              v-model="tempDateRange"
              type="daterange"
              :clear-icon="false"
              placeholder="请选择日期范围"
              @change="onDateChange"
            />
          </view>
        </view>

        <view class="popup-footer">
          <up-button
            type="primary"
            text="确定"
            @click="confirmFilter"
            :custom-style="{ width: '100%' }"
          ></up-button>
        </view>
      </view>
    </Popup>
  </BaseLayout>
</template>

<script setup>
import { ref } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { getClassDynamic } from '@/api/api.js'
import { getclassDetail } from '@/api/classApi.js'
import { getChildrenList } from '@/api/children.js'
import BaseLayout from '@/components/base-layout/base-layout.vue'
import Popup from '@/components/Popup/Popup.vue'
import { formatDate } from '@/utils/index.js'

// 页面参数
const classId = ref('')
const schoolId = ref('')

// 页面数据
const classInfo = ref({})
const dynamicList = ref([])
const loading = ref('loadmore')
const dateRange = ref([])
const childrenMap = ref(new Map()) // 儿童信息映射

// 筛选相关
const showFilterPopup = ref(false)
const selectedRecordTypes = ref([0, 1, 4, 5]) // 默认选择：观察记录、倾听记录、照片/视频/音频、童言童语
const tempSelectedRecordTypes = ref([0, 1, 4, 5]) // 临时选择状态
const tempDateRange = ref([]) // 临时日期范围

// 记录类型选项
const recordTypeOptions = [
  { value: 0, label: '观察记录' },
  { value: 1, label: '倾听记录' },
  { value: 2, label: '进区记录' },
  { value: 3, label: '儿童心情' },
  { value: 4, label: '照片/视频/音频' },
  { value: 5, label: '童言童语' }
]

// 默认头像（根据性别）
const defaultAvatars = [
  'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_boy.png', // 男孩
  'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_girl.png' // 女孩
]

// 页面加载
onLoad((options) => {
  classId.value = options.id || options.classId
  schoolId.value = options.schoolId

  // 设置默认日期范围（当前月1号至月底）
  const today = new Date()
  const year = today.getFullYear()
  const month = today.getMonth()
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)

  dateRange.value = [formatDateString(firstDay), formatDateString(lastDay)]

  // 同步临时日期范围
  tempDateRange.value = [...dateRange.value]
})

onShow(() => {
  initPage()
})

// 初始化页面
const initPage = async () => {
  await getClassInfo()
  await getChildrenInfo()
  // 确保儿童信息加载完成后再获取动态列表
  await getDynamicList()
}

// 获取班级信息
const getClassInfo = async () => {
  try {
    const res = await getclassDetail(classId.value)
    classInfo.value = res.data || {}
  } catch (error) {
    console.error('获取班级信息失败:', error)
  }
}

// 获取儿童信息
const getChildrenInfo = async () => {
  try {
    const params = {
      classId: classId.value,
      current: 1,
      pageSize: 999,
      state: 1 // 在园状态
    }

    const res = await getChildrenList(params)
    if (res.status === 0 && res.data) {
      // 创建儿童信息映射
      const map = new Map()
      res.data.forEach((child) => {
        // 处理头像，优先使用 headers 中的头像，否则根据性别使用默认头像
        let avatar = defaultAvatars[child.sex === 1 ? 0 : 1] // 1=男孩，2=女孩
        if (child.headers && child.headers.length > 0) {
          avatar = child.headers[0].uri
        } else if (child.header) {
          avatar = child.header
        }

        map.set(child.id, {
          name: child.title, // 儿童姓名字段是 title
          avatar: avatar,
          gender: child.sex // 性别字段是 sex
        })
      })
      childrenMap.value = map
    }
  } catch (error) {
    console.error('获取儿童信息失败:', error)
  }
}

// 获取动态列表
const getDynamicList = async () => {
  if (loading.value === 'loading') return

  loading.value = 'loading'

  try {
    const params = {
      classId: parseInt(classId.value),
      beginDate: dateRange.value[0],
      endDate: dateRange.value[1]
    }

    const res = await getClassDynamic(params)

    if (res.status === 0 && res.data) {
      // 处理数据，按儿童分组显示
      const groupedList = []

      res.data.forEach((childData) => {
        const childInfo = childrenMap.value.get(childData.childId) || {
          name: '未知儿童',
          avatar: defaultAvatars[0] // 默认使用男孩头像
        }

        // 筛选符合条件的记录
        const filteredRecords = childData.recordList.filter((record) =>
          selectedRecordTypes.value.includes(record.recordType)
        )

        // 如果有符合条件的记录，则添加到列表中
        if (filteredRecords.length > 0) {
          // 按时间倒序排列该儿童的记录
          filteredRecords.sort((a, b) => new Date(b.recordTime) - new Date(a.recordTime))

          groupedList.push({
            childId: childData.childId,
            childName: childInfo.name,
            childAvatar: childInfo.avatar,
            recordList: filteredRecords,
            // 使用最新记录的时间作为排序依据
            latestTime: filteredRecords[0].recordTime
          })
        }
      })

      // 按最新记录时间倒序排列
      groupedList.sort((a, b) => new Date(b.latestTime) - new Date(a.latestTime))

      dynamicList.value = groupedList
    }

    loading.value = 'nomore'
  } catch (error) {
    console.error('获取动态列表失败:', error)
    loading.value = 'loadmore'
  }
}

// 返回上一页
const goback = () => {
  uni.navigateBack({
    delta: 1
  })
}

// 加载更多
const loadMore = () => {
  // 暂时不实现分页，等接口数据结构确定后补充
}

// 显示筛选弹窗
const showFilter = () => {
  // 同步当前状态到临时状态
  tempSelectedRecordTypes.value = [...selectedRecordTypes.value]
  tempDateRange.value = [...dateRange.value]
  showFilterPopup.value = true
}

// 关闭筛选弹窗
const closeFilter = () => {
  showFilterPopup.value = false
}

// 切换记录类型选择
const toggleRecordType = (type) => {
  const index = tempSelectedRecordTypes.value.indexOf(type)
  if (index > -1) {
    // 如果已选择，则取消选择
    tempSelectedRecordTypes.value.splice(index, 1)
  } else {
    // 如果未选择，则添加选择
    tempSelectedRecordTypes.value.push(type)
  }
}

// 日期变化
const onDateChange = (e) => {
  tempDateRange.value = e
}

// 确认筛选
const confirmFilter = async () => {
  // 应用临时选择状态
  selectedRecordTypes.value = [...tempSelectedRecordTypes.value]
  dateRange.value = [...tempDateRange.value]

  closeFilter()
  // 重新获取动态列表
  await getDynamicList()
}

// 格式化时间
const formatTime = (time) => {
  return formatDate(time, 'MM-DD HH:mm')
}

// 格式化日期字符串
const formatDateString = (date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 预览图片
const previewImage = (current, urls) => {
  uni.previewImage({
    current,
    urls
  })
}

// 判断是否为视频文件
const isVideoFile = (fileName) => {
  if (!fileName) return false
  const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.m4v']
  const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
  return videoExtensions.includes(extension)
}

// 判断是否为图片文件
const isImageFile = (fileName) => {
  if (!fileName) return false
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.avif']
  const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
  return imageExtensions.includes(extension)
}

// 判断是否为其他文件（非图片非视频）
const isOtherFile = (fileName) => {
  return !isImageFile(fileName) && !isVideoFile(fileName)
}

// 获取文件名（不含扩展名）
const getFileName = (fullName) => {
  if (!fullName) return '未知文件'
  const lastDotIndex = fullName.lastIndexOf('.')
  if (lastDotIndex === -1) return fullName
  return fullName.substring(0, lastDotIndex)
}

// 获取文件扩展名
const getFileExtension = (fileName) => {
  if (!fileName) return ''
  const lastDotIndex = fileName.lastIndexOf('.')
  if (lastDotIndex === -1) return ''
  return fileName.substring(lastDotIndex + 1).toUpperCase()
}

// 格式化童言童语内容
const formatChildWords = (content) => {
  if (!content) return ''

  // 移除@符号和儿童姓名，只保留实际的童言童语内容
  // 例如：@孙莞柠  童言童语测试啊\n@孙莞柠  123
  return content
    .split('\n')
    .map(line => {
      // 移除每行开头的@儿童姓名部分
      const atIndex = line.indexOf('@')
      if (atIndex !== -1) {
        const spaceIndex = line.indexOf('  ', atIndex) // 查找两个空格
        if (spaceIndex !== -1) {
          return line.substring(spaceIndex + 2).trim() // 获取空格后的内容
        }
      }
      return line.trim()
    })
    .filter(line => line.length > 0) // 过滤空行
    .join('\n')
}

// 预览媒体文件
const previewMedia = (currentMedia, mediaList) => {
  // 如果是视频文件，不需要特殊处理，直接点击video组件即可播放
  if (isVideoFile(currentMedia.name)) {
    return
  }

  // 如果是其他文件（PDF、文档等），尝试下载或打开
  if (isOtherFile(currentMedia.name)) {
    downloadFile(currentMedia)
    return
  }

  // 只预览图片类型的文件
  const imageUrls = mediaList
    .filter(media => isImageFile(media.name))
    .map(media => media.url)

  if (imageUrls.length > 0) {
    uni.previewImage({
      current: currentMedia.url,
      urls: imageUrls
    })
  }
}

// 下载文件
const downloadFile = (media) => {
  uni.showActionSheet({
    itemList: ['预览文件', '保存到相册'],
    success: (res) => {
      if (res.tapIndex === 0) {
        // 预览文件
        uni.downloadFile({
          url: media.url,
          success: (downloadRes) => {
            if (downloadRes.statusCode === 200) {
              uni.openDocument({
                filePath: downloadRes.tempFilePath,
                success: () => {
                  console.log('打开文档成功')
                },
                fail: (err) => {
                  console.error('打开文档失败:', err)
                  uni.showToast({
                    title: '无法打开此文件',
                    icon: 'none'
                  })
                }
              })
            }
          },
          fail: (err) => {
            console.error('下载文件失败:', err)
            uni.showToast({
              title: '下载失败',
              icon: 'none'
            })
          }
        })
      } else if (res.tapIndex === 1) {
        // 保存到相册（仅适用于图片）
        uni.showToast({
          title: '此文件类型不支持保存到相册',
          icon: 'none'
        })
      }
    }
  })
}
</script>

<style lang="scss" scoped>
.header-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  // border-bottom: 1rpx solid #f0f0f0;

  .class-info {
    .class-name {
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
    }
  }

  .filter-btn {
    display: flex;
    align-items: center;
    padding: 10rpx 20rpx;
    // background: #f8f9fa;
    border-radius: 20rpx;

    .filter-text {
      margin-left: 8rpx;
      font-size: 28rpx;
      color: #007aff;
    }
  }
}

.dynamic-list {
  padding: 0 30rpx;

  .dynamic-item {
    background: #fff;
    border-radius: 16rpx;
    padding: 30rpx;
    margin: 20rpx 0;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);

    .user-info {
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;

      .avatar {
        width: 72rpx;
        height: 72rpx;
        margin-right: 20rpx;
        border-radius: 36rpx;
        overflow: hidden;
      }

      .user-details {
        flex: 1;
        display: flex;
        flex-direction: column;

        .username {
          font-size: 30rpx;
          font-weight: 500;
          color: #333;
          margin-bottom: 8rpx;
        }

        .time {
          font-size: 24rpx;
          color: #999;
        }
      }
    }

    .content {
      margin-bottom: 20rpx;

      .record-item {
        margin-bottom: 20rpx;

        &.not-first {
          border-top: 1rpx solid #f0f0f0;
          padding-top: 20rpx;
        }
      }

      .observation-content,
      .listening-content,
      .area-entry-content,
      .mood-content,
      .media-content,
      .words-content {
        text {
          display: block;
          font-size: 28rpx;
          color: #333;
          margin-bottom: 10rpx;
        }

        .record-type {
          font-weight: 500;
          color: #007aff;
          margin-bottom: 8rpx;
        }

        .record-time {
          font-size: 24rpx;
          color: #999;
          margin-bottom: 15rpx;
        }

        .audio-text {
          background: #f0f7ff;
          padding: 20rpx;
          border-radius: 12rpx;
          margin: 15rpx 0;

          .audio-text-content {
            font-size: 28rpx;
            color: #333;
            line-height: 1.6;
          }
        }

        .analysis {
          background: #f8f9fa;
          padding: 20rpx;
          border-radius: 12rpx;
          margin-top: 15rpx;

          .analysis-title {
            font-weight: 500;
            color: #333;
            margin-bottom: 10rpx;
          }

          .analysis-content {
            font-size: 28rpx;
            color: #666;
            line-height: 1.6;
          }
        }
      }

      .photo-content {
        text-align: center;
        margin: 15rpx 0;
      }

      // 心情记录样式
      .mood-image {
        margin-top: 16rpx;

        .mood-img {
          width: 120rpx;
          height: 120rpx;
          border-radius: 8rpx;
        }
      }

      // 媒体文件样式
      .media-list {
        display: flex;
        flex-wrap: wrap;
        gap: 12rpx;
        margin-top: 16rpx;

        .media-item {
          border-radius: 8rpx;
          overflow: hidden;
          background: #f5f5f5;
          position: relative;
          width: 120rpx;
          height: 120rpx;

          // 视频项目样式
          &.video-item {
            width: 100%;
            height: 300rpx;
            margin-bottom: 12rpx;
          }

          // 文件项目样式
          &.file-item-container {
            width: 120rpx;
            height: 120rpx;
            background: #f8f9fa;
            border: 1rpx solid #e5e5e7;
          }

          .media-image {
            width: 100%;
            height: 100%;
          }

          .media-video {
            width: 100%;
            height: 100%;
            border-radius: 8rpx;
          }

          .file-item {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 16rpx;
            box-sizing: border-box;

            .file-icon {
              width: 48rpx;
              height: 48rpx;
              margin-bottom: 8rpx;
            }

            .file-info {
              text-align: center;
              width: 100%;

              .file-name {
                display: block;
                font-size: 20rpx;
                color: #333;
                line-height: 1.2;
                margin-bottom: 4rpx;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }

              .file-ext {
                display: block;
                font-size: 18rpx;
                color: #999;
                font-weight: 500;
              }
            }
          }
        }

        .more-media {
          width: 120rpx;
          height: 120rpx;
          border-radius: 8rpx;
          background: rgba(0, 0, 0, 0.6);
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          font-size: 24rpx;
        }
      }

      // 童言童语样式
      .words-text-content {
        margin-top: 16rpx;
        padding: 24rpx;
        background: #f8f9fa;
        border-radius: 12rpx;
        border-left: 4rpx solid rgba(240, 145, 77, 1);

        .words-text {
          font-size: 28rpx;
          color: #333;
          line-height: 1.6;
          white-space: pre-wrap;
          word-break: break-all;
        }
      }
    }
  }
}

.filter-popup {
  padding: 40rpx 30rpx;

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40rpx;

    .popup-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
    }
  }

  .filter-content {
    .filter-item {
      margin-bottom: 30rpx;

      .filter-label {
        display: block;
        font-size: 28rpx;
        color: #333;
        margin-bottom: 20rpx;
      }
    }
  }

  .popup-footer {
    margin-top: 40rpx;
  }

  .type-options {
    display: flex;
    flex-direction: column;
    gap: 20rpx;

    .type-option {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx;
      background: #f8f9fa;
      border-radius: 12rpx;
      border: 2rpx solid transparent;

      &.active {
        background: #f0f7ff;
        border-color: #007aff;
      }

      .option-text {
        font-size: 28rpx;
        color: #333;
      }
    }
  }
}
</style>
