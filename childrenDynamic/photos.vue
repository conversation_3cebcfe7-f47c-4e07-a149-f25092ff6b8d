<template>
  <BaseLayout nav-title="照片" :content-style="{ padding: '0' }">
    <view class="photos-layout">
      <!-- 儿童信息栏 -->
      <view class="child-info-bar">
        <view class="child-avatar">
          <image :src="getChildAvatar()" mode="aspectFill" />
        </view>
        <view class="child-details">
          <view class="child-name-row">
            <text class="child-name">{{ childInfo.title || childName }}</text>
            <up-icon
              v-if="getChildGender() === 1"
              name="/static/icon/male.png"
              size="28rpx"
            />
            <up-icon
              v-else-if="getChildGender() === 2"
              name="/static/icon/female.png"
              size="28rpx"
            />
          </view>
          <view class="class-info">{{ classInfo.title || '未知班级' }}</view>
        </view>
      </view>

      <!-- 媒体文件上传区域 -->
      <view class="upload-section">
        <view class="section-title">上传文件</view>
        <view class="upload-area">
          <Upload
            type="all"
            :value="photoList"
            @callback="handlePhotoUpload"
            @emitDelFile="handlePhotoDelete"
            :showDel="true"
            :maxCount="9"
            :fileCategory="0"
          />
        </view>
      </view>

      <!-- 保存按钮 -->
      <view class="action-btn">
        <up-button
          type="primary"
          text="保存"
          color="#367CFF"
          round
          shape="circle"
          @click="savePhotos"
        ></up-button>
      </view>
    </view>
  </BaseLayout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getChildrenInfo } from './api/children.js'
import { getclassDetail } from '@/api/classApi.js'
import { addMedia } from './api/index.js'
import Upload from '@/components/dynamicUpload/Upload.vue'
import BaseLayout from '@/components/base-layout/base-layout.vue'

// 页面参数
const childId = ref('')
const childName = ref('')
const childAvatar = ref('')
const childGender = ref('')
const classId = ref('')

// 数据状态
const childInfo = ref({})
const classInfo = ref({})
const photoList = ref([])

// 默认头像
const defaultAvatars = [
  'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_boy.png',
  'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_girl.png'
]

// 页面加载
onLoad((options) => {
  childId.value = options.childId
  childName.value = options.childName
  childAvatar.value = decodeURIComponent(options.childAvatar || '')
  childGender.value = options.childGender
  classId.value = uni.getStorageSync('USER_INFO').currentClassId
})

onMounted(() => {
  initPage()
})

// 获取儿童头像
const getChildAvatar = () => {
  // 优先使用从 index 页面传递的头像参数
  if (childAvatar.value) {
    return childAvatar.value
  }

  // 如果没有传递头像，从 childInfo 中获取
  if (childInfo.value) {
    // 优先使用 headers 中的头像
    if (childInfo.value.headers && childInfo.value.headers.length > 0) {
      return childInfo.value.headers[0].uri
    }

    // 其次使用 header 字段
    if (childInfo.value.header) {
      return childInfo.value.header
    }

    // 根据性别使用默认头像
    const gender = childInfo.value.sex || childGender.value
    return defaultAvatars[gender === 1 ? 0 : 1] // 1=男孩，2=女孩
  }

  // 最后使用默认头像
  return defaultAvatars[childGender.value === 1 ? 0 : 1]
}

// 获取儿童性别
const getChildGender = () => {
  // 优先使用从 index 页面传递的性别参数
  if (childGender.value) {
    return parseInt(childGender.value)
  }

  // 其次从 childInfo 中获取
  if (childInfo.value && childInfo.value.sex) {
    return childInfo.value.sex
  }

  // 默认返回男孩
  return 1
}

// 初始化页面
const initPage = async () => {
  await getChildInfo()
  await getClassInfo()
}

// 获取儿童信息
const getChildInfo = async () => {
  if (!childId.value) return

  try {
    const res = await getChildrenInfo(childId.value)
    if (res.status === 0) {
      childInfo.value = res.data
    }
  } catch (error) {
    console.error('获取儿童信息失败:', error)
  }
}

// 获取班级信息
const getClassInfo = async () => {
  if (!classId.value) return

  try {
    const res = await getclassDetail(classId.value)
    if (res.status === 0) {
      classInfo.value = res.data
    }
  } catch (error) {
    console.error('获取班级信息失败:', error)
  }
}

// 处理照片上传
const handlePhotoUpload = (list) => {
  photoList.value.push(...list)
}

// 处理照片删除
const handlePhotoDelete = (_, index) => {
  photoList.value.splice(index, 1)
}

// 保存文件
const savePhotos = async () => {
  if (photoList.value.length === 0) {
    uni.showToast({
      title: '请先上传文件',
      icon: 'none'
    })
    return
  }

  try {
    uni.showLoading({
      title: '保存中...',
      mask: true
    })

    // 提取文件ID
    const fileIds = photoList.value.map(file => file.id).filter(id => id)
    console.log('photoList.value:', photoList.value)
    console.log('fileIds:', fileIds)

    if (fileIds.length === 0) {
      uni.hideLoading()
      uni.showToast({
        title: '文件上传未完成',
        icon: 'none'
      })
      return
    }

    // 调用 addMedia 接口
    const data = {
      childId: childId.value,
      ids: fileIds.join(',') // 将数组转换为逗号分隔的字符串
    }
    console.log('addMedia data:', data)
    const res = await addMedia(data)

    uni.hideLoading()

    if (res.status === 0) {
      uni.showToast({
        title: '保存成功',
        icon: 'success'
      })

      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    } else {
      uni.showToast({
        title: res.message || '保存失败',
        icon: 'error'
      })
    }
  } catch (error) {
    uni.hideLoading()
    console.error('保存文件失败:', error)
    uni.showToast({
      title: '保存失败',
      icon: 'error'
    })
  }
}
</script>

<style lang="scss" scoped>
.photos-layout {
  padding: 20rpx;
}

.child-info-bar {
  display: flex;
  align-items: center;
  padding: 30rpx 0rpx 60rpx 0rpx;

  .child-avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 20rpx;

    image {
      width: 100%;
      height: 100%;
    }
  }

  .child-details {
    flex: 1;

    .child-name-row {
      display: flex;
      align-items: center;
      margin-bottom: 8rpx;

      .child-name {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        margin-right: 8rpx;
      }
    }

    .class-info {
      font-size: 28rpx;
      color: #666;
    }
  }
}

.upload-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 30rpx;
  }

  .upload-area {
    min-height: 200rpx;
  }
}

.action-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx;
  background: white;
  border-top: 1rpx solid #eee;
  z-index: 100;
}
</style>
