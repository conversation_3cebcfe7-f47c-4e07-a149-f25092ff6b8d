<template>
  <view class="speech" @click.stop.prevent="emit('click')">
    <up-loading-icon style="margin-right: 6rpx" v-if="recorderLoading" mode="circle" size="36rpx" color="#3f79ff" />
    <image src="@/static/icon/evaluate_speech.png" v-else />
    <text>{{ recorderText }}</text>
  </view>
</template>
<script setup>
defineProps({
  recorderText: {
  type: String,
  default: "语音转文字",
},
  recorderLoading: {
  type: <PERSON><PERSON>an,
},
});
const emit = defineEmits(["click"]);
</script>
<style lang="scss" scoped>
.speech {
  width: fit-content;
  height: 60rpx;
  color: #3f79ff;
  font-size: 24rpx;
  font-weight: 500;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 34rpx;
  background: rgba(54, 124, 255, 0.06);
  image {
    width: 36rpx;
    height: 36rpx;
    margin-right: 6rpx;
  }
}
</style>
