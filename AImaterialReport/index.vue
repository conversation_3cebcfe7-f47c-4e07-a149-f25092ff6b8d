<template>
  <BaseLayout nav-title="AI材料报告" :content-style="{ padding: '0' }">
    <view class="container">
      <!-- 历史报告列表  -->
      <view class="report-list">
        <!-- 空状态占位符 -->
        <view v-if="reportList.length === 0" class="empty-state">
          <view class="empty-icon">📄</view>
          <view class="empty-text">暂无AI材料报告</view>
          <view class="empty-desc">点击下方按钮生成新的报告</view>
        </view>
        
        <view 
          v-for="item in reportList" 
          :key="item.id" 
          class="report-item" 
          :class="{ 
            'report-item-clickable': item.status === 1 && item.fileUrl,
            'report-item-error': item.status === -1
          }"
          @click="onReportClick(item)"
        >
          <view class="report-item-header">
            <view class="title"> {{ item.taskName }}</view>
            <view class="time">{{ item.createTime }}</view>
            <view class="status-wrapper">
              <view 
                class="status" 
                :class="{
                  'status-error': item.status === -1,
                  'status-pending': item.status === 0,
                  'status-success': item.status === 1
                }"
              >
                {{ getStatusText(item.status) }}
                <!-- 异常状态显示重试按钮 -->
                <up-icon 
                  v-if="item.status === -1" 
                  name="reload" 
                  size="24rpx" 
                  color="#d32f2f"
                  style="margin-left: 8rpx;"
                  @click.stop="retryReport(item)"
                />
              </view>
              <!-- <view v-if="item.status === 1 && item.fileUrl" class="download-hint">
                点击下载
              </view> -->
            </view>
          </view>
          <view class="item-header-center" @click.capture.stop="onOpenPop(item)">
            <up-icon class="card-icon" size="42rpx" name="more-dot-fill" />
          </view>
        </view>
      </view>
      <!-- 生成报告按钮 -->
      <view class="add-btn-wrap">
        <button class="add-btn" @click="addReport">生成最新报告</button>
      </view>
    </view>
    <Popup :show="isPop" @close="isPop = false">
      <view class="iconAction">
        <!-- <view @click="onDownloadFile" v-if="activeItem && activeItem.status === 1">
          <up-icon name="download" size="40rpx"></up-icon>
          <view>下载文件</view>
        </view>
        <view @click="onOpenFile" v-if="activeItem && activeItem.status === 1">
          <up-icon name="file-text" size="40rpx"></up-icon>
          <view>打开文件</view>
        </view> -->
        <view @click="onDelete">
          <image src="@/static/common/delete.png"></image>
          <view>删除</view>
        </view>
      </view>
    </Popup>
  </BaseLayout>
</template>

<script setup>
import BaseLayout from '@/components/base-layout/base-layout.vue'
import {
  queryMaterialReportByClass,
  createMaterialReport,
  deleteMaterialReport,
  retryMaterialReport
} from '@/api/game.js'
import { getclassList } from '@/api'
import { onShow, onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'
const schoolName = ref('')
const className = ref('')
const schoolId = ref('')
const classId = ref('')
// 弹框控制
let isPop = ref(false)
// 当前点击的item
let activeItem = ref(null)
// 报告列表
const reportList = ref([])

const addReport = async () => {
  try {
    // 参数验证，确保schoolId和schoolName匹配
    if (!schoolId.value || !schoolName.value || !classId.value || !className.value) {
      uni.showToast({
        title: '参数不完整，请重新进入页面',
        icon: 'none'
      })
      return
    }
    
    console.log('生成AI材料报告参数:', {
      schoolName: schoolName.value,
      className: className.value,
      schoolId: schoolId.value,
      classId: classId.value
    })
    
    uni.showLoading({
      title: '生成中...',
      mask: true
    })
    
    const res = await createMaterialReport({
      schoolName: schoolName.value,
      className: className.value,
      schoolId: schoolId.value,
      classId: classId.value
    })
    
    if (res.status === 0) {
      uni.showToast({
        title: '生成成功',
        icon: 'success'
      })
      // 刷新列表
      await refreshReportList()
    } else {
      uni.showToast({
        title: res.message || '生成失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('生成报告失败:', error)
    uni.showToast({
      title: '生成失败',
      icon: 'none'
    })
  } finally {
    uni.hideLoading()
  }
}

// 获取文件类型
function getFileType(fileUrl) {
  if (!fileUrl) return 'pdf'
  const extension = fileUrl.split('.').pop().toLowerCase()
  const typeMap = {
    'pdf': 'pdf',
    'doc': 'doc',
    'docx': 'docx',
    'xls': 'xls',
    'xlsx': 'xlsx',
    'ppt': 'ppt',
    'pptx': 'pptx',
    'txt': 'txt'
  }
  return typeMap[extension] || 'pdf'
}

// 获取状态文本
function getStatusText(status) {
  switch (status) {
    case -1:
      return '异常'
    case 0:
      return '生成中'
    case 1:
      return '完成'
    default:
      return '未知'
  }
}

// 点击报告项
function onReportClick(item) {
  if (item.status === 1 && item.fileUrl) {
    // 如果状态为完成且有文件URL，直接下载
    onDownloadFileDirectly(item)
  } else if (item.status === 0) {
    uni.showToast({
      title: '报告正在生成中，请稍后...',
      icon: 'none'
    })
  } else if (item.status === -1) {
    uni.showToast({
      title: '报告生成异常，点击重试按钮重新生成',
      icon: 'none'
    })
  }
}

// 重试生成报告
async function retryReport(item) {
  try {
    uni.showLoading({
      title: '重新生成中...',
      mask: true
    })
    
    const res = await retryMaterialReport(item.id)
    
    if (res.status === 0) {
      uni.showToast({
        title: '重新生成成功',
        icon: 'success'
      })
      // 刷新列表
      await refreshReportList()
    } else {
      uni.showToast({
        title: res.message || '重新生成失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('重新生成报告失败:', error)
    uni.showToast({
      title: '重新生成失败',
      icon: 'none'
    })
  } finally {
    uni.hideLoading()
  }
}

// 直接下载文件
function onDownloadFileDirectly(item) {
  if (!item.fileUrl) {
    uni.showToast({
      title: '文件不存在',
      icon: 'none'
    })
    return
  }
  
  uni.showLoading({
    title: '下载中...',
    mask: true
  })
  
  // 微信小程序端使用 wx.downloadFile 以获得更好的体验
  // #ifdef MP-WEIXIN
  wx.downloadFile({
    url: item.fileUrl,
    success: (res) => {
      if (res.statusCode === 200) {
        uni.showToast({
          title: '下载成功',
          icon: 'success'
        })
        // 打开文件
        uni.openDocument({
          filePath: res.tempFilePath,
          showMenu: true,
          fileType: getFileType(item.fileUrl),
          success: function () {
            console.log('打开文档成功')
          },
          fail: function (err) {
            console.error('打开文档失败:', err)
            uni.showToast({
              title: '打开文件失败',
              icon: 'none'
            })
          }
        })
      } else {
        uni.showToast({
          title: '下载失败',
          icon: 'none'
        })
      }
    },
    fail: (err) => {
      console.error('下载失败:', err)
      uni.showToast({
        title: '下载失败',
        icon: 'none'
      })
    },
    complete: () => {
      uni.hideLoading()
    }
  })
  // #endif
  
  // 其他平台使用 uni.downloadFile
  // #ifndef MP-WEIXIN
  uni.downloadFile({
    url: item.fileUrl,
    success: (res) => {
      if (res.statusCode === 200) {
        uni.showToast({
          title: '下载成功',
          icon: 'success'
        })
        // 打开文件
        uni.openDocument({
          filePath: res.tempFilePath,
          showMenu: true,
          fileType: getFileType(item.fileUrl),
          success: function () {
            console.log('打开文档成功')
          },
          fail: function (err) {
            console.error('打开文档失败:', err)
            uni.showToast({
              title: '打开文件失败',
              icon: 'none'
            })
          }
        })
      } else {
        uni.showToast({
          title: '下载失败',
          icon: 'none'
        })
      }
    },
    fail: (err) => {
      console.error('下载失败:', err)
      uni.showToast({
        title: '下载失败',
        icon: 'none'
      })
    },
    complete: () => {
      uni.hideLoading()
    }
  })
  // #endif
}

// 下载文件
function onDownloadFile() {
  if (!activeItem.value || !activeItem.value.fileUrl) {
    uni.showToast({
      title: '文件不存在',
      icon: 'none'
    })
    return
  }
  
  isPop.value = false
  uni.showLoading({
    title: '下载中...',
    mask: true
  })
  
  // 微信小程序端使用 wx.downloadFile
  // #ifdef MP-WEIXIN
  wx.downloadFile({
    url: activeItem.value.fileUrl,
    success: (res) => {
      if (res.statusCode === 200) {
        uni.showToast({
          title: '下载成功',
          icon: 'success'
        })
      } else {
        uni.showToast({
          title: '下载失败',
          icon: 'none'
        })
      }
    },
    fail: (err) => {
      console.error('下载失败:', err)
      uni.showToast({
        title: '下载失败',
        icon: 'none'
      })
    },
    complete: () => {
      uni.hideLoading()
    }
  })
  // #endif
  
  // 其他平台使用 uni.downloadFile
  // #ifndef MP-WEIXIN
  uni.downloadFile({
    url: activeItem.value.fileUrl,
    success: (res) => {
      if (res.statusCode === 200) {
        uni.showToast({
          title: '下载成功',
          icon: 'success'
        })
      } else {
        uni.showToast({
          title: '下载失败',
          icon: 'none'
        })
      }
    },
    fail: (err) => {
      console.error('下载失败:', err)
      uni.showToast({
        title: '下载失败',
        icon: 'none'
      })
    },
    complete: () => {
      uni.hideLoading()
    }
  })
  // #endif
}

// 打开文件
function onOpenFile() {
  if (!activeItem.value || !activeItem.value.fileUrl) {
    uni.showToast({
      title: '文件不存在',
      icon: 'none'
    })
    return
  }
  
  isPop.value = false
  uni.showLoading({
    title: '打开中...',
    mask: true
  })
  
  // 微信小程序端使用 wx.downloadFile
  // #ifdef MP-WEIXIN
  wx.downloadFile({
    url: activeItem.value.fileUrl,
    success: (res) => {
      if (res.statusCode === 200) {
        uni.openDocument({
          filePath: res.tempFilePath,
          showMenu: true,
          fileType: getFileType(activeItem.value.fileUrl),
          success: function () {
            console.log('打开文档成功')
            uni.showToast({
              title: '打开成功',
              icon: 'success'
            })
          },
          fail: function (err) {
            console.error('打开文档失败:', err)
            uni.showToast({
              title: '打开文件失败',
              icon: 'none'
            })
          }
        })
      } else {
        uni.showToast({
          title: '下载失败',
          icon: 'none'
        })
      }
    },
    fail: (err) => {
      console.error('下载失败:', err)
      uni.showToast({
        title: '下载失败',
        icon: 'none'
      })
    },
    complete: () => {
      uni.hideLoading()
    }
  })
  // #endif
  
  // 其他平台使用 uni.downloadFile
  // #ifndef MP-WEIXIN
  uni.downloadFile({
    url: activeItem.value.fileUrl,
    success: (res) => {
      if (res.statusCode === 200) {
        uni.openDocument({
          filePath: res.tempFilePath,
          showMenu: true,
          fileType: getFileType(activeItem.value.fileUrl),
          success: function () {
            console.log('打开文档成功')
            uni.showToast({
              title: '打开成功',
              icon: 'success'
            })
          },
          fail: function (err) {
            console.error('打开文档失败:', err)
            uni.showToast({
              title: '打开文件失败',
              icon: 'none'
            })
          }
        })
      } else {
        uni.showToast({
          title: '下载失败',
          icon: 'none'
        })
      }
    },
    fail: (err) => {
      console.error('下载失败:', err)
      uni.showToast({
        title: '下载失败',
        icon: 'none'
      })
    },
    complete: () => {
      uni.hideLoading()
    }
  })
  // #endif
}

// 刷新报告列表
async function refreshReportList() {
  try {
    const classId = uni.getStorageSync('USER_INFO').currentClassId
    const res = await queryMaterialReportByClass({
      classId: classId
    })
    reportList.value = res.data || []
  } catch (error) {
    console.error('刷新列表失败:', error)
  }
}

// 打开操作弹窗
function onOpenPop(item) {
  isPop.value = true
  activeItem.value = item
}

// 删除一项
async function onDelete() {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这条倾听记录吗？',
    success: async function (res) {
      if (res.confirm) {
        uni.showLoading({
          title: '删除中...',
          mask: true
        })
        try {
          const r = await deleteMaterialReport(activeItem.value.id)
          if (r.status === 0) {
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            })
            isPop.value = false
            // 刷新列表
            await refreshReportList()
          } else {
            uni.showToast({
              title: r.message || '删除失败',
              icon: 'none'
            })
          }
        } catch (err) {
          console.error('删除记录失败:', err)
          uni.showToast({
            title: '删除失败',
            icon: 'none'
          })
        } finally {
          uni.hideLoading()
        }
      }
    }
  })
}

onLoad(async (options) => {
  schoolName.value = options.schoolName
  className.value = options.className
  schoolId.value = options.schoolId
  classId.value = options.classId
  
  // 如果从URL参数获取的学校信息不完整，尝试从本地存储补充
  if (!schoolName.value && schoolId.value) {
    const savedSchoolTitle = uni.getStorageSync('CURRENT_SCHOOL_TITLE')
    if (savedSchoolTitle) {
      schoolName.value = savedSchoolTitle
    }
  }
  
  // 如果从URL参数获取的班级信息不完整，尝试从本地存储补充
  if (!className.value && classId.value) {
    try {
      const userInfo = uni.getStorageSync('USER_INFO')
      if (userInfo.currentClassId === classId.value) {
        // 可以通过API获取班级名称
        const res = await getclassList({ schoolId: schoolId.value })
        if (res.status === 0) {
          const classInfo = res.data.find(item => item.id === classId.value)
          if (classInfo) {
            className.value = classInfo.title
          }
        }
      }
    } catch (error) {
      console.error('获取班级信息失败:', error)
    }
  }
  
  console.log('AI材料报告页面参数:', {
    schoolName: schoolName.value,
    className: className.value,
    schoolId: schoolId.value,
    classId: classId.value
  })
})
onShow(async () => {
  await refreshReportList()
})
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
  position: relative;
  padding: 20rpx;
  .report-list {
    padding-bottom: 140rpx;
    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 120rpx 40rpx;
      text-align: center;
      
      .empty-icon {
        font-size: 120rpx;
        margin-bottom: 40rpx;
        opacity: 0.6;
      }
      
      .empty-text {
        font-size: 32rpx;
        color: #333333;
        font-weight: 600;
        margin-bottom: 20rpx;
      }
      
      .empty-desc {
        font-size: 26rpx;
        color: #808080;
        line-height: 1.4;
      }
    }
    
    .report-item {
      background: #fff;
      margin-bottom: 20rpx;
      border-radius: 12rpx;
      box-shadow: 4rpx 8rpx 16rpx #eee;
      padding: 30rpx;
      border-radius: 28rpx;
      position: relative;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      transition: all 0.3s ease;
      
      &.report-item-clickable {
        cursor: pointer;
        
        &:hover {
          transform: translateY(-2rpx);
          box-shadow: 4rpx 12rpx 24rpx rgba(0, 0, 0, 0.15);
        }
        
        &:active {
          background: #f5f7fa;
        }
      }
      
      &.report-item-error {
        border-left: 8rpx solid #d32f2f;
        background: #fff5f5;
      }
    }
    .report-item-header {
      flex: 1;
      .title {
        font-size: 28rpx;
        color: #333333;
        font-weight: 600;
        margin-bottom: 10rpx;
        width: 600rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .time {
        font-size: 24rpx;
        color: #808080;
        margin-bottom: 10rpx;
      }
      .status-wrapper {
        .status {
          display: inline-flex;
          align-items: center;
          padding: 4rpx 12rpx;
          border-radius: 8rpx;
          font-size: 20rpx;
          font-weight: 500;
          margin-bottom: 8rpx;
          
          &.status-error {
            background: #ffebee;
            color: #d32f2f;
            
            .u-icon {
              cursor: pointer;
              transition: transform 0.3s ease;
              
              &:hover {
                transform: rotate(180deg);
              }
            }
          }
          
          &.status-pending {
            background: #fff3e0;
            color: #f57c00;
          }
          
          &.status-success {
            background: #e8f5e8;
            color: #388e3c;
          }
        }
        
        .download-hint {
          font-size: 20rpx;
          color: #3f79ff;
          font-weight: 500;
        }
      }
    }
  }
  .add-btn-wrap {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100vw;
    background: #fff;
    box-shadow: 0 -2rpx 16rpx rgba(0, 0, 0, 0.04);
    padding: 30rpx 20rpx 50rpx 20rpx;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    .add-btn {
      width: 75%;
      height: 80rpx;
      line-height: 80rpx;
      background: #3f79ff;
      color: #fff;
      border-radius: 44rpx;
      font-size: 30rpx;
      font-weight: 600;
      border: none;
      margin: 0 !important;
    }
  }
}
.iconAction {
  & > view {
    height: 88rpx;
    display: flex;
    align-items: center;
    font-size: 30rpx;
    font-weight: 400;
    letter-spacing: 0rpx;
    line-height: 48rpx;
    color: rgba(51, 51, 51, 1);
    text-align: left;
    vertical-align: middle;

    image {
      width: 40rpx;
      height: 40rpx;
      margin-right: 28rpx;
    }
    
    .u-icon {
      margin-right: 28rpx;
    }
  }
}
</style>
